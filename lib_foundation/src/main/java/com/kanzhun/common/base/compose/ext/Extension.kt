package com.kanzhun.common.base.compose.ext

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Picture
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams
import androidx.activity.ComponentActivity
import androidx.annotation.IdRes
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.IntrinsicMeasurable
import androidx.compose.ui.layout.IntrinsicMeasureScope
import androidx.compose.ui.layout.LayoutModifier
import androidx.compose.ui.layout.Measurable
import androidx.compose.ui.layout.MeasureResult
import androidx.compose.ui.layout.MeasureScope
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.core.graphics.createBitmap
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.findViewTreeViewModelStoreOwner
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.setViewTreeViewModelStoreOwner
import androidx.savedstate.findViewTreeSavedStateRegistryOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.kanzhun.foundation.R

@Composable
fun Modifier.noRippleClickable(
    preventMultipleClick: Boolean = true,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    onClick: () -> Unit
): Modifier {
    val multipleEventsCutter = remember { MultipleEventsCutter.get() }
    return this then Modifier.clickable(
        indication = null,
        interactionSource = interactionSource,
        onClick = { if (preventMultipleClick) multipleEventsCutter.processEvent { onClick() } else onClick() },
    )
}

fun Modifier.conditional(condition: Boolean, modifier: Modifier.() -> Modifier): Modifier {
    return if (condition) {
        then(modifier(Modifier))
    } else {
        this
    }
}

fun Modifier.conditional(
    condition: Boolean,
    ifTrue: Modifier.() -> Modifier,
    ifFalse: (Modifier.() -> Modifier)? = null
): Modifier {
    return if (condition) {
        then(ifTrue(Modifier))
    } else if (ifFalse != null) {
        then(ifFalse(Modifier))
    } else {
        this
    }
}

@Composable
fun Dp.dpToPx() = with(LocalDensity.current) { <EMAIL>() }


@Composable
fun Int.pxToDp() = with(LocalDensity.current) { <EMAIL>() }

// 设置默认的View组合策略：ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed
fun ComposeView.onSetViewTreeContent(yourContent: @Composable () -> Unit) {
    setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)

    setContent {
        yourContent()
    }
}

// 设置默认的View组合策略：ViewCompositionStrategy.DisposeOnDetachedFromWindowOrReleasedFromPool
fun ComposeView.onSetWindowContent(yourContent: @Composable () -> Unit) {
    setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnDetachedFromWindowOrReleasedFromPool)

    setContent {
        yourContent()
    }
}

@Composable
fun boldFontFamily() = FontFamily(
    Font(
        path = "fonts/SourceHanSerifCN-Bold.otf",
        assetManager = LocalContext.current.assets
    )
)

@Suppress("unused")
@Composable
fun kanzhunFontFamily() =
    FontFamily(Font(path = "fonts/kanzhun_regular.otf", assetManager = LocalContext.current.assets))

fun attachOwners(owner: ComponentActivity, targetView: View) {
    targetView.apply {
        if (findViewTreeLifecycleOwner() == null) {
            setViewTreeLifecycleOwner(owner)
        }
        if (findViewTreeViewModelStoreOwner() == null) {
            setViewTreeViewModelStoreOwner(owner)
        }
        if (findViewTreeSavedStateRegistryOwner() == null) {
            setViewTreeSavedStateRegistryOwner(owner)
        }
    }
}

fun ComponentActivity.onSetContent(
    @IdRes id: Int = R.id.id_compose_view,
    yourContent: @Composable () -> Unit
) {
    window.decorView.apply {
        onRemoveContent(id = id)

        (this as ViewGroup).addView(ComposeView(this@onSetContent).apply {
            this.id = id

            onSetViewTreeContent {
                yourContent()
            }
        }, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))

        attachOwners(this@onSetContent, this)
    }
}

fun ComponentActivity.onRemoveContent(@IdRes id: Int = R.id.id_compose_view) {
    window.decorView.apply {
        (this as ViewGroup).removeView(findViewById(id))
    }
}

fun createBitmapFromPicture(
    picture: Picture,
    drawBgColor: Int = android.graphics.Color.WHITE
): Bitmap {
    val bitmap = createBitmap(picture.width, picture.height)

    val canvas = Canvas(bitmap)
    canvas.drawColor(drawBgColor)
    canvas.drawPicture(picture)
    return bitmap
}

/**
 * Rotates the composable by 90 degrees increments, taking layout into account: the composable
 * is rendered taking into account the fact usable space changes as the composable rotates.
 *
 * Usage of this API renders this composable into a separate graphics layer.

 * @see Modifier.rotate
 * @see graphicsLayer

 * https://stackoverflow.com/a/********/********
 */
fun Modifier.rotateLayout(rotation: Rotation): Modifier {
    return when (rotation) {
        Rotation.ROT_0, Rotation.ROT_180 -> this
        Rotation.ROT_90, Rotation.ROT_270 -> then(HorizontalLayoutModifier)
    } then rotate(rotation.degrees)
}

enum class Rotation(val degrees: Float) {
    ROT_0(0f),
    ROT_90(90f),
    ROT_180(180f),
    ROT_270(270f),
}

/** Swap horizontal and vertical constraints */
private fun Constraints.transpose(): Constraints {
    return copy(
        minWidth = minHeight,
        maxWidth = maxHeight,
        minHeight = minWidth,
        maxHeight = maxWidth
    )
}

private object HorizontalLayoutModifier : LayoutModifier {
    override fun MeasureScope.measure(
        measurable: Measurable,
        constraints: Constraints
    ): MeasureResult {
        val placeable = measurable.measure(constraints.transpose())
        return layout(placeable.height, placeable.width) {
            placeable.place(
                x = -(placeable.width / 2 - placeable.height / 2),
                y = -(placeable.height / 2 - placeable.width / 2)
            )
        }
    }

    override fun IntrinsicMeasureScope.minIntrinsicHeight(
        measurable: IntrinsicMeasurable,
        width: Int
    ): Int {
        return measurable.maxIntrinsicWidth(width)
    }

    override fun IntrinsicMeasureScope.maxIntrinsicHeight(
        measurable: IntrinsicMeasurable,
        width: Int
    ): Int {
        return measurable.maxIntrinsicWidth(width)
    }

    override fun IntrinsicMeasureScope.minIntrinsicWidth(
        measurable: IntrinsicMeasurable,
        height: Int
    ): Int {
        return measurable.minIntrinsicHeight(height)
    }

    override fun IntrinsicMeasureScope.maxIntrinsicWidth(
        measurable: IntrinsicMeasurable,
        height: Int
    ): Int {
        return measurable.maxIntrinsicHeight(height)
    }
}