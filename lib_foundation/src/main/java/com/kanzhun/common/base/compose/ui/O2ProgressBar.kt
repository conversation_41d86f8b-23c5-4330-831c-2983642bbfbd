package com.kanzhun.common.base.compose.ui

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

enum class ProgressOrientation {
    Horizontal, Vertical
}

@Composable
fun O2ProgressBar(
    modifier: Modifier = Modifier,
    progress: Float,
    orientation: ProgressOrientation = ProgressOrientation.Horizontal,
    thickness: Dp = 8.dp,
    cornerRadius: Dp = 4.dp,
    trackBrush: Brush = Brush.linearGradient(listOf(Color(0xFFEBEBEB), Color(0xFFEBEBEB))),
    progressBrush: Brush = Brush.linearGradient(
        0.0f to Color(0xFFEB66FF),
        1.0f to Color(0xFF4281FF)
    )
) {
    val progressModifier = when (orientation) {
        ProgressOrientation.Horizontal -> modifier
            .fillMaxWidth()
            .height(thickness)

        ProgressOrientation.Vertical -> modifier
            .fillMaxHeight()
            .width(thickness)
    }

    Canvas(modifier = progressModifier) {
        // Draw track (background)
        drawRoundRect(
            brush = trackBrush,
            size = Size(size.width, size.height),
            cornerRadius = CornerRadius(cornerRadius.toPx())
        )

        // Draw progress
        when (orientation) {
            ProgressOrientation.Horizontal -> {
                drawRoundRect(
                    brush = progressBrush,
                    size = Size(size.width * progress.coerceIn(0f, 1f), size.height),
                    cornerRadius = CornerRadius(cornerRadius.toPx())
                )
            }

            ProgressOrientation.Vertical -> {
                val progressHeight = size.height * progress.coerceIn(0f, 1f)
                drawRoundRect(
                    brush = progressBrush,
                    topLeft = Offset(0f, size.height - progressHeight),
                    size = Size(size.width, progressHeight),
                    cornerRadius = CornerRadius(cornerRadius.toPx())
                )
            }
        }
    }
}

@Preview
@Composable
private fun HorizontalProgressBarPreview() {
    O2ProgressBar(
        modifier = Modifier.width(200.dp),
        progress = 0.7f,
        orientation = ProgressOrientation.Horizontal,
        thickness = 8.dp,
        progressBrush = Brush.linearGradient(
            0.0f to Color(0xFFEB66FF),
            1.0f to Color(0xFF4281FF)
        )
    )
}

@Preview
@Composable
private fun VerticalProgressBarPreview() {
    O2ProgressBar(
        modifier = Modifier.height(200.dp),
        progress = 0.7f,
        orientation = ProgressOrientation.Vertical,
        thickness = 8.dp,
        progressBrush = Brush.verticalGradient(
            0.0f to Color(0xFFEB66FF),
            1.0f to Color(0xFF4281FF)
        )
    )
}

@Preview
@Composable
private fun BigFiveVerticalProgressBarPreview() {
    O2ProgressBar(
        modifier = Modifier.height(50.dp),
        progress = 0.7f,
        orientation = ProgressOrientation.Vertical,
        thickness = 20.dp,
        trackBrush = Brush.verticalGradient(colors = listOf(Color(0xFFF5F5F5), Color(0xFFF5F5F5))),
        progressBrush = Brush.verticalGradient(
            0.0f to Color(0xFFF2B1FC),
            1.0f to Color(0xFFFFB58E)
        )
    )
}