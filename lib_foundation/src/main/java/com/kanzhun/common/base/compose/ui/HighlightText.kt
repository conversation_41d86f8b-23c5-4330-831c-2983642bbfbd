package com.kanzhun.common.base.compose.ui

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.techwolf.lib.tlog.TLog

private const val TAG = "HighlightText"

@Composable
fun HighlightedText(
    text: String,
    totalLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    highlightColor: Color = Color(0xFFA0190D),
    textStyle: TextStyle = TextStyle(
        fontSize = 16.sp,
        fontWeight = FontWeight(400),
        color = Color(0xFF7F7F7F),
    ),
    modifier: Modifier = Modifier
) {
    val spans = remember(text) {
        buildAnnotatedString {
            try {
                var startIndex = 0
                while (true) {
                    /*
                        String PART_BEGIN = "\u200b"; // 特殊处理部分文字开头
                        String PART_END = "\u2060"; // 特殊处理部分文字结尾
                    */
                    val matchResult = "\u200b([\\s\\S]*?)\u2060".toRegex().find(text, startIndex)
                    if (matchResult == null) break
                    val endIndex = matchResult.range.last + 1
                    append(text.substring(startIndex, matchResult.range.first))
                    append(buildAnnotatedString {
                        withStyle(
                            style = SpanStyle(
                                color = highlightColor,
                                fontWeight = FontWeight.Companion.W600
                            )
                        ) {
                            append(matchResult.groupValues[1])
                        }
                    })
                    startIndex = endIndex
                }
                if (startIndex < text.length) {
                    append(text.substring(startIndex))
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "HighlightedText error: $e")
            }
        }
    }
    Text(
        text = spans,
        style = textStyle,
        maxLines = totalLines,
        overflow = overflow,
        modifier = modifier
    )
}