package com.kanzhun.foundation.model.profile.ext

import android.graphics.Color
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.foundation.R
import com.kanzhun.foundation.model.profile.OpenScreen

fun OpenScreen?.getCharacterColor(): Int {
    return if (this?.bgColor.isNullOrBlank()) {
        R.color.common_color_33FF648F.toResourceColor()
    } else {
        try {
            Color.parseColor(this?.bgColor)
        } catch (e: Exception) {
            R.color.common_color_33FF648F.toResourceColor()
        }
    }

}

/**
 * 转换成20%透明度的颜色
 */
fun Int.toColor20p(): Int {
    return Color.argb(40, Color.red(this), Color.green(this), Color.blue(this))
}

fun Int.toColor30p(): Int {
    return Color.argb(178, Color.red(this), Color.green(this), Color.blue(this))
}
