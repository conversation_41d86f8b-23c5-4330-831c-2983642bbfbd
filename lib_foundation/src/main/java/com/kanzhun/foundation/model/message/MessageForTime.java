package com.kanzhun.foundation.model.message;

import com.xxjz.orange.protocol.codec.ChatProtocol;

public class MessageForTime extends ChatMessage {

    public MessageForTime() {
        setMediaType(MessageConstants.MSG_TIME);
    }

    @Override
    protected void parserMessage(ChatProtocol.OgMessage message) {
        setContent(message.getBody().getText().getText());
    }

    @Override
    public void parseFromDB() {
        super.parseFromDB();

    }
}
