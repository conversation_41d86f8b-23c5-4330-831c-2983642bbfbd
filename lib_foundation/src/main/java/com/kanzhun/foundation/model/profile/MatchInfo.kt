package com.kanzhun.foundation.model.profile

import java.io.Serializable

data class MatchInfo(var match: <PERSON><PERSON>an,
                     var matchContent: String? = "",
                     var matchTips: String? = "",
                     var score: Int = 0,
                     var loginUser: MatchUserInfo? = null,
                     var viewUser: MatchUserInfo? = null,
                     var compareContent: List<CompareContent>? = null
) : Serializable

data class MatchUserInfo(var tinyAvatar: String? = null,
                         var mbtiName: String? = null,
                         var mbtiCode: String? = null) : Serializable

data class CompareContent(var left: String?,
                          var right: String?,
                          var ourMatchContent: String? = null
) : Serializable