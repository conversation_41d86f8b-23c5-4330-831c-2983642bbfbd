package com.kanzhun.foundation.model

import java.io.Serializable

data class SelectLocationModel(
    var levelCode1:String? = "",//省份
    var levelName1:String? = "",
    var levelCode2:String? = "",//地区或者城市
    var levelName2:String? = "",
    var specialProvince:Boolean = false
):Serializable {

    /**
     * 这里不管直辖市
     * 拼接好的显示内容，例如 北京·海淀区
     */
    fun getDisplayString():String{
        return listOf(levelName1, levelName2).filter { !it.isNullOrBlank() }.joinToString("·")
    }

}