package com.kanzhun.foundation.model;

import static com.kanzhun.foundation.db.DBConstants.TAB_MSG_READ;

import androidx.room.Entity;
import androidx.room.Ignore;

import org.jetbrains.annotations.NotNull;

@Entity(tableName = TAB_MSG_READ, primaryKeys = {"chatId", "type"})
public class ReadStatus {
    @NotNull
    private String chatId;
    private int type;
    private long messageId;
    private long readTime;
    @Ignore
    private int status;

    @Ignore
    public ReadStatus(long messageId, String chatId, int type, int status) {
        this.messageId = messageId;
        this.chatId = chatId;
        this.type = type;
        this.status = status;
    }

    public ReadStatus(long messageId, String chatId, int type, long readTime) {
        this.messageId = messageId;
        this.chatId = chatId;
        this.type = type;
        this.readTime = readTime;
    }

    public long getMessageId() {
        return messageId;
    }

    public void setMessageId(long messageId) {
        this.messageId = messageId;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatid) {
        this.chatId = chatid;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getReadTime() {
        return readTime;
    }

    public void setReadTime(long readTime) {
        this.readTime = readTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
