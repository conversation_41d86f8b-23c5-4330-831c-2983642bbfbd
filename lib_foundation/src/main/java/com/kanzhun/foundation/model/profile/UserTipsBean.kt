package com.kanzhun.foundation.model.profile

import java.io.Serializable

data class UserTipsBean(
    val bgColor: String?,
    val button: Button?,
    val content: String?,
    val icon: String?,
    val showTip: Boolean?,
    val textColor: String?,
):Serializable

data class Button(
    val color: String?,
    val text: String?,
    val textColor: String?,
    val url: String?,
):Serializable



data class UserInfo(
    val addressLevel1: String?,
    val addressLevel2: String?,
    val nickName: String?,
    val tinyAvatar: String?
):Serializable


data class UserTipsBeanV2(
    val noticeTips: UserTipsBean?,
    val completeTips: UserTipsBean?,
    val userInfoProcess: Int,
):Serializable
