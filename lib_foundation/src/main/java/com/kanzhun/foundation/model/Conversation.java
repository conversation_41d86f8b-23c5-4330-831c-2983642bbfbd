package com.kanzhun.foundation.model;

import android.text.TextUtils;

import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.TypeConverters;

import com.kanzhun.foundation.converter.ConversationConverters;
import com.kanzhun.foundation.db.DBConstants;
import com.kanzhun.foundation.model.message.MessageConstants;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity(tableName = DBConstants.TAB_CONVERSATION, primaryKeys = {"chatId", "type"})
@TypeConverters(ConversationConverters.class)
public class Conversation implements Serializable {
    private static final long serialVersionUID = 8901967512585205122L;
    /**
     * 接收方用户id
     */
    @NotNull
    private String chatId;

    //聊天类型 1:单聊 2:群聊
    private int type = MessageConstants.MSG_SINGLE_CHAT;
    /**
     * 最后消息内容
     */
    private String content;

    @Ignore
    public boolean showChristmas;

    public boolean showFilmRedPoint;

    /**
     * 最后联系时间
     */
    private long lastTime;
    /**
     * 消息状态，ref:Message.status
     */
    private int lastMsgStatus = MessageConstants.MSG_STATE_NONE;

    private int relationStatus;// 关系：11-你喜欢TA，12-TA喜欢你，20-普通好友，30-相识中，40-情侣，50-历史，60-删除

    private int status;//已经废弃 不再使用
    /**
     * 消息类型
     */
    private int msgType;
    private long systemId;
    //未读个数
    private int unreadCount;
    private long msgId;
    private String nickName;
    private String avatar;
    private boolean isSend;
    private long lastSeq;
    private String sender;
    private long readSyncId;
    private String jumpProto;
    /**
     * 最后修改时间
     */
    private long modifyTime;

    private int withdraw;//0-正常，12-撤回

    private boolean badged;//是否计数

    private Draft draft;

    private int linkMsgFunc;// 语音聊天功能，1-未开启，2-已开启

    private int dateCardStatus;// 相识卡功能：1-未解锁，1-已解锁

    private int loveCardStatus;// 表白信功能：1-未解锁，2-已解锁

    private long minSeq;//服务端给出的最小seq，需要+1后使用，比如1000，则使用1001

    private int inChat;// // 是否是聊天会话， 1-是，0-否, 是否在F3展示

    private List<String> tags;

    private String momentId;//最后展示的动态id

    private String icon;//icon 小火苗图标

    private int iconShowType;// icon 展示类型  1 仅内部展示，2 内外都展示

    private String moodIcon;
    private String moodTitle;

    private long sort; // 排序值，高值在前
    private int shield; // 是否屏蔽 1 屏蔽 0 未屏蔽

    public Conversation() {
    }

    public String getIcon() {
        if ("null".equals(icon)) {
            return "";
        }
        return icon;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setIcon(String icon) {
        if (TextUtils.isEmpty(icon)) {
            this.icon = "";
            return;
        }
        this.icon = icon;
    }

    public boolean hasIcon() {
        return !TextUtils.isEmpty(icon);
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public int getLastMsgStatus() {
        return lastMsgStatus;
    }

    public void setLastMsgStatus(int status) {
        this.lastMsgStatus = status;
    }

    public boolean isSend() {
        return isSend;
    }

    public void setSend(boolean send) {
        this.isSend = send;
    }

    public long getLastTime() {
        return lastTime;
    }

    public void setLastTime(long lastTime) {
        this.lastTime = lastTime;
    }


    public String getContent() {
        if (content == null) {
            return "";
        }
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getMsgType() {
        return msgType;
    }

    public void setMsgType(int msgType) {
        this.msgType = msgType;
    }

    public long getLastSeq() {
        return lastSeq;
    }

    public void setLastSeq(long lastSeq) {
        this.lastSeq = lastSeq;
    }

    public int getUnreadCount() {
        return unreadCount;
    }

    public void setUnreadCount(int unreadCount) {
        this.unreadCount = unreadCount;
    }

    public long getMsgId() {
        return msgId;
    }

    public void setMsgId(long msgId) {
        this.msgId = msgId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar == null ? "" : avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public boolean isShowFilmRedPoint() {
        return showFilmRedPoint;
    }

    public void setShowFilmRedPoint(boolean showFilmRedPoint) {
        this.showFilmRedPoint = showFilmRedPoint;
    }

    public String getJumpProto() {
        return jumpProto;
    }

    public void setJumpProto(String jumpProto) {
        this.jumpProto = jumpProto;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public long getReadSyncId() {
        return readSyncId;
    }

    public void setReadSyncId(long readSyncId) {
        this.readSyncId = readSyncId;
    }

    public int getRelationStatus() {
        return relationStatus;
    }

    public void setRelationStatus(int relationStatus) {
        this.relationStatus = relationStatus;
    }

    public int getWithdraw() {
        return withdraw;
    }

    public void setWithdraw(int withdraw) {
        this.withdraw = withdraw;
    }

    public boolean isBadged() {
        return badged;
    }

    public void setBadged(boolean badged) {
        this.badged = badged;
    }

    public Draft getDraft() {
        return draft;
    }

    public void setDraft(Draft draft) {
        this.draft = draft;
    }

    public String getDraftContent() {
        return draft == null ? "" : draft.getContent();
    }

    public void setDraftPhotoList(List<String> photoList) {
        this.draft = new Draft();
        this.draft.setPhotoList(photoList);
    }

    public List<String> getDraftPhotoList() {
        if(draft == null) return new ArrayList<>();
        if(draft.getPhotoList() == null) return new ArrayList<>();
        return draft.getPhotoList();
    }

    public int getLinkMsgFunc() {
        return linkMsgFunc;
    }

    public void setLinkMsgFunc(int linkMsgFunc) {
        this.linkMsgFunc = linkMsgFunc;
    }

    public int getDateCardStatus() {
        return dateCardStatus;
    }

    public void setDateCardStatus(int dateCardStatus) {
        this.dateCardStatus = dateCardStatus;
    }

    public int getLoveCardStatus() {
        return loveCardStatus;
    }

    public void setLoveCardStatus(int loveCardStatus) {
        this.loveCardStatus = loveCardStatus;
    }

    public long getMinSeq() {
        if (minSeq < MessageConstants.MSG_SEQ_STARTING_VALUE) {
            setMinSeq(MessageConstants.MSG_SEQ_STARTING_VALUE);
        }
        return minSeq;
    }

    public void setMinSeq(long minSeq) {
        this.minSeq = minSeq;
    }

    public int getInChat() {
        return inChat;
    }

    public void setInChat(int inChat) {
        this.inChat = inChat;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getMomentId() {
        return momentId;
    }

    public void setMomentId(String momentId) {
        this.momentId = momentId;
    }

    public long getSystemId() {
        return systemId;
    }

    public void setSystemId(long systemId) {
        this.systemId = systemId;
    }


    public String getMoodIcon() {
        return moodIcon;
    }

    public void setMoodIcon(String moodIcon) {
        this.moodIcon = moodIcon;
    }

    public String getMoodTitle() {
        return moodTitle;
    }

    public void setMoodTitle(String moodTitle) {
        this.moodTitle = moodTitle;
    }

    /**
     * 重置，用于清空聊天记录用
     */
    public void resetShowInfo(long minSeq) {
        this.minSeq = minSeq;
        content = "";
        lastMsgStatus = MessageConstants.MSG_STATE_NONE;
        lastTime = 0;
        unreadCount = 0;
    }

    public void resetShowInfo() {
        content = "";
        lastMsgStatus = MessageConstants.MSG_STATE_NONE;
        lastTime = 0;
        unreadCount = 0;
    }

    public static Conversation createConversation(String chatId, String avatar, String nickName, String moodIcon, String moodTitle, int type) {
        Conversation conversation = new Conversation();
        conversation.setChatId(chatId);
        conversation.setAvatar(avatar);
        conversation.setNickName(nickName);
        conversation.setMoodIcon(moodIcon);
        conversation.setMoodTitle(moodTitle);
        conversation.setType(type);
        return conversation;
    }

//    public static Conversation createConversation(String chatId, String avatar, String nickName, int type) {
//        Conversation conversation = new Conversation();
//        conversation.setChatId(chatId);
//        conversation.setAvatar(avatar);
//        conversation.setNickName(nickName);
//        conversation.setType(type);
//        return conversation;
//    }

    @Override
    public String toString() {
        return "Conversation{" +
                "chatId='" + chatId + '\'' +
                ", type=" + type +
                ", content='" + content + '\'' +
                ", lastTime=" + lastTime +
                ", lastMsgStatus=" + lastMsgStatus +
                ", relationStatus=" + relationStatus +
                ", status=" + status +
                ", msgType=" + msgType +
                ", systemId=" + systemId +
                ", moodIcon=" + moodIcon +
                ", moodTitle=" + moodTitle +
                ", unreadCount=" + unreadCount +
                ", msgId=" + msgId +
                ", nickName='" + nickName + '\'' +
                ", avatar='" + avatar + '\'' +
                ", isSend=" + isSend +
                ", lastSeq=" + lastSeq +
                ", sender='" + sender + '\'' +
                ", readSyncId=" + readSyncId +
                ", modifyTime=" + modifyTime +
                ", withdraw=" + withdraw +
                ", badged=" + badged +
                ", draft=" + draft +
                ", linkMsgFunc=" + linkMsgFunc +
                ", dateCardStatus=" + dateCardStatus +
                ", loveCardStatus=" + loveCardStatus +
                ", minSeq=" + minSeq +
                ", inChat=" + inChat +
                ", tags=" + tags +
                ", momentId='" + momentId + '\'' +
                ", icon='" + icon + '\'' +
                ", sort=" + sort +
                ", shield=" + shield +
                '}';
    }

    public void setIconShowType(int iconShowType) {
        this.iconShowType = iconShowType;
    }

    public int getIconShowType() {
        return iconShowType;
    }

    public long getSort() {
        return sort;
    }

    public void setSort(long sort) {
        this.sort = sort;
    }

    public int getShield() {
        return shield;
    }

    public void setShield(int shield) {
        this.shield = shield;
    }
}
