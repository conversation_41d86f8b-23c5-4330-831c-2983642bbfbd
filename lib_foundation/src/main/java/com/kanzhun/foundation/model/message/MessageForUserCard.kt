package com.kanzhun.foundation.model.message

import android.widget.TextView
import com.drake.spannable.span.CenterImageSpan
import com.itxca.spannablex.spannable
import com.itxca.spannablex.utils.drawableSize
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.views.span.internal.ExpandableStateRecord
import com.kanzhun.common.views.span.internal.StateType
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.model.ABFace
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.kotlin.ktx.isAuthSuccess
import com.kanzhun.foundation.model.profile.UserTag
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable

/**
 * 这是一个本地插入的消息，来自接口
 * 用于展示顶部用户卡片
 */
class MessageForUserCard : ChatMessage() {

    init {
        mediaType = MessageConstants.MSG_USER_CARD
    }

    var chatUserCard: ChatUserInfo? = null

    var userId: String? = ""


    override fun parserMessage(message: ChatProtocol.OgMessage?) {

    }


}

data class ChatUserInfo(
    val age: Int,
    val height: Int,
    val msgCount: Long, //消息数
    val industry: String?,
    val career: String?,
    val addressLevel1: String?,
    val addressLevel2: String?,
    val degreeInfo: String?,
    val eduCertStatus: Int,
    val companyCertStatus: Int,
    val securityId: String?,
    val showTags: List<UserTag>?,
    val userTagList: List<UserTag>?,
    val sameActivity: SameActivity? = null,
    val userInterestList: List<UserTag>?,
    val storyList: List<ProfileInfoModel.Story>?,
    val ABFaceList: List<ABFace>?,
    val matchInfo: MatchInfo?,
    val introImgList: List<ProfileInfoModel.Story>?,
    val interestImgList: List<ProfileInfoModel.Story>?,
    val familyImgList: List<ProfileInfoModel.Story>?,
    val questionAnswerImgs: List<String>?,
    val lifePhotoList: List<ProfileInfoModel.Story>?,
) : Serializable {

    fun setBaeInfo(tv: TextView) {
        tv.text = spannable {
            val addDivider = {
                image(
                    tv.context,
                    R.drawable.common_text_divider,
                    marginLeft = 5.dp.toInt(),
                    marginRight = 5.dp.toInt(),
                    align = CenterImageSpan.Align.CENTER
                )
            }
            "${age}岁".text()
            addDivider()
            "${height}cm".text()
            if (!degreeInfo.isNullOrBlank()) {
                addDivider()
                if (eduCertStatus.isAuthSuccess()) {
                    image(
                        tv.context,
                        R.drawable.common_icon_home_auth_new,
                        size = 16.dpI.drawableSize,
                        marginRight = 2.dp.toInt(),
                        align = CenterImageSpan.Align.CENTER
                    )
                }
                degreeInfo.text()
            }
            if (!career.isNullOrBlank()) {
                addDivider()
                career.text()
            }
            if (!addressLevel1.isNullOrBlank() || !addressLevel2.isNullOrBlank()) {
                addDivider()
                if (!addressLevel2.isNullOrBlank()) {
                    addressLevel2.text()
                } else {
                    addressLevel1.text()
                }
            }
        }
    }


    fun getAllTags(): List<UserTag> {
        val list = mutableListOf<UserTag>()
        val tags = showTags ?: mutableListOf()
        if (tags.isNotEmpty()) {
            for (index in tags.indices) {
                if (index == 10) {
                    list.add(UserTag(tagName = "更多标签"))
                    break
                } else {
                    list.add(tags[index])
                }
            }
        }
        return list
    }

    fun getAllPics(): List<String> {
        val list = mutableListOf<String>()
//        ABFaceList?.forEach { abFace ->
//            list.add(abFace.tinyPhotoA)
//            list.add(abFace.tinyPhotoB)
//        }
//        storyList?.forEach { story ->
//            list.add(story.tinyPhoto)
//        }
//        introImgList?.forEach { story ->
//            list.add(story.tinyPhoto)
//        }
//        interestImgList?.forEach { story ->
//            list.add(story.tinyPhoto)
//        }
//        familyImgList?.forEach { story ->
//            list.add(story.tinyPhoto)
//        }
//        questionAnswerImgs?.filter { it.isNotEmpty() }?.forEach { url ->
//            list.add(url)
//        }
        lifePhotoList?.forEach { story ->
            list.add(story.tinyPhoto)
        }

        return list

    }

}

data class SameActivity(var showText: String?) : Serializable

data class MatchInfo(
    val content: String?,
    val highlight: List<Highlight>?
) : Serializable, ExpandableStateRecord {
    var status: StateType = StateType.COLLAPSE
    override fun setState(status: StateType) {
        this.status = status
    }

    override fun getState(): StateType {
        return this.status
    }
}

data class Highlight(
    val start: Int,
    val end: Int
) : Serializable