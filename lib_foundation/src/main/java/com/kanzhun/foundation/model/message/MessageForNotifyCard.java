package com.kanzhun.foundation.model.message;

import android.text.TextUtils;

import com.xxjz.orange.protocol.codec.ChatProtocol;
import com.kanzhun.utils.GsonUtils;

import org.w3c.dom.Text;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON>
 * Date: 2022/6/1
 */
public class MessageForNotifyCard extends ChatMessage {
    private MessageForNotifyCard.NotifyCardInfo mNotifyCardInfo = new MessageForNotifyCard.NotifyCardInfo();

    @Override
    protected void parserMessage(ChatProtocol.OgMessage message) {
        ChatProtocol.OgNotifyCardMedia notifyCard = message.getBody().getNotifyCard();
        setNotifyCardInfo(new NotifyCardInfo(notifyCard.getType(), notifyCard.getStatus(), notifyCard.getTime(),notifyCard.getContent()));
    }

    @Override
    public void parseFromDB() {
        super.parseFromDB();
        mNotifyCardInfo = GsonUtils.getGson().fromJson(getContent(), MessageForNotifyCard.NotifyCardInfo.class);
    }

    @Override
    public void prepare2DB() {
        super.prepare2DB();
        String content = GsonUtils.getGson().toJson(mNotifyCardInfo);
        setContent(content);
    }

    @Override
    public String getSummary() {
        if (mNotifyCardInfo != null && !TextUtils.isEmpty(mNotifyCardInfo.getContent())){
            return mNotifyCardInfo.getContent();
        }
        if (mNotifyCardInfo != null) {
            if (mNotifyCardInfo.type == 1) {// 相识卡
                return mNotifyCardInfo.status == 1 ? "你们的48h相识正式开启" : "你们的48h相识已结束";
            } else {// 表白信
                return mNotifyCardInfo.status == 1 ? "你们的情侣关系正式开启" : "你们的情侣关系已结束";
            }
        }
        return "";
    }

    public NotifyCardInfo getNotifyCardInfo() {
        return mNotifyCardInfo;
    }

    public void setNotifyCardInfo(NotifyCardInfo mNotifyCardInfo) {
        this.mNotifyCardInfo = mNotifyCardInfo;
    }

    public MessageForNotifyCard() {
        setMediaType(MessageConstants.MSG_NOTIFY_CARD);
    }

    public static class NotifyCardInfo implements Serializable {
        private static final long serialVersionUID = -6353277694147561856L;
        private int type;// 类型 1:相识卡 2:表白信 3见面计划
        private int status;// 状态 1:建立关系 2:解除关系
        private long time;// 关系创建/解除时间
        private String content;

        public NotifyCardInfo() {
        }

        public NotifyCardInfo(int type, int status, long time,String content) {
            this.type = type;
            this.status = status;
            this.time = time;
            this.content = content;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }
    }
}
