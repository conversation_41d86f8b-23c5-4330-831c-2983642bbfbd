package com.kanzhun.foundation.model.message

import com.kanzhun.utils.GsonUtils
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable

/**
 * 默契考验消息卡片
 */
class MessageForMeetingPlanTaskCard : ChatMessage() {
    var meetupTask: MeetupTask? = null

    init {
        mediaType = MessageConstants.MSG_MEDIA_TYPE_MEETUP_TASK
    }

    override fun parserMessage(message: ChatProtocol.OgMessage?) {
        message?.run {
            val meetupTaskProtocol = message.body?.meetupTask ?: return
            meetupTask = MeetupTask(
                title = meetupTaskProtocol.title,
                content = meetupTaskProtocol.content,
                maleUserId = meetupTaskProtocol.maleUserId,
                femaleUserId = meetupTaskProtocol.femaleUserId,
                maleJumpProto = meetupTaskProtocol.maleJumpProto,
                femaleJumpProto = meetupTaskProtocol.femaleJumpProto,
                taskNum = meetupTaskProtocol.taskNum,
                type = meetupTaskProtocol.type,
                maleUserStatus = meetupTaskProtocol.maleUserStatus,
                femaleUserStatus = meetupTaskProtocol.femaleUserStatus,
            )
        }
    }

    override fun parseFromDB() {
        super.parseFromDB()
        val meetupTaskDB = GsonUtils.getGson().fromJson(content, MeetupTask::class.java)
        if (meetupTaskDB != null) {
            meetupTask = meetupTaskDB
        }
    }

    override fun prepare2DB() {
        super.prepare2DB()
        content = GsonUtils.getGson().toJson(meetupTask)
    }

    override fun getSummary(): String {
        return meetupTask?.title?:"[见面任务]"
    }
}

data class MeetupTask(val title: String,//任务标题
                      val content: String,//任务内容
                      val maleUserId: String,//男用户id
                      val femaleUserId: String,//女用户id
                      val maleUserStatus: Int,//男用户状态 0 未完成 1 已完成
                      val femaleUserStatus: Int,//女用户状态 0 未完成 1 已完成
                      val maleJumpProto: String,// 男性用户跳转协议
                      val femaleJumpProto: String,// 女性用户跳转协议
                      val taskNum: Int,//任务序号
                      var type: Int,//任务类型
    )