package com.kanzhun.foundation.model.message

import com.kanzhun.common.model.ImageInfo
import com.kanzhun.utils.GsonUtils
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable

/**
 * 表情包消息体
 */
class MessageForEmotion : ChatMessage() {
    init {
        mediaType = MessageConstants.MSG_STICKER
    }

    var mEmotionInfo: EmotionInfo? = null


    override fun parserMessage(message: ChatProtocol.OgMessage?) {
        val stickerBean = message?.body?.sticker ?: return
        mEmotionInfo = EmotionInfo(
            sid = stickerBean.sid,
            packId = stickerBean.packId,
            name = stickerBean.name,
            tinyImage = ImageInfo(stickerBean.tinyImage?.width ?: 0, stickerBean.tinyImage?.height ?: 0, stickerBean.tinyImage?.url ?: ""),
            originImage = ImageInfo(stickerBean.originImage?.width ?: 0, stickerBean.originImage?.height ?: 0, stickerBean.originImage?.url ?: "")
        )
    }

    override fun prepare2DB() {
        super.prepare2DB()
        content = GsonUtils.getGson().toJson(mEmotionInfo)
    }

    override fun parseFromDB() {
        super.parseFromDB()
        mEmotionInfo = GsonUtils.getGson().fromJson(content, EmotionInfo::class.java)
    }

    override fun getSummary(): String {
        val name = mEmotionInfo?.name ?: ""
        return if(mEmotionInfo?.packId == "1"){
            if(name.startsWith("[") && name.endsWith("]")) name else "[${name}]"
        }else{
            "[动画表情]${name}"
        }
    }

    fun getOriginal():ImageInfo?{
        return mEmotionInfo?.originImage
    }

    fun getTiny():ImageInfo?{
        return mEmotionInfo?.tinyImage
    }

    fun isInnerEmoji():Boolean{
        return !mEmotionInfo?.packId.isNullOrBlank() && mEmotionInfo?.packId == "1"
    }

}

data class EmotionInfo(
    val sid: String? = "",
    val packId: String? = "",
    val name: String = "",
    val tinyImage: ImageInfo? = null,
    val originImage: ImageInfo? = null,
) : Serializable
