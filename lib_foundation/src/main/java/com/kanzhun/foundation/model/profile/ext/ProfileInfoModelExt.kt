package com.kanzhun.foundation.model.profile.ext

import com.kanzhun.common.kotlin.ext.joinNotNull
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.kernel.account.AccountHelper


fun String?.isMyself():Boolean{
    return if(isNullOrBlank()){
        false
    }else{
        this == AccountHelper.getInstance().userId
    }
}

fun Int?.isSameGender():Boolean{
    return this == AccountHelper.getInstance().userInfo?.gender
}
/**
 * 获取家乡
 */
fun ProfileInfoModel.BaseInfo?.getHometownString(): String {
    return this?.hometownLevel2 ?: this?.hometownLevel1 ?: ""
}

/**
 * 获取家乡：湖北武汉
 */
fun ProfileInfoModel.BaseInfo?.getHometownString1(divider: String = ""): String {
    return listOf(this?.hometownLevel1, this?.hometownLevel2).joinNotNull(divider)
}

/**
 * 获取现居地
 */
fun ProfileInfoModel.BaseInfo?.getCurrentAddressString(): String {
    return this?.addressLevel2 ?: this?.addressLevel1 ?: ""
}

/**
 * 获取现居地:湖北武汉,或者湖北·武汉,分割符自定义
 */
fun ProfileInfoModel.BaseInfo?.getCurrentAddressString1(divider: String = ""): String {
    return listOf(this?.addressLevel1, this?.addressLevel2).joinNotNull(divider)

}

/**
 * 获取户口，先取二级再取一级
 */
fun ProfileInfoModel.BaseInfo?.getOriginAddressString(): String {
    return this?.hukouLevel2 ?: this?.hukouLevel1 ?: ""
}

/**
 * 获取户口:湖北武汉,或者湖北·武汉,分割符自定义
 */
fun ProfileInfoModel.BaseInfo?.getOriginAddressString1(divider: String = ""): String {
    return listOf(this?.hukouLevel1, this?.hukouLevel2).joinNotNull(divider)
}

/**
 * 是否需要展示房产信息
 */
fun ProfileInfoModel.BaseInfo?.canShowHouseInfo(): Boolean {
    if (this?.houseHold == 2 && this?.houseCertStatus != 2) {
        return true
    }
    return false
}

/**
 * 显示房产认证和房产地址：认证通过的会带省市区信息，eg：北京朝阳区已购房、山西太原已购房
 * 不带地区的直接展示”已购房“
 */
fun ProfileInfoModel.BaseInfo?.getShowHouseInfo(): String {

    return if (this?.houseHold == 2) {
        if (this.houseCertStatus == 3) {
            listOf(houseAddressName, "已购房").joinNotNull("")
        } else if (this.houseCertStatus != 2) {
            "已购房"
        } else {
            ""
        }
    } else {
        ""
    }
}

fun ProfileInfoModel.BaseInfo?.getShowHouseInfo1(): String {
    return if (canShowHouseInfo()) {
        "已购房"
    } else {
        ""
    }
}


/**
 * 是否需要展示车产信息
 */
fun ProfileInfoModel.BaseInfo?.canShowCarInfo(): Boolean {
    if (this?.carHold == 2 && this?.carCertStatus != 2) {
        return true
    }
    return false
}

/**
 * 显示车产认证文案
 */
fun ProfileInfoModel.BaseInfo?.getShowCarInfo(): String {
    return if (canShowCarInfo()) {
        "已购车"
    } else {
        ""
    }

}

/**
 * 学历是否完成
 */
fun ProfileInfoModel.BaseInfo?.isDegreeAuthSuccess(): Boolean {
    if (this?.eduCertStatus == 3) {
        return true
    }
    return false
}

fun ProfileInfoModel.BaseInfo?.getEducationInfoString(): String {
    if (this == null) return ""
    return if (schoolType == 2) { //海外
        listOf(degreeInfo, school).joinNotNull("·")
    } else if (schoolType == 1) { //国内
        val schoolTagList = schoolTag
        if (!schoolTagList.isNullOrEmpty() && (schoolTagList.contains(10)
                    || schoolTagList.contains(11))
        ) {
            listOf(degreeInfo, school).joinNotNull("·")
        } else {
            degreeInfo ?: ""
        }
    } else {
        degreeInfo ?: ""
    }
}

/**
 * 是否需要展示公司信息，不隐藏公司信息且认证通过的才展示
 */
fun ProfileInfoModel.BaseInfo?.canShowCompanyInfo(): Boolean {
    if (this?.hideCompany == 0 && this?.companyCertStatus == 3) {
        return true
    }
    return false
}

/**
 * 获取公司名称，优先简称，简称为空显示全程
 */
fun ProfileInfoModel.BaseInfo?.getCompanyShowName(): String {
    return this?.companyNameShort ?: this?.companyName ?: ""
}

/**
 * 行业·职业，不为空才展示
 */
fun ProfileInfoModel.BaseInfo?.getWorkShowInfo(): String {
    return listOf(this?.industry, this?.career).joinNotNull("·")
}

/**
 * 行业·职业·公司名称，不为空才展示
 */
fun ProfileInfoModel.BaseInfo?.getWorkShowInfoWithCompany(): String {
    return listOf(this?.industry, this?.career, this.getCompanyShowName()).joinNotNull("·")
}

/**
 * 是否需要展示收入信息
 */
fun ProfileInfoModel.BaseInfo?.canShowIncome(): Boolean {
    return this?.hideIncome == 0 && !this?.annualIncomeInfo.isNullOrBlank()
}

fun ProfileInfoModel.buildAuthInfoList(): MutableList<String> {
    return mutableListOf<String>().also {list->
        certInfo?.certTagList?.forEach {
            if(!it.content.isNullOrEmpty()){
                list.add(it.content)
            }
        }
    }
}

/**
 * 学历认证材料
 */
fun ProfileInfoModel.BaseInfo?.getEduAuthMaterialString(): String {
    return when (this?.eduCertType) {
        10 -> "学信网"
        20, 30 -> "毕业证/学位证"
        40, 50 -> "教留服证书"
        else -> ""
    }
}

private fun ProfileInfoModel.BaseInfo?.getWorkAuthMaterialString(): String {
    return when (this?.companyCertType) {
        1 -> "公司社保证明"
        2 -> "工牌照片"
        else -> ""
    }
}

fun ProfileInfoModel.BaseInfo?.getWeightStringMoreThan40(): String? {
    if(this == null || this.weight <= 0){
        return null
    }
    if (this.weight < Constants.MIN_WEIGHT_KG) {
        return "40kg以下"
    }
    return if (this.weight > Constants.MAX_WEIGHT_KG) {
        "120kg以上"
    } else this.weight.toString() + "kg"
}

