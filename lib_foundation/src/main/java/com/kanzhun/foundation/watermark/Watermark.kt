package com.kanzhun.foundation.watermark

import android.app.Activity
import android.app.Application
import android.graphics.Paint
import android.os.Bundle
import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.layout.Placeable
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.base.compose.ext.attachOwners
import com.kanzhun.common.base.compose.ext.dpToPx
import com.kanzhun.foundation.R
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.techwolf.lib.tlog.TLog
import kotlin.math.atan


private const val TAG = "Watermark"

private const val SPACING = 100

private val watermarkLiveData: MutableLiveData<String> = MutableLiveData()

private val excludedActivities = listOf(
    "com.kanzhun.marry.login.OSplashActivity"
)

private fun refreshWatermark(watermarkText: String = getWatermarkText()) {
    watermarkLiveData.postValue(watermarkText)
}

fun Application.registerWatermarkLifecycle() {
    registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            try {
                if (activity is ComponentActivity && !excludedActivities.contains(activity.javaClass.name)) {
                    TLog.info(TAG, "registerWatermarkLifecycle, onActivityCreated: $activity")

                    activity.run {
                        val decorView = window.decorView
                        if (decorView is ViewGroup) {
                            decorView.addView(ComposeView(activity).apply {
                                setContent {
                                    val watermark by watermarkLiveData.observeAsState("")
                                    Watermark(
                                        watermarkText = watermark,
                                        inPreview = isQaDebugUser()
                                    ) { }
                                }

                            })

                            attachOwners(owner = activity, targetView = decorView)
                        }
                    }
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "registerWatermarkLifecycle, onActivityCreated error: $e")
            }
        }

        override fun onActivityStarted(activity: Activity) {

        }

        override fun onActivityResumed(activity: Activity) {
            if (activity is ComponentActivity) {
                refreshWatermark()
            }
        }

        override fun onActivityPaused(activity: Activity) {
        }

        override fun onActivityStopped(activity: Activity) {
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        }

        override fun onActivityDestroyed(activity: Activity) {
        }
    })
}

/**
 * 水印内容：
 * 客户端生成水印里的字符串拼接规则为："#" + serverWatermark + "#" + clientWatermark + "#"
 * clientWatermark生成方法为标准v3加密时间戳（毫秒）
 */
private fun getWatermarkText(): String {
    val serverWatermark: String = try {
        AccountHelper.getInstance().account?.watermark ?: ""
    } catch (e: Throwable) {
        TLog.error(TAG, "get apmUid error: $e")
        ""
    }.toString()
    TLog.info(TAG, "serverWatermark: $serverWatermark")

    val clientWatermark: String = try {
        Algo.encodeWatermarkV3(System.currentTimeMillis())
    } catch (e: Throwable) {
        TLog.error(TAG, "encodeWatermarkV3 error: $e")
        ""
    }
    TLog.info(TAG, "clientWatermark: $clientWatermark")

    return if (serverWatermark.isEmpty() || clientWatermark.isEmpty()) {
        ""
    } else {
        listOf(
            "",
            serverWatermark,
            clientWatermark,
            ""
        ).joinToString("#")
    }
}

@Composable
fun Watermark(
    watermarkText: String,
    inPreview: Boolean = false,
    content: @Composable BoxScope.() -> Unit,
) {
    Box {
        content()

        val watermarkColor =
            colorResource(if (inPreview) R.color.common_color_000000_4 else R.color.common_color_000000_1)

        val watermarkTextSize = 16.dp.dpToPx()

        Layout(
            content = {
                repeat(27) {
                    Canvas(modifier = Modifier.size(160.dp)) {
                        val paint = Paint().apply {
                            color = watermarkColor.toArgb()
                            textSize = watermarkTextSize
                        }

                        val path = android.graphics.Path()
                        paint.getTextPath(watermarkText, 0, watermarkText.length, 0f, 0f, path)

                        drawContext.canvas.nativeCanvas.drawPath(path, paint)
                    }
                }
            }
        ) { measurables, constraints ->
            val placeables: List<Placeable> = measurables
                .map { measurable -> measurable.measure(constraints) }

            layout(constraints.maxWidth, constraints.maxHeight) {
                val maxWidth: Double = placeables.maxOf { it.width }.toDouble()

                val tileSize: Int = (constraints.maxWidth / atan(maxWidth)).toInt()

                placeables
                    .chunked(3)
                    .forEachIndexed { index, (first, second, third) ->
                        val indexedTileSize: Int = index * tileSize / 2

                        val baseX = -SPACING

                        val rotation = -20f

                        first.placeRelativeWithLayer(
                            x = baseX,
                            y = indexedTileSize
                        ) { rotationZ = rotation }

                        second.placeRelativeWithLayer(
                            x = baseX + SPACING * 5,
                            y = indexedTileSize
                        ) { rotationZ = rotation }

                        third.placeRelativeWithLayer(
                            x = baseX + SPACING * 10,
                            y = indexedTileSize
                        ) { rotationZ = rotation }
                    }
            }
        }
    }
}

@Preview(showSystemUi = true)
@Composable
private fun PreviewWatermark() {
    val watermark by watermarkLiveData.observeAsState("")
    Watermark(watermarkText = watermark, inPreview = true) {}

    refreshWatermark(watermarkText = "#21ET#3dWFaG#")
}