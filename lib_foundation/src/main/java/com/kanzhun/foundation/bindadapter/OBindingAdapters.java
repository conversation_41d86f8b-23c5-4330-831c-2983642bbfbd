package com.kanzhun.foundation.bindadapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.databinding.BindingAdapter;
import androidx.databinding.BindingMethod;
import androidx.databinding.BindingMethods;

import com.kanzhun.common.util.DateFormatUtils;
import com.kanzhun.common.views.image.OImageView;
import com.kanzhun.foundation.R;
import com.kanzhun.foundation.api.bean.MomentListItemBean;
import com.kanzhun.foundation.api.model.MatchingPageInfo;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.foundation.views.VideoDownloadProgressbar;
import com.kanzhun.utils.base.LList;

import java.util.List;
import java.util.Locale;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/30
 */
@BindingMethods({@BindingMethod(type = TextView.class, attribute = "movementMethod", method = "setMovementMethod")})
public class OBindingAdapters {
    @BindingAdapter({"videoDownloadProgress"})
    public static void setVideoDownloadProgress(VideoDownloadProgressbar downloadProgressbar, int progress) {
        downloadProgressbar.setProgress(progress);
    }

    @BindingAdapter({"textNum", "textType"})
    public static void setTextNum(TextView textView, int num, int type) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(StringUtil.getDynamicFormatNum(num));
        textView.setText(stringBuilder.toString());
    }

    @BindingAdapter({"foundationMomentList", "index"})
    public static void setFoundationMomentImage(OImageView view, List<MomentListItemBean> list, int index) {
        if (!LList.isEmpty(list) && list.size() > index) {
            MomentListItemBean bean = list.get(index);
            if (bean != null) {
                if (!LList.isEmpty(bean.pictures)) {
                    view.load(bean.pictures.get(0).url);
                    view.setVisibility(View.VISIBLE);
                } else {
                    view.setVisibility(View.GONE);
                }
            }
        }
    }

    @BindingAdapter({"foundationMomentContentList", "index"})
    public static void setFoundationMomentContent(TextView view, List<MomentListItemBean> list, int index) {
        if (!LList.isEmpty(list) && list.size() > index) {
            MomentListItemBean bean = list.get(index);
            if (bean != null) {
                if (!LList.isEmpty(bean.pictures)) {
                    view.setVisibility(View.GONE);
                } else {
                    view.setVisibility(View.VISIBLE);
                    view.setText(list.get(index).content);
                }
            }
        }
    }

    @BindingAdapter({"momentList", "index", "type"})
    public static void setTextNum(TextView textView, List<MomentListItemBean> list, int index, String type) {
        try {

        } catch (Exception e) {

        }
        if (!LList.isEmpty(list) && list.size() > index) {
            textView.setText(DateFormatUtils.getFormatTime(list.get(index).createTime, type));
        }
    }

    @BindingAdapter({"chatUnReadNum"})
    public static void setChatUnReadNum(TextView textView, int num) {
        if (num > 99) {
            num = 99;
            textView.setText(String.valueOf(num)+"+");
        }else {
            textView.setText(String.valueOf(num));
        }

    }

    @BindingAdapter("abFaceIcon")
    public static void setChatAbFaceIcon(ImageView imageView, int subType) {
        if (subType == 1) {
            imageView.setImageResource(R.mipmap.me_a_b_a_tips);
        } else {
            imageView.setImageResource(R.mipmap.me_a_b_b_tips);
        }
    }

    @BindingAdapter({"relationshipSubTitle"})
    public static void setRelationshipSubTitle(TextView textView, MatchingPageInfo pageInfo) {
        if (pageInfo == null || pageInfo.friend == null || TextUtils.isEmpty(pageInfo.friend.nickName)) return;
        String subTitle;
        if (pageInfo.matchStatus == 2) {
            subTitle = String.format(Locale.getDefault(), "你与%s的48h相识已开启", pageInfo.friend.nickName);
        } else {
            subTitle = String.format(Locale.getDefault(), "你与%s的情侣模式已开启", pageInfo.friend.nickName);
        }
        textView.setText(subTitle);
    }

}
