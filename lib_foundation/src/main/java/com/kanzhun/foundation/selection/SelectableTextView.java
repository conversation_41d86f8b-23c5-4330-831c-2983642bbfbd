package com.kanzhun.foundation.selection;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.text.Layout;
import android.util.AttributeSet;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.foundation.R;

/**
 * <AUTHOR>
 * @date 2020/10/9.
 */
public class SelectableTextView extends androidx.appcompat.widget.AppCompatTextView {

    private final Rect rectTop = new Rect(0, 0, 0, 0);
    private final Rect rectMiddle = new Rect(0, 0, 0, 0);
    private final Rect rectBottom = new Rect(0, 0, 0, 0);
    private int endSelection = 0;
    private int startSelection = 0;
    private final Paint paint = new Paint();
    private int selectionColor = R.color.common_color_191919;
    private int maxWith = 0;
    int closeHeight;

    public SelectableTextView(Context context) {
        this(context, null);
    }

    public SelectableTextView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SelectableTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        closeHeight = QMUIDisplayHelper.dp2px(context, 440);
        TypedArray attr = context.obtainStyledAttributes(attrs, R.styleable.SelectableText, 0, 0);
        maxWith = attr.getDimensionPixelSize(R.styleable.SelectableText_max_with, 0);
        attr.recycle();
    }

    public void setMaxWith(int maxWith) {
        this.maxWith = maxWith;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (startSelection == 0 && endSelection == 0) {
            super.onDraw(canvas);
            return;
        }
        paint.setColor(getResources().getColor(selectionColor));
        final int save = canvas.save();
        canvas.translate(getPaddingLeft(), getPaddingTop());
        canvas.drawRect(rectTop, paint);
        canvas.drawRect(rectMiddle, paint);
        canvas.drawRect(rectBottom, paint);
        canvas.restoreToCount(save);
        super.onDraw(canvas);
    }

    public void cleanSelectText() {
        selectText(0, 0);
    }

    public void selectText(int start, int end) {
        startSelection = start;
        endSelection = end;
        if (startSelection == 0 && endSelection == 0) {
            rectTop.left = 0;
            rectTop.top = 0;
            rectTop.right = 0;
            rectTop.bottom = 0;

            rectMiddle.left = 0;
            rectMiddle.top = 0;
            rectMiddle.right = 0;
            rectMiddle.bottom = 0;

            rectBottom.left = 0;
            rectBottom.top = 0;
            rectBottom.right = 0;
            rectBottom.bottom = 0;
            invalidate();
            return;
        }
        generateRects();
        invalidate();
    }

    private void generateRects() {
        Layout layout = getLayout();
        if (layout == null) {
            return;
        }

        int lineS = getLayout().getLineForOffset(startSelection);
        float xS = getLayout().getPrimaryHorizontal(startSelection);
        float ySTop = getLayout().getLineTop(lineS);
        float ySBottom = getLayout().getLineBottom(lineS);

        int lineE = getLayout().getLineForOffset(endSelection);
        int lineEP = getLayout().getLineForOffset(endSelection - 1);
        float xE = getLayout().getPrimaryHorizontal(endSelection);
        float yETop = getLayout().getLineTop(lineE);
        float yEBottom = getLayout().getLineBottom(lineE);
        if (lineE > lineEP) {
            xE = getLayout().getWidth();
            yEBottom = getLayout().getLineBottom(lineEP);
        } else {
            if (endSelection >= getText().length()) {
                xE = getLayout().getWidth();
            }
        }

        if (lineS == lineE) {
            rectTop.left = 0;
            rectTop.top = 0;
            rectTop.right = 0;
            rectTop.bottom = 0;

            rectMiddle.left = (int) xS;
            rectMiddle.top = (int) ySTop;
            rectMiddle.right = (int) xE;
            rectMiddle.bottom = (int) yEBottom;

            rectBottom.left = 0;
            rectBottom.top = 0;
            rectBottom.right = 0;
            rectBottom.bottom = 0;

            return;
        }

        if (lineE - lineS == 1) {
            rectTop.left = (int) xS;
            rectTop.top = (int) ySTop;
            rectTop.right = getLayout().getWidth();
            rectTop.bottom = (int) ySBottom;

            rectMiddle.left = 0;
            rectMiddle.top = 0;
            rectMiddle.right = 0;
            rectMiddle.bottom = 0;

            rectBottom.left = 0;
            rectBottom.top = (int) yETop;
            rectBottom.right = (int) xE;
            rectBottom.bottom = (int) yEBottom;

            return;
        }
        rectTop.left = (int) xS;
        rectTop.top = (int) ySTop;
        rectTop.right = getLayout().getWidth();
        rectTop.bottom = (int) ySBottom;

        rectMiddle.left = 0;
        rectMiddle.top = (int) ySBottom;
        rectMiddle.right = getLayout().getWidth();
        rectMiddle.bottom = (int) yETop;

        rectBottom.left = 0;
        rectBottom.top = (int) yETop;
        rectBottom.right = (int) xE;
        rectBottom.bottom = (int) yEBottom;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int with = getMeasuredWidth();
        if (maxWith > 0) {
            int height = getMeasuredHeight();
            if (with > maxWith || height > closeHeight) {
                super.onMeasure(MeasureSpec.makeMeasureSpec(maxWith, MeasureSpec.EXACTLY), heightMeasureSpec);
            }
        }
    }
}
