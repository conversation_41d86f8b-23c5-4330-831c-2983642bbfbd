package com.kanzhun.foundation.dialog

import android.text.method.LinkMovementMethod
import android.view.Gravity
import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.foundation.databinding.FoundationDialogProtocolConfirmBinding

/**
 *
 * 协议确认弹窗
 */
class ProtocolConfirmDialog(val activity: FragmentActivity,
                            val title: CharSequence,
                            val content:CharSequence,
                            val confirmClick: () -> Unit,
                            val cancelClick: () -> Unit) {

    fun show() {
        CommonViewBindingDialog(activity,
            mCancelable = true,
            mCanceledOnTouchOutside = true,
            mGravity = Gravity.CENTER, //这里可以设置对话框显示的位置
            mPaddingLeft = 32,
            mPaddingRight = 32,
            mAnimationStyle = 0,
            onInflateCallback = { inflater, dialog ->
                //在这里inflate你的viewbinding
                val binding = FoundationDialogProtocolConfirmBinding.inflate(inflater)
                //对你的view赋值
                binding.apply {
                    tvTitle.text = title
                    tvContent.text = content
                    tvContent.movementMethod = LinkMovementMethod.getInstance()
                    tvHPositive.setOnClickListener {
                        cancelClick()
                        dialog.dismiss()
                    }
                    tvHNegative.setOnClickListener {
                        confirmClick()
                        dialog.dismiss()
                    }
                }
                binding
            })
            .show()
    }
}