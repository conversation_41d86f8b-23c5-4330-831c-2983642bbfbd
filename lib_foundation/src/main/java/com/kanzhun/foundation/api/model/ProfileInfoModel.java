package com.kanzhun.foundation.api.model;

import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.util.LDate;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.R;
import com.kanzhun.foundation.api.bean.MomentListItemBean;
import com.kanzhun.foundation.kernel.account.Account;
import com.kanzhun.foundation.model.UserMoodInfoBean;
import com.kanzhun.foundation.model.profile.LoveAttitude;
import com.kanzhun.foundation.model.profile.OpenScreen;
import com.kanzhun.foundation.model.profile.QuestionTemplateModel;
import com.kanzhun.foundation.model.profile.RomanticMode;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.utils.platform.Utils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18.
 * 个人信息基类
 */
public class ProfileInfoModel implements Serializable {
    public static final int PROGRESS_TYPE_UN_CERTIFY = 0;//未认证
    public static final int PROGRESS_TYPE_CERTIFY = 1;//已实名人脸
    public static final int PROGRESS_TYPE_EDU_UNDER_REVIEW = 2;//学历认证审核中
    public static final int PROGRESS_TYPE_EDU_REJECTED = 3;//学历认证审核被驳回
    public static final int PROGRESS_TYPE_EDU_PASS_THROUGH = 4;//学历认证完成
    public static final int PROGRESS_TYPE_EDU_UNDER_REVIEW_AGAIN = 5;//学历认证完成后审核中
    public static final int PROGRESS_TYPE_EDU_REJECTED_AGAIN = 6;//学历认证完成后被驳回

    public static final int CONTENT_TYPE_REJECTED = 2;//故事，AB面驳回
    public static final int CONTENT_TYPE_UN_REJECTED = 1;

    private static final long serialVersionUID = 8626229583855165019L;
    public String userId;
    public BaseInfo baseInfo;// 基本信息
    //    public PrivateInfo privateInfo;// 隐私信息
    public List<Story> storyList;// 用户故事
    public List<ABFace> ABFaceList;// AB 面
    public List<QuestionAnswer> audioAnswerList;// 语音问答
    public List<QuestionAnswer> textAnswerList;// 文本问答
    public List<Label> userTagList;// 用户标签
    public List<Label> userInterestList;// 兴趣标签
    public @Nullable OpenScreen openScreenInfo;//开屏测试结果解析

    public LoveAttitude loveAttitude;
    public RomanticMode romanticMode;
    public CertInfo certInfo;//认证信息
    public List<MomentListItemBean> moments;

    public int profileLocked;
    /// / 用户是否锁定 0 未锁定 1 锁定
    public String securityId;

    public int userStatus;//对方状态 1 正常 2 封禁 3 待注销 4 已注销
    public int notMatch;//不让任何人看到我 1 是 0 否
    public int userCurrentRelation; // 用户当前的关系状态  1 普通状态  2 约会中 3 情侣模式中
    public CompareInfo compareInfo;//相同标签
    public @Nullable LoginUserCertStatus loginUserCertStatus;
    public @Nullable UserMoodInfoBean moodInfo;

    public @Nullable List<Story> introImgList;
    public @Nullable List<Story> interestImgList;
    public @Nullable List<Story> familyImgList;

    public @Nullable ThumbInfo thumbInfo;//点赞信息
    public @Nullable SameActivityBean sameActivity;//相同活动

    public static class ThumbInfo implements Serializable {//点赞信息
        public @Nullable ThumbInfoItem avatar;//头像
        public @Nullable ThumbInfoItem intro;//简介
        public @Nullable ThumbInfoItem interest;//兴趣爱好
        public @Nullable ThumbInfoItem familydesc;//家庭介绍
        public @Nullable ThumbInfoItem singleReason;//单身姻缘
        public @Nullable ThumbInfoItem idealPartnerDesc;//我的理想型
    }


    /**
     * 被封禁和注销的用户 或者 锁定的用户 仅仅用于其他用户 不包含自己
     */
    public boolean abnormalUser() {
        return userStatus == Constants.CONTACT_STATUS_DELETED || userStatus == Constants.CONTACT_STATUS_BAN || profileLocked == Account.PROFILE_LOCKED;
    }

    public boolean deleteUser() {
        return userStatus == Constants.CONTACT_STATUS_DELETED;
    }

    public String abnormalUserDesc() {
        String desc = "";
        if (userStatus == Constants.CONTACT_STATUS_DELETED) {
            desc = Utils.getApp().getString(R.string.common_contact_delete_desc);
        } else if (userStatus == Constants.CONTACT_STATUS_BAN) {
            desc = Utils.getApp().getString(R.string.common_contact_ban_desc);
        } else if (profileLocked == Account.PROFILE_LOCKED) {
            desc = Utils.getApp().getString(R.string.common_contact_locked_desc);
        }
        return desc;
    }

    /**
     * 是否是隐身模式或者约会，恋爱模式
     */
    public boolean hideStatus() {
        return notMatch == 1 || userCurrentRelation == 2 || userCurrentRelation == 3;
    }

    /**
     * 用户主动隐藏了个人信息
     *
     * @return
     */
    public boolean hideByUser() {
        return notMatch == 1;
    }

    public boolean isCarAuth() {
        return baseInfo != null && baseInfo.carCertStatus == 3;
    }

    public boolean isHouseAuth() {
        return baseInfo != null && baseInfo.houseCertStatus == 3;
    }

    public boolean isBothCarAndHouseAuth() {
        return isCarAuth() && isHouseAuth();
    }


    public static class BaseInfo implements Serializable {
        private static final long serialVersionUID = 2917609962590309005L;
        public int certifyProgress;//实名认证、学历认证进度 0:未认证 1:已实名人脸 2:学历认证审核中 3:学历认证审核被驳回 4:学历认证完成 5:学历认证完成后审核中 6:学历认证完成后被驳回
        public String avatar;// 头像地址
        public String tinyAvatar;// 缩略图头像地址
        public String liveVideo;// 头像 livePhoto的视频 地址 可空
        public String avatarCertInfo; // 头像审核反馈信息，avatarCertStatus=2时表示驳回原因 可空
        public String nickName;// 昵称
        public String userId;
        public String intro;// 个人简介
        public String interest;// 兴趣
        public int schoolCertStatus;//学校名称和学历审核状态，1-审核中,2-已驳回,3-通过  空时作为没有审核过处理
        public String schoolCertInfo;//学校和学历审核的驳回原因
        public int interestCertStatus;
        public String interestCertInfo;
        // "relationStatus": 11, // 关系：11-你喜欢TA，12-TA喜欢你，20-普通好友，30-相识中，40-情侣，50-历史，60-删除
        public int relationStatus;

        public String birthday;// 出生日期 "1990-05-01"
        public int constellation;// 星座: 1-摩羯座; 2-水瓶座; 3-双鱼座; 4-白羊座; 5-金牛座; 6-双子座; 7-巨蟹座; 8-狮子座; 9-处女座; 10-天秤座; 11-天蝎座; 12-射手座
        public String constellationInfo;//"水瓶座"
        public int gender;//性别  1:男 2:女
        public String school;//学校名称
        public List<Integer> schoolTag; // 学校标签 可空 10 - 985; 11 - 211; 20 QS200; 21 QS500; 30 双一流
        public int schoolType;//学校类型 可空按未知处理 0 未知 1 国内 2 海外
        public int degree;//学历 1 本科 2 硕士 3 博士
        public int schoolTime;//1：全日制 2：非全日制
        public String degreeInfo;//学历 1 本科 2 硕士 3 博士
        public String addressCode;// 现居地码 二级code
        public String addressLevel1;// 现居地 省/直辖市
        public String addressLevel2;// 现居地 市/直辖市的区
        public String hometownCode;// 家乡码 二级code
        public String hometownLevel1;// 家乡 省/直辖市
        public String hometownLevel2;// 家乡 市
        public int height;// 身高 cm
        public int weight;// 体重公斤 <40 为 40 以下 大于 120 为 120 以上

        public String getWeightStr() {
            if (weight < Constants.MIN_WEIGHT_KG) {
                return "40kg以下";
            }
            if (weight > Constants.MAX_WEIGHT_KG) {
                return "120kg以上";
            }
            return weight + "kg";

        }

        public String industryCode;// 行业码
        public String industry;// 行业
        public int houseHold;// 房子 1 无 2 有
        public int carHold;// 车 1 无 2 有

        public String careerCode;// 职业码，空为自定义输入内容
        public String career;// 职业
        public String familyDesc;// 家庭描述
        public int familyDescStatus;// 家庭描述审核状态，0-初始化,1-审核中,2-已驳回,3-通过  空时作为通过处理，初始化作为审核中处理
        public String familyDescInfo;// 家庭描述审核反馈信息，familyDescStatus=2时表示驳回原因 可空

        public String idealPartnerDesc;// 理想的另一半
        public int idealPartnerDescStatus;// 理想的另一半审核状态，0-初始化,1-审核中,2-已驳回,3-通过  空时作为通过处理，初始化作为审核中处理
        public String idealPartnerDescInfo;// 理想的另一半审核反馈信息，idealPartnerDescStatus=2时表示驳回原因 可空

        //个人信息页使用
        public int avatarCertStatus;// 头像审核状态，1-审核中 2-已驳回 3-通过  可空
        public int nickNameCertStatus;// 昵称审核状态，1-审核中 2-已驳回 3-通过  可空
        public int introCertStatus;// 个人简介审核状态，1-审核中 2-已驳回 3-通过  可空
        public int houseCertStatus;// 房子审核状态 0:未认证 1-待审核，2-已驳回，3-已通过

        public String houseAddressName;//房产地址
        public int carCertStatus;// 车审核状态 0:未认证 1-待审核，2-已驳回，3-已通过
        public int careerCertStatus;// 职业审核状态，1-审核中 2-已驳回 3-通过  可空
        public int eduCertStatus;// 学校审核状态，1-审核中,2-已驳回,3-通过  空时作为没有审核过处理

        public int eduCertType;
        public Integer updateBirthTimes;// 可编辑生日次数，0表示不能编辑，空表示不限制
        // "nickNameCertInfo": "审核驳回",
        public String nickNameCertInfo;
        //"introCertInfo": "审核驳回",
        public String introCertInfo;
        // 职业审核反馈信息，careerCertStatus=2时表示驳回原因 可空
        public String careerCertInfo;

        public int age;//年龄
        public String ethnicity;//民族
        public int ethnicityCode;//民族code

        public String companyName;//公司全称
        public String companyNameShort;//公司简称
        public boolean addCompanyName;//是否有已通过的公司名称
        public boolean addAnnualIncome;//是否有已通过的年收入
        public int companyCertType;//公司审核类型 1 社保截图 2 工卡 可空
        public int companyCertStatus; // 公司审核状态 1-待审核，2-已驳回，3-已通过 //工作认证的状态，用来控制展示不展示V标
        public int workCompanyStatus;//工作信息中对应的公司名称的审核状态
        public String workCompanyCertInfo;//工作信息中的公司名称审核反馈信息，careerCertStatus=2时表示驳回原因 可空
        public int hideCompany;// 公司展示 2 仅自己可见 1 仅互相喜欢可见 0 所有人可见
        public int maritalStatus; // 婚姻状态 1 未婚 2 离异 3 丧偶
        public String maritalStatusInfo;//未婚
        public int marriageCertStatus; //认证状态：0:未认证  1:查询中 2:认证失败 3:认证成功

        public int childbearingPreference; // 生育偏好 1 想要孩子 2 不想要孩子 3 看情况决定
        public String childbearingPreferenceInfo;//想要孩子

        public int loveGoal; // 恋爱目标 10:短期内结婚 20:3-5年结婚 30:恋爱开始 40:还没想好
        public String loveGoalInfo;//还没想好

        public int annualIncome;// 年收入 1: 5万以下; 2: 5-10万; 3: 10-20万; 4: 20-30万; 5: 30-40万; 6: 40-50万; 7: 50-70万; 8: 70-100万; 9: 100万以上
        public int incomeCertStatus;//收入认证 0 未认证  1待审核  2已驳回  3已通过
        public String annualIncomeInfo;//5万以下
        public int hideIncome;//隐藏收入 2 仅自己可见 1 仅互相喜欢可见 0 所有人可见
        public String hukouLevel1;// 户口 省/直辖市
        public String hukouLevel2;// 户口 市/直辖市的区
        public String hukouCode;// 户口所在地码  二级code
        public int hideHukou;// 隐藏户口 1 隐藏 2 不隐藏

        public String companyCertInfo;//"审核驳回", // 2 驳回时返回

        public int smokingHabit;// 抽烟习惯 1 不抽烟 2 偶尔抽烟 3 经常抽烟
        public String smokingHabitInfo;//"不抽烟",

        public int drinkingHabit;// 喝酒习惯 1 不喝酒 2 偶尔喝酒 3 经常喝酒
        public String drinkingHabitInfo;//"不喝酒",

        public int phase;//  5 可匹配用户 6 正式用户 判断 IN 5，6

        public String singleReason;//单身原因
        public int singleReasonCertStatus;//单身原因审核状态： 1-待审核，2-已驳回，3-已通过
        public String singleReasonCertInfo;//单身原因审核反馈信息，singleReasonCertStatus=2时表示驳回原因 可空
        public int hideThumb;//是否隐藏点赞  0不隐藏 1是隐藏

        public String getAddressLevel2() {
            if (TextUtils.isEmpty(addressLevel2)) {
                return "";
            }
            return addressLevel2;
        }

        public String getHometownLevel2() {
            if (TextUtils.isEmpty(hometownLevel2)) {
                return "";
            }
            return hometownLevel2;
        }

        public String getBirthdayAndConstellation() {
            if (!TextUtils.isEmpty(birthday)) {
                int age = LDate.getAgeBYString(birthday, LDate.yMdFormat);
                StringBuilder ageBuilder = new StringBuilder();
                ageBuilder.append(age);
                ageBuilder.append(BaseApplication.getApplication().getResources().getString(R.string.me_age));
                ageBuilder.append("·");
                ageBuilder.append(StringUtil.getConstellation(constellation));
                return ageBuilder.toString();
            }
            return "";
        }

        public String getSchoolDegree() {
            if (!TextUtils.isEmpty(school)) {
                StringBuilder schoolDegreeBuilder = new StringBuilder();

                String degreeStr = StringUtil.getEducationBackGround(degree);
                if (!TextUtils.isEmpty(degreeStr)) {
                    schoolDegreeBuilder.append(degreeStr);
                    schoolDegreeBuilder.append('·');
                }
                schoolDegreeBuilder.append(school);

                if (schoolTime == 1) {
                    schoolDegreeBuilder.append('·');
                    schoolDegreeBuilder.append("全日制");
                } else if (schoolTime == 2) {
                    schoolDegreeBuilder.append('·');
                    schoolDegreeBuilder.append("非全日制");
                }

                return schoolDegreeBuilder.toString();
            }
            return "";
        }

        public String getIndustryCareer() {
            return industry;
        }

        public String getIndustryJob() {
            return career;
        }

        public boolean localHideCompany() {
            return hideCompany == 1;
        }

        public boolean localHideIncome() {
            return hideIncome == 1;
        }

        public boolean havePrivateInfo() {
            return annualIncome > 0 || houseHold > 0 || carHold > 0 || !TextUtils.isEmpty(hukouCode);
        }


        public String getCarShow() {
            if (carHold == 1) {
                return Utils.getApp().getString(R.string.me_info_preview_car_no);
            } else {
                return Utils.getApp().getString(R.string.me_info_preview_car);
            }
        }

        public String getHouseShow() {
            if (houseHold == 1) {
                return Utils.getApp().getString(R.string.me_info_preview_house_no);
            } else {
                return Utils.getApp().getString(R.string.me_info_preview_house);
            }
        }

        public String getIncomeShow() {
            String[] stringArray = Utils.getApp().getResources().getStringArray(R.array.common_array_annual_income);
            if (annualIncome > stringArray.length) {
                return "";
            }
            return Utils.getApp().getString(R.string.me_info_preview_income, stringArray[annualIncome - 1]);
        }

        public String getHukouShow() {
            return Utils.getApp().getString(R.string.me_info_preview_hukou, hukouLevel1, getHukouLevel2());
        }

        public String getHukouLevel2() {
            if (TextUtils.isEmpty(hukouLevel2)) {
                return "";
            }
            return hukouLevel2;
        }
    }

    public static class Story implements Serializable {
        public static final int TYPE_LIVE_PHOTO = 2;
        public static final int TYPE_VIDEO = 3;
        public static final int TYPE_EMPTY = 0;
        private static final long serialVersionUID = -2366041527699451319L;
        public String id;// 唯一id
        public int type;// 类型：1-图片; 2-live_photo; 3-视频
        public String photo;// 图片地址
        public String tinyPhoto;// 图片缩略图地址
        public int photoStyle; //照片样式 1原样展示 2模糊处理
        public String video;// 视频地址 可空，有则为视频故事
        public String extendInfo;// 视频扩展信息，同上传时内容 可空
        public String text; // 照片故事描述

        public int certStatus;// 1-审核中 2-已驳回 3-通过  可空(// 审核状态，0-初始化,1-审核中,2-已驳回,3-通过  空时作为通过处理，初始化作为审核中处理)
        public String certInfo;//
        public int textReject;//照片故事文字驳回 1:正常 2:驳回
        public int mediaReject;//照片故事图片驳回 1:正常 2:驳回
        public int width;//图片宽
        public int height;//图片高
        public ThumbInfoItem thumbInfo;
    }

    public static class ThumbInfoItem implements Serializable {
        public String thumbId;//点赞数据加密id
        public int status;//点赞状态，0-已取消；1-已点赞
        public int thumbCount;//点赞数量
    }

    public static class Label implements Serializable {
        public int tagId;
        public String tagName;
        public boolean sameTag;
        public String icon;
        public int parentId;
        public String parentName;

        public Label() {
        }

        public Label(int tagId, String tagName, boolean sameTag, String icon, int parentId, String parentName) {
            this.tagId = tagId;
            this.tagName = tagName;
            this.sameTag = sameTag;
            this.icon = icon;
            this.parentId = parentId;
            this.parentName = parentName;
        }
    }

    public static class QuestionAnswer implements Serializable {
        private static final long serialVersionUID = 9035243621793626796L;
        public String id;// 唯一id
        public String question;// 问题
        public long questionId;// 问题id
        public String answer;// 回答内容 或者 音频地址
        public String extendInfo; // 语音扩展信息，同上传时内容 可空

        public int answerCertStatus; // "answerCertStatus": 2, // 回答文本审核状态 2-已驳回 3-通过
        public List<AnswerImage> imgs; // 文本问答图片列表，可空

        public int certStatus;// 1-审核中 2-已驳回 3-通过  可空
        // "certInfo": "审核驳回", // 审核反馈信息，certStatus=2时表示驳回原因 可空
        public String certInfo;

        public List<QuestionTemplateModel.AnswerOptionBean> options;//选项

        public int showGuide; //是否在该信息下展示引导填写模块, 1-是，0-否
        public ThumbInfoItem thumbInfo;


        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getQuestion() {
            return question;
        }

        public void setQuestion(String question) {
            this.question = question;
        }

        public String getAnswer() {
            return answer;
        }

        public void setAnswer(String answer) {
            this.answer = answer;
        }

        public String getExtendInfo() {
            return extendInfo;
        }

        public void setExtendInfo(String extendInfo) {
            this.extendInfo = extendInfo;
        }

        public int getCertStatus() {
            return certStatus;
        }

        public void setCertStatus(int certStatus) {
            this.certStatus = certStatus;
        }

        public String getCertInfo() {
            return certInfo;
        }

        public void setCertInfo(String certInfo) {
            this.certInfo = certInfo;
        }

    }

    public static class AnswerImage extends Story {
        /*
                "id": 13223234221, // 文本问答图片id
                "photo": "img.png",
                "width":100,
                "height":100,
                "tinyPhoto":"img.png",
                "tinyWidth":50,
                "tinyHeight":50,
                "token": "xxxxx", // 图片 token
                "certStatus": 3, // 2-已驳回 3-通过
         */

        public int tinyWidth;
        public int tinyHeight;

        public String token;

        public transient Uri localImage;

        public boolean isSame(@NonNull AnswerImage image) {
            return (
                    image.token != null && TextUtils.equals(image.token, this.token))
                    || (image.photo != null && TextUtils.equals(image.photo, this.photo))
                    || (image.localImage != null && TextUtils.equals(image.localImage.toString(), this.localImage.toString())
            );
        }
    }

    public static class CertInfo implements Serializable {
        private static final long serialVersionUID = -2366041527699451119L;
        public int certPassCount;//认证通过数量
        public int waitCertCount;//待认证数量

        public List<CertInfoBean> certTagList;
        public String certContent;

        public static class CertInfoBean implements Serializable {
            public int certStatus;//2驳回 3通过  // 0:未认证 3-已通过 仅通过状态展示 V 标志
            public String content;//认证内容
            public String tag;// 认证 tag
            public String icon;// 图标地址
            public String parentContent;

            /**
             * 是否已通过
             *
             * @return
             */
            public boolean hasApprove() {
                return certStatus == 3;
            }
        }
    }

    public static class CompareInfo implements Serializable {
        public int sameTagNum;
    }


}
