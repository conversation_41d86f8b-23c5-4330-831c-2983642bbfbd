package com.kanzhun.foundation.api.model

import com.kanzhun.foundation.api.model.ProfileInfoModel.AnswerImage
import java.io.Serializable


data class ThumbListResponse(val hasMore:Boolean ?, val rows: List<ThumbBean>?) : Serializable

/**
 * USER(0, "用户", true, "照片"),
 * AVATAR(10, "形象照", true, "照片"),
 * INTRO(30, "关于我", false, "自我介绍"),
 * FAMILY_DESC(31, "家庭介绍", false, "家庭介绍"),
 * IDEAL_PARTNER_DESC(32, "我的理想型模块", false, "理想型"),
 * STORY_PHOTO(50, "我的生活图片", true, "照片"),
 * AUDIO_ANSWER(61, "我的声音模块", true, "声音"),
 * TEXT_ANSWER(62, "文字问答", true, "回答"),
 * AB_FACE(70, "AB面", true, "AB面"),
 * INTEREST(200, "我的兴趣爱好", false, "兴趣爱好"),
 * SINGLE_REASON(210, "单身原因", false, "单身原因"),
 * INTRO_PHOTO(321, "自我介绍图片", true, "照片"),
 * INTEREST_PHOTO(322, "兴趣爱好图片", true, "照片"),
 * FAMILY_DESC_PHOTO(323, "家庭情况图片", true, "照片"),
 */
data class ThumbBean(
    val userId: String?,
    val securityId: String?,
    val nickName: String?,//头像
    val avatar: String?, // 头像地址
    val tinyAvatar: String?,//用户头像缩略图
    val statusTag: String?,//满足的状态标签
    val thumbModuleContent: String?,//点赞模块儿文案
    val recommendTag: List<String>?,// 推荐优质标签
    val resourceType: Int?,//点赞类型
    val timeStr: String?,//点赞时间字符串
    val createTime: Long?,//点赞时间
    val thumbId: String?,//点赞id
    val readStatus: Int?,//已读状态，0-未读，1-已读
    val isDeleted: Int?,//重置后的删除状态，0-未删除，1-删除
    val userStatus: Int?,//1-正常；2-封禁；3-待注销；4-注销
    val phase: Int?,//小于5为不可匹配，大于等于5是可匹配
    val resourceInfo: String?,//json字符串数据数据源，见备注
    val jumpUrl:String?,//右侧热区跳转链接
) : Serializable


/**
 * 单张图片(视频)类型
 */
data class ResourcePhotoBean(
    val photo: String?,
) : Serializable

/**
 * 文本类型
 */
data class ResourceTextBean(
    val text: String?,
) : Serializable

/**
 * 文字问答
 */
data class ResourceQABean(
    val question: String?,
    val answer: String?,
    val optionInfo: String?,//OptionInfoBean
    val imgs: List<AnswerImage>?,
) : Serializable

/**
 * 语音问答：answer是音频地址
 */
data class ResourceVoiceBean(
    val question: String?,
    val answer: String?,
    val extendInfo: String?,//ExtendInfoBean
) : Serializable

/**
 * AB面
 */
data class ResourceABFaceBean(
    val photoA: String?,
    val photoB: String?,
) : Serializable


data class OptionInfoBean(
    val id: Int?,
    val name: String?
) : Serializable

data class ExtendInfoBean(
    val duration: Int?,
    val wave: List<Int>?,//List<Int>?
) : Serializable
