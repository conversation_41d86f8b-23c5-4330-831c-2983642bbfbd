package com.kanzhun.foundation.api.base;

import android.content.Context;

import com.kanzhun.common.base.LBase;
import com.kanzhun.utils.SettingBuilder;

import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.CertificateFactory;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

/**
 * Created by monch on 15/5/22.
 */
public class HostConfig {

    // DEBUG
    public static int MQTT_PROT_QA = 12443;
    public static int MQTT_PORT_PRE = 20002;
    private static Addr CONFIG = null;

    public static final String API_PATH_PREFIX = "/api/";

    public static final String H5_PATH_PREFIX = "/h5/";

    public static final String HTTPS_PREFIX = "https://";

    public static final String MEDIA_ONLINE_HOST = "marry-media.themarryapp.com";
    public static final String MEDIA_QA_HOST = "o2-qa.weizhipin.com";


    public static void init() {
        CONFIG = getAddrByType(LBase.getBuildConfig().CONFIG);
    }

    private static X509TrustManager x509TrustManager;

//    /**
//     *
//     * **/
//    public static int getRequestTimeThreshold(){
//        return LBase.getRequestTimeThreshold();
//    }

    public enum Addr {
        ONLINE(SettingBuilder.HOST_CONFIG_TYPE_ONLINE, "线上环境", "https://marry-api.themarryapp.com",
                "https://marry-media.themarryapp.com",
                "https://www.themarryapp.com", "https://nebula.zhipin.com", "https://hi-api.zhipin.com",
                "*************", 12443),

        // 预发环境
        PRE(SettingBuilder.HOST_CONFIG_TYPE_PRE, "预发环境", "https://marry-api-pre.weizhipin.com",
                "https://marry-pre.weizhipin.com",
                "https://themarryapp-pre.weizhipin.com", "https://pre-nebula.zhipin.com",
                "https://hi-api-qa.weizhipin.com",
                "*************", MQTT_PORT_PRE),

        QA(SettingBuilder.HOST_CONFIG_TYPE_QA, "QA环境", "https://o2-api-qa.weizhipin.com",
                HTTPS_PREFIX+MEDIA_QA_HOST,
                "https://orange-qa.weizhipin.com", "https://qa-nebula.weizhipin.com",
                "https://hi-api-qa.weizhipin.com",
                "*************", MQTT_PROT_QA),

        RD(SettingBuilder.HOST_CONFIG_TYPE_RD, "开发环境", "https://o2-api-pre-qa.weizhipin.com",
           HTTPS_PREFIX+"o2-pre-qa.weizhipin.com",
                "https://o2-h5-pre-qa.weizhipin.com", "https://qa-nebula.weizhipin.com",
                   "https://hi-api-qa.weizhipin.com",
                   "***********", MQTT_PROT_QA);

        private final int type; // 类型
        private final String name;  // 名称
        private String apiAddr;   // Api地址
        private String loadAddr; //文件上传地址
        private String mqttAddr;  // Mqtt地址
        private String webAddr; //web地址
        private final int mqttPort;     // Mqtt端口
        private String videoChatAddr;//视频通话地址
        private String logUploadAddr;

        Addr(int type,
             String name,
             String apiAddr,
             String loadAddr,
             String webAddr,
             String videoChatAddr,
             String logUploadAddr,
             String mqttAddr,
             int mqttPort) {
            this.type = type;
            this.name = name;
            this.apiAddr = apiAddr;
            this.loadAddr = loadAddr;
            this.webAddr = webAddr;
            this.videoChatAddr = videoChatAddr;
            this.logUploadAddr = logUploadAddr;
            this.mqttAddr = mqttAddr;
            this.mqttPort = mqttPort;
        }

        public int getType() {
            return type;
        }

        public String getName() {
            return name;
        }

        public String getApiAddr() {
            return apiAddr;
        }

        public String getLoadAddr() {
            return loadAddr;
        }

        public String getMqttAddr() {
            return mqttAddr;
        }

        public int getMqttPort() {
            return mqttPort;
        }

        public String getWebAddr() {
            return webAddr;
        }

        public String getVideoChatAddr() {
            return videoChatAddr;
        }

        public String getLogUploadAddr() {
            return logUploadAddr;
        }

        public void setLogUploadAddr(String logUploadAddr) {
            this.logUploadAddr = logUploadAddr;
        }
    }

    public static List<Addr> getAllAddrConfig() {
        List<Addr> list = new ArrayList<>();
        for (Addr addr : Addr.values()) {
            list.add(addr);
        }
        return list;
    }

    public static Addr getAddrByType(int type) {
        for (Addr addr : Addr.values()) {
            if (addr.getType() == type) {
                return addr;
            }
        }
        return Addr.PRE;
    }

    private static Map<String, String> URL_HOST_MAP = new HashMap<>();

//    public static void refreshApiHostList(){
//        if(HostConfig.CONFIG != HostConfig.Addr.ONLINE){
//            return;
//        }
//        GetApiHostListRequest request = new GetApiHostListRequest(new ApiRequestCallback<GetApiHostListResponse>() {
//            @Override
//            public void onSuccess(ApiData<GetApiHostListResponse> data) {
//                synchronized (URL_HOST_MAP){
//                    try {
//                        if(data.resp.result != null){
//                            for (ApiHostBean bean:data.resp.result){
//                                URL_HOST_MAP.put(bean.url,bean.host);
//                            }
//                        }
//                    }catch (Exception e){
//
//                    }
//                }
//            }
//
//            @Override
//            public void onComplete() {
//
//            }
//
//            @Override
//            public void onFailed(ErrorReason reason) {
//
//            }
//        });
//        HttpExecutor.execute(request);
//    }

    public static void setCONFIG(Addr addr) {
        CONFIG = addr;
    }

    public static Addr getCONFIG() {
        if (CONFIG == null) {
            init();
        }
        return CONFIG;
    }

    private static String getHost(String url) {
        if (CONFIG == null) {
            init();
        }
        if (URLConfig.FILE_UPLOAD_LIST.contains(url)) {
            return CONFIG.getLoadAddr() + API_PATH_PREFIX;
        } else if (URLConfig.FILE_H5_LIST.contains(url)) {
            return CONFIG.getWebAddr() + H5_PATH_PREFIX;
        } else if (URLConfig.LOG_UPLOAD_LIST.contains(url)) {
            return CONFIG.getLogUploadAddr() + API_PATH_PREFIX;
        }
        return CONFIG.getApiAddr() + API_PATH_PREFIX;
    }

    public static String getBaseUrl() {
        if (CONFIG == null) {
            init();
        }
        return CONFIG.getApiAddr() + API_PATH_PREFIX;
    }

    public static String getUploadBaseUrl() {
        if (CONFIG == null) {
            init();
        }
        return CONFIG.getLoadAddr() + API_PATH_PREFIX;
    }

    public static String transformUrlHost(String url) {
        return getHost(url) + url;
    }

    // TODO: 2022/2/23
    public static String getWXPayAppId() {
        if (getCONFIG() == Addr.ONLINE) {
            return "";
        } else {
            return "";
        }
    }

    public static SSLSocketFactory createSSLSocketFactory(Context context) {
        String ksPath;
        String certPath;
        char[] ps;
        if (getCONFIG() == Addr.ONLINE || getCONFIG() == Addr.PRE) {
            ksPath = "client.p12";
            certPath = "server.crt";
            ps = "LSDI10JDF0AJDFKMVCZKJV129DFJLKOQWIEJLKADFOI1280AFDJ".toCharArray();
        } else {
            ksPath = "client_dev.p12";
            certPath = "server_dev.crt";
            ps = "LWOEIR01I4L1DSF0JSLKQWNE012IJLNASDQE".toCharArray();
        }

        SSLContext sslContext = null;
        try {
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            InputStream ksInputStream = context.getResources().getAssets().open(ksPath);
            keyStore.load(ksInputStream, ps);
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(keyStore, ps);

            KeyStore cert = KeyStore.getInstance("PKCS12");
            InputStream certInputStream = context.getResources().getAssets().open(certPath);
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
            cert.load(null);
            cert.setCertificateEntry("bosso-server", certificateFactory.generateCertificate(certInputStream));

            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(cert);
            try {
                TrustManager[] trustManagers = tmf.getTrustManagers();
                x509TrustManager = (X509TrustManager) trustManagers[0];
            } catch (Exception e) {
                e.printStackTrace();
            }
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(kmf.getKeyManagers(), null, null);
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static HostnameVerifier createHostnameVerifier() {
        HostnameVerifier hostnameVerifier;
//        if (getCONFIG() == Addr.ONLINE) {
//            hostnameVerifier = OkHostnameVerifier.INSTANCE;
//        } else {
        hostnameVerifier = new HostnameVerifier() {
            @Override
            public boolean verify(String hostname, SSLSession session) {
                return true;
            }
        };
//        }
        return hostnameVerifier;
    }

    public static X509TrustManager getX509TrustManager() {
        return x509TrustManager;
    }

    public static SSLSocketFactory createMqttSSLSocketFactory(Context context) {
        String ksPath;
        String certPath;
        char[] ps;
        if (getCONFIG() == Addr.ONLINE || getCONFIG() == Addr.PRE) {
            ksPath = "client.p12";
            certPath = "server.crt";
            ps = "LSDI10JDF0AJDFKMVCZKJV129DFJLKOQWIEJLKADFOI1280AFDJ".toCharArray();
        } else {
            ksPath = "client_dev.p12";
            certPath = "server_dev.crt";
            ps = "LWOEIR01I4L1DSF0JSLKQWNE012IJLNASDQE".toCharArray();
        }

        SSLContext sslContext = null;
        try {
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            InputStream ksInputStream = context.getResources().getAssets().open(ksPath);
            keyStore.load(ksInputStream, ps);
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(keyStore, ps);

            KeyStore cert = KeyStore.getInstance("PKCS12");
            InputStream certInputStream = context.getResources().getAssets().open(certPath);
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
            cert.load(null);
            cert.setCertificateEntry("bosso-server", certificateFactory.generateCertificate(certInputStream));

            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(cert);
            TrustManager[] trustManagers = null;
            try {
                trustManagers = tmf.getTrustManagers();
            } catch (Exception e) {
                e.printStackTrace();
            }
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(kmf.getKeyManagers(), trustManagers, null);
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getH5Host(String url){
        return HostConfig.getCONFIG().webAddr + HostConfig.H5_PATH_PREFIX + url;
    }

}
