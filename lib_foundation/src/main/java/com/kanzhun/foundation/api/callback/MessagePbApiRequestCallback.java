package com.kanzhun.foundation.api.callback;

import com.google.gson.JsonObject;
import com.kanzhun.foundation.api.bean.MessageResponse;
import com.kanzhun.foundation.utils.MessageUtils;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;

import org.json.JSONObject;

public class MessagePbApiRequestCallback extends BaseRequestCallback<MessageResponse> {
    @Override
    public MessageResponse parseResponse(JsonObject result) {
        try {
            MessageResponse response = new MessageResponse();
            JSONObject jsonObject = new JSONObject(result.toString());
            response.messages = MessageUtils.jsonPb2Message(jsonObject.optJSONArray("result"));
            return response;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return super.parseResponse(result);
    }

    @Override
    public void onSuccess(MessageResponse data) {

    }

    @Override
    public void dealFail(ErrorReason reason) {

    }

    @Override
    public void showFailed(ErrorReason reason) {
    }
}
