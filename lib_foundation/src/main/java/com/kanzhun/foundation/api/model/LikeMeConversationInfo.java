package com.kanzhun.foundation.api.model;

import java.io.Serializable;
import java.util.List;

public class LikeMeConversationInfo implements Serializable {
    private static final long serialVersionUID = 2335377103584921077L;

    public int newCount;//喜欢你的数量
    public int hasNew;// 是否有新的喜欢，1-是，0-否
    public int allViewed; // // 是否已全部查看， 1-是，0-否
    public List<LikeLastInfo> latestInfo;

    /**
     * "matchPageAvatars":["https://xxxx", "https://zzzzzz"], // 匹配页面展示的头像列表
     * "defaultAvatars":["https://xxxx", "https://zzzzzz"], // allViewed=1时，只展示的头像列表
     */
    public List<String> matchPageAvatars;//前几个头像缩略图
    public List<String> defaultAvatars;//前几个头像缩略图

    public static class LikeLastInfo {
        /**
         * "userId": "zzz", // 用户id
         * "nickName": "xxxx", // 昵称
         * "tinyAvatar": "https://xxxx", // 缩略图头像
         * "createTime": 1551665159037, // 创建时间
         */
        public String userId;
        public String nickName;
        public String tinyAvatar;
        public long createTime;
    }
}
