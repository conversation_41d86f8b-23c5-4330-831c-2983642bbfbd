package com.kanzhun.foundation.api.bean;

import android.net.Uri;

import androidx.databinding.ObservableBoolean;

import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.player.OPlayerHelper;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/26
 * 个人信息页视频故事bean
 */
public class VideoStoryBean extends ProfileInfoModel.Story {
    private static final long serialVersionUID = 771377070080521401L;
    public Uri fileUri;
    public boolean downloading;
    //是否是静音
    public ObservableBoolean silence = new ObservableBoolean(true);
    public OPlayerHelper oPlayerHelper;
}
