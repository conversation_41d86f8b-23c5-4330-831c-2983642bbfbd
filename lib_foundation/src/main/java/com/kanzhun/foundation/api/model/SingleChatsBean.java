package com.kanzhun.foundation.api.model;

import com.kanzhun.foundation.model.message.ChatMessage;

public class SingleChatsBean {
//    "chatId": "xxxx", // 聊天用户id，类型：long
//                "relationStatus": 11, // 关系：11-你喜欢TA，12-TA喜欢你，20-普通好友，30-相识中，40-情侣，50-历史，60-删除
//                "linkMsgFunc": 1, // 是否开启语音聊天功能，1-未开启，2-已开启
//     "inChat": 1, // 是否是聊天会话， 1-是，0-否
//                "dateCardStatus": 1, // 相识卡状态：1-未解锁，2-已解锁，3-已使用
//                "loveCardStatus": 1, // 表白信状态：1-未解锁，2-已解锁，3-已使用

    public String chatId;
    public int relationStatus;
    public int linkMsgFunc;
    public int dateCardStatus;
    public int loveCardStatus;
    public int inChat;
    public int minSeq;
    public String icon;
    // 以下字段只有在inChat从0到1的时候才返回
    public int unreadCount;//": 12, // 未读消息数量
    public long readMarkMid;//": 10012323, // 最后一条已读标记消息id
    public long readSyncMid;//": 112312323, // 最后一条已读同步消息id
    public ChatMessage visibleMessage;//": {}, // 最后一条可见消息。和chat.proto中OgMessage结构一致
    public ChatMessage lastMessage;//": {}, // 最后一条消息，当最后一条消息id和最后一条可见消息不一致时，才有值。和chat.proto中OgMessage结构一致
    public int iconShowType;// icon 展示类型  1 仅内部展示，2 内外都展示

    public long chatSystemId; // 如果是系统用户，返回id

    public long sort; // 排序值，高值在前
    public int shield; // 是否屏蔽 1 屏蔽 0 未屏蔽
}
