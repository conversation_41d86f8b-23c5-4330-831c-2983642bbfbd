package com.kanzhun.foundation.api.model;

import java.io.Serializable;

@Deprecated
public class ConversationTabSummaryInfo implements Serializable {
    private static final long serialVersionUID = 5335377103584921077L;

    public static final int TYPE_HIDE_ME_LIKE = 0;
    public static final int TYPE_SHOW_ME_LIKE = 1;

    public static final int TYPE_HIDE_CHAT_HISTORY = 0;
    public static final int TYPE_SHOW_CHAT_HISTORY = 1;

    public LikeMeConversationInfo likeMeInfo;
    public long nowTime;

    public String getDefaultAvatar(int index){
        if(likeMeInfo == null || likeMeInfo.defaultAvatars == null || likeMeInfo.defaultAvatars.size() <= index){
            return null;
        }
        return likeMeInfo.defaultAvatars.get(index);
    }
}