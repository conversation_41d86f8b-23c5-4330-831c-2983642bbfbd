package com.kanzhun.foundation.api.model;

import java.io.Serializable;

public class MatchingBlockGuide implements Serializable {
    private static final long serialVersionUID = 1005057584742724107L;

    public String title;
    public String subtitle;
    public String icon;
    public String buttonText;
    /**
     * 跳转协议
     *
     * URL(100, "打开链接"),
     * F2_PAGE(101, "匹配页面"),
     * CHAT_DIALOG(102, "聊天会话框"),
     *
     * MY_TAB(201, "我的tab页面"),
     * PROFILE_EDIT(202, "个人页编辑态"),
     * CERT_LIST(203, "认证列表页"),
     * COMMUNITY(204, "社区"),
     *
     * LIKE_ME_PAGE(301, "喜欢我的列表"),
     * COMMUNITY_NOTICE(302, "社区通知列表"),
     * TOPIC_GAME_REPLY(303,"话题游戏回复"),
     * CIRCLE(304,"圈子页面"),
     * MOMENT_SUBMIT(305,"发布器页面"),
     * MATCH_GAME(306,"匹配游戏"),
     */
    public String jumpProtocol;

    @Override
    public String toString() {
        return "MatchingBlockGuide{" +
                "title='" + title + '\'' +
                ", subtitle='" + subtitle + '\'' +
                ", icon='" + icon + '\'' +
                ", buttonText='" + buttonText + '\'' +
                ", jumpProtocol='" + jumpProtocol + '\'' +
                '}';
    }
}
