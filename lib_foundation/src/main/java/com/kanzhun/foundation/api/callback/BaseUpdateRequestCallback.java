package com.kanzhun.foundation.api.callback;

import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;

public class BaseUpdateRequestCallback<R> extends BaseRequestCallback<R> {
    @Override
    public void onSuccess(R data) {

    }

    @Override
    public void dealFail(ErrorReason reason) {

    }

    @Override
    public void onComplete() {

    }

    @Override
    public boolean isErrorMsgShow() {
        return false;
    }

    @Override
    public void showFailed(ErrorReason reason) {

    }
}
