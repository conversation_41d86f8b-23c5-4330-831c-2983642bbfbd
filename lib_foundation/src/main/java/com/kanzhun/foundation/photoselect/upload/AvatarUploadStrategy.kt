package com.kanzhun.foundation.photoselect.upload

import android.net.Uri
import androidx.lifecycle.MutableLiveData
import com.kanzhun.foundation.api.model.ImageUploadModel
import com.kanzhun.foundation.utils.UploadFileUtil
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.http.upload.UploadRequestCallback

class AvatarUploadStrategy : IUploadStrategy {

    override fun upload(
        liveData: MutableLiveData<UploadResult>,
        sceneType: UploadSceneType,
        uri: Uri,
        cropInfo: UploadCropInfo?
    ) {
        UploadFileUtil.uploadAvatar(
            uri, cropInfo?.tinyBeginX ?: 0,
            cropInfo?.tinyBeginY ?: 0,
            cropInfo?.tinyEndX ?: 0,
            cropInfo?.tinyEndY ?: 0,
            object : UploadRequestCallback<ImageUploadModel>() {
                override fun onSuccess(data: ImageUploadModel?) {
                    if (data == null) {
                        liveData.value = UploadResult(status = UploadStatus.FAIL, sourceUri = uri.toString())
                    } else {
                        liveData.value = UploadResult(status = UploadStatus.SUCCESS, result = data, sourceUri = uri.toString())
                    }
                }

                override fun dealFail(reason: ErrorReason?) {
                    liveData.value = UploadResult(status = UploadStatus.FAIL, sourceUri = uri.toString())
                }
            })
    }
}