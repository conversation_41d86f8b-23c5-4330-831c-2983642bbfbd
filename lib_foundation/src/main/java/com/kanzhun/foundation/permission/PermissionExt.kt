package com.kanzhun.foundation.permission

import android.os.Build
import androidx.fragment.app.FragmentActivity


fun isAboveAndroid12() = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
fun FragmentActivity.requestBluetoothPermission(callback: (Boolean) -> Unit) {
    if (isAboveAndroid12()) {
        PermissionHelper.getBluetoothPermissionHelper(this).setPermissionCallback { yes, _ ->
            callback(yes)
        }.requestPermission()
    } else {
        callback(true)
    }
}