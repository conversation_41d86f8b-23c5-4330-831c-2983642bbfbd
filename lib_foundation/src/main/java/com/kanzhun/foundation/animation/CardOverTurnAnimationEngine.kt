package com.kanzhun.foundation.animation

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.DecelerateInterpolator
import com.kanzhun.common.animator.BaseViewAnimator
import com.kanzhun.common.animator.YoYo
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 卡片正反面旋转效果
 */
class CardOverTurnAnimationEngine(val llSideA: View, val llSideB: View, val mDuration: Long = 400L,val onOverTurnListener:OnOverTurnListener) {

    private val mIsRunning = AtomicBoolean(false)

    private var index: Int = 1

    private val minScale = 0.95F

    private val distance = 15000

    init {
        llSideB.rotationY = -90F
        llSideB.scaleX = minScale
        llSideB.scaleY = minScale

        val cameraScale: Float = llSideA.context.resources.displayMetrics.density * distance
        llSideA.cameraDistance = cameraScale
        llSideB.cameraDistance = cameraScale
    }

    fun isRunning():Boolean{
        return mIsRunning.get()
    }

    fun isSideA():Boolean{
        return index%2==1
    }

    private fun rotationYFromA() {
        if (mIsRunning.compareAndSet(false, true)) {
            onOverTurnListener.onAnimationStart()
            YoYo.with(object : BaseViewAnimator() {
                override fun prepare(target: View?) {
                    animatorAgent.playTogether(
                        ObjectAnimator.ofFloat(target, "scaleX", 1f, minScale),
                        ObjectAnimator.ofFloat(target, "scaleY", 1f, minScale),
                        ObjectAnimator.ofFloat(target, "rotationY", 0f, 90f))
                }
            }).duration(mDuration).interpolate(DecelerateInterpolator())
                .withListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        llSideB.bringToFront()
                        YoYo.with(object : BaseViewAnimator() {
                            override fun prepare(target: View?) {
                                animatorAgent.playTogether(
                                    ObjectAnimator.ofFloat(target, "scaleX", minScale, 1f),
                                    ObjectAnimator.ofFloat(target, "scaleY", minScale, 1f),
                                    ObjectAnimator.ofFloat(target, "rotationY", -90f, 0f))
                            }
                        }).withListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                super.onAnimationEnd(animation)
                                mIsRunning.set(false)
                                index++
                                onOverTurnListener.onAnimationEnd()
                            }
                        })
                            .duration(mDuration)
                            .interpolate(DecelerateInterpolator())
                            .playOn(llSideB)
                    }
                }).playOn(llSideA)
        }
    }

    private fun rotationYFromB() {
        if (mIsRunning.compareAndSet(false, true)) {
            onOverTurnListener.onAnimationStart()
            YoYo.with(object : BaseViewAnimator() {
                override fun prepare(target: View?) {
                    animatorAgent.playTogether(
                        ObjectAnimator.ofFloat(target, "scaleX", 1f, minScale),
                        ObjectAnimator.ofFloat(target, "scaleY", 1f, minScale),
                        ObjectAnimator.ofFloat(target, "rotationY", 0f, 90f))
                }
            }).duration(mDuration).interpolate(DecelerateInterpolator())
                .withListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        llSideA.bringToFront()
                        YoYo.with(object : BaseViewAnimator() {
                            override fun prepare(target: View?) {
                                animatorAgent.playTogether(
                                    ObjectAnimator.ofFloat(target, "scaleX", minScale, 1f),
                                    ObjectAnimator.ofFloat(target, "scaleY", minScale, 1f),
                                    ObjectAnimator.ofFloat(target, "rotationY", -90f, 0f))
                            }
                        }).withListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                super.onAnimationEnd(animation)
                                mIsRunning.set(false)
                                index++
                                onOverTurnListener.onAnimationEnd()

                            }
                        })
                            .duration(mDuration)
                            .interpolate(DecelerateInterpolator())
                            .playOn(llSideA)
                    }
                }).playOn(llSideB)
        }
    }

    fun overTurn() {
        if (index % 2 == 1) {
            rotationYFromA()
        } else {
            rotationYFromB()
        }
    }
}

interface OnOverTurnListener {
    fun onAnimationStart()

    fun onAnimationEnd()
}