package com.kanzhun.foundation.apm;

import android.util.Log;

import com.kanzhun.utils.platform.BuildInfoUtils;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.beta.tinker.TinkerManager;
import com.tencent.tinker.loader.shareutil.ShareTinkerLog;

public class <PERSON><PERSON>ogger implements ShareTinkerLog.TinkerLogImp {
    public static final String TAG = "Tinker.PatchLogger";


    public synchronized void onTLogInit() {
        boolean isRelease = !BuildInfoUtils.isDebug();
        TinkerManager.setTinkerLogImpl(new ShareTinkerLog.TinkerLogImp() {
            @Override
            public void v(String tag, String fmt, Object... values) {
                String log = values != null && values.length > 0 ? String.format(fmt, values) : fmt;
                TLog.info(tag, log);
                if (isRelease) Log.v(tag, log);
            }

            @Override
            public void d(String tag, String fmt, Object... values) {
                String log = values != null && values.length > 0 ? String.format(fmt, values) : fmt;
                if (isRelease) Log.d(tag, log);
                TLog.info(tag, log);
            }

            @Override
            public void i(String tag, String fmt, Object... values) {
                String log = values != null && values.length > 0 ? String.format(fmt, values) : fmt;
                if (isRelease) Log.i(tag, log);
                TLog.info(tag, log);
            }

            @Override
            public void w(String tag, String fmt, Object... values) {
                String log = values != null && values.length > 0 ? String.format(fmt, values) : fmt;
                if (isRelease) Log.w(tag, log);
                TLog.info(tag, log);
            }

            @Override
            public void e(String tag, String fmt, Object... values) {
                String log = values != null && values.length > 0 ? String.format(fmt, values) : fmt;
                if (isRelease) Log.e(tag, log);
                TLog.error(tag, log);
            }

            @Override
            public void printErrStackTrace(String tag, Throwable tr, String fmt, Object... values) {
                String log = values != null && values.length > 0 ? String.format(fmt, values) : fmt;
                if (log == null) {
                    log = "";
                }

                log = log + "  " + Log.getStackTraceString(tr);
                if (isRelease) Log.e(tag, log);
                TLog.error(tag, log);
            }
        });

    }

    private PatchLogger() {
    }

    private static class SingleTonHolder {
        private static PatchLogger INSTANCE = new PatchLogger();
    }

    public static PatchLogger getInstance() {
        return SingleTonHolder.INSTANCE;
    }


    public void v(String s, String s1, Object... objects) {
        ShareTinkerLog.v(s, s1, objects);
    }

    public void i(String s, String s1, Object... objects) {
        ShareTinkerLog.i(s, s1, objects);
    }

    public void w(String s, String s1, Object... objects) {
        ShareTinkerLog.w(s, s1, objects);
    }

    public void d(String s, String s1, Object... objects) {
        ShareTinkerLog.d(s, s1, objects);
    }

    public void e(String s, String s1, Object... objects) {
        ShareTinkerLog.e(s, s1, objects);
    }

    public void printErrStackTrace(String s, Throwable throwable, String s1, Object... objects) {
        ShareTinkerLog.printErrStackTrace(s, throwable, s1, objects);
    }
}
