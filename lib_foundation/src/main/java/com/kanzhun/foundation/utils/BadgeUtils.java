package com.kanzhun.foundation.utils;

import android.content.Context;

import me.leolin.shortcutbadger.ShortcutBadger;

/**
 * Created by monch on 15/8/4.
 * 修改桌面ICON未读数量
 */
public class BadgeUtils {

    public static void setBadge(Context context, int count) {
        if (count < 0) {
            count = 0;
        }
        ShortcutBadger.applyCount(context, count);
    }

    public static void clearBadge(Context context) {
        ShortcutBadger.removeCount(context);
    }
}
