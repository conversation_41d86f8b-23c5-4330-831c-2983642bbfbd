package com.kanzhun.foundation.utils

import android.content.Context
import com.kanzhun.foundation.constant.ShareKey
import com.kanzhun.utils.T
import com.tencent.mm.opensdk.constants.Build
import com.tencent.mm.opensdk.modelbiz.WXOpenCustomerServiceChat
import com.tencent.mm.opensdk.openapi.WXAPIFactory

class WXChatService {

    fun chat(context: Context?){
        if (context == null)return
        //跳转微信企业客服
        val api = WXAPIFactory.createWXAPI(context, ShareKey.getWxAppId())
        if (!api.isWXAppInstalled()) {
            T.ssd("未安装微信")
            return
        }

        // 判断当前版本是否支持拉起客服会话
        if (api.getWXAppSupportAPI() >= Build.SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT) {
            val req = WXOpenCustomerServiceChat.Req()
            req.corpId = "ww8843c261b0587a17" // 企业ID
            req.url = "https://work.weixin.qq.com/kfid/kfc4d05b7c2c9a55089" // 客服URL
            api.sendReq(req)
        }
    }
}