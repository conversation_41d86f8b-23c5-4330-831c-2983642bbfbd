package com.kanzhun.foundation.utils;

import android.app.Activity;
import android.text.TextUtils;

import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.bean.BaseStoryShowItem;
import com.kanzhun.foundation.model.profile.ExtInfoBean;
import com.kanzhun.foundation.model.profile.GuideItemBean;
import com.kanzhun.foundation.model.profile.GuideItemsResponse;
import com.kanzhun.foundation.model.profile.IndustryBean;
import com.kanzhun.foundation.model.profile.ProfileMetaModel;
import com.kanzhun.foundation.model.profile.UserTipsBean;
import com.kanzhun.foundation.model.profile.UserTipsBeanV2;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.rxbus.RxBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 2022/4/29
 */
public class ProfileHelper {

    /**
     * 个人信息，避免新手任务等流程到处传参
     */
    private ProfileMetaModel profileMetaModel;
    /**
     * 引导流程及是否可跳过
     */
    private List<GuideItemBean> guideItems;// 为空则已填写完成
    /**
     * 当前引导项
     */
    private GuideItemBean targetGuideItem;
    /**
     * 修改行业的临时存储，需要等保存职业后才生效
     */
    private IndustryBean tmpIndustryBean;

    private ExtInfoBean extInfo;

    private List<BaseStoryShowItem> uploadCache = new ArrayList<>();//故事上传界面在引导流程中销毁缓存故事数据，用于重新显示

    public UserTipsBean userTipsBean;
    public UserTipsBeanV2 mUserTipsBeanV2;

    public void checkJump(Activity activity, int index) {
        checkJump(activity, index, false);
    }

    /**
     * 根据引导流程，判断跳转到新手流程的哪一页
     *
     * @param index             从1开始，与页面中显示的第几步一致
     * @param fromBasicInfoOver 是否从基本信息完成页过来的
     */
    public void checkJump(Activity activity, int index, boolean fromBasicInfoOver) {
        if (index < 1 || LList.isEmpty(guideItems)) return;
        if (index > LList.getCount(guideItems)) {
            AppUtil.finishActivity(activity);
            RxBus.getInstance().post("refresh", CertificationIdentifySource.MAIN_TAB);
            return;
        }
        targetGuideItem = guideItems.get(index - 1);
        if (targetGuideItem == null) return;

        // 找到第一个没完善的隐私信息
        int firstPrivacyCode = findFirstPrivacyCode();
        if (!fromBasicInfoOver && firstPrivacyCode > 0 && targetGuideItem.getCode() == firstPrivacyCode) {
            MePageRouter.jumpToBasicInfoOverActivity(activity, index);
            return;
        }

        switch (targetGuideItem.getCode()) {
            case Constants.NEWCOMER_NICKNAME_CODE: {
            }
            break;
            case Constants.NEWCOMER_AVATAR_CODE: {
                MePageRouter.jumpToAvatarUploadActivity(activity, index);
            }
            break;
            case Constants.NEWCOMER_INTRO_CODE: {
            }
            break;
            case Constants.NEWCOMER_OCCUPATION_CODE: {
                MePageRouter.jumpToOccupationActivity(activity, index,false);
            }
            break;
            case Constants.NEWCOMER_STORY_CODE: {
                MePageRouter.jumpToStoryUploadActivity(activity, index);
            }
            break;
            case Constants.NEWCOMER_HOMETOWN_CODE: {
                MePageRouter.jumpToHometownActivity(activity, index);
            }
            break;
            case Constants.NEWCOMER_REVENUE_CODE: {
                MePageRouter.jumpToRevenueActivity(activity, index);
            }
            break;
            case Constants.NEWCOMER_HOUSE_CAR_CODE: {
                MePageRouter.jumpToHouseCarActivity(activity, index);
            }
            break;
            case Constants.NEWCOMER_RESIDENCE_CODE: {
                MePageRouter.jumpToResidenceActivity(activity, index);
            }
            break;
            case Constants.NEWCOMER_STATURE_CODE: {
                MePageRouter.jumpToStatureActivity(activity, index);
            }
            break;
            case Constants.NEWCOMER_LIVING_PLACE_CODE: {
                MePageRouter.jumpToLivingPlaceActivity(activity, index);
            }
            break;
            default:
                break;
        }
    }

    public ProfileMetaModel getProfileMetaModel() {
        return profileMetaModel;
    }

    public void setProfileMetaModel(ProfileMetaModel profileMetaModel) {
        this.profileMetaModel = profileMetaModel;
    }

    public List<GuideItemBean> getGuideItems() {
        return guideItems;
    }

    public GuideItemBean getTargetGuideItem() {
        return targetGuideItem;
    }

    public void setGuideItemsResponse(GuideItemsResponse guideItemsResponse) {
        this.guideItems = guideItemsResponse != null ? guideItemsResponse.result : null;
        this.extInfo = guideItemsResponse != null ? guideItemsResponse.extInfo : null;
        this.uploadCache.clear();

        if (profileMetaModel == null) {
            profileMetaModel = new ProfileMetaModel();
            profileMetaModel.baseInfo = new ProfileInfoModel.BaseInfo();
        }
        if (extInfo != null && extInfo.baseInfo != null) {
            updateBaseInfo(extInfo.baseInfo);// 以接口返回的最新数据为准
        }

        int occupationIndex = -1;
        if (!LList.isEmpty(guideItems)) {
            for (int i = 0; i < guideItems.size(); i++) {
                GuideItemBean bean = guideItems.get(i);
                if (bean == null) continue;
                if (bean.getCode() == Constants.NEWCOMER_OCCUPATION_CODE) {
                    occupationIndex = i;
                    break;
                }
            }
        }

        // 如果存在职业引导页，则手动添加一条行业引导页在其前面
        if (occupationIndex >= 0) {
            GuideItemBean industryBean = new GuideItemBean();
            industryBean.setCode(Constants.NEWCOMER_INDUSTRY_CODE);
            guideItems.add(occupationIndex, industryBean);
        }
    }

    /**
     * 找到第一个隐私信息的code
     */
    private int findFirstPrivacyCode() {
        if (!LList.isEmpty(guideItems)) {
            for (GuideItemBean bean : guideItems) {
                if (isPrivacyCode(bean.getCode())) {
                    return bean.getCode();
                }
            }
        }
        return -1;
    }

    /**
     * code是否属于隐私信息
     */
    private boolean isPrivacyCode(int code) {
        return code == Constants.NEWCOMER_REVENUE_CODE || code == Constants.NEWCOMER_HOUSE_CAR_CODE || code == Constants.NEWCOMER_RESIDENCE_CODE;
    }

    public IndustryBean getTmpIndustryBean() {
        return tmpIndustryBean;
    }

    public void setTmpIndustryBean(IndustryBean tmpIndustryBean) {
        this.tmpIndustryBean = tmpIndustryBean;
    }

    public ExtInfoBean getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(ExtInfoBean extInfo) {
        this.extInfo = extInfo;
    }

    public List<BaseStoryShowItem> getUploadCache() {
        return uploadCache;
    }

    public void clearUploadCache() {
        this.uploadCache.clear();

    }

    public void setUploadCache(List<BaseStoryShowItem> uploadCache, HashMap<String, BaseStoryShowItem> rejected) {
        this.uploadCache.clear();
        if (!LList.isEmpty(uploadCache)) {
            for (BaseStoryShowItem s : uploadCache) {
                if (!TextUtils.isEmpty(s.getId())) {
                    BaseStoryShowItem story = rejected.remove(s.getId());
                    if (story != null) {
                        this.uploadCache.add(story);
                    } else {
                        this.uploadCache.add(s);
                    }
                } else {
                    this.uploadCache.add(s);
                }
            }
        }
    }

    public void clear() {
        profileMetaModel = null;
        if (guideItems != null) {
            guideItems.clear();
            guideItems = null;
        }
        targetGuideItem = null;
        tmpIndustryBean = null;
        extInfo = null;
        uploadCache.clear();
    }

    private void updateBaseInfo(ProfileInfoModel.BaseInfo baseInfo) {
        if (!TextUtils.isEmpty(baseInfo.avatar)) {
            profileMetaModel.baseInfo.avatar = baseInfo.avatar;
        }
        if (baseInfo.avatarCertStatus != 0) {
            profileMetaModel.baseInfo.avatarCertStatus = baseInfo.avatarCertStatus;
        }
        if (!TextUtils.isEmpty(baseInfo.avatarCertInfo)) {
            profileMetaModel.baseInfo.avatarCertInfo = baseInfo.avatarCertInfo;
        }
        if (!TextUtils.isEmpty(baseInfo.tinyAvatar)) {
            profileMetaModel.baseInfo.tinyAvatar = baseInfo.tinyAvatar;
        }
        if (!TextUtils.isEmpty(baseInfo.liveVideo)) {
            profileMetaModel.baseInfo.liveVideo = baseInfo.liveVideo;
        }
        if (!TextUtils.isEmpty(baseInfo.nickName)) {
            profileMetaModel.baseInfo.nickName = baseInfo.nickName;
        }
        if (baseInfo.nickNameCertStatus != 0) {
            profileMetaModel.baseInfo.nickNameCertStatus = baseInfo.nickNameCertStatus;
        }
        if (!TextUtils.isEmpty(baseInfo.nickNameCertInfo)) {
            profileMetaModel.baseInfo.nickNameCertInfo = baseInfo.nickNameCertInfo;
        }
        if (!TextUtils.isEmpty(baseInfo.intro)) {
            profileMetaModel.baseInfo.intro = baseInfo.intro;
        }
        if (baseInfo.introCertStatus != 0) {
            profileMetaModel.baseInfo.introCertStatus = baseInfo.introCertStatus;
        }
        if (!TextUtils.isEmpty(baseInfo.introCertInfo)) {
            profileMetaModel.baseInfo.introCertInfo = baseInfo.introCertInfo;
        }
        if (!TextUtils.isEmpty(baseInfo.industryCode)) {
            profileMetaModel.baseInfo.industryCode = baseInfo.industryCode;
        }
        if (!TextUtils.isEmpty(baseInfo.industry)) {
            profileMetaModel.baseInfo.industry = baseInfo.industry;
        }
        if (!TextUtils.isEmpty(baseInfo.careerCode)) {
            profileMetaModel.baseInfo.careerCode = baseInfo.careerCode;
        }
        if (!TextUtils.isEmpty(baseInfo.career)) {
            profileMetaModel.baseInfo.career = baseInfo.career;
        }
        if (baseInfo.careerCertStatus != 0) {
            profileMetaModel.baseInfo.careerCertStatus = baseInfo.careerCertStatus;
        }
        if (!TextUtils.isEmpty(baseInfo.careerCertInfo)) {
            profileMetaModel.baseInfo.careerCertInfo = baseInfo.careerCertInfo;
        }
    }

    private static class ProfileHolder {
        private static ProfileHelper instance = new ProfileHelper();
    }

    private ProfileHelper() {
    }

    public static ProfileHelper getInstance() {
        return ProfileHolder.instance;
    }

}
