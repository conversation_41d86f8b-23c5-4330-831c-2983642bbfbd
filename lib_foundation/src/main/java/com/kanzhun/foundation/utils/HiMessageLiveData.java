package com.kanzhun.foundation.utils;

import androidx.lifecycle.MediatorLiveData;

import java.util.List;

import com.kanzhun.foundation.model.message.ChatMessage;

public class HiMessageLiveData extends MediatorLiveData<HiMessage> {
    private String chatId;

    public HiMessageLiveData(String chatId) {
        this.chatId = chatId;
    }

    public String getChatId() {
        return chatId;
    }

    public void postValue(List<ChatMessage> chatMessages) {
        HiMessage hi = getValue();
        if (hi == null) {
            hi = new HiMessage(chatMessages, true, false);
        } else {
            hi.setChatMessages(chatMessages);
        }
        postValue(hi);
    }

    public void postValue(List<ChatMessage> chatMessages, boolean isLastest, boolean hasMore) {
        HiMessage hi = getValue();
        if (hi == null) {
            hi = new HiMessage(chatMessages, hasMore, isLastest);
        } else {
            hi.setChatMessages(chatMessages);
            hi.setLatest(isLastest);
            hi.setHasMore(hasMore);
        }
        postValue(hi);
    }

//    public void postValue(List<ChatMessage> chatMessages, boolean hasMore, boolean isLastest) {
//        HiMessage hi = getValue();
//        if (hi == null) {
//            hi = new HiMessage(chatMessages, hasMore, isLastest);
//        } else {
//            hi.setChatMessages(chatMessages);
//            hi.setHasMore(hasMore);
//            hi.setLastest(isLastest);
//        }
//        postValue(hi);
//    }

    @Override
    public void postValue(HiMessage value) {
        super.postValue(value);
    }
}

