package com.kanzhun.foundation.utils

import android.app.Activity
import android.text.TextUtils
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.common.util.toast.OToast
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.R
import com.kanzhun.foundation.ai.AgcFeedbackDialog
import com.kanzhun.foundation.bean.LikeEachOtherListItemBean
import com.kanzhun.foundation.dialog.ComposeDialogFragment
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.model.matching.MatchingLikeModel
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.sp.SpManager
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.views.DoubleLikedDialog
import com.kanzhun.foundation.views.NotificationDialog
import com.kanzhun.http.chuck.internal.support.NotificationHelper
import com.kanzhun.utils.SettingBuilder
import com.kanzhun.utils.configuration.UserSettingConfig
import com.kanzhun.utils.ui.ActivityUtils
import com.petterp.floatingx.imp.FxAppLifecycleProvider
import com.techwolf.lib.tlog.TLog

class SendLikeResultHandler {

    val itemBean: MutableState<LikeEachOtherListItemBean?> = mutableStateOf(null)
    var fromEmojLike = false

    private fun getCurrentActivity(): Activity? {
        return ActivityUtils.getCurrentActivity()
    }

    fun handleResult(result: MatchingLikeModel?) {
        val activity = getCurrentActivity() ?: return
        //防止多个页面重复弹出
        if (result?.hasShowToast == true) {
            return
        }
        result?.hasShowToast = true
        val myInfo = result?.likeEachOther?.myInfo
        val otherInfo = result?.likeEachOther?.otherInfo
        fromEmojLike = result?.fromEmojLike ?: false

        TLog.print("myInfo:", myInfo.toString())
        TLog.print("otherInfo:", otherInfo.toString())
        if (myInfo != null && otherInfo != null) {
            ExecutorFactory.getMainHandler().postDelayed({
                //男左女右
                if (AccountHelper.getInstance().userInfo?.gender == 1) {
                    itemBean.value = LikeEachOtherListItemBean(
                        boyAvatar = myInfo.tinyPhoto,
                        boyNickname = myInfo.nickName,
                        girlAvatar = otherInfo.tinyPhoto,
                        girlNickname = otherInfo.nickName,
                        friendId = otherInfo.userId
                    )
                } else {
                    itemBean.value = LikeEachOtherListItemBean(
                        girlAvatar = myInfo.tinyPhoto,
                        girlNickname = myInfo.nickName,
                        boyAvatar = otherInfo.tinyPhoto,
                        boyNickname = otherInfo.nickName,
                        friendId = otherInfo.userId
                    )
                }

                //双向喜欢
                showDoubleLikeDialog(activity, pageSource = result.pageSource)

            }, 300)
        }else if(result?.showNoUseAIDialog == true && System.currentTimeMillis() - SpManager.get()
                .user().getLong(
                    Constants.NOTIFY_TIPS_SHOW_LIKE_TIME_AI,
                    0
                ) > Constants.S_ONE_WEEK){
            ExecutorFactory.getMainHandler().postDelayed({
                val topActivity = FxAppLifecycleProvider.getTopActivity()
                if (topActivity is FragmentActivity && ActivityUtils.isValid(topActivity)) {
                    SpManager.putUserLong(Constants.NOTIFY_TIPS_SHOW_LIKE_TIME_AI, System.currentTimeMillis())
                    AgcFeedbackDialog.shouldShow(topActivity,getStringScene = "1")
                }
            }, 300)
        }else {
            //弹出"喜欢已送出"
            OToast.show(activity, "喜欢已送出", R.drawable.matching_icon_send_like_toast)
            ExecutorFactory.getMainHandler().postDelayed({
                showOpenNotifyView(activity)
            }, 300)
        }

    }


    fun showOpenNotifyView(activity: Activity) {
        TLog.print(
            Constants.NOTIFY_TIPS_SHOW_LIKE_TIME,
            "currentTimeMillis:" + System.currentTimeMillis()
        )
        TLog.print(
            Constants.NOTIFY_TIPS_SHOW_LIKE_TIME, "sp:" + SpManager.get()
                .user().getLong(
                    Constants.NOTIFY_TIPS_SHOW_LIKE_TIME,
                    0
                )
        )
        TLog.print(Constants.NOTIFY_TIPS_SHOW_LIKE_TIME, "S_ONE_DAY:" + Constants.S_ONE_DAY)

        if (!NotificationHelper.checkNotifySetting(activity) && System.currentTimeMillis() - SpManager.get()
                .user().getLong(
                    Constants.NOTIFY_TIPS_SHOW_LIKE_TIME,
                    0
                ) > Constants.S_ONE_DAY
        ) {
            SpManager.putUserLong(Constants.NOTIFY_TIPS_SHOW_LIKE_TIME, System.currentTimeMillis())
            TLog.print(
                Constants.NOTIFY_TIPS_SHOW_LIKE_TIME,
                "2:" + System.currentTimeMillis().toString()
            )
            showOpenNotifyViewDialog(activity,1)
        } else if (TextUtils.equals(
                SettingBuilder.getInstance()
                    .getUserSettingValue(UserSettingConfig.PERSONALITY_PUSH_115), "0"
            ) && System.currentTimeMillis() - SpManager.get()
                .user().getLong(
                    Constants.NOTIFY_TIPS_SHOW_LIKE_TIME,
                    0
                ) > Constants.S_ONE_DAY
        ) {
            SpManager.putUserLong(Constants.NOTIFY_TIPS_SHOW_LIKE_TIME, System.currentTimeMillis())
            TLog.print(
                Constants.NOTIFY_TIPS_SHOW_LIKE_TIME,
                "2:" + System.currentTimeMillis().toString()
            )
            showOpenNotifyViewDialog(activity,2)
        }
    }
    var notifyDialogFragment:ComposeDialogFragment? = null
    var doubleLikeDialogFragment:ComposeDialogFragment? = null
    private fun showOpenNotifyViewDialog(activity: Activity,style:Int) {
        if(activity is FragmentActivity && ActivityUtils.isValid(activity)){
            val p2 = if (style == 1) "引导开启系统通知" else if (style == 2) "引导开启APP通知" else ""

            notifyDialogFragment = ComposeDialogFragment.Companion.shouldShow(activity){
                NotificationDialog(style,
                    onClosed = {
                        notifyDialogFragment?.dismiss()
                        reportPoint("notification-popup-click"){
                            actionp2 = p2
                            type = "关闭"
                        }
                    }, onSetting = {
                        if (style == 1) {
                            PermissionHelper.requestNotificationPermission(
                                activity,
                                null
                            )
                        } else if (style == 2) {
                            MePageRouter.jumpToMeNotifySettingActivity(activity)
                        }
                        reportPoint("notification-popup-click"){
                            actionp2 = p2
                            type = "去设置"
                        }
                        notifyDialogFragment?.dismiss()
                    }
                )
            }


            reportPoint("notification-popup-expo"){
                actionp2 = p2
            }
        }

    }

    private fun showDoubleLikeDialog(activity: Activity,pageSource: PageSource) {
        if(activity is FragmentActivity){
            doubleLikeDialogFragment = ComposeDialogFragment.Companion.shouldShow(activity){
                DoubleLikedDialog(
                    1,
                    itemBean,
                    onDismiss = {
                        doubleLikeDialogFragment?.dismiss()
                        reportPoint("like-eachother-congratulation-click"){
                            peer_id = itemBean.value?.friendId
                            source = getSource(pageSource)
                            type = "关闭"
                        }
                    },
                    onClick = {
//                        if(!fromEmojLike){
                            ChatPageRouter.jumpToSingleChatActivity(
                                activity, itemBean.value?.friendId,
                                PageSource.NONE
                            )
//                        }

                        doubleLikeDialogFragment?.dismiss()

                        reportPoint("like-eachother-congratulation-click"){
                            peer_id = itemBean.value?.friendId
                            source = getSource(pageSource)
                            type = "去聊天"
                        }
                    })
            }
            reportPoint("like-eachother-congratulation-expo"){
                peer_id = itemBean.value?.friendId
                source = getSource(pageSource)
            }
        }

    }

    private fun getSource(pageSource: PageSource): String? {
        return when(pageSource){
            PageSource.CHAT -> "聊天"
            PageSource.ME_USER_INFO_PREVIEW_ACTIVITY -> "个人信息页"
            PageSource.F1_RECOMMEND_CHILD_FRAGMENT -> "首页"
            PageSource.ACTIVITY_RECOMMEND -> "签到嘉宾列表"
            else -> ""
        }
    }


}