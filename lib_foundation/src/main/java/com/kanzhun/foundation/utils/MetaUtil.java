package com.kanzhun.foundation.utils;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;

/**
 * Meta-data获取相关
 *
 * <AUTHOR> on 2020/02/26.
 */
public class MetaUtil {

    /**
     * 获取Manifest中Application下的meta-data
     */
    public static String getMeta(Context context, String key) {
        ApplicationInfo appInfo;
        try {
            appInfo = context.getPackageManager().getApplicationInfo(context.getPackageName(),
                    PackageManager.GET_META_DATA);
            Bundle bundle = appInfo.metaData;

            if (bundle != null) {
                return bundle.getString(key);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
