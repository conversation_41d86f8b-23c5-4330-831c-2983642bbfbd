package com.kanzhun.foundation.utils.point

import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.utils.ChannelUtils
import java.io.Serializable

/**
 * {
 *   "event": "", //事件名称
 *   "event_ts": 0, //时间发生的时间，ms
 *   "distinct_id": 0,//uid
 *   "event_no": 0,//事件发生的编号
 *   "peer_id": "",//对方id 加密的
 *   "bg": 0, //0 child, 1 parent
 *   "lid": "", //列表id 加密的
 *   "ab": "", //ab参数
 *   "result": "", //结果
 *   "binding_status": 0, //父母孩子账号绑定状态
 *   "friend": "", //好友关系
 *   "count": 0, //总数量
 *   "duration": 0, //停留时长, ms, 整数
 *   "type": "", //类型
 *   "page": 0, //列表分页
 *   "rcd_reason": "", //推荐理由
 *   "show_ids": [ //列表的item Ids，加密的
 *     "0"
 *   ],
 *   "msg": "", //消息
 *   "source": "", //来源
 *   "status": "", //状态
 *   "idx": 0, //列表的第几个
 *   "step": "", //页面步骤
 *   "ip_city": "", //ip_city
 *   "actionp1": "",
 *   "actionp2": "",
 *   "actionp3": "",
 *   "actionp4": "",
 *   "actionp5": "",
 *   "actionp6": "",
 *   "actionp7": "",
 *   "actionp8": "",
 *   "actionp9": "",
 *   "actionp10": "",
 *   "v": "",
 *   "ip": "",
 *   "model": "",
 *   "sys": "",
 *   "deviceid": "",
 *   "platform": "",
 *   "latitude": 0,
 *   "longitude": 0,
 * }
 */
data class PointBean(
    var event: String,
    var event_ts: Long = System.currentTimeMillis(), //时间发生的时间，ms
    var distinct_id: String? = null, //uid
    var peer_id: String? = null, //对方id 加密的
    var bg: Int = if(AccountHelper.getInstance().isParent) 2 else 1, //0 child, 1 parent
    var lid: String? = null, //列表id 加密的
    var ab: String? = null, //ab参数
    var result: String? = null, //结果
    var binding_status: Int? = null, //父母孩子账号绑定状态
    var friend: String? = null, //好友关系
    var count: Int? = null, //总数量
    var duration: Long? = null, //停留时长, ms, 整数
    var type: String? = null, //类型
    var page: Int? = null, //列表分页
    var rcd_reason: String? = null, //推荐理由
    var show_ids: List<String?>? = null,//列表的item Ids，加密的
    var msg: String? = null,//消息
    var source: String? = null,//来源
    var status: String? = null,
    var idx: Int? =  null,//列表的第几个
    var step: String? = null,//页面步骤
    var ip_city: String? = null,//ip_city
    var actionp1: String? = null,//actionp1
    var actionp1NeedDecrypt: Boolean? = null,
    var actionp2: String? = null,//actionp2
    var actionp2NeedDecrypt: Boolean? = null,
    var actionp3: String? = null,
    var actionp3NeedDecrypt: Boolean? = null,
    var actionp4: String? = null,
    var actionp5: String? = null,
    var actionp6: String? = null,
    var actionp7: String? = null,
    var actionp8: String? = null,
    var actionp9: String? = null,
    var actionp10: String? = null,
    var ip: String? = null,//ip
    val channel:String? = ChannelUtils.getChannel(),
    var latitude: Double? = null,//    纬度
    var longitude: Double? = null//    经度
) : Serializable{
}