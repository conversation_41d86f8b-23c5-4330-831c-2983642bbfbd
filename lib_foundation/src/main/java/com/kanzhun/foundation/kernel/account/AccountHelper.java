package com.kanzhun.foundation.kernel.account;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.api.model.UserInfoModel;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.utils.DataInitException;
import com.kanzhun.foundation.utils.ProcessHelper;
import com.kanzhun.foundation.utils.ProcessInfo;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.utils.L;
import com.kanzhun.utils.file.FileUtils;

import java.io.File;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/9
 */
public class AccountHelper {
    private static final String TAG = "AccountHelper";
    private static volatile AccountHelper gAccountHelper = null;
    public static final String KEY_UID = "ac_uid";
    public static final String KEY_U_SK = "ac_u_sk";
    public static final String KEY_U_PHASE = "ac_u_phase";
    public static final String KEY_U_PARENT_PHASE = "ac_u_parent_phase";

    public static final String KEY_U_TICKET = "ac_u_ticket";
    public static final String KEY_U_TICKET_TYPE = "ac_u_ticket_type";
    public static final String KEY_U_APM_UID = "ac_u_apm_uid";
    public static final String KEY_COMMUNITY_LOCKED = "ac_u_community_locked";
    public static final String KEY_PROFILE_LOCKED = "ac_u_profile_locked";
    public static final String KEY_ACCOUNT_TYPE = "ac_u_account_type";
    public static final String KEY_MARRY_INTENT = "ac_u_marryIntent";
    public static final String KEY_MARRY_INTENT_CERT_STATUS = "ac_u_marryIntentCertStatus";
    public static final String KEY_INVITE_CODE_TYPE = "ac_u_inviteCodeType";
    public static final String KEY_CUSTOMER_INVITE_CODE_H5_URL = "ac_u_customerInviteCodeH5Url";
    public static final String KEY_WATERMARK = "ac_u_watermark";

    public static final String KEY_MEETING_ID = "ac_u_meeting_id";
//    public static final String KEY_MEETING_STATUS = "ac_u_meeting_status";
    public static final String KEY_MEETING_USER_STATUS = "ac_u_meeting_user_status";
    public static final String KEY_MEETING_TEST_REPORT_URL = "ac_u_meeting_test_report_url";

    private static AccountCore mAccountCore;
    private static Account mLogoutAccount;

    private CoreStorage mCoreStorage;

    private AccountHelper(ProcessInfo processInfo) {
        mCoreStorage = new CoreStorage();
        mAccountCore = new AccountCore();
        mAccountCore.addAccountLifecycle(mCoreStorage);
        mAccountCore.addAccountLifecycles(processInfo.getLifecycleList());
        mLogoutAccount = new Account("", "");
    }

    public static synchronized void initialized(ProcessInfo processInfo) {
        if (gAccountHelper == null) {
            gAccountHelper = new AccountHelper(processInfo);
            try {
                if (ProcessHelper.isMainProcess()) {
                    gAccountHelper.initialized();
                }
            } catch (Exception e) {
                if (e instanceof DataInitException) {
                    Account account = gAccountHelper.getAccount();
                    if (account != null
                            && !TextUtils.isEmpty(account.getUserId())) {
                        HttpExecutor.cancelAll();
                        ExecutorFactory.cancelAll();
                        FileUtils.delete(new File(""));
//                        CoreStorage.removeUserRoot(account.getUserId(), account.getCompanyId());
                        gAccountHelper.refreshNotify();
                    }
                }
                throw e;
            }
        }
    }

    private void initialized() {
        mAccountCore.initialized();
    }


    /**
     * 仅仅判断登录
     */
    public boolean isOnlyLogin() {
        return !TextUtils.isEmpty(getAccount().getUserId());
    }

    /**
     * 是否注册已邀请
     */
    public boolean isInvite() {
        return getAccount().getPhase() == Account.ACCOUNT_STATUS_REGISTER_INVITE;
    }

    public int getPhase() {
        return getAccount().getPhase();
    }

    /**
     * 首善已完成
     *
     * @return
     */
    public boolean isCompletedUserInfo() {
        int type = getAccount().getAccountType();
        switch (type) {
            case 1:
                return !TextUtils.isEmpty(getAccount().getUserId()) && getAccount().getPhase() >= Account.ACCOUNT_STATUS_REGISTER_INVITE_ACTIVATE;
            case 2:
                return !TextUtils.isEmpty(getAccount().getUserId()) && getAccount().getParentPhase() > 0;
            default:
                return false;
        }
    }

    /**
     * 表示登录整个流程完成 包括选择模式 和儿女模式的激活
     */
    public boolean isLogin() {
        if (!TextUtils.isEmpty(getAccount().getUserId()) && isParent()) {
            return !needParentFirstEdit();
        }
        return !TextUtils.isEmpty(getAccount().getUserId()) && getAccount().getPhase() >= Account.ACCOUNT_STATUS_REGISTER_INVITE_ACTIVATE;
    }


    /**
     * 是否是正式用户
     */
    public boolean isFormalUser() {
        return !TextUtils.isEmpty(getAccount().getUserId()) && getAccount().getPhase() >= Account.ACCOUNT_STATUS_FORMAL;
    }

    /**
     * 可匹配
     */
    public boolean isCanMatch() {
        return !TextUtils.isEmpty(getAccount().getUserId()) && getAccount().getPhase() >= Account.ACCOUNT_STATUS_MATCH;
    }

    public boolean isUserCommunityLocked() {
        return !TextUtils.isEmpty(getAccount().getUserId()) && getAccount().getCommunityLocked() == Account.COMMUNITY_LOCKED;
    }

    public boolean isUserProfileLocked() {
        return !TextUtils.isEmpty(getAccount().getUserId()) && getAccount().getProfileLocked() == Account.PROFILE_LOCKED;
    }

    /**
     * 是否是家长给孩子找对象
     *
     * @return
     */
    public boolean isParent() {
//         0 未知需要选择类型 1 自己找 2 帮人找
        return getAccount().getAccountType() == AccountType.PARENT.getValue();
    }

    public boolean isMeetingPlan() {
        return getAccount().getMeetPlanUserStatus() == 40;
    }

    public boolean chatEnable() {
        return getAccount().getAccountType() == AccountType.CHILD.getValue();
    }

    public boolean hasBindChild() {
        if (isParent()) {
            return true;
        } else {
            return false;
        }
    }

    //用户是否需要去选择模式
    public boolean needSelectModel() {
        return getAccount().getAccountType() == AccountType.NONE.getValue();
    }

    //父母是否需要去首善
    public boolean needParentFirstEdit() {
        return getAccount().getParentPhase() == 0;
    }

    public String getUserId() {
        return getAccount().getUserId();
    }

    public static AccountHelper getInstance() {
        if (gAccountHelper == null) {
            throw new RuntimeException("AccountHelper not initialized!");
        }
        return gAccountHelper;
    }


    public void login(Account account) {
        AccountCore accountCore = mAccountCore;
        if (accountCore != null) {
            accountCore.login(account);
        }
    }

    public void activateLogin(Account account) {
        AccountCore accountCore = mAccountCore;
        if (accountCore != null) {
            accountCore.activateLogin(account);
        }
    }

    public void logout() {
        mAccountCore.logout();
    }

    /**
     * 首善完成
     */
    public void completeUserInfoFirstTime() {
        mAccountCore.notifyLifecycleUserInfoCompletedFirstTime();
    }

    public Account getAccount() {
        if (mAccountCore != null && mAccountCore.getAccount() != null) {
            return mAccountCore.getAccount();
        }
        return mLogoutAccount;
    }

    public void saveAccountConfig(Account account) {
        if (mAccountCore != null && mAccountCore.getAccount() != null) {
            mAccountCore.saveConfig(account);
        }
    }

    /**
     * 注意：ProfileSyncHandler 同步成功以后才有数据，请注意判空
     *
     * @return
     */
    public @Nullable UserInfoModel getUserInfo() {
        if (mAccountCore != null) {
            return mAccountCore.getUserInfo();
        }
        return null;
    }

    public Account getLogoutAccount() {
        return mLogoutAccount;
    }

    public void refreshNotify() {
        mAccountCore.errorStatusRefresh();
    }

    public void refreshActivateConfig(int phash) {
        mAccountCore.refreshActivateConfig(phash);
    }

    public void refreshParentActivateConfig(int parentPhash) {
        mAccountCore.refreshParentActivateConfig(parentPhash);
    }

    public void refreshAccountType(int accountType) {
        L.e(TAG, "refreshAccountType:" + accountType);
        mAccountCore.refreshAccountType(accountType);
    }

    public void refreshUserInfo(UserInfoModel user) {
        mAccountCore.refreshUserInfo(user);
    }

    public void refreshCustomerInviteCodeH5Url(String refreshCustomerInviteCodeH5Url) {
        mAccountCore.refreshCustomerInviteCodeH5Url(refreshCustomerInviteCodeH5Url);
    }

    public void refreshWatermark(@NonNull String watermark) {
        mAccountCore.refreshWatermark(watermark);
    }

    public void refreshInviteCodeType(int inviteCodeType) {
        mAccountCore.refreshInviteCodeType(inviteCodeType);
    }

    public String getAccessToken() {
        L.e(TAG, "getAccessToken:" + mAccountCore.getAccessToken());
        return mAccountCore.getAccessToken();
    }

    public String getTokenType() {
        return mAccountCore.getTokenType();
    }

    public void setTokenType(String token_type) {
        mAccountCore.setTokenType(token_type);
    }

    public void refreshAccessToken(String access_token) {
        L.e(TAG, "refreshAccessToken:" + access_token);
        mAccountCore.refreshAccessToken(access_token);
    }

    public void setAccount(Account account) {
        mAccountCore.setAccount(account);
    }

    public void refreshCommunityLocked(int communityLocked) {
        mAccountCore.refreshCommunityLocked(communityLocked);
    }

    public void refreshProfileLocked(int profileLocked) {
        mAccountCore.refreshProfileLocked(profileLocked);
    }

    public void refreshMeetPlanId(String meetPlanId) {
        mAccountCore.refreshMeetPlanId(meetPlanId);
    }

    public void refreshMeetPlanUserStatus(int meetPlanUserStatus) {
        mAccountCore.refreshMeetPlanUserStatus(meetPlanUserStatus);
    }

    public void refreshMeetPlanTestReportUrl(String meetPlanTestReportUrl) {
        mAccountCore.refreshMeetPlanTestReportUrl(meetPlanTestReportUrl);
    }

    public void refreshMarryIntent(int marryIntent) {
        mAccountCore.refreshMarryIntent(marryIntent);
    }

    public void refreshMarryIntentCertStatus(int marryIntentCertStatus) {
        mAccountCore.refreshMarryIntentCertStatus(marryIntentCertStatus);
    }

    /**
     * 主动刷新用户信息
     */
    public void updateProfile() {
        ServiceManager.getInstance().getProfileService().updateProfile();
    }

    public boolean editMarry() {
        return getAccount().getMarryIntent() != 1;
    }
}
