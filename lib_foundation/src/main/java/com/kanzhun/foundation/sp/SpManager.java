package com.kanzhun.foundation.sp;

import android.content.SharedPreferences;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.tencent.mmkv.MMKV;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.utils.platform.Utils;

import java.util.HashMap;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/7
 * sp 相关使用此类
 */
public final class SpManager {
    // TODO: 2022/2/7 修改成包名
    private final static String GLOBAL_PREFIX = "com.kanzhun.orange";//全局文件前缀
    private final static String DEFAULT_SUFFIX = "default";//默认文件后缀
    private final HashMap<String, SharedPreferences> mSpCache = new HashMap<>();
    private volatile String mUserPrefix;

    private SpManager() {
        //全局文件 路径
        MMKV.initialize(Utils.getApp());
        SpHandler handler = new SpHandler();
        MMKV.registerHandler(handler);
    }

    private static class SingleTonHolder {
        private static final SpManager INSTANCE = new SpManager();
    }

    public static SpManager get() {
        return SingleTonHolder.INSTANCE;
    }

    /**
     * 存储  用户无关、全局唯一的sp
     * 例如：设备ID、当前登录用户的UID、ID
     * 存储文件：twl_default
     *
     * @return SharedPreferences
     */
    public SharedPreferences global() {
        return getSharePreferences(GLOBAL_PREFIX, DEFAULT_SUFFIX, true);
    }

    /**
     * 存储  用户无关、全局唯一的sp(独立文件)
     * <p>
     * 例如：设备ID、当前登录用户的UID、ID
     * 存储文件：twl_spaceName
     *
     * @return SharedPreferences
     */
    public SharedPreferences global(@NonNull String spaceName) {
        checkNull(spaceName);
        return getSharePreferences(GLOBAL_PREFIX, spaceName, true);
    }

    public void setUserPrefix(String userPrefix) {
        this.mUserPrefix = userPrefix;
    }

    /**
     * 存储  当前用户态_私有的sp
     * <p>
     * 例如：搜索历史记录、通知
     * 存储文件：uid_身份id_default
     *
     * @return SharedPreferences
     */
    public SharedPreferences user() {
        checkUserPrefix();
        return getSharePreferences(mUserPrefix, DEFAULT_SUFFIX, true);
    }

    /**
     * 存储  当前用户态_私有的sp_独立文件
     * <p>
     * 例如：搜索历史记录、通知
     * 存储文件：uid_身份id_spaceName
     *
     * @return SharedPreferences
     */
    public SharedPreferences user(@NonNull String spaceName) {
        checkNull(spaceName);
        checkUserPrefix();
        return getSharePreferences(mUserPrefix, spaceName, true);
    }


    /**
     * 获取根据命名id 生成对应的Sp实例，并放到map中缓存
     * <p>
     * 如后续 需要废弃MMKV，直接变更这里实现即可
     *
     * @param uid
     * @param spaceName
     * @param isMultiProcess
     * @return
     */
    private SharedPreferences getSharePreferences(String uid, String spaceName, boolean isMultiProcess) {
        String mmkvId = generateMmkvId(uid, spaceName);
        SharedPreferences mmkvSharePreferences = mSpCache.get(mmkvId);
        if (mmkvSharePreferences == null) {
            int processMode = isMultiProcess ? MMKV.MULTI_PROCESS_MODE : MMKV.SINGLE_PROCESS_MODE;
            mmkvSharePreferences = MMKV.mmkvWithID(mmkvId, processMode);
            mSpCache.put(mmkvId, mmkvSharePreferences);
        }
        return mmkvSharePreferences;
    }

    /**
     * 合成mmkvid
     *
     * @param prefix
     * @param spaceName
     * @return
     */
    private String generateMmkvId(String prefix, String spaceName) {
        return prefix + "_" + spaceName;
    }

    public void flushUserPrefix() {
        mUserPrefix = null;
        checkUserPrefix();
    }

    private synchronized void checkUserPrefix() {
        // TODO: 2022/2/7 需要根据自己业务逻辑修改
        if (!TextUtils.isEmpty(mUserPrefix)) return;
        String accountUid = global().getString(AccountHelper.KEY_UID, "");
        String userPrefix = generateUserPrefix(accountUid);
//        if (BuildInfoUtils.isDebug()) {
//            if (mUserPrefix != null) {
//                if (!TextUtils.equals(mUserPrefix, userPrefix)) {
//                    CrashReport.putUserData(Utils.getApp(), "oldUserPrefix", mUserPrefix);
//                    CrashReport.putUserData(Utils.getApp(), "newUserPrefix", userPrefix);
//                    CrashReport.postCatchedException(new RuntimeException("UserPrefix  异常 不要提bug"));
//                }
//            }
//        }
        mUserPrefix = userPrefix;
    }

    private void checkNull(String spaceName) {
        if (TextUtils.isEmpty(spaceName)) {
            throw new RuntimeException("spaceName can't be empty");
        }
    }

    /**
     * 根据 uid 生成sp文件前缀
     * 后续如有加密，例如md5，可以改这里
     *
     * @param accountUid
     * @return
     */
    private String generateUserPrefix(String accountUid) {
        return String.valueOf(accountUid);
    }

    public static void putGlobalString(String key, String value) {
        SpManager.get().global().edit().putString(key, value).apply();
    }

    public static void putGlobalInt(String key, int value) {
        SpManager.get().global().edit().putInt(key, value).apply();
    }

    public static int getGlobalInt(String key, int value) {
        return SpManager.get().global().getInt(key, value);
    }

    public static void putGlobalLong(String key, long value) {
        SpManager.get().global().edit().putLong(key, value).apply();
    }

    public static void putGlobalFloat(String key, float value) {
        SpManager.get().global().edit().putFloat(key, value).apply();
    }

    public static void putGlobalBoolean(String key, boolean value) {
        SpManager.get().global().edit().putBoolean(key, value).apply();
    }

    public static void putUserString(String key, String value) {
        SpManager.get().user().edit().putString(key, value).apply();
    }

    public static void putUserInt(String key, int value) {
        SpManager.get().user().edit().putInt(key, value).apply();
    }

    public static void putUserLong(String key, long value) {
        SpManager.get().user().edit().putLong(key, value).apply();
    }

    public static void putUserFloat(String key, float value) {
        SpManager.get().user().edit().putFloat(key, value).apply();
    }

    public static void putUserBoolean(String key, boolean value) {
        SpManager.get().user().edit().putBoolean(key, value).apply();
    }

}
