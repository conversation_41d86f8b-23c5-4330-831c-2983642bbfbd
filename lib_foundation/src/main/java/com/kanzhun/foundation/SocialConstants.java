package com.kanzhun.foundation;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/7/15
 */
public class SocialConstants {
    public static final int DYNAMIC_VISIBLE_ALL = 1;//全部可见
    public static final int DYNAMIC_VISIBLE_FRIEND = 2; //好友可见
    public static final int DYNAMIC_VISIBLE_SELF = 3; //自己可见


    //1 同性  2 非好友同意查看 3 好友接触匹配 4 好友 5 非好友已发 6 非好友未发
    public static final int STATUS_SOCIAL_KNOCK_GAY = 1;
    public static final int STATUS_SOCIAL_KNOCK_NO_FRIEND_AGREE = 2;
    public static final int STATUS_SOCIAL_KNOCK_FRIEND_BAN = 3;
    public static final int STATUS_SOCIAL_KNOCK_FRIEND = 4;
    public static final int STATUS_SOCIAL_KNOCK_NO_FRIEND_SEND = 5;
    public static final int STATUS_SOCIAL_KNOCK_NO_FRIEND_NO_SEND = 6;

    /**
     * 来源  对应枚举
     * 1:广场feed内容卡片
     * 2：圈子feed内容卡片
     * 3：个人动态聚合页-主态
     * 4：个人动态聚合页-客态
     * 6:动态详情页
     * 7：详情页评论区
     * 8：评论二级页
     * 5：消息页面
     * 9: 聊天页
     */
    public static final int SOURCE_SOCIAL_FEED = 1;
    public static final int SOURCE_SOCIAL_CIRCLE_FEED = 2;
    public static final int SOURCE_SOCIAL_DYNAMIC_SELF = 3;
    public static final int SOURCE_SOCIAL_DYNAMIC_FRIEND = 4;
    public static final int SOURCE_SOCIAL_DYNAMIC_MESSAGE = 5;
    public static final int SOURCE_SOCIAL_DYNAMIC_DETAIL = 6;
    public static final int SOURCE_SOCIAL_DYNAMIC_DETAIL_COMMENT = 7;
    public static final int SOURCE_SOCIAL_DYNAMIC_DETAIL_COMMENT_REPLY = 8;
    public static final int SOURCE_SOCIAL_CHAT = 9;
}
