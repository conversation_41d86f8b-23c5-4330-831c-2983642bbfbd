package com.kanzhun.foundation.facade;

import com.kanzhun.foundation.utils.HiObservable;

public class SyncDispatch extends HiObservable {
    public static final String EVENT_CONTACT_SYNC_COMPLETE = "contact_complete";//带有数据时候为单次拉取数据通知到会话列表、不带有数据时候为非初次拉取用户数据完毕通知会话列表更新
    public static final String EVENT_CONTACT_SYNC_COMPLETE_DATA = "contact_complete_data";//带有数据的单次拉取用户数据更新
    public static final String EVENT_CONTACT_SYNC_COMPLETE_FROM_ZERO = "contact_sync_complete_from_zero";//通知会话列表第一次拉取全部用户数据完毕，缓存更新完毕，刷新全部数据
    public static final String EVENT_CONTACT_SYNC_DATA_COMPLETE_FROM_ZERO = "contact_sync_data_complete_from_zero";//通知第一次拉取全部用户数据完毕，用户数据缓存更新

    public static final String EVENT_CONVERSATION_SYNC_COMPLETE = "conversation_complete";
    public static final String EVENT_CONVERSATION_SYNC_COMPLETE_DATA = "conversation_complete_data";

    public static final String EVENT_CONTACT_SYNC = "contact_sync";
    public static final String EVENT_FRIEND_APPLY_SYNC = "friend_sync";
    public static final String EVENT_PROFILE_SYNC = "profile_sync";
    public static final String EVENT_CONVERSATION_PROP_SYNC = "conversation_prop_sync";

    public static final String EVENT_NOTICE_LIST_SYNC_COMPLETE_DATA = "notice_list_complete_data";//同步小秘书通知列表

    public static final String EVENT_TLOG_UPLOAD = "event_tlog_upload";

    public static final String EVENT_LINK_ROOM_BAD_NETWORK_SYNC = "event_link_room_bad_network_sync";

    public static final String EVENT_LINK_ROOM_CHANGE_AUDIO_SYNC = "event_link_room_change_audio_sync";

    public static final String EVENT_TAB_CONVERSATION_SUMMARY_INFO_SYNC = "event_tab_message_sync";

    public static final String EVENT_TAB_MATCH_SYNC = "event_tab_match_sync";

    public static final String EVENT_SYS_CONFIG_SYNC = "sys_config_sync";

    public static final String EVENT_SYNC_CARD_STATUS = "event_sync_card_status";

    public static final String EVENT_SOCIAL_UNREAD = "event_social_unread";
    public static final String EVENT_TEMP_TASK = "event_temp_task";

    public static final String EVENT_EMPATHY_MATCH_SUCCESS = "event_empathy_match_success";
    public static final String EVENT_SYNC_INVITE_BIND = "event_sync_invite_bind";
    public static final String EVENT_CHAT_ICON_SYNC = "event_chat_icon_sync";
    public static final String EVENT_NOTICE_LIST_SYNC = "event_notice_list_sync";

    private static SyncDispatch gSyncDispatch = new SyncDispatch();

    public static SyncDispatch getInstance() {
        return gSyncDispatch;
    }

    public void setEvent(String event) {
        setChanged();
        notifyObservers(event);
    }

    public void setEvent(SyncIntent intent) {
        setChanged();
        notifyObservers(intent);
    }

    public void setEvent(String event, Object obj) {
        setChanged();
        notifyObservers(new SyncIntent(event, obj));
    }

    public static class SyncIntent {
        private String event;
        private Object mObj;

        public SyncIntent(String event, Object obj) {
            this.event = event;
            this.mObj = obj;
        }

        public String getEvent() {
            return event;
        }

        public Object getObj() {
            return mObj;
        }
    }
}
