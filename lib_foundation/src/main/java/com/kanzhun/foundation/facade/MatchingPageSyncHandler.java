package com.kanzhun.foundation.facade;

import com.kanzhun.common.util.SingleLiveEvent;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.model.MatchingPageInfo;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.utils.GsonUtils;

import io.reactivex.rxjava3.core.Observable;

public class MatchingPageSyncHandler extends BaseHandler<MatchingPageInfo> {

    private SingleLiveEvent<MatchingPageInfo> matchingPageInfoLiveData = new SingleLiveEvent<>();

    private SingleLiveEvent<MatchingPageInfo> matchingGuideLiveData = new SingleLiveEvent<>();

    public MatchingPageSyncHandler() {

    }

    @Override
    protected void syncData() {
        Observable<BaseResponse<MatchingPageInfo>> observable = RetrofitManager.getInstance().createApi(FoundationApi.class).getMatchingPageInfo();
        HttpExecutor.execute(observable, this);
    }

    @Override
    public void handleInChildThread(MatchingPageInfo data) {
        super.handleInChildThread(data);
        if (data != null) {
            SpManager.putUserString(Constants.MATCH_DATE_LOVE_INFO, GsonUtils.getGson().toJson(data));
            int lastMatchStatus = SpManager.get().user().getInt("match_card", -1);
            if (lastMatchStatus != -1 && lastMatchStatus != data.matchStatus && data.matchStatus > 1) {
                if (((data.matchStatus == 2 && data.isDateSender == 1) || (data.matchStatus == 3 && data.isLoveSender == 1))) {
                    /* 发起方才弹匹配页 */
                    matchingPageInfoLiveData.postValue(data);
                } else {
                    matchingGuideLiveData.postValue(data);
                }
            }
            SpManager.putUserInt("match_card", data.matchStatus);
            ServiceManager.getInstance().getDatabaseService().getUserDao().updateMatchStatus(AccountHelper.getInstance().getUserId(), data.matchStatus);
        }
    }

    public SingleLiveEvent<MatchingPageInfo> getMatchingPageInfoLiveData() {
        return matchingPageInfoLiveData;
    }

    public SingleLiveEvent<MatchingPageInfo> getMatchingGuideLiveData() {
        return matchingGuideLiveData;
    }
}
