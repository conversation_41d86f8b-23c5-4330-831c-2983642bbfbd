package com.kanzhun.foundation.facade;

import java.util.Arrays;
import java.util.List;

/**
 * create by sunyangyang
 * on 2019-11-27
 */
public class TLogRepository extends BaseRepository {
    private TLogHandler tLogHandler;

    public TLogRepository() {
        super(Arrays.asList(SyncDispatch.EVENT_TLOG_UPLOAD));
        tLogHandler = new TLogHandler();
    }

    @Override
    protected void onUpdate(String event, Object obj) {
        super.onUpdate(event, obj);
        switch (event) {
            case SyncDispatch.EVENT_TLOG_UPLOAD:
                String date = (String) obj;
                upLoadTLogFile(date);
                break;
        }
    }

    private void upLoadTLogFile(String date) {
        tLogHandler.setTLogInfo(date);
        tLogHandler.syncData();
    }

    @Override
    public List<BaseHandler> getBaseHandle() {
        return Arrays.asList(tLogHandler);
    }
}
