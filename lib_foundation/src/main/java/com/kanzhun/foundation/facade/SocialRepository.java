package com.kanzhun.foundation.facade;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.model.SocialUnreadResponse;

import java.util.Arrays;
import java.util.List;

public class SocialRepository extends BaseRepository{
    private SocialUnreadSyncHandler socialUnreadSyncHandler;
    public SocialRepository() {
        super(Arrays.asList(SyncDispatch.EVENT_SOCIAL_UNREAD));
        socialUnreadSyncHandler = new SocialUnreadSyncHandler();
        socialUnreadSyncHandler.startUpdate();
    }

    @Override
    protected void onUpdate(String event) {
        super.onUpdate(event);
        switch (event) {
            case SyncDispatch.EVENT_SOCIAL_UNREAD:
                socialUnreadSyncHandler.startUpdate();
                break;
        }
    }

    @Override
    public List<BaseHandler> getBaseHandle() {
        return Arrays.asList(socialUnreadSyncHandler);
    }

    public MutableLiveData<SocialUnreadResponse> getSocialUnreadCount() {
        return socialUnreadSyncHandler.getSocialUnreadCount();
    }

    public void setSocialUnreadCount(SocialUnreadResponse response) {
        socialUnreadSyncHandler.setSocialUnreadCount(response);
    }
}
