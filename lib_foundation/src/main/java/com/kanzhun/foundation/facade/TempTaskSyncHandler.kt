package com.kanzhun.foundation.facade

import androidx.lifecycle.MutableLiveData
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.model.LimitedActivityTaskExpoBean
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager

class TempTaskSyncHandler : BaseHandler<LimitedActivityTaskExpoBean?>() {
    val limitedActivityTaskExpoBean: MutableLiveData<LimitedActivityTaskExpoBean> = MutableLiveData()

    override fun startUpdate() {
        super.startUpdate()
        getData()
    }

    override fun syncData() {
        getData()
    }

    fun getData(){
        val responseObservable = RetrofitManager.getInstance().createApi(
            FoundationApi::class.java
        ).requestLimitedActivityTaskExpoBean()
        HttpExecutor.execute(responseObservable, this)
    }

    override fun handleInChildThread(data: LimitedActivityTaskExpoBean?) {
        super.handleInChildThread(data)
        if (data != null) {
            limitedActivityTaskExpoBean.postValue(data!!)
        }
    }
}
