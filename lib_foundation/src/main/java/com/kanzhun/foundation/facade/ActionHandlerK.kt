package com.kanzhun.foundation.facade

import android.R.attr.contentDescription
import android.annotation.SuppressLint
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.font.FontWeight.Companion.W400
import androidx.compose.ui.text.font.FontWeight.Companion.W600
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.fragment.app.FragmentActivity
import coil.compose.AsyncImage
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.dialog.createOneButtonDialog
import com.kanzhun.common.kotlin.ext.colorResource
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.FoundationApiK
import com.kanzhun.foundation.api.response.CompleteGuideRecommendInfoResponse
import com.kanzhun.foundation.api.response.GetUserTagResponse
import com.kanzhun.foundation.api.response.LevelOne
import com.kanzhun.foundation.api.response.LevelThree
import com.kanzhun.foundation.api.response.LevelThreeTag
import com.kanzhun.foundation.api.response.LevelTwo
import com.kanzhun.foundation.api.response.RecommendTag
import com.kanzhun.foundation.bean.RecommendTagIdealPartnerMatchResponse
import com.kanzhun.foundation.dialog.ComposeDialogFragment
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.SecurityNoticeManager.dialog
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.CertificationIdentifySource
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.petterp.floatingx.imp.FxAppLifecycleProvider

class ActionHandlerK {

    var dialog: ComposeDialogFragment? = null

    fun handler10070x(type: Int) {
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            FoundationApiK::class.java
        ).getCompleteGuideRecommendInfo(type.toString())
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<CompleteGuideRecommendInfoResponse>() {

                override fun onSuccess(data: CompleteGuideRecommendInfoResponse) {
                    when (type) {
                        1 -> {
                            if (data.smallPop != null) {
                                handlerSmallPop(data, R.drawable.icon_new_user_dialog_level_2)
                            } else {
                                handler100701New(data)
                            }
                        }

                        2 -> {
                            if (data.smallPop != null) {
                                handlerSmallPop(data, R.drawable.icon_new_user_dialog_level_1)
                            } else {
                                handler100702New(data)
                            }
                        }

                        3 -> {
                            if (data.smallPop != null) {
                                handlerSmallPop(data, R.drawable.icon_new_user_dialog_level_3)
                            } else {
                                handler100703New(data)
                            }
                        }
                    }
                }

                override fun dealFail(reason: ErrorReason?) {
                }

            })
    }

    private fun handler100703New(data: CompleteGuideRecommendInfoResponse) {
        FxAppLifecycleProvider.getTopActivity()?.let { activity ->
            dialog = ComposeDialogFragment.Companion.shouldShow(activity as FragmentActivity){
                LevelThreeGuideScreen(
                    data = data,
                    onClose = {
                        dialog?.dismiss()
                    },
                    onButtonClick = {
                        dialog?.dismiss()
                        ProtocolHelper.parseProtocol(data.button?.url)
                    }
                )
            }
        }
    }

    private fun handler100702New(data: CompleteGuideRecommendInfoResponse) {
        FxAppLifecycleProvider.getTopActivity()?.let { activity ->
            dialog = ComposeDialogFragment.Companion.shouldShow(activity as FragmentActivity){
                LevelTwoGuideScreen(
                    data = data,
                    showBottomTxt = (AccountHelper.getInstance().userInfo?.eduCertStatus ?: 0) == 0,
                    isMale = AccountHelper.getInstance().userInfo?.gender == 1,
                    onClose = {
                        dialog?.dismiss()
                    },
                    onButtonClick = {
                        dialog?.dismiss()
                        ProtocolHelper.parseProtocol(data.button?.url)
                    },
                    userName = AccountHelper.getInstance().userInfo?.nickName?:""
                )
            }
        }
    }

    private fun handler100701New(data: CompleteGuideRecommendInfoResponse) {
        FxAppLifecycleProvider.getTopActivity()?.let { activity ->
            dialog = ComposeDialogFragment.Companion.shouldShow(activity as FragmentActivity){
                LevelOneGuideScreen(
                    data = data,
                    onClose = {
                        dialog?.dismiss()
                    },
                    onButtonClick = {
                        dialog?.dismiss()
                        ProtocolHelper.parseProtocol(data.button?.url)
                    },
                    userName = AccountHelper.getInstance().userInfo?.nickName?:""
                )
            }
        }
    }


    @Preview
    @Composable
    fun PreviewLevelOneGuideScreen() {
        LevelOneGuideScreen(
            userName = "派大星lalala",
            data = CompleteGuideRecommendInfoResponse(
                button = null,
                levelOne = LevelOne(
                    listOf("","",""),
                    "恭喜你完成了信息完善",
                    80,
                    10,
                    RecommendTag("", "80%用户和你一样加长加长加粗，也是也是也是也是")
                ),
                levelThree = null,
                levelTwo = null,
                smallPop = null
            ),
            onButtonClick = {
            }
        )
    }

    @Preview
    @Composable
    fun PreviewLevelTwoGuideScreen() {
        LevelTwoGuideScreen(
            userName = "派大星lalala",
            isMale = true,

            data = CompleteGuideRecommendInfoResponse(
                button = null,
                levelOne = null,
                levelThree = null,
                levelTwo = LevelTwo("恭喜你\n完成了实名及头像认证","Ta们是","abc",null,listOf("本科","硕士","博士")),
                smallPop = null
            ),
            onButtonClick = {
            }
        )
    }

    @Preview
    @Composable
    fun PreviewLevelThreeGuideScreen() {
        LevelThreeGuideScreen(
            data = CompleteGuideRecommendInfoResponse(
                button = null,
                levelOne = null,
                levelThree = LevelThree(title = "恭喜完成全部新手任务", content = "解锁看准全部新手福利", tagList = listOf(
                    LevelThreeTag("根据你的信息推荐，专属于你","","福利1：每日10名优质推荐嘉宾"),
                    LevelThreeTag("每日喜欢不限量，喜欢就要说出来","","福利2：解锁无限喜欢特权"),
                    LevelThreeTag("免费参加一次看准线下活动","","福利3：免费线下活动券"),
                )),
                levelTwo = null,
                smallPop = null
            ),
            onButtonClick = {
            }
        )
    }

    @Composable
    private fun LevelThreeGuideScreen(
        data: CompleteGuideRecommendInfoResponse,
        onButtonClick: () -> Unit,
        onClose: () -> Unit = {},
    ) {
        val levelThreeData = data.levelThree ?: return

        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Background with gradient effect
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(0xFFFFFFFF),
                                Color(0xFFFFFFFF)
                            )
                        )
                    )
            )

            // Content
            androidx.compose.foundation.layout.Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 20.dp, start = 20.dp, end = 20.dp),
                horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                verticalArrangement = androidx.compose.foundation.layout.Arrangement.SpaceBetween
            ) {
                // Top title
                Spacer(modifier = Modifier.height(10.dp))

                Image(
                    painter = painterResource(id = R.drawable.common_ic_black_cancel),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.End)
                        .size(24.dp)
                        .noRippleClickable { onClose() }
                )

                Spacer(modifier = androidx.compose.ui.Modifier.height(10.dp))

                Box(modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 15.dp)){
                    Image(
                        painter = painterResource(id = R.drawable.icon_new_user_level_3_logo),
                        contentDescription = null,
                        modifier = Modifier
                            .aspectRatio(305f / 243f)
                            .fillMaxWidth()
                    )
                }

                Spacer(modifier = androidx.compose.ui.Modifier.height(12.dp))
                Text(
                    text = levelThreeData.title?:"",
                    style = androidx.compose.ui.text.TextStyle(
                        fontSize = 28.sp,
                        fontFamily = boldFontFamily(),
                        color = R.color.common_color_191919.colorResource()
                    )
                )

                Spacer(modifier = androidx.compose.ui.Modifier.height(2.dp))

                Text(
                    textAlign = TextAlign.Center,
                    text = levelThreeData.content?:"",
                    style = androidx.compose.ui.text.TextStyle(
                        fontSize = 15.sp,
                        fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                        color = R.color.common_color_7F7F7F.colorResource()
                    )
                )

                // Center content
                androidx.compose.foundation.layout.Spacer(modifier = androidx.compose.ui.Modifier.weight(
                    1f
                ))

                // Bottom benefits content
                androidx.compose.foundation.layout.Column(
                    horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,

                ) {
                    HorizontalDivider(color = Color(0xFFF5F5F5),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(1.dp)
                    )

                    Spacer(modifier = Modifier.height(10.dp))

                                // Benefits list
                    levelThreeData.tagList?.forEachIndexed { index, tag ->
                        BenefitItem(tag)
                        
                        if (index < (levelThreeData.tagList.size - 1)) {
                            Spacer(modifier = Modifier.height(16.dp))
                        }
                    }

                    // Button
                    Spacer(modifier = Modifier.height(16.dp))
                    Button(
                        onClick = onButtonClick,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp)
                            .noRippleClickable { onButtonClick() },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF191919),
                        ),
                        shape = RoundedCornerShape(24.dp)
                    ) {
                        Text(
                            text = data.button?.text ?: "去看看",
                            color = R.color.common_white.colorResource(),
                            fontSize = 16.sp
                        )
                    }

                    Spacer(modifier = Modifier.height(40.dp))
                }

            }
        }
    }

    @Composable
    private fun BenefitItem(tag: LevelThreeTag) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color.White.copy(alpha = 0.2f),
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Icon
            if (!tag.icon.isNullOrEmpty()) {
                AsyncImage(
                    model = tag.icon,
                    contentDescription = null,
                    modifier = Modifier.size(42.dp)
                )
            }else{
                Image(
                    painter = painterResource(id = R.drawable.icon_new_user_level_1_logo),
                    contentDescription = null,
                    modifier = Modifier.size(42.dp)
                )
            }

            Spacer(modifier = Modifier.width(16.dp))
            // Left content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = tag.title ?: "",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = R.color.common_color_191919.colorResource()
                    )
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = tag.content ?: "",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal,
                        color = R.color.common_color_7F7F7F.colorResource()
                    )
                )
            }
        }
    }

    @Composable
    private fun LevelTwoGuideScreen(
        data: CompleteGuideRecommendInfoResponse,
        userName:String = "",
        showBottomTxt:Boolean = true,
        isMale:Boolean = true,
        onButtonClick: () -> Unit,
        onClose: () -> Unit = {},
    ) {
        val levelTwoData = data.levelTwo ?: return

        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Background with blur effect
            if ((levelTwoData.avatarList?.size ?: 0) > 0){
                BackgroundWithBlur(levelTwoData.avatarList?.get(0)?:"")
            }else{
                Image(
                    painter = painterResource(id = R.drawable.common_bg_level_two_guide),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxSize()
                        .blur(40.dp),
                    contentScale = androidx.compose.ui.layout.ContentScale.Crop,
                )
            }

            // Content
            androidx.compose.foundation.layout.Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp),
                horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                verticalArrangement = androidx.compose.foundation.layout.Arrangement.SpaceBetween
            ) {
                // Top title
                Spacer(modifier = Modifier.height(24.dp))

                Image(
                    painter = painterResource(id = R.drawable.common_ic_white_close),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.End)
                        .size(24.dp)
                        .noRippleClickable { onClose() }
                )

                Spacer(modifier = androidx.compose.ui.Modifier.height(30.dp))

                Image(
                    painter = painterResource(id = R.drawable.icon_new_user_level_2_logo),
                    contentDescription = null,
                    modifier = Modifier
                        .size(80.dp)
                )

                Text(
                    text = "HI~${userName}",
                    style = androidx.compose.ui.text.TextStyle(
                        fontSize = 16.sp,
                        fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                        color = androidx.compose.ui.graphics.Color.White
                    )
                )

                Spacer(modifier = androidx.compose.ui.Modifier.height(8.dp))

                Text(
                    textAlign = TextAlign.Center,
                    text = levelTwoData.title ?: "",
                    style = androidx.compose.ui.text.TextStyle(
                        fontSize = 28.sp,
                        fontFamily = boldFontFamily(),
                        color = androidx.compose.ui.graphics.Color.White
                    )
                )

                // Center content
                androidx.compose.foundation.layout.Spacer(modifier = androidx.compose.ui.Modifier.weight(
                    1f
                ))

                // Bottom avatars and content
                androidx.compose.foundation.layout.Column(
                    horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                ) {
                    Text(
                        text = "刚刚系统又为你找到了两名非常适合你的嘉宾",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFFFFFF),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier.alpha(0.5f)
                    )

                    Spacer(modifier = androidx.compose.ui.Modifier.height(10.dp))

                    if (levelTwoData.degreeList?.isNotEmpty() == true){
                        Row {
                            Text(
                                text = "Ta们是",
                                style = TextStyle(
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFFFFFFFF),
                                    textAlign = TextAlign.Center,
                                )
                            )

                            Spacer(modifier = Modifier.width(2.dp))
                            levelTwoData.degreeList.forEach {degree->
                                Spacer(modifier = Modifier.width(2.dp))
                                Box(
                                    modifier = Modifier
                                        .background(
                                            Color(0xFFFFFFFF),
                                            RoundedCornerShape(17.dp)
                                        )
                                        .padding(horizontal = 12.dp, vertical = 1.dp),
                                ) {
                                    Text(
                                        text = degree,
                                        style = TextStyle(
                                            fontSize = 20.sp,
                                            fontWeight = FontWeight(500),
                                            color = Color(0xFF4A5A60),
                                            textAlign = TextAlign.Center,
                                        )
                                    )
                                }
                                Spacer(modifier = Modifier.width(2.dp))
                            }
                            Spacer(modifier = Modifier.width(2.dp))


                            Text(
                                text = "学历",
                                style = TextStyle(
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFFFFFFFF),
                                    textAlign = TextAlign.Center,
                                ),
                            )
                        }
                    }



                    Spacer(modifier = androidx.compose.ui.Modifier.height(20.dp))


                    // Avatar row
                    if (levelTwoData.avatarList?.isNotEmpty() == true){
                        AvatarLevelTwoRow(levelTwoData.avatarList)
                    }else{
                        ConstraintLayout{
                            val (avatar1, avatar2) = createRefs()
                            Image(
                                painter = painterResource(id = if(isMale) R.drawable.common_female_level_two_guide else R.drawable.common_male_level_two_guide),
                                contentDescription = null,
                                modifier = Modifier
                                    .constrainAs(avatar1) {
                                        start.linkTo(parent.start)
                                        top.linkTo(parent.top)
                                        bottom.linkTo(parent.bottom)
                                    }
                                    .size(70.dp)
                                    .border(
                                        width = 2.dp,
                                        color = androidx.compose.ui.graphics.Color.White,
                                        shape = androidx.compose.foundation.shape.CircleShape
                                    )
                                    .clip(androidx.compose.foundation.shape.CircleShape),
                                contentScale = androidx.compose.ui.layout.ContentScale.Crop
                            )

                            Image(
                                painter = painterResource(id = if(isMale) R.drawable.common_female_level_two_guide else R.drawable.common_male_level_two_guide),
                                contentDescription = null,
                                modifier = Modifier
                                    .constrainAs(avatar2) {
                                        start.linkTo(avatar1.start, 53.dp)
                                        top.linkTo(parent.top)
                                        bottom.linkTo(parent.bottom)
                                    }
                                    .size(70.dp)
                                    .border(
                                        width = 2.dp,
                                        color = androidx.compose.ui.graphics.Color.White,
                                        shape = androidx.compose.foundation.shape.CircleShape
                                    )
                                    .clip(androidx.compose.foundation.shape.CircleShape),
                                contentScale = androidx.compose.ui.layout.ContentScale.Crop
                            )
                        }
                    }

                    Spacer(modifier = androidx.compose.ui.Modifier.height(20.dp))

                    if(showBottomTxt){
                        Row {
                            Image(
                                painter = painterResource(id = R.drawable.icon_new_user_level_2_logo_warn),
                                contentDescription = null,
                                modifier = Modifier
                                    .size(24.dp)
                            )

                            val text1 = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFFFFFFFF),
                                    )
                                ) {
                                    append("但是你的 ")
                                }

                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(600),
                                        color = Color(0xFFFFFFFF),
                                    )
                                ) {
                                    append(levelTwoData.certTypeDesc  ?: "")
                                }

                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFFFFFFFF),
                                    )
                                ) {
                                    append(" 还没提交，提交完成后才能与Ta们聊天")
                                }
                            }


                            Text(
                                text = text1,
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight(600),
                                    color = Color(0xFFFFFFFF),
                                    textAlign = TextAlign.Center,
                                )
                            )

                        }
                    }


                    // Button
                    Spacer(modifier = Modifier.height(80.dp))
                    Button(
                        onClick = onButtonClick,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF80AFFF),
                        ),
                        shape = RoundedCornerShape(24.dp)
                    ) {
                        Text(
                            text = data.button?.text ?: "继续完善",
                            color = R.color.common_color_191919.colorResource(),
                            fontSize = 16.sp
                        )
                    }
                    Spacer(modifier = Modifier.height(40.dp))
                }
            }
        }
    }

    @Composable
    private fun LevelOneGuideScreen(
        data: CompleteGuideRecommendInfoResponse,
        userName:String = "",
        onButtonClick: () -> Unit,
        onClose: () -> Unit = {},
    ) {
        val levelOneData = data.levelOne ?: return

        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Background with blur effect
            if ((levelOneData.avatarList?.size ?: 0) > 1){
                BackgroundWithBlur(levelOneData.avatarList?.get(1)?:"")
            }else{
                Image(
                    painter = painterResource(id = R.drawable.common_bg_level_two_guide),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxSize()
                        .blur(40.dp),
                    contentScale = androidx.compose.ui.layout.ContentScale.Crop,
                )
            }

            // Content
            androidx.compose.foundation.layout.Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp),
                horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                verticalArrangement = androidx.compose.foundation.layout.Arrangement.SpaceBetween
            ) {
                // Top title
                Spacer(modifier = Modifier.height(24.dp))

                Image(
                    painter = painterResource(id = R.drawable.common_ic_white_close),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.End)
                        .size(24.dp)
                        .noRippleClickable { onClose() }
                )

                Spacer(modifier = androidx.compose.ui.Modifier.height(30.dp))

                Image(
                    painter = painterResource(id = R.drawable.icon_new_user_level_1_logo),
                    contentDescription = null,
                    modifier = Modifier
                        .size(80.dp)
                )

                Text(
                    text = "HI~${userName}",
                    style = androidx.compose.ui.text.TextStyle(
                        fontSize = 16.sp,
                        fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                        color = androidx.compose.ui.graphics.Color.White
                    )
                )

                Spacer(modifier = androidx.compose.ui.Modifier.height(8.dp))

                Text(
                    text = levelOneData.title ?: "",
                    style = androidx.compose.ui.text.TextStyle(
                        fontSize = 28.sp,
                        fontFamily = boldFontFamily(),
                        color = androidx.compose.ui.graphics.Color.White
                    )
                )

                // Center content
                androidx.compose.foundation.layout.Spacer(modifier = androidx.compose.ui.Modifier.weight(
                    1f
                ))

                // Bottom avatars and content
                androidx.compose.foundation.layout.Column(
                    horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
                ) {
                    Text(
                        text = "我们发现平台中有${data.levelOne.userCount}+用户与你的",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFFFFFF),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier.alpha(0.5f)
                    )

                    Spacer(modifier = androidx.compose.ui.Modifier.height(10.dp))

                    Row {
                        Text(
                            text = "匹配分数超过",
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFFFFFFFF),
                                textAlign = TextAlign.Center,
                            )
                        )

                        Spacer(modifier = androidx.compose.ui.Modifier.width(4.dp))

                        Row(
                            modifier = Modifier
                                .background(
                                    Color(0xFFFFFFFF),
                                    RoundedCornerShape(17.dp)
                                )
                                .padding(horizontal = 12.dp, vertical = 1.dp),
                            verticalAlignment = Alignment.Bottom
                        ) {
                            Text(
                                text = data.levelOne.score.toString(),
                                style = TextStyle(
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF4A5A60),
                                    textAlign = TextAlign.Center,
                                )
                            )

                            Text(
                                text = "分",
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF4A5A60),
                                    textAlign = TextAlign.Center,
                                ),
                            )
                        }

                    }


                    Spacer(modifier = androidx.compose.ui.Modifier.height(10.dp))

                    Text(
                        text = "完善信息与Ta们相遇",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFFFFFF),
                            textAlign = TextAlign.Center,
                        )
                    )

                    Spacer(modifier = androidx.compose.ui.Modifier.height(20.dp))

                    // Avatar row
                    AvatarRow(levelOneData.avatarList?:listOf())

                    Spacer(modifier = androidx.compose.ui.Modifier.height(20.dp))


                    // Replacing the fixed Row with a BoxWithConstraints to handle responsive layout

                    val contentText = levelOneData.recommendTag?.content ?: ""
                    val tagText = levelOneData.recommendTag?.tag ?: ""

                    if (tagText.isNotEmpty())
                    BoxWithConstraints(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center,
                        content = {
                            // Create a TextMeasurer to measure text widths
                            val density = androidx.compose.ui.platform.LocalDensity.current
                            val textMeasurer = androidx.compose.ui.text.rememberTextMeasurer()

                            // Measure both text elements
                            val contentTextWidth = textMeasurer.measure(
                                text = contentText,
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    color = Color.White,
                                    textAlign = TextAlign.Center
                                )
                            ).size.width / density.density

                            val tagTextWidth = textMeasurer.measure(
                                text = tagText,
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight(600),
                                    color = Color(0xFF44555A),
                                    textAlign = TextAlign.Center
                                )
                            ).size.width / density.density

                            // Calculate total width including spacing and padding
                            val totalWidth = contentTextWidth + tagTextWidth + 20 // Adding some extra space for padding and spacer

                            // If total width exceeds available width, use Column layout instead of Row
                            if (totalWidth > maxWidth.value) {
                                // Vertical layout
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = contentText,
                                        style = TextStyle(
                                            fontSize = 16.sp,
                                            color = Color.White,
                                            textAlign = TextAlign.Center
                                        ),
                                    )



                                    if(tagText.isNotEmpty()){
                                        Spacer(modifier = androidx.compose.ui.Modifier.height(8.dp))
                                        Row(
                                            modifier = Modifier
                                                .background(
                                                    Color(0xFFFFFFFF),
                                                    RoundedCornerShape(7.dp)
                                                )
                                                .padding(horizontal = 8.dp, vertical = 2.dp),
                                            verticalAlignment = Alignment.Bottom
                                        ) {
                                            Text(
                                                text = tagText,
                                                style = TextStyle(
                                                    fontSize = 16.sp,
                                                    fontWeight = FontWeight(600),
                                                    color = Color(0xFF44555A),
                                                    textAlign = TextAlign.Center,
                                                )
                                            )
                                        }
                                    }

                                }
                            } else {
                                // Horizontal layout (original)
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    Text(
                                        text = contentText,
                                        style = TextStyle(
                                            fontSize = 16.sp,
                                            color = Color.White,
                                            textAlign = TextAlign.Center
                                        ),
                                    )

                                    Spacer(modifier = androidx.compose.ui.Modifier.width(4.dp))

                                    if(tagText.isNotEmpty()){
                                        Row(
                                            modifier = Modifier
                                                .background(
                                                    Color(0xFFFFFFFF),
                                                    RoundedCornerShape(7.dp)
                                                )
                                                .padding(horizontal = 8.dp, vertical = 2.dp),
                                            verticalAlignment = Alignment.Bottom
                                        ) {
                                            Text(
                                                text = tagText,
                                                style = TextStyle(
                                                    fontSize = 16.sp,
                                                    fontWeight = FontWeight(600),
                                                    color = Color(0xFF44555A),
                                                    textAlign = TextAlign.Center,
                                                )
                                            )
                                        }
                                    }

                                }
                            }
                        }
                    )else{
                        Spacer(modifier = androidx.compose.ui.Modifier.height(20.dp))

                        Row {
                                Image(
                                    painter = painterResource(id = R.drawable.icon_new_user_level_2_logo_warn),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(24.dp)
                                )

                                val text1 = buildAnnotatedString {
                                    withStyle(
                                        style = SpanStyle(
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFFFFFFFF),
                                        )
                                    ) {
                                        append("但你还没有完成")
                                    }

                                    withStyle(
                                        style = SpanStyle(
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight(600),
                                            color = Color(0xFFFFFFFF),
                                        )
                                    ) {
                                        append(" 信息认证 ")
                                    }

                                    withStyle(
                                        style = SpanStyle(
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFFFFFFFF),
                                        )
                                    ) {
                                        append("，需要完成认证后才能与Ta们相遇")
                                    }
                                }


                                Text(
                                    text = text1,
                                    style = TextStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(600),
                                        color = Color(0xFFFFFFFF),
                                        textAlign = TextAlign.Center,
                                    )
                                )

                            }
                    }





                    // Button
                    Spacer(modifier = Modifier.height(80.dp))
                    Button(
                        onClick = onButtonClick,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF9AEFF),
                        ),
                        shape = RoundedCornerShape(24.dp)
                    ) {
                        Text(
                            text = data.button?.text ?: "继续完善",
                            color = R.color.common_color_191919.colorResource(),
                            fontSize = 16.sp
                        )
                    }
                    Spacer(modifier = Modifier.height(40.dp))
                }
            }
        }
    }

    @androidx.compose.runtime.Composable
    private fun BackgroundWithBlur(centerAvatarUrl: String?) {
        // Center blurred avatar
        centerAvatarUrl?.let { url ->
            androidx.compose.foundation.layout.Box(
                modifier = androidx.compose.ui.Modifier.fillMaxSize(),
                contentAlignment = androidx.compose.ui.Alignment.Center
            ) {
                coil.compose.AsyncImage(
                    model = coil.request.ImageRequest.Builder(androidx.compose.ui.platform.LocalContext.current)
                        .data(url)
                        .crossfade(true)
                        .build(),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxSize()
                        .blur(40.dp),
                    contentScale = androidx.compose.ui.layout.ContentScale.Crop,
                )
            }
        }

        androidx.compose.foundation.layout.Box(
            modifier = Modifier
                .fillMaxSize()
                .background(androidx.compose.ui.graphics.Color(0x800E0E0E))
        )
    }

    @androidx.compose.runtime.Composable
    private fun AvatarLevelTwoRow(avatars: List<String>?) {
        if (avatars.isNullOrEmpty()) return
        if (avatars.size < 2)return

        ConstraintLayout{
            val (avatar1, avatar2) = createRefs()
            AsyncImage(
                model = avatars[0],
                contentDescription = null,
                modifier = Modifier
                    .constrainAs(avatar1) {
                        start.linkTo(parent.start)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
                    .size(70.dp)
                    .border(
                        width = 2.dp,
                        color = androidx.compose.ui.graphics.Color.White,
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
                    .clip(androidx.compose.foundation.shape.CircleShape),
                contentScale = androidx.compose.ui.layout.ContentScale.Crop
            )

            AsyncImage(
                model = avatars[1],
                contentDescription = null,
                modifier = Modifier
                    .constrainAs(avatar2) {
                        start.linkTo(avatar1.start, 53.dp)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
                    .size(70.dp)
                    .border(
                        width = 2.dp,
                        color = androidx.compose.ui.graphics.Color.White,
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
                    .clip(androidx.compose.foundation.shape.CircleShape),
                contentScale = androidx.compose.ui.layout.ContentScale.Crop
            )
        }
    }

    @androidx.compose.runtime.Composable
    private fun AvatarRow(avatars: List<String>?) {
        if (avatars.isNullOrEmpty()) return
        if (avatars.size < 3)return

        ConstraintLayout{
            val (avatar1, avatar2, avatar3) = createRefs()
            AsyncImage(
                model = avatars[0],
                contentDescription = null,
                modifier = Modifier
                    .constrainAs(avatar1) {
                        end.linkTo(avatar2.start, -16.dp)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
                    .size(60.dp)
                    .border(
                        width = 2.dp,
                        color = androidx.compose.ui.graphics.Color.White,
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
                    .clip(androidx.compose.foundation.shape.CircleShape),
                contentScale = androidx.compose.ui.layout.ContentScale.Crop
            )

            AsyncImage(
                model = avatars[1],
                contentDescription = null,
                modifier = Modifier
                    .constrainAs(avatar2) {
                        start.linkTo(parent.start)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        end.linkTo(parent.end)
                    }
                    .size(70.dp)
                    .border(
                        width = 2.dp,
                        color = androidx.compose.ui.graphics.Color.White,
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
                    .clip(androidx.compose.foundation.shape.CircleShape),
                contentScale = androidx.compose.ui.layout.ContentScale.Crop
            )

            AsyncImage(
                model = avatars[2],
                contentDescription = null,
                modifier = Modifier
                    .constrainAs(avatar3) {
                        start.linkTo(avatar2.end, -16.dp)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
                    .size(60.dp)
                    .border(
                        width = 2.dp,
                        color = androidx.compose.ui.graphics.Color.White,
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
                    .clip(androidx.compose.foundation.shape.CircleShape),
                contentScale = androidx.compose.ui.layout.ContentScale.Crop
            )
        }


    }


    private fun handlerSmallPop(data: CompleteGuideRecommendInfoResponse, @DrawableRes resId: Int) {
        FxAppLifecycleProvider.getTopActivity()?.let {
            it.createOneButtonDialog(
                title = data.smallPop?.title,
                content = data.smallPop?.content,
                cancelable = false,
                canceledOnTouchOutside = false,
                buttonText = data.button?.text ?: "",
                resId = resId,
            ) {
                ProtocolHelper.parseProtocol(data.button?.url)
            }.apply { show() }
        }
    }
}