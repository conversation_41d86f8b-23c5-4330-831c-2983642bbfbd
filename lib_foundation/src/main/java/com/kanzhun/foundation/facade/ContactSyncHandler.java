package com.kanzhun.foundation.facade;

import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.model.ContactResponse;
import com.kanzhun.foundation.db.dao.ContactDao;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import io.reactivex.rxjava3.core.Observable;

public class ContactSyncHandler extends BaseHandler<ContactResponse> {
    private static final String TAG = "ContactSyncHandler";

    private ContactDao mContactDao;
    private long mVersion;
    private long mSubVersion;
    private int mHasMore;
//    private long mDefaultVersion;
//    private MutableLiveData<Boolean> mContactSyncFromZeroStatus = new MutableLiveData<>();

    public ContactSyncHandler() {
        mContactDao = ServiceManager.getInstance().getDatabaseService().getContactDao();
        mVersion = SpManager.get().user().getLong(Constants.CONTACT_UPDATE_TIMES, 0);
//        mDefaultVersion = mVersion;
//        if (mDefaultVersion == 0L) {
//            mContactSyncFromZeroStatus.postValue(true);
//        }
    }

    @Override
    protected void syncData() {
        TLog.info(TAG, "syncData: " +   " chatEnable:" + AccountHelper.getInstance().chatEnable());
        if(!AccountHelper.getInstance().chatEnable()){
            return;
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("version", mVersion);
        map.put("subVersion", mSubVersion);
        Observable<BaseResponse<ContactResponse>> baseResponseObservable = RetrofitManager.getInstance().createApi(FoundationApi.class).getFriends(map);
        HttpExecutor.execute(baseResponseObservable, this);
    }

    @Override
    public void handleInChildThread(ContactResponse data) {
        super.handleInChildThread(data);
        mVersion = data.version;
        mSubVersion = data.subVersion;
        mHasMore = data.hasMore;
        SpManager.get().user().edit().putLong(Constants.CONTACT_UPDATE_TIMES, data.version).commit();
        mContactDao.insert(data.friends);
        List<String> ids = new ArrayList<>(data.friends.size());
        for (Contact contact : data.friends) {
            ids.add(contact.getUserId());
        }
//        if (mDefaultVersion != 0) {
            SyncDispatch.SyncIntent syncIntent = new SyncDispatch.SyncIntent(SyncDispatch.EVENT_CONTACT_SYNC_COMPLETE_DATA, ids);
            SyncDispatch.getInstance().setEvent(syncIntent);
//        }
        if (mHasMore == 0) {
//            if (mDefaultVersion != 0) {
                SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_CONTACT_SYNC_COMPLETE);
//            } else {
//                mDefaultVersion = mVersion;
//                SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_CONTACT_SYNC_DATA_COMPLETE_FROM_ZERO);
//                mContactSyncFromZeroStatus.postValue(false);
//            }
        }
    }

    @Override
    public void onComplete() {
        super.onComplete();
        if (mHasMore == 1) {
            startUpdate();
        }
    }

    @Override
    public void handleErrorInChildThread(ErrorReason reason) {
        super.handleErrorInChildThread(reason);
        mHasMore = 0;
    }

//    public MutableLiveData<Boolean> getContactSyncFromZeroStatus() {
//        return mContactSyncFromZeroStatus;
//    }
}
