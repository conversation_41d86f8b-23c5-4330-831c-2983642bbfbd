package com.kanzhun.foundation.facade;


import static com.kanzhun.foundation.model.message.MessageConstants.SEND_READ_MAX_SIZE;

import android.text.TextUtils;

import com.kanzhun.common.kotlin.ext.ResourceExtKt;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.R;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.bean.MessageHistoryResponse;
import com.kanzhun.foundation.api.bean.MessageResponse;
import com.kanzhun.foundation.api.callback.MessageHistoryRequestCallback;
import com.kanzhun.foundation.api.callback.MessagePbApiRequestCallback;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.MessageService;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.MessageSection;
import com.kanzhun.foundation.model.Patch;
import com.kanzhun.foundation.model.ReadStatus;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageForAction;
import com.kanzhun.foundation.model.message.MessageForEmpty;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.mms.ServerResponse;
import com.kanzhun.mms.client.ISendCallback;
import com.kanzhun.mms.client.MMSServiceSDK;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.utils.base.LList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

final class MessageHandler {
    private static final String TAG = "MessageHandler";
    private MessageCallback mMessageCallback;
    MessageCache mMessageCache;

    public MessageHandler(MessageCallback messageCallback, MessageCache messageCache) {
        mMessageCallback = messageCallback;
        mMessageCache = messageCache;
    }

    public void onConnectionConnected() {
        mDoing.clear();
        mPatches.clear();
        sendReads();
    }

    private void sendReads() {
        ExecutorFactory.execLocalTask(this::run);
    }

    private void innerSendRead(List<ReadStatus> readStatuses) {
        MMSServiceSDK.get().send(Message2PBConvert.createRead(readStatuses), new ISendCallback() {
            @Override
            public void onSuccess(ServerResponse serverResponse) {
                mMessageCache.deleteReads(readStatuses);
            }

            @Override
            public void onFailure() {
            }
        }, false);
    }

    public void syncHistory(String chatId, int chatType, Patch patch, boolean isEarlier) {
        BLog.d(TAG, "syncHistory() called with: chatId = %s], chatType = [%d], patch = [%s]", chatId, chatType, patch);
        if (patch.beginid > patch.endid) {
            return;
        }
        HashMap<String, Object> messageKey = new HashMap<>();
        messageKey.put("chatId", chatId);
        messageKey.put("beginSeq", patch.beginid);
        messageKey.put("endSeq", patch.endid);
        HttpExecutor.executeJsonGet(URLConfig.URL_GET_MSG_HISTORY, messageKey, new MessageHistoryRequestCallback() {
            @Override
            public void handleInChildThread(MessageHistoryResponse data) {
                super.handleInChildThread(data);
                MessageCallback messageCallback = mMessageCallback;
                if (data.messages != null && data.messages.size() > 0 && messageCallback != null) {
                    long startSeq = data.messages.get(0).getSeq();
                    long endSeq = data.messages.get(data.messages.size() - 1).getSeq();
                    mMessageCache.setStartSeq(chatId, data.minSeq + 1);
                    boolean isEmpty = true;
                    int size = data.messages.size();
                    for (int i = 0; i < size; i++) {
                        if (data.messages.get(i).isShow()) {
                            isEmpty = false;
                            break;
                        }
                    }

                    MessageSection section = mMessageCache.getMessageSection(chatId, chatType);
                    long nextStartSeq;//下一次的更新seq
                    if (isEmpty) {
                        if (isEarlier) {
                            nextStartSeq = startSeq - 1;
                            if (nextStartSeq >= mMessageCache.getStartSeq(chatId)) {
                                long beginid = nextStartSeq - MessageConstants.MSG_SYNC_PAGE_SIZE;
                                if (beginid < mMessageCache.getStartSeq(chatId)) {
                                    beginid = mMessageCache.getStartSeq(chatId);
                                }
                                syncHistory(chatId, chatType, new Patch(beginid, nextStartSeq), true);
                            }
                        } else {
                            long maxShowSeq = mMessageCache.getMaxShowSeq(chatId, chatType);
                            nextStartSeq = endSeq + 1;
                            if (nextStartSeq <= maxShowSeq) {
                                long endId = nextStartSeq + MessageConstants.MSG_SYNC_PAGE_SIZE;
                                if (endId > maxShowSeq) {
                                    endId = maxShowSeq;
                                }
                                syncHistory(chatId, chatType, new Patch(nextStartSeq, endId), false);
                            }
                        }
                    } else {
                        if (section != null) {
                            boolean sectionUpdate = false;
                            if (startSeq < section.startSeq) {
                                section.startSeq = startSeq;
                                sectionUpdate = true;
                            }
                            if (endSeq > section.endSeq) {
                                section.endSeq = endSeq;
                                sectionUpdate = true;
                            }
                            if (sectionUpdate) {
                                mMessageCache.updateSection(section);
                            }
                        }
                    }
                    messageCallback.onMessageSync(data.messages);
                }
            }
        });
    }

    public List<ChatMessage> ArrangementMessages(ActionHandler actionHandler, List<ChatMessage> messages, boolean isRealTime) {
        List<MessageForAction> list = new ArrayList<>();
        List<Long> replyList = new ArrayList<>();
        List<MessageForAction> videoActionMessages = new ArrayList<>();
        Iterator<ChatMessage> iterator = messages.iterator();
        while (iterator.hasNext()) {
            ChatMessage chatMessage = iterator.next();
            if (chatMessage instanceof MessageForAction) { //引用消息处理之前需要先处理刷新action，防止错误引用
                if (TextUtils.equals(chatMessage.getContent(), MessageForAction.TYPE_REFRESH_STATUS_MULTIPLE)) {
                    mMessageCache.onReceive(chatMessage);
                    list.add((MessageForAction) chatMessage);
                    iterator.remove();
                } else if (TextUtils.equals(chatMessage.getContent(), MessageForAction.TYPE_VIDEO_CHAT_INVITATION)) {
                    mMessageCache.onReceive(chatMessage);
                    videoActionMessages.add((MessageForAction) chatMessage);
                    iterator.remove();
                }
            } else {
                if (chatMessage.getReplyId() > 0) {
                    replyList.add(chatMessage.getReplyId());
                }
            }
        }
        refreshMessages(list, actionHandler, isRealTime);
        syncQuoteMessages(replyList);
        videoChatHandel(actionHandler, videoActionMessages, isRealTime);
        return messages;
    }

    public boolean syncQuoteMessages(List<Long> messageIds) {
        if (messageIds != null && messageIds.size() > 0) {
            List<ChatMessage> list = mMessageCache.queryReplayAndQuote(messageIds);
            Iterator<Long> iterator = messageIds.iterator();
            while (iterator.hasNext()) { //清除掉本地已有的数据，其余从网络获取
                Long msgId = iterator.next();
                for (int i = 0; i < list.size(); i++) {
                    if (msgId == list.get(i).getMid()) {
                        iterator.remove();
                        break;
                    }
                }
            }

            if (messageIds.size() > 0) {
                HashMap<String, Object> messageKey = new HashMap<>();
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < messageIds.size(); i++) {
                    stringBuilder.append(messageIds.get(i));
                    stringBuilder.append(",");
                }
                stringBuilder.delete(stringBuilder.length() - 1, stringBuilder.length());
                messageKey.put("msgIds", stringBuilder.toString());
                HttpExecutor.executeJsonGet(URLConfig.URL_GET_MSG_BY_ID, messageKey, new MessagePbApiRequestCallback() {
                    @Override
                    public void handleInChildThread(MessageResponse data) {
                        super.handleInChildThread(data);
                        MessageCallback messageCallback = mMessageCallback;
                        if (data.messages != null && data.messages.size() > 0) {
                            messageCallback.onMessageQuoteSync(data.messages);
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                    }
                });
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private void videoChatHandel(ActionHandler actionHandler, List<MessageForAction> messages, boolean isRealTime) {
        if (messages.size() > 0) {
            String roomId = null;
            long messageTime = 0;
            long time = System.currentTimeMillis();
            for (MessageForAction messageForAction : messages) {
                //如果对方拨打的时间在1分钟内，则处理，如果有反馈对方收不到邀请通话，请留意这里，关注他们两个手机的系统时间差
                if (time - messageForAction.getTime() <= 2 * MessageConstants.LINK_CALL_TIME_OUT && messageForAction.getTime() > messageTime) {
                    roomId = ((MessageForAction.VideoChatActionInfo) messageForAction.getBody()).roomId;
                }
            }
            if (!TextUtils.isEmpty(roomId)) {
                for (MessageForAction messageForAction : messages) {
                    if (TextUtils.equals(((MessageForAction.VideoChatActionInfo) messageForAction.getBody()).roomId, roomId)) {
                        actionHandler.handleAction(messageForAction, isRealTime);
                        break;
                    }
                }
            }
        }

    }

    public void dealActionMessages(List<MessageForAction> chatMessages, ActionHandler actionHandler) {
        List<Long> refreshIds = new ArrayList<>();
        List<Long> hideIds = new ArrayList<>();
        for (int i = 0; i < chatMessages.size(); i++) {
            MessageForAction messageForAction = chatMessages.get(i);
//            if (TextUtils.equals(messageForAction.getContent(), MessageForAction.TYPE_REFRESH_STATUS)) {
//                MessageForAction.ActionInfo refresh = (MessageForAction.ActionInfo) messageForAction.getBody();
//                refreshIds.add(refresh.mid);
//            } else
            if (TextUtils.equals(messageForAction.getContent(), MessageForAction.TYPE_REFRESH_STATUS_MULTIPLE)) {
                MessageForAction.MultipleActionInfo multipleActionInfo = (MessageForAction.MultipleActionInfo) messageForAction.getBody();
                refreshIds.addAll(multipleActionInfo.mids);
            }
//            else if (TextUtils.equals(messageForAction.getContent(), MessageForAction.TYPE_HIDE)) {
//                MessageForAction.HideInfo hide = (MessageForAction.HideInfo) messageForAction.getBody();
//                hideIds.add(hide.mid);
//            } else if (TextUtils.equals(messageForAction.getContent(), MessageForAction.TYPE_HIDE_MULTIPLE)) {
//                MessageForAction.MultipleHideInfo hideInfos = (MessageForAction.MultipleHideInfo) messageForAction.getBody();
//                hideIds.addAll(hideInfos.clearBadgedMids);
//            }
        }


//        case MessageForAction.TYPE_HIDE:
//        MessageForAction.HideInfo hide = (MessageForAction.HideInfo) messageForAction.getBody();
//        if (hide != null && hide.mid > 0) {
//            mMessageCache.hideMessages(Arrays.asList(hide.mid));
//            if (messageCallback != null && hide.clearBadged == 1) {
//                messageCallback.onHide(messageForAction, Arrays.asList(hide.mid), isRealTimeMessage);
//            }
//        }
//        interceptHover = true;
//        break;
//        case MessageForAction.TYPE_HIDE_MULTIPLE:
//        MessageForAction.MultipleHideInfo hideInfos = (MessageForAction.MultipleHideInfo) messageForAction.getBody();
//        if (hideInfos != null && hideInfos.mids != null && hideInfos.mids.size() > 0) {
//            mMessageCache.hideMessages(hideInfos.mids);
//            if (messageCallback != null && !LList.isEmpty(hideInfos.clearBadgedMids)) {
//                messageCallback.onHide(messageForAction, hideInfos.clearBadgedMids, isRealTimeMessage);
//            }
//        }
//        interceptHover = true;
//        break;
        if (!LList.isEmpty(refreshIds)) {
            actionHandler.refreshMessagesFormHistory(refreshIds);
        }
        if (!LList.isEmpty(hideIds)) {
            actionHandler.hideMessageFromHistory(hideIds);
        }
    }

    public void refreshMessages(List<MessageForAction> chatMessages, ActionHandler actionHandler, boolean isRealTime) {
        for (int i = 0; i < chatMessages.size(); i++) {
            actionHandler.handleAction(chatMessages.get(i), isRealTime);
        }
    }

    public void refreshMessages(List<Long> messageIds, List<Long> clearBadgeIds) {
        if (messageIds != null && messageIds.size() > 0 && AccountHelper.getInstance().chatEnable()) {
            HashMap<String, Object> messageKey = new HashMap<>();
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < messageIds.size(); i++) {
                stringBuilder.append(messageIds.get(i));
                stringBuilder.append(",");
            }
            stringBuilder.delete(stringBuilder.length() - 1, stringBuilder.length());
            messageKey.put("msgIds", stringBuilder.toString());
            HttpExecutor.executeJsonGet(URLConfig.URL_GET_MSG_BY_ID, messageKey, new MessagePbApiRequestCallback() {
                @Override
                public void handleInChildThread(MessageResponse data) {
                    super.handleInChildThread(data);
                    MessageCallback messageCallback = mMessageCallback;
                    if (data.messages != null && messageCallback != null) {
                        messageCallback.onRefresh(data.messages, clearBadgeIds);
                    }
                }
            });
        }
    }

    private List<Patch> mDoing = new ArrayList<>(0);
    private ConcurrentHashMap<Patch, Long> mPatches = new ConcurrentHashMap();
    private ConcurrentHashMap<Patch, Long> mStartPatches = new ConcurrentHashMap();

    public synchronized void syncPatch(String chatId, int chatType, Patch patch, long messageRecordSeq, long minSeq) {
        if (mDoing.contains(patch)) {
            return;
        }
        mDoing.add(patch);
        long endSeq = patch.endid;
        long startSeq = patch.beginid;
        long size = patch.endid - patch.beginid;
//        final Long firstSeq = mStartPatches.get(patch);
        if (size > MessageConstants.MSG_SYNC_PAGE_SIZE) {
            Long lastSeq = mPatches.get(patch);
            if (lastSeq != null) {//如果有上一个分片，接上一个分片
                endSeq = lastSeq;
            }
            startSeq = endSeq - MessageConstants.MSG_SYNC_PAGE_SIZE;
//            if (firstSeq != null) {
//                if (startSeq > firstSeq) {
//                    startSeq = firstSeq;
//                }
//            } else {
            if (startSeq < patch.beginid) {
                startSeq = patch.beginid;
            }
//            }
        }
        if (startSeq > endSeq) {
            BLog.e(TAG, "syncPatch startSeq = %s, endSeq = %s", startSeq, endSeq);
            return;
        }
        final long tagBeginSeq = startSeq;
        BLog.d(TAG, "syncPatch() called with: chatId = %s], chatType = [%d], patch = [%s]", chatId, chatType, patch);
        HashMap<String, Object> messageKey = new HashMap<>();
        messageKey.put("chatId", chatId);
        messageKey.put("beginSeq", startSeq);
        messageKey.put("endSeq", endSeq);
        HttpExecutor.executeJsonGet(URLConfig.URL_GET_MSG_HISTORY, messageKey, new MessageHistoryRequestCallback() {
            @Override
            public void handleInChildThread(MessageHistoryResponse data) {
                super.handleInChildThread(data);
                MessageCallback messageCallback = mMessageCallback;
                if (data.messages != null && data.messages.size() > 0) {
                    long historyMinSeq = minSeq;
                    if (historyMinSeq != data.minSeq) {
                        historyMinSeq = data.minSeq;
                        mMessageCache.setStartSeq(chatId, historyMinSeq + 1);
                    }
                    mPatches.put(patch, tagBeginSeq == historyMinSeq ? historyMinSeq : tagBeginSeq - 1);
                    long beginId = patch.beginid;
//                    if (firstSeq != null) {
//                        beginId = firstSeq;
//                    }
                    if (tagBeginSeq > beginId) {
                        mDoing.remove(patch);
                        if (tagBeginSeq > messageRecordSeq) {
                            syncPatch(chatId, chatType, patch, messageRecordSeq, historyMinSeq);
                        }
                    }

                    if (data.messages != null && messageCallback != null) {
                        messageCallback.onMessageSync(data.messages);
                    }
                }
            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                super.handleErrorInChildThread(reason);
                mDoing.remove(patch);
            }
        });
    }

    public void hideMessages(String chatId, int chatType, List<Long> mids) {
        mMessageCallback.onHide(chatId, chatType, mids);
    }

    /**
     * 判定漏消息中有没有首条消息
     *
     * @param patch patchs中第一个
     * @return
     */
    public boolean isFirstMessageLost(Patch patch, long minSeq) {
        Long seq = mPatches.get(patch);
        if (seq == null) {
            return true;
        } else {
            if (seq <= minSeq) {
                return false;
            }
        }
        return true;
    }

    public void insertMessages(List<ChatMessage> messages) {
        if (!LList.isEmpty(messages)) {
            String chatId = messages.get(0).getChatId();
            int type = messages.get(0).getType();
            mMessageCache.insertMessages(messages);
            mMessageCallback.onNotifyUI(chatId, type);
        }
    }

    public void dealActionFailedMessage(ChatMessage chatMessage,String toast) {
        MessageForEmpty messageForEmpty = new MessageForEmpty();
        messageForEmpty.setChatId(chatMessage.getChatId());
        messageForEmpty.setShow(false);
        messageForEmpty.setSeq(chatMessage.getSeq());
        messageForEmpty.setMid(chatMessage.getMid());
        messageForEmpty.setTime(chatMessage.getTime());
        mMessageCache.insertMessage(messageForEmpty);

        chatMessage.setStatus(MessageConstants.MSG_STATE_FAILURE);
        mMessageCache.addMessageResetMid(chatMessage);
        mMessageCallback.onNotifyUI(chatMessage.getChatId(), chatMessage.getType());
        mMessageCallback.setErrorTips(TextUtils.isEmpty(toast)? ResourceExtKt.toResourceString(R.string.foundation_message_not_send) :toast);
    }

    private void run() {
        List<ReadStatus> readStatuses = mMessageCache.queryReads();
        if (readStatuses.size() >= SEND_READ_MAX_SIZE) {
            innerSendRead(readStatuses);
        } else {
            for (int i = 0; i < readStatuses.size(); i += SEND_READ_MAX_SIZE) {
                int len = readStatuses.size() - i;
                List<ReadStatus> reads = readStatuses.subList(i, len > SEND_READ_MAX_SIZE ? SEND_READ_MAX_SIZE : len);
                innerSendRead(reads);
            }
        }
        BLog.d(TAG, "sendReads");
        refreshMessages(MessageHandlerKT.Companion.handlerMessage(), null);
    }
}
