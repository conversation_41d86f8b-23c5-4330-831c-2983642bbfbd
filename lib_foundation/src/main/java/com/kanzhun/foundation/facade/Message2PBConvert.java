package com.kanzhun.foundation.facade;


import static com.kanzhun.foundation.model.message.MessageConstants.MSG_AUDIO;
import static com.kanzhun.foundation.model.message.MessageConstants.MSG_PIC;
import static com.kanzhun.foundation.model.message.MessageConstants.MSG_TEXT;
import static com.kanzhun.foundation.model.message.MessageConstants.MSG_VIDEO;

import android.util.Log;

import com.kanzhun.common.model.ImageInfo;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.ReadStatus;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.EmotionInfo;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageForAudio;
import com.kanzhun.foundation.model.message.MessageForEmotion;
import com.kanzhun.foundation.model.message.MessageForPic;
import com.kanzhun.foundation.model.message.MessageForText;
import com.kanzhun.foundation.model.message.MessageForVideo;
import com.kanzhun.foundation.utils.ChannelUtils;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.utils.base.LList;
import com.xxjz.orange.protocol.codec.ChatProtocol;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Message2PBConvert {
    private static final String TAG = "Message2PBConvert";

    public static byte[] messageToByte(ChatMessage messageRecord) {
        ChatProtocol.OrangeChatProtocol.Builder builder = ChatProtocol.OrangeChatProtocol.newBuilder();
        ChatProtocol.OgMessage.Builder messageBuilder = ChatProtocol.OgMessage.newBuilder();
        messageBuilder.setCmid(messageRecord.getCmid());
        ChatProtocol.OgMediaBody.Builder bodyBuilder = ChatProtocol.OgMediaBody.newBuilder();
        bodyBuilder.setType(messageRecord.getMediaType());
        switch (messageRecord.getMediaType()) {
            case MSG_TEXT:
                bodyBuilder.setText(createText((MessageForText) messageRecord));
                break;
            case MSG_PIC:
                bodyBuilder.setImage(createImage((MessageForPic) messageRecord));
                break;
            case MSG_AUDIO:
                bodyBuilder.setAudio(createAudio((MessageForAudio) messageRecord));
                break;
            case MSG_VIDEO:
                bodyBuilder.setVideo(createVideo((MessageForVideo) messageRecord));
                break;
//            case MSG_FILE:
//                bodyBuilder.setFile(createFile((MessageForFile) messageRecord));
//                break;
//            case MSG_CHAT_SHARE:
//                bodyBuilder.setChatShare(createChatShare((MessageForChatShare) messageRecord));
//                break;
            case MessageConstants.MSG_STICKER:
                bodyBuilder.setSticker(createSticker((MessageForEmotion) messageRecord));
                break;
//            case MSG_USER_CARD:
//                bodyBuilder.setUserCard(createUserCard((MessageForUserCard) messageRecord));
//                break;
//            case MSG_RED_ENVELOPE:
//                bodyBuilder.setRedPacket(createRedPacket((MessageForRedEnvelope) messageRecord));
//                break;
//            case MSG_LINK:
//                bodyBuilder.setLinkShare(createLinkShare((MessageForLink) messageRecord));
//                break;
//            case MSG_RICH:
//                bodyBuilder.setRichText(createRichText((MessageForRichText) messageRecord));
//                break;
//            case MSG_IMAGE_CARD:
//                bodyBuilder.setImageCard(createImageCard((MessageForImageCard) messageRecord));
//                break;
//            case MSG_FILE_ONLINE:
//                bodyBuilder.setOnlineFileCard(createOnlineFile((MessageForOnlineFile) messageRecord));
//                break;
        }
        messageBuilder.setBody(bodyBuilder.build());
        messageBuilder.setSender(AccountHelper.getInstance().getAccount().getUserId());
        messageBuilder.setChatId(messageRecord.getChatId());
        messageBuilder.setTime(messageRecord.getTime());
        messageBuilder.setReplyId(messageRecord.getReplyId());
        builder.addMessages(messageBuilder.build());
        builder.setScene(1);
        builder.setType(MessageConstants.MSG_TYPE_MESSAGE);
        ChatProtocol.OrangeChatProtocol hiChatProtocol = builder.build();
        BLog.d(TAG, "messageToByte: " + hiChatProtocol);
        return hiChatProtocol.toByteArray();
    }

    private static ChatProtocol.OgTextMedia createText(MessageForText messageForText) {
        ChatProtocol.OgTextMedia.Builder hiTextMedia = ChatProtocol.OgTextMedia.newBuilder();
        hiTextMedia.setText(messageForText.getContent());
        return hiTextMedia.build();
    }

    private static ChatProtocol.OgImageMedia createImage(MessageForPic messageForPic) {
        ChatProtocol.OgImageMedia.Builder hiImageMedia = ChatProtocol.OgImageMedia.newBuilder();
        hiImageMedia.setOriginImage(imageInfo2Hi(messageForPic.getOriginReal()));
        hiImageMedia.setTinyImage(imageInfo2Hi(messageForPic.getTinyReal()));
        return hiImageMedia.build();
    }


    private static ChatProtocol.OgAudioMedia createAudio(MessageForAudio messageForAudio) {
        ChatProtocol.OgAudioMedia.Builder builder = ChatProtocol.OgAudioMedia.newBuilder();
        MessageForAudio.AudioInfo audioInfo = messageForAudio.getAudioInfo();
        builder.setDuration((int) audioInfo.getDuration());
        builder.setUrl(audioInfo.getUrl());
        int[] wave = audioInfo.getWaveArray();
        List<Integer> list = new ArrayList<>(wave.length);
        LList.IntArrayToInteger(list, wave);
        builder.addAllWave(list);
        builder.setSize((int) audioInfo.getSize());
        return builder.build();
    }

    private static ChatProtocol.OgVideoMedia createVideo(MessageForVideo messageForVideo) {
        ChatProtocol.OgVideoMedia.Builder builder = ChatProtocol.OgVideoMedia.newBuilder();
        MessageForVideo.VideoInfo videoInfo = messageForVideo.getVideoInfo();
        builder.setDuration(videoInfo.getDuration());
        builder.setUrl(videoInfo.getUrl());
        builder.setSize(videoInfo.getSize());
        builder.setCoverImage(imageInfo2Hi(videoInfo.getThumbnail()));
        return builder.build();
    }
//
//    private static ChatProtocol.HiFileMedia createFile(MessageForFile messageForFile) {
//        ChatProtocol.HiFileMedia.Builder build = ChatProtocol.HiFileMedia.newBuilder();
//        build.setName(messageForFile.getName());
//        build.setSize(messageForFile.getSize());
//        build.setUrl(messageForFile.getUrl());
//        return build.build();
//    }
//
//    private static ChatProtocol.HiChatShareMedia createChatShare(MessageForChatShare messageForChatShare) {
//        ChatProtocol.HiChatShareMedia.Builder builder = ChatProtocol.HiChatShareMedia.newBuilder();
//        MessageForChatShare.ChatShareInfo chatShareInfo = messageForChatShare.getChatShareInfo();
//        if (chatShareInfo.getRootMid() > 0) {
//            builder.setRootMid(chatShareInfo.getRootMid());
//        }
//        if (!TextUtils.isEmpty(chatShareInfo.getShareId())) {
//            builder.setShareId(chatShareInfo.getShareId());
//        }
//        builder.setTitle(chatShareInfo.getTitle());
//        builder.addAllMids(chatShareInfo.getMsgIds());
//        builder.addAllSummaries(chatShareInfo.getSummaries());
//        return builder.build();
//    }
//
    private static ChatProtocol.OgStickerMedia createSticker(MessageForEmotion messageForSticker) {
        ChatProtocol.OgStickerMedia.Builder builder = ChatProtocol.OgStickerMedia.newBuilder();
        EmotionInfo stickerInfo = messageForSticker.getMEmotionInfo();
        if(stickerInfo != null){
            builder.setSid(stickerInfo.getSid());
            builder.setPackId(stickerInfo.getPackId());
            builder.setName(stickerInfo.getName());
        }
        return builder.build();
    }

//    private static ChatProtocol.HiUserCardMedia createUserCard(MessageForUserCard messageForUserCard) {
//        ChatProtocol.HiUserCardMedia.Builder builder = ChatProtocol.HiUserCardMedia.newBuilder();
//        MessageForUserCard.UserCard userCard = messageForUserCard.getUserCard();
//        builder.setUid(userCard.getUid());
//        return builder.build();
//    }

    private static ChatProtocol.OgImageInfo imageInfo2Hi(ImageInfo imageInfo) {
        ChatProtocol.OgImageInfo.Builder original = ChatProtocol.OgImageInfo.newBuilder();
        original.setHeight(imageInfo.getHeight());
        original.setWidth(imageInfo.getWidth());
        original.setUrl(imageInfo.getUrl());
        return original.build();
    }

//    private static ChatProtocol.HiRedPacketMedia createRedPacket(MessageForRedEnvelope redEnvelope) {
//        ChatProtocol.HiRedPacketMedia.Builder hiRedPacketMedia = ChatProtocol.HiRedPacketMedia.newBuilder();
//        hiRedPacketMedia.setTicketId(redEnvelope.getRedEnvelope().getTicketId());
//        if (redEnvelope.getThemeId() > 0) {
//            try {
//                JSONObject object = new JSONObject();
//                object.put(MessageConstants.RED_ENVELOPE_THEME_ID, redEnvelope.getThemeId());
//                hiRedPacketMedia.setExtension(object.toString());
//            } catch (JSONException e) {
//                e.printStackTrace();
//            }
//        }
//        return hiRedPacketMedia.build();
//    }

//    private static ChatProtocol.HiLinkShareMedia createLinkShare(MessageForLink messageRecord) {
//        ChatProtocol.HiLinkShareMedia.Builder hiLinkShareMedia = ChatProtocol.HiLinkShareMedia.newBuilder();
//        hiLinkShareMedia.setUrl(messageRecord.getUrl());
//        return hiLinkShareMedia.build();
//    }

//    private static ChatProtocol.HiRichTextMedia createRichText(MessageForRichText messageRecord) {
//        ChatProtocol.HiRichTextMedia.Builder hiRichText = ChatProtocol.HiRichTextMedia.newBuilder();
//        hiRichText.setText(messageRecord.getContent());
//        hiRichText.setTitle(messageRecord.richInfo.title);
//        List<Long> ats = messageRecord.richInfo.atIds;
//        if (ats != null) {
//            for (long at : ats) {
//                hiRichText.addAts(at);
//            }
//        }
//        return hiRichText.build();
//    }

//    private static ChatProtocol.HiImageCardMedia createImageCard(MessageForImageCard messageForImageCard) {
//        ChatProtocol.HiImageCardMedia.Builder hiImageCard = ChatProtocol.HiImageCardMedia.newBuilder();
//        hiImageCard.setOriginImage(imageInfo2Hi(messageForImageCard.getImageCardInfo().original));
//        hiImageCard.setText(messageForImageCard.getImageCardInfo().text);
//        hiImageCard.setProtocol(messageForImageCard.getImageCardInfo().protocol);
//        return hiImageCard.build();
//    }
//
//    private static ChatProtocol.HiOnlineFileCardMedia createOnlineFile(MessageForOnlineFile messageForOnlineFile) {
//        ChatProtocol.HiOnlineFileCardMedia.Builder onlineMedia = ChatProtocol.HiOnlineFileCardMedia.newBuilder();
//        onlineMedia.setContent(messageForOnlineFile.getFileInfo().getContent());
//        onlineMedia.setTitle(messageForOnlineFile.getFileInfo().getTitle());
//        onlineMedia.setFileType(messageForOnlineFile.getFileInfo().getFileType());
//        onlineMedia.setFileUrl(messageForOnlineFile.getFileInfo().getFileUrl());
//        onlineMedia.setIcon(messageForOnlineFile.getFileInfo().getIcon());
//        return onlineMedia.build();
//    }

    public static byte[] createPresence(long cid, int type) {
        Log.d("createPresence", "createPresence() called with: cid = [" + cid + "], type = [" + type + "]");
        ChatProtocol.OrangeChatProtocol.Builder builder = ChatProtocol.OrangeChatProtocol.newBuilder();
        builder.setType(MessageConstants.MSG_TYPE_PRESENCE);
        ChatProtocol.OgPresence.Builder presence = ChatProtocol.OgPresence.newBuilder();
        presence.addHwm(cid);
        presence.setClientInfo(createClientInfo());
        presence.setType(type);
        builder.setPresence(presence.build());
        return builder.build().toByteArray();
    }

    private static ChatProtocol.OgClientInfo createClientInfo() {
        ChatProtocol.OgClientInfo.Builder builder = ChatProtocol.OgClientInfo.newBuilder();
        builder.setAppid(MessageConstants.MSG_PRESENCE_APP_ID);
        builder.setChannel(ChannelUtils.getChannel());
        return builder.build();
    }

    public static byte[] createRead(ReadStatus readStatu) {
        return createRead(Arrays.asList(readStatu));
    }

    public static byte[] createRead(List<ReadStatus> readStatus) {
        ChatProtocol.OrangeChatProtocol.Builder builder = ChatProtocol.OrangeChatProtocol.newBuilder();
        builder.setType(MessageConstants.MSG_TYPE_READ_MARK);
        for (ReadStatus status : readStatus) {
            BLog.d(TAG, "chatid = [%s], mid = [%d]", status.getChatId(), status.getMessageId());
            ChatProtocol.OgMessageRead.Builder readBuilder = ChatProtocol.OgMessageRead.newBuilder();
            readBuilder.setChatId(status.getChatId());
            readBuilder.setMid(status.getMessageId());
            builder.addReadMarks(readBuilder.build());
        }
        builder.setScene(1);
        return builder.build().toByteArray();
    }

    /**
     * 进入聊天消息
     * @param chatId
     * @return
     */
    public static byte[] createInChat(String chatId) {
        ChatProtocol.OrangeChatProtocol.Builder builder = ChatProtocol.OrangeChatProtocol.newBuilder();
        builder.setType(MessageConstants.MSG_TYPE_IN_CHAT);
        ChatProtocol.OgInChat.Builder readBuilder = ChatProtocol.OgInChat.newBuilder();
        readBuilder.setChatId(chatId);
        readBuilder.setType(1);//1:进入聊天 2:离开聊天
        builder.setInChat(readBuilder.build());
        return builder.build().toByteArray();
    }

    /**
     * 进入聊天消息
     * @param chatId
     * @return
     */
    public static byte[] createLeaveChat(String chatId) {
        ChatProtocol.OrangeChatProtocol.Builder builder = ChatProtocol.OrangeChatProtocol.newBuilder();
        builder.setType(MessageConstants.MSG_TYPE_IN_CHAT);
        ChatProtocol.OgInChat.Builder readBuilder = ChatProtocol.OgInChat.newBuilder();
        readBuilder.setChatId(chatId);
        readBuilder.setType(2);//1:进入聊天 2:离开聊天
        builder.setInChat(readBuilder.build());
        return builder.build().toByteArray();
    }



}