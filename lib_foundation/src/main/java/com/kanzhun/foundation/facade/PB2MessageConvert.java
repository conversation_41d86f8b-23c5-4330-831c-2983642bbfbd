package com.kanzhun.foundation.facade;

import com.kanzhun.foundation.model.message.ChatMessage;
import com.xxjz.orange.protocol.codec.ChatProtocol;

public class PB2MessageConvert {

    public static ChatMessage parseMessage(ChatProtocol.OgMessage hiMessage) {
        ChatProtocol.OgMediaBody mediaBody = hiMessage.getBody();
        //pb 转成 json 参考
//        try {
//            StringBuilder sb = new StringBuilder();
//            new JsonFormat().print(mediaBody,sb);
//            TLog.info("PB2MessageConvert", "parseMessage: %s", sb.toString());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        ChatMessage chatMessage = MessageFactory.createMessage(mediaBody.getType());
        chatMessage.parseFromPB(hiMessage);
        return chatMessage;
    }
}