package com.kanzhun.foundation.views

import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.kanzhun.foundation.R

fun RecyclerView.addHorizontalDivider() {
    val drawable = ResourcesCompat.getDrawable(resources, R.drawable.bg_divider, null)
    val lm = layoutManager
    if (drawable != null && lm is LinearLayoutManager) {
        val decoration = DividerItemDecoration(context, lm.orientation)
        decoration.setDrawable(drawable)
        addItemDecoration(decoration)
    }
}