package com.kanzhun.foundation.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.airbnb.lottie.LottieAnimationView
import com.kanzhun.common.kotlin.ext.invisible
import com.kanzhun.foundation.R
import com.kanzhun.utils.L
import com.scwang.smart.refresh.classics.ClassicsAbstract
import com.scwang.smart.refresh.header.ClassicsHeader
import com.scwang.smart.refresh.layout.api.RefreshHeader
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import kotlin.math.ceil

/**
 * Created by ChaiJiangpeng
 * Date: 2022/6/12
 */
class OClassicsHeader @JvmOverloads constructor(context: Context?, attrs: AttributeSet? = null) :
    ClassicsAbstract<ClassicsHeader?>(context, attrs, 0), RefreshHeader {
    init {
        inflate(context, R.layout.common_srl_classics_header, this)
        val thisView: View = this
        mArrowView = thisView.findViewById(R.id.srl_classics_arrow)
        val arrowView: View = mArrowView
        mArrowView.invisible()
        mProgressView = thisView.findViewById(R.id.srl_classics_progress)
        val progressView: View = mProgressView

        //        mProgressDrawable = new ProgressDrawable();
//        mProgressDrawable.setColor(0xff666666);
//        mProgressView.setImageDrawable(mProgressDrawable);
        mArrowView.setImageDrawable(resources.getDrawable(R.drawable.common_ic_icon_refresh_arrow))

        //        progressView.animate().setInterpolator(null);
        if (thisView.isInEditMode) {
            arrowView.visibility = GONE
        } else {
            progressView.visibility = GONE
        }
    }

    override fun onFinish(layout: RefreshLayout, success: Boolean): Int {
        val progressView: View = mProgressView
        if (mProgressView is LottieAnimationView) {
            val lottieAnimationView = mProgressView as LottieAnimationView
            L.e("OClassicsHeader", "Duration：" + lottieAnimationView.duration)
            L.e("OClassicsHeader", "Progress：" + lottieAnimationView.progress)
            var d = lottieAnimationView.duration.toInt()
            if (lottieAnimationView.progress != 1f) {
                d =
                    ceil(((1 - lottieAnimationView.progress) * lottieAnimationView.duration).toDouble())
                        .toInt()
            }
            lottieAnimationView.loop(false)
            mFinishDuration = d
            L.e("OClassicsHeader", "mFinishDuration：$mFinishDuration")

        }
        return mFinishDuration //延迟500毫秒之后再弹回
    }


    override fun onStateChanged(
        refreshLayout: RefreshLayout,
        oldState: RefreshState,
        newState: RefreshState
    ) {
        val arrowView: View = mArrowView
        L.e("OClassicsHeader", "newState:$newState")
        when (newState) {
            RefreshState.None, RefreshState.PullDownToRefresh -> {
                mArrowView.invisible()
                arrowView.animate().rotation(0f)
                if (mProgressView is LottieAnimationView) {
                    val lottieAnimationView = mProgressView as LottieAnimationView
                    lottieAnimationView.pauseAnimation()
                    lottieAnimationView.setVisibility(GONE)
                    L.e("OClassicsHeader", "onAnimationEnd")
                }
            }

            RefreshState.Refreshing, RefreshState.RefreshReleased -> arrowView.visibility = GONE
            RefreshState.ReleaseToRefresh -> arrowView.animate().rotation(180f)
            RefreshState.ReleaseToTwoLevel -> arrowView.animate().rotation(0f)
            RefreshState.Loading -> arrowView.visibility = GONE
            else -> {}
        }
    }

    override fun onStartAnimator(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
        val progressView: View = mProgressView
//        if (progressView.visibility != VISIBLE) {
        if (mProgressView is LottieAnimationView) {
            val lottieAnimationView = mProgressView as LottieAnimationView
            lottieAnimationView.loop(true)
            if (lottieAnimationView.isAnimating) {
                lottieAnimationView.pauseAnimation()
            }
            lottieAnimationView.playAnimation()

        }
        progressView.visibility = VISIBLE
//        }
    }

    override fun onMoving(
        isDragging: Boolean,
        percent: Float,
        offset: Int,
        height: Int,
        maxDragHeight: Int
    ) {
        super.onMoving(isDragging, percent, offset, height, maxDragHeight)
        if (isDragging) {
            if (mProgressView is LottieAnimationView) {
                val lottieAnimationView = mProgressView as LottieAnimationView
                var f = if (percent > 2) 1f else (percent / 2.0f)
                lottieAnimationView.progress = f
            }
            mProgressView.visibility = VISIBLE
        }
    }
}
