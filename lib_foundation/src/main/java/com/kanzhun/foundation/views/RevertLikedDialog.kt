package com.kanzhun.foundation.views

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.LinearGradient
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.fontResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.constraintlayout.compose.ConstraintLayout
import coil.compose.AsyncImage
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ui.O2Button
import com.kanzhun.common.base.compose.ui.O2Toolbar
import com.kanzhun.common.kotlin.ext.colorResource
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.foundation.R
import com.kanzhun.foundation.bean.LikeEachOtherListBean
import com.kanzhun.foundation.bean.LikeEachOtherListItemBean
import com.kanzhun.foundation.kernel.account.AccountHelper

@Preview(showBackground = true, widthDp = 375, heightDp = 786)
@Composable
fun PreviewRevertLikeDialog() {
    RevertLikeDialog(2)
}

@Preview(showBackground = true, widthDp = 375, heightDp = 786)
@Composable
fun PreviewRevertLikeItemDialog() {
    ReviewLikeItem()
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun RevertLikeDialog(
    style:Int,
    str: String? = null,
    likeEachOtherListItemBeanList: MutableState<List<LikeEachOtherListItemBean?>?> = mutableStateOf(null),
    onClick: (Int) -> Unit = {},
    onDismiss: (Int) -> Unit = {},
    onSelect:(Int)->Unit = {}
) {

    val pagerState = rememberPagerState(pageCount = {
        likeEachOtherListItemBeanList.value?.size ?:1
    })
    LaunchedEffect(pagerState) {
        // Collect from the a snapshotFlow reading the currentPage
        snapshotFlow { pagerState.currentPage }.collect { page ->
            onSelect(page)
        }
    }
    if (style == 2){
        Dialog(properties = DialogProperties(usePlatformDefaultWidth = false), onDismissRequest = { }) {

            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                if(pagerState.pageCount > 1){
                    ConstraintLayout() {
                        val (
                            imageRef,
                            textRef,
                        ) = createRefs()
                        Image(
                            painter = R.mipmap.image_revert_like_dialog_white_star.painterResource(),
                            contentDescription = "",
                            contentScale = ContentScale.FillBounds,
                            modifier = Modifier.constrainAs(imageRef){
                                end.linkTo(textRef.end,-13.dp)
                                bottom.linkTo(textRef.top,-6.dp)
                            }
                                .size(21.dp)
                        )
                        Text(
                            text = "有${likeEachOtherListItemBeanList.value?.size}个人回复了你的喜欢",
                            modifier = Modifier.constrainAs(textRef){
                                start.linkTo(parent.start)
                                bottom.linkTo(parent.bottom)
                            },
                            style = TextStyle(
                                color = colorResource(R.color.common_white),
                                fontSize = 20.sp,
                                fontWeight = FontWeight(weight = 500),
                                textAlign = TextAlign.Center,
                            ),
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                HorizontalPager(
                    state = pagerState,
                ) { page ->
                    ReviewLikeItem(str,likeEachOtherListItemBeanList.value?.get(page), onClick = {onClick(page)},onDismiss = {onDismiss(page)})
//                    if (!pagerState.isScrollInProgress) {
//
//                    }
                }

                Spacer(modifier = Modifier.height(30.dp))


                if(pagerState.pageCount > 1){
                    Row(
                        modifier = Modifier
                            .wrapContentHeight()
                            .clip(RoundedCornerShape(30.dp))
                            .background(colorResource(R.color.common_color_000000_25))
                            .padding(top = 10.dp, bottom = 10.dp, start = 15.dp, end = 15.dp),
                        horizontalArrangement = Arrangement.Center
                    ) {
                        repeat(pagerState.pageCount) { iteration ->
                            val color =
                                if (pagerState.currentPage == iteration) R.color.common_color_FEFEFE.colorResource() else R.color.common_color_FEFEFE_20.colorResource()
                            Box(
                                modifier = Modifier
                                    .padding(5.dp)
                                    .clip(CircleShape)
                                    .background(color)
                                    .size(10.dp)
                            )
                        }
                    }
                }

            }

        }
    }


}

@Composable
fun ReviewLikeItem(str:String?=null,bean:LikeEachOtherListItemBean?=null,onClick: () -> Unit = {},onDismiss: () -> Unit = {}) {
    Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Card(
            modifier = Modifier
                .width(311.dp),
            shape = RoundedCornerShape(32.dp),
        ) {

            ConstraintLayout(
                modifier = Modifier
                    .background(colorResource(R.color.common_white))
                    .fillMaxWidth()
            ) {
                var (image, bottom, image1, image2, centerImage, box1, box2, title, subTitle, bottomSpacer, idClose) = createRefs()

                Image(
                    painter = R.mipmap.image_bg_revert_liked_pink.painterResource(),
                    contentDescription = "",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(215.dp)
                        .constrainAs(image) {
                            top.linkTo(parent.top)
                            start.linkTo(parent.start)
                        }
                )

                Image(
                    painter = R.mipmap.common_ic_close.painterResource(),
                    contentDescription = "",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .noRippleClickable { onDismiss ()}
                        .size(24.dp)
                        .constrainAs(idClose) {
                            top.linkTo(parent.top, 20.dp)
                            end.linkTo(parent.end, 20.dp)
                        }
                )

                AsyncImage(
                    model = bean?.boyAvatar,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .constrainAs(image1) {
                            top.linkTo(parent.top, 45.dp)
                            start.linkTo(parent.start, 30.dp)
                        }
                        .border(
                            6.dp,
                            color = R.color.common_color_6BEFFF.colorResource(),
                            shape = CircleShape
                        )
                        .clip(CircleShape)
                        .width(110.dp)
                        .height(110.dp)
                )

                AsyncImage(
                    model = bean?.girlAvatar,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .constrainAs(image2) {
                            top.linkTo(parent.top, 45.dp)
                            end.linkTo(parent.end, 30.dp)
                        }
                        .border(
                            6.dp,
                            color = R.color.common_color_FF8BC4.colorResource(),
                            shape = CircleShape
                        )
                        .clip(CircleShape)
                        .width(110.dp)
                        .height(110.dp)
                )

                Image(
                    painter = R.mipmap.image_bg_revert_liked_heart_center.painterResource(),
                    contentDescription = "",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .constrainAs(centerImage) {
                            top.linkTo(parent.top, 70.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .width(65.dp)
                        .height(62.dp)
                )

                Text(
                    text = "@"+bean?.boyNickname?:"",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = TextStyle(
                        color = colorResource(R.color.common_color_191919),
                        fontSize = 14.sp,
                        fontWeight = FontWeight(weight = 500),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .background(
                            color = R.color.common_color_FFFFFF.colorResource(),
                            shape = RoundedCornerShape(14.dp)
                        )
                        .widthIn(max = 110.dp)
                        .padding(horizontal = 10.dp, vertical = 4.dp)
                        .constrainAs(box1) {
                            top.linkTo(image1.bottom, -14.dp)
                            start.linkTo(image1.start)
                            end.linkTo(image1.end)
                        }
                )

                Text(
                    text = "@"+bean?.girlNickname?:"",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = TextStyle(
                        color = colorResource(R.color.common_color_191919),
                        fontSize = 14.sp,
                        fontWeight = FontWeight(weight = 500),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .widthIn(max = 110.dp)
                        .background(
                            color = R.color.common_color_FFFFFF.colorResource(),
                            shape = RoundedCornerShape(14.dp)
                        )
                        .padding(horizontal = 10.dp, vertical = 4.dp)
                        .constrainAs(box2) {
                            top.linkTo(image2.bottom, -14.dp)
                            start.linkTo(image2.start)
                            end.linkTo(image2.end)
                        }
                )

                Text(
                    text = "${str}回复了你的喜欢",
                    style = TextStyle(
                        color = colorResource(R.color.common_color_292929),
                        fontSize = 20.sp,
                        fontWeight = FontWeight(weight = 600)
                    ),
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .constrainAs(title) {
                            top.linkTo(box2.bottom, 43.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                )

                Text(
                    text = "你们可以自由聊天啦",
                    style = TextStyle(
                        color = colorResource(R.color.common_color_5E5E5E),
                        fontSize = 16.sp,
                        fontWeight = FontWeight(weight = 400),
                        textAlign = TextAlign.Center
                    ),
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .constrainAs(subTitle) {
                            top.linkTo(title.bottom, 12.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                )

                O2Button(modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .constrainAs(bottom) {
                        top.linkTo(subTitle.bottom, 29.dp)
                    }, text = "去聊天",
                    onClick = onClick)

                Spacer(modifier = Modifier
                    .height(28.dp)
                    .constrainAs(bottomSpacer) {
                        top.linkTo(bottom.bottom)
                    })


            }
        }

    }
}