package com.kanzhun.foundation.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.ViewConfiguration;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.kanzhun.utils.L;

public class MinDistanceRecyclerView extends RecyclerView {

    private float x1, x2;
    private float y1, y2;

    public MinDistanceRecyclerView(Context context) {
        super(context);
    }

    public MinDistanceRecyclerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                x1 = ev.getRawX();
                y1 = ev.getRawY();
//                getParent().requestDisallowInterceptTouchEvent(true);
                break;
            case MotionEvent.ACTION_MOVE:
                x2 = ev.getRawX();
                y2 = ev.getRawY();
                float deltaX = Math.abs(x2 - x1);
                float deltaY = Math.abs(y2 - y1);
                if(deltaX < deltaY && deltaY > ViewConfiguration.get(getContext()).getScaledTouchSlop()){
                    getParent().requestDisallowInterceptTouchEvent(true);
                    return true;
                }else {
                    getParent().requestDisallowInterceptTouchEvent(false);
                    break;
                }
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                getParent().requestDisallowInterceptTouchEvent(false);
        }
        return super.onInterceptTouchEvent(ev);
    }

}