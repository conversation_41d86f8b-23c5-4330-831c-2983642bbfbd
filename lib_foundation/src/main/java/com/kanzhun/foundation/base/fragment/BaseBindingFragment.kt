package com.kanzhun.foundation.base.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.viewbinding.ViewBinding
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.ktx.viewbinding.FragmentBindingModel
import com.kanzhun.common.kotlin.ktx.viewbinding.FragmentBindingModelDelegate
import com.kanzhun.common.kotlin.ui.fragment.BaseFragment
import com.kanzhun.common.kotlin.ui.statelayout.IStatePage
import com.kanzhun.common.kotlin.ui.statelayout.StateLayoutManager

/**
 * <AUTHOR>
 */
abstract class BaseBindingFragment<VB : ViewBinding, M : BaseViewModel> : BaseFragment(),
    FragmentBindingModel<VB, M> by FragmentBindingModelDelegate(), IStatePage {

    val mTag = this.javaClass.simpleName

    private var stateLayoutManager: StateLayoutManager?  = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = initViewBindingAndViewModel(inflater, container)
        observerLoadingDialogLiveData()
        attachStateLayout()
        preInit(arguments?:Bundle())
        initView()
        initData()
        return view
    }

    private fun observerLoadingDialogLiveData() {
        loadingDialogDelegate?.observerLoadingLiveData(this, mViewModel.loadingDialog)
    }

    private fun attachStateLayout() {
        getStateLayout()?.run {
            if(stateLayoutManager == null){
                stateLayoutManager = StateLayoutManager()
            }
            stateLayoutManager?.observerStateLayout(this, this@BaseBindingFragment, mViewModel.pageState,this@BaseBindingFragment::onRetry)
        }
    }

    abstract fun preInit(arguments:Bundle)

    abstract fun initView()

    abstract fun initData()

    abstract fun onRetry()
}