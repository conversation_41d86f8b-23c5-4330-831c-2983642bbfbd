package com.kanzhun.foundation.base;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2019/03/31.
 * <p>
 * https://www.jianshu.com/p/fff1ef649fc0
 */
public class BackHandlerHelper {

    /**
     * 将back事件分发给 FragmentManager 中管理的子Fragment，如果该 FragmentManager 中的所有Fragment都
     * 没有处理back事件，则尝试 FragmentManager.popBackStack()
     *
     * @return 如果处理了back键则返回 <b>true</b>
     * @see #handleBackPress(Fragment)
     * @see #handleBackPress(FragmentActivity)
     */
    public static boolean handleBackPress(FragmentManager fragmentManager) {
        return handleBackPress(fragmentManager, 0);
    }

    /**
     * @param fragmentManager
     * @param stackLiveNumber fragment栈中不被pop的存活数量
     * @return
     */
    public static boolean handleBackPress(FragmentManager fragmentManager, int stackLiveNumber) {
        List<Fragment> fragments = fragmentManager.getFragments();

        if (fragments == null) return false;

        for (int i = fragments.size() - 1; i >= 0; i--) {
            Fragment child = fragments.get(i);

            if (isFragmentBackHandled(child)) {
                return true;
            }
        }

        int liveNumber = stackLiveNumber;
        if (liveNumber <= 0) {
            liveNumber = 0;
        }
        if (fragmentManager.getBackStackEntryCount() > liveNumber) {
            fragmentManager.popBackStack();
            return true;
        }
        return false;
    }

    public static boolean handleBackPress(Fragment fragment) {
        return handleBackPress(fragment.getChildFragmentManager());
    }

    public static boolean handleBackPress(FragmentActivity fragmentActivity) {
        return handleBackPress(fragmentActivity.getSupportFragmentManager());
    }

    /**
     * @param fragmentActivity
     * @param stackLiveNumber  fragment栈中不被pop的存活数量
     * @return
     */
    public static boolean handleBackPress(FragmentActivity fragmentActivity, int stackLiveNumber) {
        return handleBackPress(fragmentActivity.getSupportFragmentManager(), stackLiveNumber);
    }

    /**
     * 判断Fragment是否处理了Back键
     *
     * @return 如果处理了back键则返回 <b>true</b>
     */
    public static boolean isFragmentBackHandled(Fragment fragment) {
        return fragment != null
                && fragment.isVisible()
                && fragment.getUserVisibleHint() //for ViewPager
                && fragment instanceof FragmentBackHandler
                && ((FragmentBackHandler) fragment).onBackPressed();
    }

}
