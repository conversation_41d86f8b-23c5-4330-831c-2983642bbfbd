package com.kanzhun.foundation.logic.mqtt;

import android.os.SystemClock;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.techwolf.lib.tlog.TLog;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.base.DeviceInfoEntiry;
import com.kanzhun.foundation.facade.Message2PBConvert;
import com.kanzhun.foundation.facade.MessageParser;
import com.kanzhun.foundation.kernel.account.Account;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.mms.ServerResponse;
import com.kanzhun.mms.client.IConnectionListener;
import com.kanzhun.mms.client.IReceiveListener;
import com.kanzhun.mms.client.ISendCallback;
import com.kanzhun.mms.client.MMSServiceSDK;
import com.kanzhun.mms.service.AppStatus;
import com.kanzhun.utils.base.MD5;


public class ConnectControler implements IConnectionListener, IReceiveListener {
    private static final String TAG = "ConnectControler";
    public static final String PROTOCOL_VERSION = "2";
    private static final long INTERVAL = 10;

    /**
     * MQTT连接成功
     */
    public static final byte SUCCESS = 1;
    /**
     * MQTT连接失败
     */
    public static final byte ERROR = 2;
    /**
     * MQTT连接中
     */
    public static final byte CONNECTING = 3;
    private byte status = ERROR;
    private PresentProvider mPresentProvider;

    public LiveData<Byte> getStatusObserver() {
        return mStatusObserver;
    }

    private MutableLiveData<Byte> mStatusObserver = new MutableLiveData<>();

    private MutableLiveData<Boolean> mPCObserver = new MutableLiveData<>(false);

    private DeviceInfoEntiry mDeviceInfoEntiry;

    private ConnectControler() {
        MMSServiceSDK.get().setConnectionListener(this);
        MMSServiceSDK.get().setReceiveListener(this);
        AppStatus.setAppStatusCallbacks(new AppStatus.IAppStatusCallback() {
            @Override
            public void onForeground() {
                PresentProvider presentProvider = mPresentProvider;
                if (presentProvider != null) {
                    TLog.debug(TAG,"onForeground");
                    sendPresence(presentProvider, MessageConstants.MSG_PRESENCE_VISIBLE);
                }
            }

            @Override
            public void onBackground() {
                PresentProvider presentProvider = mPresentProvider;
                if (presentProvider != null) {
                    TLog.debug(TAG,"onBackground");
                    sendPresence(presentProvider, MessageConstants.MSG_PRESENCE_GONE);
                }
            }
        });
//        mDeviceInfoEntiry = DeviceIdUtils.getDeviceId(ProcessHelper.getContext());
    }

    private static volatile ConnectControler gConnectontCroller;

    public static ConnectControler getInstance() {
        if (gConnectontCroller == null) {
            synchronized (ConnectControler.class) {
                if (gConnectontCroller == null) {
                    gConnectontCroller = new ConnectControler();
                }
            }
        }
        return gConnectontCroller;
    }

    public void setPCStatus(boolean login) {
        mPCObserver.postValue(login);
    }

    public LiveData<Boolean> getPCStatus() {
        return mPCObserver;
    }

    public void setPresentProvider(PresentProvider presentProvider) {
        mPresentProvider = presentProvider;
    }

    public void connect() {
        try {
            mPCObserver.postValue(false);
            String clientId = getClientId();
            String username = getUserName();
            String password = getPassword();
            MMSServiceSDK.get().connect(clientId, username, password);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public int getStatus() {
        return mStatusObserver.getValue();
    }

    public void disconnect() {
        MMSServiceSDK.get().disconnect();
    }

    @Override
    public byte[] getIdentifyData() {
        return null;
    }

    private long mLastTime = 0;

    @Override
    public void onConnectionConnected() {
        mMessageParser.onConnectionConnected();
        mStatusObserver.postValue(SUCCESS);
        long time = SystemClock.elapsedRealtime();
        PresentProvider presentProvider = mPresentProvider;
        if ((time - mLastTime) > INTERVAL && presentProvider != null) {
            mLastTime = time;
            int type = MessageConstants.MSG_PRESENCE_ONLINE | (BaseApplication.getApplication().isForeground() ? MessageConstants.MSG_PRESENCE_VISIBLE : MessageConstants.MSG_PRESENCE_GONE);
            sendPresence(presentProvider, type);
        }
    }

    private void sendPresence(PresentProvider presentProvider, int type) {
        long[] ids = presentProvider.getMaxIds(type);
        MMSServiceSDK.get().send(Message2PBConvert.createPresence(ids[0], type), new ISendCallback() {

            @Override
            public void onSuccess(ServerResponse serverResponse) {
                TLog.debug(TAG, "onSuccess() called with: serverResponse = [" + ids[0] + "]");
            }

            @Override
            public void onFailure() {
                TLog.error(TAG, "onFailure: ");
            }
        }, false);
    }

    @Override
    public void onConnectionFailed() {
        mStatusObserver.postValue(ERROR);
        status = ERROR;
        TLog.debug(TAG, "onConnectionFailed() ");
    }

    @Override
    public void onConnectionConnecting() {
        mStatusObserver.postValue(CONNECTING);
        status = CONNECTING;
    }

    @Override
    public void onConnectionDisconnected(int code) {
        mStatusObserver.postValue(ERROR);
        status = ERROR;
        if (code == 1) {
            ServiceManager.getInstance().logout();
            TLog.error(TAG, "onConnectionDisconnected() logout = " + code);
        }
        TLog.debug(TAG, "onConnectionDisconnected() called with: code = " + code);

    }

    protected String getUserName() {
        String result = "";
        Account account = AccountHelper.getInstance().getAccount();
        if (account != null) {
            result = String.format("%s-%s", account.getUserId(), PROTOCOL_VERSION);
        }
        return result;
    }

    private String getPassword() {
        return AccountHelper.getInstance().getTokenType() + " " + AccountHelper.getInstance().getAccessToken();
    }


    private String getClientId() {
        String clientId = getUserName();
        clientId = MD5.convert(clientId);
        clientId = clientId.substring(8, 24);
        return "AN" + clientId;
    }

    private MessageParser mMessageParser = new MessageParser();

    public MessageParser getMessageParser() {
        return mMessageParser;
    }

    @Override
    public void onReceive(byte[] data) {
        mMessageParser.onReceive(data);
    }


}
