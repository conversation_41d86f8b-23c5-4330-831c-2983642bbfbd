package com.kanzhun.foundation.logic.service;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.foundation.facade.TLogRepository;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.kernel.core.BaseService;
import com.kanzhun.localNotify.SwipeNotificationManager;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/6/30
 */
public class TLogService extends BaseService {
    private TLogRepository mTLogRepository;
    @Override
    protected void onAccountInitialized() {
        if (AccountHelper.getInstance().isOnlyLogin()) {
            mTLogRepository = new TLogRepository();
            SwipeNotificationManager.getInstance(BaseApplication.getApplication()).reStart();
        }
    }

    @Override
    protected void onAccountRelease() {
        if (mTLogRepository != null) {
            mTLogRepository.destroy();
        }
    }
}
