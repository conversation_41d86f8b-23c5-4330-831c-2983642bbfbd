package com.kanzhun.foundation.logic.service;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.views.image.MultiViewerBean;
import com.kanzhun.foundation.facade.MessageProcessor;
import com.kanzhun.foundation.facade.VideoMessageRepository;
import com.kanzhun.foundation.kernel.core.BaseService;
import com.kanzhun.foundation.logic.mqtt.ConnectControler;
import com.kanzhun.foundation.model.ChangeToConnectBean;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageForAction;
import com.kanzhun.foundation.utils.HiMessage;
import com.kanzhun.foundation.utils.HiMessageLiveData;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

public class MessageService extends BaseService {
    private static final String TAG = "MessageService";
    private MessageProcessor mMessageProcessor;
    private VideoMessageRepository mVideoMessageRepository;
    private MutableLiveData<Boolean> mSendMessage = new MutableLiveData<>();
    private MutableLiveData<MessageForAction> mVideoInvitation;
    private MutableLiveData<Boolean> mUnreadMessageGetFinish = new MutableLiveData<>();

    MessageService() {

    }

    @Override
    protected void onAccountInitialized() {
        mMessageProcessor = new MessageProcessor(ServiceManager.getInstance().getConversationService().getMessageCallback());
        mVideoMessageRepository = new VideoMessageRepository();
        mMessageProcessor.setVideoChatCallback(mVideoMessageRepository);
        ConnectControler.getInstance().getMessageParser().setCallback(mMessageProcessor);
    }


    @Override
    protected void onAccountRelease() {

    }

    public void setSendMessage(boolean sendMessage) {
        mSendMessage.postValue(sendMessage);
    }

    public MutableLiveData<Boolean> getSendMessage() {
        return mSendMessage;
    }

    public LiveData<Byte> getConnectionStatus() {
        return ConnectControler.getInstance().getStatusObserver();
    }

    public LiveData<Boolean> getPCStatus() {
        return ConnectControler.getInstance().getPCStatus();
    }

    public boolean getContactMessages(String chatId, int type, Boolean isEarlier,boolean isMeetingPlan,long minseq) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.getMessagesByChatId(chatId, type, isEarlier,isMeetingPlan,minseq);
        }
        return false;
    }


    public void prepareMessage(ChatMessage chatMessage) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.prepareMessage(chatMessage);
            mSendMessage.postValue(true);
        }
    }

    public void sendPrepareMessage(ChatMessage messageRecord, boolean success) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            Conversation conversation = ServiceManager.getInstance().getConversationService().getConversation(messageRecord.getChatId(), messageRecord.getType());
            messageRecord.setRelationStatus(conversation.getRelationStatus());
            messageProcessor.sendPrepareMessage(messageRecord, success);
            mSendMessage.postValue(true);
        }
    }

    public void sendMessage(ChatMessage chatMessage) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.sendMessage(chatMessage);
            mSendMessage.postValue(true);
        }
    }

    public LiveData<HiMessage> register(String userId, int type) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.register(userId, type);
        }
        return new HiMessageLiveData(userId);
    }

    public void removeCache(long chatId, int chatType) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.removeCache(chatId, chatType);
        }
    }

    public List<ChatMessage> getContactPicMessages(String uid, int type) {
        List<ChatMessage> chatMessageList = Collections.emptyList();
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            chatMessageList = messageProcessor.getContactPicMessages(uid, type);
        }
        return chatMessageList;
    }

//    public List<ChatMessage> getContactFileMessages(long chatId, int type) {
//        List<ChatMessage> chatMessageList = Collections.emptyList();
//        MessageProcessor messageProcessor = mMessageProcessor;
//        if (messageProcessor != null) {
//            chatMessageList = messageProcessor.getContactFileMessages(chatId, type);
//        }
//        return chatMessageList;
//    }

    public List<ChatMessage> getContactPicAndVideoMessages(String uid, int type) {
        List<ChatMessage> chatMessageList = Collections.emptyList();
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            chatMessageList = messageProcessor.getContactPicAndVideoMessages(uid, type);
        }
        return chatMessageList;
    }

    public void resend(ChatMessage messageRecord) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.resend(messageRecord);
        }
    }

    /**
     * 发送已读
     *
     * @param chatId 联系人或者群聊ID
     * @param type   单聊或者群聊
     */
    public void sendRead(String chatId, int type) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.sendRead(chatId, type);
        }
    }

//    /**
//     * 撤回消息
//     *
//     * @param msgId
//     */
//    public void withdraw(long msgId, long uid, int type, long time) {
//        MessageProcessor messageProcessor = mMessageProcessor;
//        if (messageProcessor != null) {
//            messageProcessor.withdraw(msgId, uid, type, time);
//        }
//    }

    /**
     * 删除消息
     *
     * @param chatMessage
     */
    public void delete(ChatMessage chatMessage, String uid, int type) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.delete(chatMessage, uid, type);
        }
    }

    public ChatMessage queryLatestMessage(String chatId, int type) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.queryLatestMessage(chatId, type);
        }
        return null;
    }

    public ChatMessage queryLatestMessage(String chatId) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.queryLatestMessage(chatId);
        }
        return null;
    }

    public ChatMessage query(long msgId) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            ChatMessage messageRecord = messageProcessor.query(msgId);
            return messageRecord;
        }
        return null;
    }

//    public void updateReadEnvelope(long chatId, int type, long mid, int redStatus) {
//        MessageProcessor messageProcessor = mMessageProcessor;
//        if (messageProcessor != null) {
//            messageProcessor.updateReadEnvelope(chatId, type, mid, redStatus);
//        }
//    }


    public long getMaxShowSeq(String chatId, int type) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.getMaxShowSeq(chatId, type);
        }
        return 0;
    }

    public void setStartSeq(String chatId, long startSeq) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.setStartSeq(chatId, startSeq);
        }
    }

    public MutableLiveData<MessageForAction> getVideoInvitation() {
        VideoMessageRepository repository = mVideoMessageRepository;
        if (repository != null) {
            mVideoInvitation = repository.getVideoInvitation();
            return mVideoInvitation;
        }
        return new MutableLiveData<>();
    }

    public HashMap<String, String> getPostInviteCancelAction() {
        VideoMessageRepository repository = mVideoMessageRepository;
        if (repository != null) {
            return mVideoMessageRepository.getPostInviteCancelAction();
        }
        return new HashMap<String, String>();
    }

    public HashMap<String, ChangeToConnectBean> getChangeToConnectAction() {
        VideoMessageRepository repository = mVideoMessageRepository;
        if (repository != null) {
            return mVideoMessageRepository.getChangeToConnectAction();
        }
        return new HashMap<String, ChangeToConnectBean>();
    }

    public void setVideoInvitation(MessageForAction videoInvitation) {
        this.mVideoInvitation.postValue(videoInvitation);
    }

//    public void setUnreadMessage(String chatId, int chatType, long msgId) {
//        if (mMessageUnread == null) {
//            mMessageUnread = new MessageForHint();
//        }
//        mMessageUnread.setChatId(chatId);
//        mMessageUnread.setType(chatType);
//        mMessageUnread.setMid(-msgId);
//        mMessageUnread.setContent("以下是新消息");
//        mMessageUnread.setHintType(MessageForHint.TYPE_WITH_DRAW_LINE);
//        mMessageProcessor.onNotifyUI(chatId, chatType);
//    }

//    public MessageForHint getUnreadMessage() {
//        return mMessageUnread;
//    }

//    public void setNewMessage(String chatId, int chatType, long msgId) {
//        if (mNewMessage == null) {
//            mNewMessage = new MessageForHint();
//        }
//        mNewMessage.setChatId(chatId);
//        mNewMessage.setType(chatType);
//        mNewMessage.setMid(-msgId);
//        mNewMessage.setContent("以下是新消息");
//        mNewMessage.setHintType(MessageForHint.TYPE_WITH_DRAW_LINE);
//        mMessageProcessor.onNotifyUI(chatId, chatType);
//    }

//    public MessageForHint getNewMessage() {
//        return mNewMessage;
//    }

//    public void removeUnreadMessage() {
//        mMessageUnread = null;
//        mNewMessage = null;
//    }

    public MutableLiveData<Boolean> getUnreadMessageGetFinish() {
        return mUnreadMessageGetFinish;
    }

    public void setUnreadMessageGetFinish(boolean unreadMessageGetFinish) {
        this.mUnreadMessageGetFinish.postValue(unreadMessageGetFinish);
    }

    public void insertMessages(List<ChatMessage> chatMessages) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            mMessageProcessor.insertMessages(chatMessages);
        }
    }

    public void insertMessage(ChatMessage chatMessage) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            mMessageProcessor.insertMessage(chatMessage);
        }
    }

    public void notifyUI(String chatId, int type) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            mMessageProcessor.onNotifyUI(chatId, type);
        }
    }

    public void insertWithdrawnMessage(ChatMessage chatMessage) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            mMessageProcessor.insertWithdrawnMessage(chatMessage);
        }
    }

    public List<Long> queryObsoleteMessage() {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.queryObsoleteMessage();
        }
        return new ArrayList<>();
    }

    public List<Long> queryUpdateMessage() {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.queryUpdateMessage();
        }
        return new ArrayList<>();
    }


    public ChatMessage getMaxShowMessage(String chatId, int type) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.getMaxShowMessage(chatId, type);
        }
        return null;
    }

    public List<ChatMessage> queryMessagesOrder(List<Long> messageIds) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.queryMessageOrder(messageIds);
        }
        return new ArrayList<>();
    }

    public ChatMessage getChatMessage(long msgId) {
        return mMessageProcessor.query(msgId);
    }

//    public void setMinSeq(String chatId, int chatType, long seq) {
//        MessageProcessor messageProcessor = mMessageProcessor;
//        if (messageProcessor != null) {
//            messageProcessor.setMinSeq(chatId, chatType, seq);
//        }
//    }

    public void clearPatch(String chatId, int type) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            mMessageProcessor.clearPatch(chatId, type);
        }
    }

//    public void updateAudioTransText(long mid, String transText, int transType) {
//        MessageProcessor messageProcessor = mMessageProcessor;
//        if (messageProcessor != null) {
//            mMessageProcessor.updateAudioTransText(mid, transText, transType);
//        }
//    }

    public void resetPatches(String chatId, int chatType) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            mMessageProcessor.resetPatches(chatId, chatType);
        }
    }

    public HashMap<Long, Long> getLocalWithDrawMsg() {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.getLocalWithDrawMsg();
        }
        return new HashMap<>();
    }

    public List<MultiViewerBean> getPreviewImages() {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.getPreviewImages();
        }
        return new ArrayList<>();
    }

    public void setPreviewImages(List<MultiViewerBean> previewImages) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.setPreviewImages(previewImages);
        }
    }

    public MutableLiveData<String> getErrorTips() {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            return messageProcessor.getErrorTips();
        }
        return new MutableLiveData<>();
    }

    public void resetErrorTips() {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.setErrorTips(null);
        }
    }

    public void clearMessages(String chatId, int type, long seq) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.clearMessages(chatId, type, seq);
        }
    }


    /**
     * 进入聊天页面
     *
     * @param chatId
     */
    public void sendInChat(String chatId) {
        MessageProcessor messageProcessor = mMessageProcessor;
        if (messageProcessor != null) {
            messageProcessor.sendInChat(chatId);
        }
    }

}
