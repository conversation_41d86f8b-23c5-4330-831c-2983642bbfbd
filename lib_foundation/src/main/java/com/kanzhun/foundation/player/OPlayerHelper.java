package com.kanzhun.foundation.player;

import android.content.Context;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import com.kanzhun.utils.AudienceUtils;
import com.techwolf.lib.tlog.TLog;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.kernel.account.AccountHelper;

import org.alita.config.AfantyConfig;
import org.alita.core.AlitaPlayer;
import org.alita.core.IAlitaPlayerListener;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/7/4
 */
public class OPlayerHelper implements IAlitaPlayerListener {
    public static final String TAG = "OPlayerHelper";

    public static final int STATE_NONE = 0;
    public static final int STATE_PLAYING = 1;
    public static final int STATE_STOPPED = 2;
    public static final int STATE_PAUSED = 3;
    public static final int STATE_FINISH = 4;
    public static final int STATE_ERROR = 5;

    private final Context context;
    private OVideoPlayer oVideoPlayer;
    private CallBack callBack;
    private boolean isLoop;
    private String mCurrentPlayVideoURL;
    private int mCurrentPlayState = STATE_NONE;
    private int failRetryCount = 0;
    private boolean mute;
    private int progress;
    private int duration;

    public OPlayerHelper(Context context, CallBack callBack) {
        this.initVodPlayer(context);
        this.context = context;
        this.callBack = callBack;
    }

    private void initVodPlayer(Context context) {
        if (this.oVideoPlayer == null) {
            this.oVideoPlayer = new OVideoPlayer(context);
//            AfantyConfig config = new AfantyConfig();
//            String accessToken = AccountHelper.getInstance().getAccessToken();
//            String tokenType = AccountHelper.getInstance().getTokenType();
//            if (!TextUtils.isEmpty(accessToken)) {
//                config.headers.put("Authorization", tokenType + " " + accessToken);
//            }
//            config.enableHardwareDecode = true;
//            config.multiPlayerTrafficLimit = false;
//            this.oVideoPlayer.setConfig(config);
            this.oVideoPlayer.setPlayListener(this);
        }
    }

    public void setFrameScaleType(int frameScaleType) {
        this.oVideoPlayer.setFrameScaleType(frameScaleType);
    }

    public void setLooper(boolean isLoop) {
        if (oVideoPlayer != null) {
            this.oVideoPlayer.setLoop(isLoop);
            this.isLoop = isLoop;
        }

    }

    public boolean isLooper() {
        return this.isLoop;
    }

    public void setPlayView(OViewRenderer videoView) {
        if (oVideoPlayer != null) {
            this.oVideoPlayer.setViewRender(videoView);
        }
    }

    public OViewRenderer getViewRender() {
        if (oVideoPlayer != null) {
            return oVideoPlayer.getViewRenderer();
        }
        return null;
    }

    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    public synchronized int startCache(String playUrl) {
        if (this.oVideoPlayer != null) {
            return this.oVideoPlayer.startCache(playUrl);
        }
        return 0;

    }

    public void setPlayUrl(String playUrl) {
        //根据url设置config
        AfantyConfig config = new AfantyConfig();
        if (playUrl.contains("media.lengjing.com")) {
            String accessToken = AccountHelper.getInstance().getAccessToken();
            String tokenType = AccountHelper.getInstance().getTokenType();
            if (!TextUtils.isEmpty(accessToken)) {
                config.headers.put("Authorization", tokenType + " " + accessToken);
            }
        }
        config.enableHardwareDecode = true;
        config.multiPlayerTrafficLimit = false;
        if (oVideoPlayer != null) this.oVideoPlayer.setConfig(config);

        this.mCurrentPlayVideoURL = playUrl;
//        if (this.oVideoPlayer != null) {
//            this.playURL(this.mCurrentPlayVideoURL);
//        }
    }

    public void start() {
        if (this.mCurrentPlayState == STATE_PAUSED) {
            this.resume();
        } else if (!isPlaying()) {
            this.playURL(this.mCurrentPlayVideoURL);
        }

    }

    private void playURL(String url) {
        if (url != null && !"".equals(url)) {
            this.mCurrentPlayVideoURL = url;
            if (this.oVideoPlayer != null) {
                this.oVideoPlayer.setStartTime(0.0F);
                this.oVideoPlayer.setPlayListener(this);
                int ret = this.oVideoPlayer.startPlay(url);
                if (ret == 0) {
                    this.failRetryCount = 0;
                    this.updatePlayState(STATE_PLAYING);
                } else {
                    ++this.failRetryCount;
                    if (this.failRetryCount < 3) {
                        this.playURL(url);
                    } else {
                        this.oVideoPlayer.stopPlay(true);
                        this.updatePlayState(STATE_STOPPED);
                    }
                }
            }

        }
    }

    public void stop() {
        this.updatePlayState(STATE_STOPPED);
        this.mCurrentPlayVideoURL = null;
        this.callBack = null;
        if (this.oVideoPlayer != null) {
            this.oVideoPlayer.setPlayListener(null);
            this.oVideoPlayer.stopPlay(false);
        }

    }

    public void resume() {
        this.updatePlayState(STATE_PLAYING);
        if (this.oVideoPlayer != null) {
            this.oVideoPlayer.resume();
        }
        requestAudioFocus(this.context, true);
    }

    public void pause() {
        this.updatePlayState(STATE_PAUSED);
        if (this.oVideoPlayer != null) {
            this.oVideoPlayer.pause();
        }

    }

    public static boolean requestAudioFocus(Context context, boolean focus) {
        boolean bool = false;
        if (context != null) {
            AudioManager am = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (focus) {
                int result = am.requestAudioFocus(null, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT);
                bool = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
            } else {
                int result = am.abandonAudioFocus(null);
                bool = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
            }
        }
        return bool;
    }

    public void setMute(boolean mute) {
        if (oVideoPlayer != null) {
            oVideoPlayer.setMute(mute);
            this.mute = mute;
        }
    }

    public boolean isMute() {
        return mute;
    }

    public void updatePlayState(int status) {
        this.mCurrentPlayState = status;
    }

    /**
     * @param time 时间毫秒
     */
    public void seekTo(long time) {
        if (this.oVideoPlayer != null) {
            this.oVideoPlayer.seek((int) (time / 1000L));
            if (this.mCurrentPlayState == STATE_PAUSED) {
                this.resume();
            }
        }

    }

    public boolean isPlayed() {
        return this.mCurrentPlayState == STATE_PLAYING || this.mCurrentPlayState == STATE_PAUSED;
    }

    public boolean isPlaying() {
        return this.mCurrentPlayState == STATE_PLAYING;
    }

    public boolean isPlayFinish() {
        return this.mCurrentPlayState == STATE_FINISH;
    }

    public boolean isPaused() {
        return this.mCurrentPlayState == STATE_PAUSED;
    }

    public int getCurrentPlayState() {
        return mCurrentPlayState;
    }

    public int getCurrentPosition() {
        return this.progress;
    }

    public int getDuration() {
        return this.duration;
    }

    public void speed(float speed) {
        if (this.oVideoPlayer != null) {
            this.oVideoPlayer.setRate(speed);
        }
    }

    public void onDestroy() {
        this.updatePlayState(STATE_STOPPED);
        this.callBack = null;
        OViewRenderer renderer = null;
        if (this.oVideoPlayer != null) {
            this.oVideoPlayer.setPlayListener(null);
            this.oVideoPlayer.stopPlay(true);
            this.oVideoPlayer.destroy();
            renderer = oVideoPlayer.getViewRenderer();
            this.oVideoPlayer = null;
        }
        if (renderer != null) {
            AudienceUtils.removeFromParent(renderer);
            renderer.release();
            renderer = null;
        }
    }

    public void onDestroyUnRemove() {
        this.updatePlayState(STATE_STOPPED);
        this.callBack = null;
        if (this.oVideoPlayer != null) {
            this.oVideoPlayer.setPlayListener(null);
            this.oVideoPlayer.stopPlay(true);
            this.oVideoPlayer.destroy();
            this.oVideoPlayer = null;
        }
    }

    public boolean isSameSource(String url) {
        return !TextUtils.isEmpty(url) && TextUtils.equals(url, this.mCurrentPlayVideoURL);
    }

    @Override
    public void onPlayEvent(AlitaPlayer alitaPlayer, int event, Bundle param) {
        ExecutorFactory.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                switch (event) {
                    case AfantyConfig.PLAY_EVT_RCV_FIRST_I_FRAME:
                        TLog.debug(TAG, "onPlayEvent event = %d name = %s msg= %s", event, "PLAY_EVT_RCV_FIRST_I_FRAME", "视频第一帧");
                        if (OPlayerHelper.this.callBack != null) {
                            OPlayerHelper.this.callBack.onFirstFrame();
                        }
                        break;
                    case AfantyConfig.PLAY_EVT_PLAY_BEGIN:
                        TLog.debug(TAG, "onPlayEvent event = %d name = %s msg= %s", event, "PLAY_EVT_PLAY_BEGIN", "视频播放开始，如果有转菊花什么的这个时候该停了");
                        OPlayerHelper.this.updatePlayState(STATE_PLAYING);
//                        if (OPlayerHelper.this.callBack != null) {
//                            OPlayerHelper.this.callBack.onBufferEnd();
//                            OPlayerHelper.this.callBack.onRenderingStart();
//                        }
                        break;
                    case AfantyConfig.PLAY_EVT_PLAY_PROGRESS:
                        OPlayerHelper.this.progress = (int) (param.getFloat(AfantyConfig.EVT_PLAY_PROGRESS) * 1000);
                        OPlayerHelper.this.duration = (int) (param.getFloat(AfantyConfig.EVT_PLAY_DURATION) * 1000);
                        if (OPlayerHelper.this.callBack != null && OPlayerHelper.this.progress >= 0) {
                            OPlayerHelper.this.callBack.onProgress(OPlayerHelper.this.progress, duration);
                        }
                        break;
                    case AfantyConfig.PLAY_EVT_PLAY_END:
                        TLog.debug(TAG, "onPlayEvent event = %d name = %s msg= %s", event, "PLAY_EVT_PLAY_END", "视频播放结束");
                        if (!isLoop) {
                            OPlayerHelper.this.updatePlayState(STATE_FINISH);
                            if (OPlayerHelper.this.callBack != null) {
                                OPlayerHelper.this.callBack.onPlayEnd();
                            }
                        }
//                        OPlayerHelper.this.lastCompletionTime = SystemClock.elapsedRealtime();
//                        if (OPlayerHelper.this.callBack != null) {
//                            OPlayerHelper.this.callBack.onCompletion();
//                        }
                        break;
                    case AfantyConfig.PLAY_EVT_PLAY_LOADING:
                        TLog.debug(TAG, "onPlayEvent event = %d name = %s msg= %s ", event, "PLAY_EVT_PLAY_LOADING", "视频播放 loading");
                        if (OPlayerHelper.this.callBack != null) {
                            OPlayerHelper.this.callBack.loading(true);
                        }
                        break;
                    case AfantyConfig.PLAY_EVT_VOD_LOADING_END:
                        TLog.debug(TAG, "onPlayEvent event = %d name = %s msg= %s", event, "PLAY_EVT_VOD_LOADING_END", "视频播放 loading 结束，视频继续播放");
                        if (OPlayerHelper.this.callBack != null) {
                            OPlayerHelper.this.callBack.loading(false);
                        }
                        break;
                    case AfantyConfig.PLAY_EVT_PLAY_BUFFER_DURATION:
                        // 播放
                        break;
                    case AfantyConfig.PLAY_WARNING_VIDEO_DECODE_FAIL:
                        TLog.error(TAG, "event = %d name = %s msg = %s", event, "PLAY_WARNING_VIDEO_DECODE_FAIL", "当前视频帧解码失败");
                        break;
                    case AfantyConfig.PLAY_WARNING_AUDIO_DECODE_FAIL:
                        TLog.error(TAG, "event = %d name = %s msg = %s", event, "PLAY_WARNING_AUDIO_DECODE_FAIL", "当前音频帧解码失败");
                        break;
                    default:
                        TLog.error(TAG, "event = %s", event + (param == null ? "" : param.toString()));
                        break;
                }
                if (event < 0) {
                    if (OPlayerHelper.this.oVideoPlayer != null) {
                        OPlayerHelper.this.oVideoPlayer.stopPlay(true);
                        OPlayerHelper.this.updatePlayState(STATE_ERROR);
                    }
                }
            }
        });
    }

    @Override
    public void onNetStatus(AlitaPlayer alitaPlayer, Bundle bundle) {

    }

    public interface CallBack {
        /**
         * 第一帧
         */
        default void onFirstFrame() {
        }

        default void onPlayEnd() {
        }

        default void onProgress(int progress, int duration) {
        }

        default void loading(boolean b) {

        }
    }
}
