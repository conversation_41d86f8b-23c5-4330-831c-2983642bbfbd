package com.kanzhun.foundation.router

import android.content.Context
import android.os.Bundle
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.util.AppUtil
import org.json.JSONObject

class KmpPageRouter {
    companion object{
        const val MODULE_KMP_BIND = "/kmp/"
        const val KMP_PAGE_ROUTER_KUIKLY = MODULE_KMP_BIND + "kuikly"

        private const val KEY_PAGE_NAME = "pageName"
        private const val KEY_PAGE_DATA = "pageData"

        //跳转绑定孩子绑定父母页面
        fun jumpKmpActivity(context: Context, pageName: String? = "", pageData: JSONObject = JSONObject(), pageSource: PageSource = PageSource.NONE) {
            AppUtil.startUri(context, KMP_PAGE_ROUTER_KUIKLY, Bundle().apply {
                putString(KEY_PAGE_NAME, pageName)
                putString(KEY_PAGE_DATA, pageData.toString())
            })
        }

    }


}