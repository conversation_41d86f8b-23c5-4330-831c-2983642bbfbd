package com.kanzhun.foundation.router;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.sankuai.waimai.router.common.DefaultUriRequest;
import com.kanzhun.common.util.ActivityAnimType;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.api.model.ProfileInfoModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/30.
 */
public class LoginPageRouter {
    private static final String MODULE_NAME_LOGIN = "/login";

    public static final String APP_LOGIN_ENTRY_ACTIVITY = MODULE_NAME_LOGIN + "/login_entry_activity";
    public static final String APP_LOGIN_ACTIVITY = MODULE_NAME_LOGIN + "/login_activity";
    public static final String LOGIN_LOGIN_MARRY_PLAN_ACTIVITY = MODULE_NAME_LOGIN + "/login_marry_plan_activity";
    public static final String LOGIN_SKIP_ACTIVITY = MODULE_NAME_LOGIN + "/login_skip_activity";
    public static final String LOGIN_ACTIVATE_ACTIVITY = MODULE_NAME_LOGIN + "/login_activate_activity";//标签
    public static final String LOGIN_SELECT_MODEL_ACTIVITY = MODULE_NAME_LOGIN + "/login_select_model_activity";
    public static final String LOGIN_PARENT_FIRST_EDIT_INFO_ACTIVITY = MODULE_NAME_LOGIN + "/login_parent_first_edit_info_activity";
    //父母模式修改孩子信息
    public static final String LOGIN_PARENT_SEE_CHILD_INFO_ACTIVITY= MODULE_NAME_LOGIN + "/login_parent_see_child_info_activity";

    public static void jumpToSkipActivity(Context context, boolean gotoComplete) {
        Bundle bundle = new Bundle();
        bundle.putBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN, gotoComplete);
        AppUtil.startUri(context, LOGIN_SKIP_ACTIVITY, bundle,ActivityAnimType.DEFAULT);
    }

    /**
     * 登录入口页面
     * @param context
     */
    public static void jumpToLoginActivity(Context context) {
        jumpToLoginActivity(context,false);
    }

    /**
     * 登录入口页面
     * @param context
     * @param clearTop 是否把栈顶的activity清除
     */
    public static void jumpToLoginActivity(Context context, boolean clearTop) {
        DefaultUriRequest defaultUriRequest = AppUtil.getDefaultUriRequest(context, APP_LOGIN_ENTRY_ACTIVITY, null,ActivityAnimType.ALPHA);
        if(context instanceof Activity){
            if(clearTop)defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        }else {
            defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        defaultUriRequest.start();
    }

    /**
     * 手机号登录页面
     * @param context
     * @param clearTop
     */
    public static void jumpToPhoneLoginActivity(Context context,boolean clearTop) {
        DefaultUriRequest defaultUriRequest = AppUtil.getDefaultUriRequest(context, APP_LOGIN_ACTIVITY);
        if(clearTop)defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        defaultUriRequest.start();
    }

    /**
     * 手机号登录页面
     * @param context
     * @param clearTop
     */
    public static void jumpToPhoneLoginActivity(Context context,String wxCode,boolean clearTop) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_STRING_1,wxCode);
        DefaultUriRequest defaultUriRequest = AppUtil.getDefaultUriRequest(context, APP_LOGIN_ACTIVITY,bundle, ActivityAnimType.SLIDE_FROM_LEFT_TO_RIGHT);
        if(clearTop)defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        defaultUriRequest.start();
    }

    public static void jumpToSelectModel(Context context, boolean clearTop) {
        jumpToSelectModel(context,clearTop,ActivityAnimType.DEFAULT);
    }

    public static void jumpToSelectModel(Context context, boolean clearTop,int animType) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT_1, 1);
        DefaultUriRequest defaultUriRequest = AppUtil.getDefaultUriRequest(context, APP_LOGIN_ACTIVITY,bundle, animType);
        if(clearTop)defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        defaultUriRequest.start();
    }

    public static void jumpToInviteCode(Context context, boolean clearTop) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT_1, 2);
        DefaultUriRequest defaultUriRequest = AppUtil.getDefaultUriRequest(context, APP_LOGIN_ACTIVITY,bundle, ActivityAnimType.DEFAULT);
        if(clearTop)defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        defaultUriRequest.start();
    }

    //type 1个性标签  2兴趣爱好
    public static void jumpToKeyLabel(Context context, List<ProfileInfoModel.Label> bean,int requestCode,int type) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT,type);
        ArrayList<Integer> arrayList = new ArrayList<>();
        if(bean != null && bean.size() > 0){
            for (int i = 0; i < bean.size(); i++) {
                arrayList.add(bean.get(i).tagId);
            }
        }
        bundle.putIntegerArrayList(BundleConstants.BUNDLE_DATA_INT_ARRAY,arrayList);
        AppUtil.startUriForResult(context, LOGIN_ACTIVATE_ACTIVITY,requestCode, bundle);
    }

    public static void jumpToParentFirstEdit(Context context, boolean clearTop) {
        jumpToParentFirstEdit(context,clearTop,null);
    }

    public static void jumpToParentFirstEdit(Context context, boolean clearTop,Bundle data) {
        DefaultUriRequest defaultUriRequest = AppUtil.getDefaultUriRequest(context, LoginPageRouter.LOGIN_PARENT_FIRST_EDIT_INFO_ACTIVITY,data,ActivityAnimType.DEFAULT);
        if(clearTop)defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        defaultUriRequest.start();
    }

    public static void jumpToChildFirstEdit(Context context, boolean clearTop) {
        DefaultUriRequest defaultUriRequest = AppUtil.getDefaultUriRequest(context, LoginPageRouter.LOGIN_ACTIVATE_ACTIVITY);
        if(clearTop)defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        defaultUriRequest.start();
    }

    public static void jumpMarryPlan(Context context, boolean clearTop) {
        DefaultUriRequest defaultUriRequest = AppUtil.getDefaultUriRequest(context, LoginPageRouter.LOGIN_LOGIN_MARRY_PLAN_ACTIVITY);
        if(clearTop)defaultUriRequest.setIntentFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        defaultUriRequest.start();
    }
}
