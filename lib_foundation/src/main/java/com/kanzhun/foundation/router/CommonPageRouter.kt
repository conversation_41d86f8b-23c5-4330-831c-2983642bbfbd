package com.kanzhun.foundation.router

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.kotlin.ktx.getPageSourceBundle

class CommonPageRouter {

    companion object{
        const val MODULE_PARENT_BIND = "/common/"
        const val SCAN_ACTIVITY = MODULE_PARENT_BIND + "common_scan_activity"

        //跳转绑定父母或孩子页面
        fun jumpToScanActivity(context: Context?, pageSource: PageSource = PageSource.NONE, protocolFrom: String = "", sourceType:String ="", isH5UseResult:Int = 0) {
            val bundle = getPageSourceBundle(pageSource)
            bundle.putString(BundleConstants.BUNDLE_SOURCE_TYP,sourceType)
            bundle.putInt(BundleConstants.BUNDLE_DATA_INT,isH5UseResult)
            AppUtil.startUri(context, SCAN_ACTIVITY, bundle)
        }


    }
}