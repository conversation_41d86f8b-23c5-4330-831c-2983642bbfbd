package com.kanzhun.foundation.router

import android.content.Context
import android.os.Bundle
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.util.ActivityAnimType
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.kotlin.ktx.getPageSourceBundle
import com.kanzhun.foundation.model.UserGuideBlockInfoBean

/**
 * <AUTHOR>
 * @date 2022/7/6.
 */
object TaskPageRouter {
    private const val MODULE_NAME_TAKE = "/task"
    const val TASK_NEW_USER_ACTIVITY = MODULE_NAME_TAKE + "/task_new_user_activity"
    const val FRAGMENT_PARENT_ACTIVITY = MODULE_NAME_TAKE + "/fragment_parent_activity"
    const val NEW_TASK_SERVICE = "new_task_service"

    /**
     * 新手引导体系
     */
    fun jumpToTaskNewUserActivity(context: Context?, bean: UserGuideBlockInfoBean? = UserGuideBlockInfoBean.getDefault(),pageSource: PageSource = PageSource.NONE,
                                  protocol:String = "") {
        if (bean == null) {
            return
        }
//        if (bean.baseInfoBlock) {
            val bundle = getPageSourceBundle(pageSource)

            bundle.putSerializable(BundleConstants.BUNDLE_USER_GUIDE_BLOCK_INFO_BEAN, bean)
            bundle.putString(BundleConstants.BUNDLE_PROTOCOL_FROM,protocol)
            AppUtil.startUri(context!!, TASK_NEW_USER_ACTIVITY, bundle, ActivityAnimType.DEFAULT)
//        }
    }

    fun jumpToLoverTagActivity(context: Context?,pageSource: PageSource = PageSource.NONE,
                                  protocol:String = "",bundleFrom: Bundle = Bundle()) {
        val bundle = getPageSourceBundle(pageSource)
        bundle.putAll(bundleFrom)
        bundle.putString(BundleConstants.BUNDLE_PROTOCOL_FROM,protocol)
        AppUtil.startUri(context!!, FRAGMENT_PARENT_ACTIVITY, bundle, ActivityAnimType.DEFAULT)
    }
}