package com.kanzhun.foundation.router.service;

import android.content.Context;

import androidx.fragment.app.FragmentActivity;

import com.kanzhun.common.base.PageSource;
import com.kanzhun.foundation.api.bean.ProfileStatusBean;
import com.kanzhun.foundation.model.UserGuideBlockInfoBean;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/8/8
 */
public interface IMeRouterService {
    void jumpUserInfoPerfectDialog(FragmentActivity activity, List<ProfileStatusBean> profileStatusBeans);

    void showUserGuideBlockDialogWhenSendLike(Context activity, UserGuideBlockInfoBean blockInfoBean, PageSource currentPage,String peerId);
    void showUserGuideBlockDialogWhenChat(FragmentActivity activity, PageSource currentPage,String peerId);

    void showUserInviteBlockDialog(FragmentActivity activity, Function0<Unit> callback);

    void showNewUserTaskDialog(FragmentActivity activity);

    void getAdviceGray();

    void showXueXinHelpDialog(FragmentActivity activity,int type);

    void showMeBlockInfoWithInfo(FragmentActivity activity, UserGuideBlockInfoBean blockInfo,PageSource pageSource,String p3,String peerId);
}
