package com.kanzhun.foundation.screenShot

import android.net.Uri
import android.provider.MediaStore
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.kanzhun.foundation.screenShot.ScreenShotHandler.getInstance
import com.techwolf.lib.tlog.TLog
import java.lang.Exception

class ScreenShotSetting(activity: FragmentActivity?) : LifecycleObserver {
    private var mActivity: FragmentActivity?

    /**
     * 内部存储器内容观察者
     */
    protected var mInternalObserver: ScreenShotContentObserver

    /**
     * 外部存储器内容观察者
     */
    protected var mExternalObserver: ScreenShotContentObserver

    protected var iScreenShotCallback: IScreenShotCallback? = null


    protected var innerScreenShotCallback: IScreenShotCallback = object : IScreenShotCallback {
        override fun screenShot(path: String?, uri: Uri?) {
            TLog.print("ScreenShotContentObserver","screenShot:"+path?.toString())
            if (iScreenShotCallback != null) {
                iScreenShotCallback!!.screenShot(path, uri)
            }
        }
    }


    init {
        this.mActivity = activity
        mInternalObserver = ScreenShotContentObserver(
            MediaStore.Images.Media.INTERNAL_CONTENT_URI,
            getInstance(),
            innerScreenShotCallback
        )
        mExternalObserver = ScreenShotContentObserver(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            getInstance(),
            innerScreenShotCallback
        )
    }

    fun setScreenShotCallback(iScreenShotCallback: IScreenShotCallback?) {
        this.iScreenShotCallback = iScreenShotCallback
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onResume() {
        // 添加监听
        try {
            mActivity!!.getContentResolver().registerContentObserver(
                MediaStore.Images.Media.INTERNAL_CONTENT_URI,
                true,
                mInternalObserver
            )
            mActivity!!.getContentResolver().registerContentObserver(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                true,
                mExternalObserver
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
        // fix bug https://bugly.qq.com/v2/crash-reporting/crashes/65d92ea8a7/6369999?pid=1
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    fun onPause() {
        mActivity!!.getContentResolver().unregisterContentObserver(mInternalObserver)
        mActivity!!.getContentResolver().unregisterContentObserver(mExternalObserver)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        iScreenShotCallback = null
        mActivity = null
    }
}
