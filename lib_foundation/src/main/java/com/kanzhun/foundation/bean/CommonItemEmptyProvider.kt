package com.kanzhun.foundation.bean

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.databinding.CommonItemEmptyBinding

class CommonItemEmptyProvider(val activity: FragmentActivity) : BaseItemProvider<BaseListItem, CommonItemEmptyBinding>() {
    override fun onBindItem(binding: CommonItemEmptyBinding, item: BaseListItem) {

    }
}

