package com.kanzhun.foundation.ai

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.base.compose.ui.O2Button
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.SCENE_HOBBY
import com.kanzhun.foundation.api.SCENE_IDEAL
import com.kanzhun.foundation.databinding.FoundationCommonDialogBinding
import com.kanzhun.foundation.dialog.FixedBottomSheetDialogFragment
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.utils.T

private const val TAG = "AgcDialog"

class AgcDialog(
    private val scene: String = "0",
    private val onReplace: (String) -> Unit = {},
    private val onAppend: (String) -> Unit = {},
    private val onClose: () -> Unit = {},
) :
    FixedBottomSheetDialogFragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return FoundationCommonDialogBinding.inflate(inflater, container, false).apply {
            composeView.onSetWindowContent {
                val dismissDialog = { dismissAllowingStateLoss() }
                val viewModel: AiViewModel = viewModel()

                LaunchedEffect(Unit) {
                    viewModel.onRegenerate(scene = scene)
                }

                val dynamicAgcContent = viewModel.dynamicAgcContent
                AgcContent(
                    scene = scene,

                    inGenerating = viewModel.inGenerating,
                    dynamicAgcContent = dynamicAgcContent,

                    regenerateStep = viewModel.regenerateStep,
                    regenerateCount = viewModel.regenerateCount,
                    onRegenerate = { ss ->
                        if (ss.isNotEmpty()) {
                            T.ss(ss)

                            reportOnRegenerate(ss)
                        } else {
                            viewModel.onRegenerate(scene = scene) { toast ->
                                reportOnRegenerate(toast)
                            }
                        }
                    },

                    onReplace = {
                        onReplace(dynamicAgcContent)
                        dismissDialog()
                    },
                    onAppend = {
                        onAppend(dynamicAgcContent)
                        dismissDialog()
                    },
                    onClose = {
                        if (dynamicAgcContent.isNotEmpty()) {
                            activity?.showTwoButtonDialog(
                                content = "关闭将清空AI帮写生成的内容，是否确认关闭？",
                                cancelable = false,
                                canceledOnTouchOutside = false,
                                positiveText = "确认",
                                positiveButtonClick = {
                                    dismissDialog()
                                    onClose()
                                },
                                negativeText = "取消",
                            )
                        } else {
                            dismissDialog()
                            onClose()
                        }
                    },
                )
            }
        }.root
    }

    private fun reportOnRegenerate(toast: String? = null) {
        reportPoint("aigenerate-text-popup-click") {
            actionp2 =
                if (scene == SCENE_IDEAL) "我的理想型" else "自我介绍" // 记录触发的类型：自我介绍、我的理想型
            type = "重新生成" // 记录点击的位置：关闭、替换已填写的内容、补充到已有内容下方、确定、重新生成
            msg =
                toast // 当type='重新生成'时，若达到了次数上限或是两次间隔过短，记录toast文案在msg里“今日已达到使用次数上限啦~”“操作有点频繁了，稍等一下~”
        }
    }

    companion object {
        @JvmStatic
        fun shouldShow(
            activity: FragmentActivity,
            scene: String = SCENE_HOBBY, // 0:兴趣爱好 2:我的理想型
            onReplace: (String) -> Unit = {},
            onAppend: (String) -> Unit = {},
            onClose: () -> Unit = {},
        ): AgcDialog {
            val dialog = AgcDialog(
                scene = scene,
                onReplace = {
                    onReplace(it)
                    AgcFeedbackDialog.shouldShowAgcFeedback = true

                    reportPoint("aigenerate-text-popup-click") {
                        actionp2 =
                            if (scene == SCENE_IDEAL) "我的理想型" else "自我介绍" // 记录触发的类型：自我介绍、我的理想型
                        type =
                            if (scene == SCENE_IDEAL) "确定" else "替换已填写的内容" // 记录点击的位置：关闭、替换已填写的内容、补充到已有内容下方、确定、重新生成
                        msg // 当type='重新生成'时，若达到了次数上限或是两次间隔过短，记录toast文案在msg里“今日已达到使用次数上限啦~”“操作有点频繁了，稍等一下~”
                    }
                },
                onAppend = {
                    onAppend(it)
                    AgcFeedbackDialog.shouldShowAgcFeedback = true

                    reportPoint("aigenerate-text-popup-click") {
                        actionp2 =
                            if (scene == SCENE_IDEAL) "我的理想型" else "自我介绍" // 记录触发的类型：自我介绍、我的理想型
                        type = "补充到已有内容下方" // 记录点击的位置：关闭、替换已填写的内容、补充到已有内容下方、确定、重新生成
                        msg // 当type='重新生成'时，若达到了次数上限或是两次间隔过短，记录toast文案在msg里“今日已达到使用次数上限啦~”“操作有点频繁了，稍等一下~”
                    }
                },
                onClose = {
                    onClose()

                    reportPoint("aigenerate-text-popup-click") {
                        actionp2 =
                            if (scene == SCENE_IDEAL) "我的理想型" else "自我介绍" // 记录触发的类型：自我介绍、我的理想型
                        type = "关闭" // 记录点击的位置：关闭、替换已填写的内容、补充到已有内容下方、确定、重新生成
                        msg // 当type='重新生成'时，若达到了次数上限或是两次间隔过短，记录toast文案在msg里“今日已达到使用次数上限啦~”“操作有点频繁了，稍等一下~”
                    }
                }
            )
            dialog.isCancelable = false
            dialog.show(activity.supportFragmentManager, TAG)

            reportPoint("aigenerate-text-popup-expo") {
                actionp2 =
                    if (scene == SCENE_IDEAL) "我的理想型" else "自我介绍" // 记录触发的类型：自我介绍、我的理想型
            }

            return dialog
        }
    }
}

@Composable
private fun AgcContent(
    scene: String = "0",

    inGenerating: Boolean = false,
    dynamicAgcContent: String = "",

    regenerateStep: Int = 1,
    regenerateCount: Int = 3,
    onRegenerate: (toast: String) -> Unit = {},

    onReplace: () -> Unit = {},
    onAppend: () -> Unit = {},
    onClose: () -> Unit = {},
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(
                shape = RoundedCornerShape(
                    topStart = 20.dp,
                    topEnd = 20.dp,
                    bottomEnd = 0.dp,
                    bottomStart = 0.dp
                )
            )
            .background(
                brush = Brush.verticalGradient(
                    colorStops = arrayOf(
                        0.0f to Color(0xFFFFFFFF),
                        0.2f to Color(0xFFE8F9FF),
                        0.4f to Color(0xFFD0F2FF),
                        1.0f to Color(0xFF9FE4FF),
                    )
                ),
                shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
            )
    ) {
        Image(
            painter = painterResource(id = R.mipmap.common_ic_close),
            contentDescription = "image description",
            modifier = Modifier
                .padding(20.dp)
                .size(24.dp)
                .align(Alignment.TopEnd)
                .noRippleClickable {
                    onClose()
                }
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
                .padding(top = 44.dp, bottom = 28.dp),
        ) {
            Image(
                painter = painterResource(id = if (scene == SCENE_IDEAL) R.mipmap.foundation_ic_ai_title_ideal else R.mipmap.foundation_ic_ai_title_intro),
                contentDescription = "image description",
                modifier = Modifier
                    .width(240.dp)
                    .height(68.dp)
                    .align(alignment = Alignment.CenterHorizontally)
            )

            Image(
                painter = painterResource(id = R.mipmap.foundation_ic_ai_chat2),
                contentDescription = "image description",
                modifier = Modifier
                    .size(width = 78.dp, height = 72.dp)
                    .offset(y = (-24).dp)
                    .zIndex(1f)
            )

            Column(
                modifier = Modifier
                    .aspectRatio(335f / 300)
                    .fillMaxWidth()
                    .offset(y = (-40).dp)
                    .border(
                        width = 1.4.dp,
                        color = Color(0xFFFFFFFF),
                        shape = RoundedCornerShape(size = 12.dp)
                    )
                    .background(color = Color(0xB3FFFFFF), shape = RoundedCornerShape(12.dp))
            ) {
                val state = rememberScrollState()
                var userScrolling by remember { mutableStateOf(false) }

                LaunchedEffect(dynamicAgcContent) {
                    if (!userScrolling) {
                        state.animateScrollTo(state.maxValue)
                    }
                }

                Text(
                    text = dynamicAgcContent,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF191919),
                    ),
                    modifier = Modifier
                        .pointerInput(Unit) {
                            detectDragGestures(
                                onDragStart = { userScrolling = true },
                                onDragEnd = { userScrolling = false },
                                onDrag = { _, _ -> }
                            )
                        }
                        .verticalScroll(state = state)
                        .fillMaxWidth()
                        .padding(12.dp)
                        .weight(1f)
                )

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val notLast = regenerateStep < regenerateCount
                    val regenerateEnabled = !inGenerating && notLast
                    Text(
                        text = if (inGenerating) "生成中…" else "重新生成 (${regenerateStep}/$regenerateCount)",
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight(500),
                            color = if (regenerateEnabled) Color(0xFF292929) else Color(0xFFB2B2B2),
                        ),
                        modifier = Modifier
                            .border(
                                width = 1.dp,
                                color = if (regenerateEnabled) Color(0xFF292929) else Color(
                                    0xFFB2B2B2
                                ),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                            .noRippleClickable {
                                var toast = ""

                                if (regenerateEnabled) {
                                    toast = ""
                                } else {
                                    if (!notLast) {
                                        toast = "今日已达到使用次数上限啦~"
                                    }
                                }

                                onRegenerate(toast)
                            }
                    )
                }
            }

            Text(
                text = "内容由AI生成，仅供参考",
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF62A4BE),
                ),
                modifier = Modifier
                    .offset(y = (-28).dp)
                    .padding(horizontal = 12.dp)
                    .noRippleClickable {

                    }
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                val enabled = !inGenerating
                O2Button(
                    modifier = Modifier
                        .fillMaxWidth(),
                    text = if (scene == SCENE_HOBBY) "替换已填写的内容" else "确定",
                    enabled = enabled
                ) {
                    onReplace()
                }

                if (scene == SCENE_HOBBY) {
                    Spacer(modifier = Modifier.height(16.dp))

                    Button(
                        onClick = onAppend,
                        modifier = Modifier.fillMaxWidth(),
                        enabled = enabled,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = colorResource(com.kanzhun.common.R.color.qmui_config_color_transparent),
                            disabledContainerColor = colorResource(com.kanzhun.common.R.color.qmui_config_color_transparent)
                        ),
                        border = BorderStroke(
                            width = 1.dp,
                            color = if (enabled) colorResource(com.kanzhun.common.R.color.common_color_191919) else colorResource(
                                com.kanzhun.common.R.color.common_color_CCCCCC
                            )
                        ),
                        contentPadding = PaddingValues(vertical = 12.dp),
                    ) {
                        Row(
                            modifier = Modifier,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "补充到已有内容下方",
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    color = colorResource(if (enabled) com.kanzhun.common.R.color.common_color_191919 else com.kanzhun.common.R.color.common_color_CCCCCC),
                                    fontWeight = FontWeight(500)
                                ),
                            )
                        }
                    }
                }
            }
        }

        Image(
            painter = painterResource(id = R.mipmap.found_ic_ai),
            contentDescription = "image description",
            modifier = Modifier
                .size(width = 72.dp, height = 44.dp)
        )
    }
}

@Preview
@Composable
private fun PreviewGrantAiProtocolContent() {
    val viewModel: AiViewModel = viewModel()
    AgcContent(
        scene = SCENE_HOBBY,
        dynamicAgcContent = viewModel.dynamicAgcContent,
        regenerateStep = viewModel.regenerateStep,
        onRegenerate = {
            viewModel.onRegenerate()
        }
    )
}

@Preview
@Composable
private fun PreviewGrantAiProtocolContent2() {
    val viewModel: AiViewModel = viewModel()
    AgcContent(
        scene = SCENE_HOBBY,
        inGenerating = true,
        dynamicAgcContent = viewModel.dynamicAgcContent,
        regenerateStep = viewModel.regenerateStep,
        onRegenerate = {
            viewModel.onRegenerate()
        }
    )
}

@Preview
@Composable
private fun PreviewGrantAiProtocolContent3() {
    val viewModel: AiViewModel = viewModel()
    AgcContent(
        scene = SCENE_IDEAL,
        dynamicAgcContent = viewModel.dynamicAgcContent,
        regenerateStep = viewModel.regenerateStep,
        onRegenerate = {
            viewModel.onRegenerate()
        }
    )
}