package com.kanzhun.foundation.adapter

import android.annotation.SuppressLint
import androidx.viewbinding.ViewBinding
import kotlin.experimental.ExperimentalTypeInference

@SuppressLint("NotifyDataSetChanged")
fun <I : Any, V : ViewBinding> MultiTypeBindingAdapter<I, V>.replaceData(newData: Collection<I>?) {
    data.clear()
    if (!newData.isNullOrEmpty()) {
        data.addAll(newData)
    }
    notifyDataSetChanged()
}

fun <I : Any, V : ViewBinding> MultiTypeBindingAdapter<I, V>.appendData(appendData: Collection<I>?) {
    if (appendData.isNullOrEmpty()) {
        return
    }
    val originSize = data.size
    data.addAll(appendData)
    notifyItemRangeInserted(originSize, appendData.size)
}

/**
 * 使用配置创建多布局Adapter
 */
fun <I : Any, V : ViewBinding> ItemViewMapperStore<I, V>.asAdapter(list: List<I> = ArrayList()) =
    MultiTypeBindingAdapter(this, list)

@OptIn(ExperimentalTypeInference::class)
@BuilderInference
@Suppress("NOTHING_TO_INLINE")
inline fun <I : Any> buildMultiTypeAdapterByIndex(noinline build: MultiTypeAdapterIndexConfigBuilder<I>.() -> Unit): MultiTypeBindingAdapter<I, ViewBinding> =
    createMultiTypeConfigByIndex(build).asAdapter()

@OptIn(ExperimentalTypeInference::class)
@BuilderInference
@Suppress("NOTHING_TO_INLINE")
inline fun <I : Any> buildMultiTypeAdapterByType(noinline build: MultiTypeAdapterTypeConfigBuilder<I>.() -> Unit): MultiTypeBindingAdapter<I, ViewBinding> =
    createMultiTypeConfigByType(build).asAdapter()

@OptIn(ExperimentalTypeInference::class)
@BuilderInference
@Suppress("NOTHING_TO_INLINE")
inline fun <I : Any> buildMultiTypeAdapterByMap(noinline build: MultiTypeAdapterMapConfigBuilder<I>.() -> Unit): MultiTypeBindingAdapter<I, ViewBinding> =
    createMultiTypeConfigByMap(build).asAdapter()