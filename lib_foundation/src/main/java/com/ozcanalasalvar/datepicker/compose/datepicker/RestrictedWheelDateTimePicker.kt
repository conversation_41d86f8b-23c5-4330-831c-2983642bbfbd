package com.ozcanalasalvar.datepicker.compose.datepicker

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ozcanalasalvar.datepicker.compose.component.SelectorView
import com.ozcanalasalvar.datepicker.model.Date
import com.ozcanalasalvar.datepicker.model.Time
import com.ozcanalasalvar.datepicker.utils.daysOfDateRestricted
import com.ozcanalasalvar.datepicker.utils.monthsOfDateRestricted
import com.ozcanalasalvar.datepicker.utils.withDay
import com.ozcanalasalvar.datepicker.utils.withMonth
import com.ozcanalasalvar.wheelview.SelectorOptions
import com.ozcanalasalvar.wheelview.WheelView
import java.util.Calendar
import java.util.Locale
import kotlin.math.abs

/**
 * A date time picker that restricts selection to a range of dates.
 * This implementation prevents selecting past dates and limits selection to one month ahead.
 */
@Composable
fun RestrictedWheelDateTimePicker(
    offset: Int = 2,
    initialTimeMillis: Long? = null,
    onDateChanged: (Int, Int, Int, Long) -> Unit = { _, _, _, _ -> },
    onTimeChanged: (Int, Int, String?) -> Unit = { _, _, _ -> },
) {
    // Get current date/time as minimum date
    val currentCalendar = Calendar.getInstance()
    val currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY)
    val currentMinute = currentCalendar.get(Calendar.MINUTE)

    // Create minimum date (current time)
    val minDate = Date(currentCalendar.timeInMillis)

    // Store current year, month, day for same-day comparison
    val currentYear = currentCalendar.get(Calendar.YEAR)
    val currentMonth = currentCalendar.get(Calendar.MONTH)
    val currentDay = currentCalendar.get(Calendar.DAY_OF_MONTH)

    // Create maximum date (one month from now)
    val maxCalendar = Calendar.getInstance()
    maxCalendar.add(Calendar.MONTH, 1)
    val maxDate = Date(maxCalendar.timeInMillis)

    var forceScroll by remember { mutableStateOf(false) }

    // Initialize with provided date/time or current date/time
    val initialDate = remember(initialTimeMillis) {
        if (initialTimeMillis != null) {
            val initialCalendar = Calendar.getInstance()
            initialCalendar.timeInMillis = initialTimeMillis

            // Ensure the initial date is not before the minimum date
            // Ensure the initial date is not after the maximum date
            val date = if (initialCalendar.timeInMillis < currentCalendar.timeInMillis) {
                minDate
            } else if (initialCalendar.timeInMillis > maxCalendar.timeInMillis) {
                maxDate
            } else {
                Date(initialCalendar.timeInMillis)
            }

            forceScroll = true

            date
        } else {
            minDate
        }
    }

    var selectedDate by remember(initialDate) { mutableStateOf(initialDate) }

    // Function to check if a date is today
    fun isSelectedDateToday(): Boolean {
        return selectedDate.year == currentYear &&
                selectedDate.month == currentMonth &&
                selectedDate.day == currentDay
    }

    // Get available months and days based on restrictions
    val months = selectedDate.monthsOfDateRestricted(minDate, maxDate)
    val days = selectedDate.daysOfDateRestricted(minDate, maxDate)

    // Initialize with provided time or current time
    var selectedTime by remember(initialTimeMillis) {
        if (initialTimeMillis != null) {
            val initialCalendar = Calendar.getInstance()
            initialCalendar.timeInMillis = initialTimeMillis
            val initialHour = initialCalendar.get(Calendar.HOUR_OF_DAY)
            val initialMinute = initialCalendar.get(Calendar.MINUTE)

            // Round to nearest 30 minutes (0 or 30)
            val roundedMinute = if (initialMinute < 30) 0 else 30

            // Check if the initial time is valid (not in the past)
            if (isSelectedDateToday() &&
                (initialHour < currentHour ||
                        (initialHour == currentHour && roundedMinute < currentMinute))
            ) {
                // If initial time is in the past, use current time
                if (currentMinute >= 30 && currentHour < 23) {
                    // Move to next hour with 0 minutes
                    mutableStateOf(Time(currentHour + 1, 0))
                } else if (currentMinute < 30) {
                    // Current hour with 30 minutes
                    mutableStateOf(Time(currentHour, 30))
                } else {
                    // Fallback (should not happen with proper validation)
                    mutableStateOf(Time(currentHour, 0))
                }
            } else {
                // Use the initial time
                mutableStateOf(Time(initialHour, roundedMinute))
            }
        } else {
            // Use current time
            if (currentMinute >= 30 && currentHour < 23) {
                // Move to next hour with 0 minutes
                mutableStateOf(Time(currentHour + 1, 0))
            } else if (currentMinute < 30) {
                // Current hour with 30 minutes
                mutableStateOf(Time(currentHour, 30))
            } else {
                // Fallback (should not happen with proper validation)
                mutableStateOf(Time(currentHour, 0))
            }
        }
    }

    // Ensure initial time is valid (not in the past)
    LaunchedEffect(Unit) {
        if (isSelectedDateToday()) {
            // For current day, validate the time
            val validStartHour =
                if (currentMinute >= 30 && currentHour < 23) currentHour + 1 else currentHour

            if (selectedTime.hour < validStartHour) {
                // Hour is in the past, update to valid hour
                selectedTime = Time(
                    validStartHour,
                    if (validStartHour == currentHour && currentMinute < 30) 30 else 0
                )
            } else if (selectedTime.hour == currentHour && currentMinute < 30 && selectedTime.minute < 30) {
                // Current hour selected but minute is invalid
                selectedTime = Time(currentHour, 30)
            }
        }
    }

    // Available hours based on current time if today is selected
    val hours = mutableListOf<Int>().apply {
        // For current day, only show hours from current hour onwards
        // For future days, show all hours
        val isSameDay = isSelectedDateToday()

        val startHour = if (isSameDay) {
            // If current minute >= 30, start from next hour since we can't select current hour
            if (currentMinute >= 30 && currentHour < 23) currentHour + 1 else currentHour
        } else 0

        for (hour in startHour..23) {
            add(hour)
        }
    }

    // Only show 0 and 30 minutes
    val minutes = mutableListOf<Int>().apply {
        // If current hour is selected on current day, start from appropriate minute
        if (isSelectedDateToday() && selectedTime.hour == currentHour && currentMinute < 30) {
            // Only show 30 minute option if current minute is less than 30
            // (if current minute >= 30, the current hour won't be in the hours list)
            add(30)
        } else {
            // For all other cases, show both 0 and 30
            add(0)
            add(30)
        }
    }

    LaunchedEffect(selectedDate) {
        // When date changes, ensure time is valid for the new date
        if (isSelectedDateToday()) {
            // For current day, validate the time
            val validStartHour =
                if (currentMinute >= 30 && currentHour < 23) currentHour + 1 else currentHour

            if (selectedTime.hour < validStartHour) {
                // Hour is in the past, update to valid hour
                selectedTime = Time(
                    validStartHour,
                    if (validStartHour == currentHour && currentMinute < 30) 30 else 0
                )
            } else if (selectedTime.hour == currentHour && currentMinute < 30 && selectedTime.minute < 30) {
                // Current hour selected but minute is invalid
                selectedTime = Time(currentHour, 30)
            }
        }

        onDateChanged(selectedDate.day, selectedDate.month, selectedDate.year, selectedDate.date)
    }

    LaunchedEffect(selectedTime) {
        onTimeChanged(selectedTime.hour, selectedTime.minute, selectedTime.format)
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
            .height(IntrinsicSize.Max),
        contentAlignment = Alignment.Center
    ) {
        val height = 48.dp
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            WheelView(
                modifier = Modifier.weight(1f),
                itemSize = DpSize(30.dp, height),
                forceScroll = forceScroll,
                selection = maxOf(months.indexOf(selectedDate.month), 0),
                itemCount = months.size,
                rowOffset = offset,
                selectorOption = SelectorOptions().copy(
                    selectEffectEnabled = true,
                    enabled = false
                ),
                onFocusItem = {
                    selectedDate = selectedDate.withMonth(months[it])
                },
                content = {
                    // Calculate position relative to center (selected) item
                    val position = it % months.size
                    val selectedPosition = months.indexOf(selectedDate.month)

                    // Calculate the distance from the selected item (considering circular nature)
                    val distance = minOf(
                        abs(position - selectedPosition),
                        months.size - abs(position - selectedPosition)
                    )

                    // Determine color based on distance from center
                    val color = when (distance) {
                        0 -> Color(0xFF191919) // Center item (selected)
                        1 -> Color(0xFFAAAAAA) // Items adjacent to center
                        else -> Color(0xFFCCCCCC)         // Items furthest from center
                    }

                    Text(
                        text = String.format(
                            Locale.getDefault(),
                            "%02d",
                            months[it] + 1
                        ),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                        fontSize = when (distance) {
                            0 -> 24 // Center item (selected)
                            1 -> 20 // Items adjacent to center
                            else -> 16  // Items furthest from center
                        }.sp,
                        color = color
                    )
                })

            Text(
                text = "月",
                style = TextStyle(
                    fontSize = 15.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF191919),
                    textAlign = TextAlign.Center,
                )
            )

            WheelView(
                modifier = Modifier.weight(1f),
                itemSize = DpSize(30.dp, height),
                forceScroll = forceScroll,
                selection = maxOf(days.indexOf(selectedDate.day), 0),
                itemCount = days.size,
                rowOffset = offset,
                selectorOption = SelectorOptions().copy(
                    selectEffectEnabled = true,
                    enabled = false
                ),
                onFocusItem = {
                    selectedDate = selectedDate.withDay(days[it])
                },
                content = {
                    // Calculate position relative to center (selected) item
                    val position = it % days.size
                    val selectedPosition = days.indexOf(selectedDate.day)

                    // Calculate the distance from the selected item (considering circular nature)
                    val distance = minOf(
                        abs(position - selectedPosition),
                        days.size - abs(position - selectedPosition)
                    )

                    // Determine color based on distance from center
                    val color = when (distance) {
                        0 -> Color(0xFF191919) // Center item (selected)
                        1 -> Color(0xFFAAAAAA) // Items adjacent to center
                        else -> Color(0xFFCCCCCC)         // Items furthest from center
                    }

                    Text(
                        text = String.format(Locale.getDefault(), "%02d", days[it]),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                        fontSize = when (distance) {
                            0 -> 24 // Center item (selected)
                            1 -> 20 // Items adjacent to center
                            else -> 16  // Items furthest from center
                        }.sp,
                        color = color
                    )
                })

            WheelView(
                modifier = Modifier.weight(1f),
                itemSize = DpSize(30.dp, height),
                forceScroll = forceScroll,
                selection = maxOf(0, minOf(hours.size - 1, hours.indexOf(selectedTime.hour))),
                itemCount = hours.size,
                rowOffset = offset,
                selectorOption = SelectorOptions().copy(
                    selectEffectEnabled = true,
                    enabled = false
                ),
                onFocusItem = {
                    // Update the hour
                    val newHour = hours[it]
                    // If selecting current hour on current day, ensure minute is valid
                    selectedTime =
                        if (isSelectedDateToday() && newHour == currentHour && currentMinute < 30) {
                            // For current hour when current minute < 30, only 30 is valid
                            selectedTime.copy(hour = newHour, minute = 30)
                        } else {
                            // For other hours, default to 0 minutes
                            selectedTime.copy(hour = newHour, minute = 0)
                        }
                },
                content = {
                    // Calculate position relative to center (selected) item
                    val position = it % hours.size
                    val selectedPosition = hours.indexOf(selectedTime.hour)

                    // Calculate the distance from the selected item (considering circular nature)
                    val distance = minOf(
                        abs(position - selectedPosition),
                        hours.size - abs(position - selectedPosition)
                    )

                    // Determine color based on distance from center
                    val color = when (distance) {
                        0 -> Color(0xFF191919) // Center item (selected)
                        1 -> Color(0xFFAAAAAA) // Items adjacent to center
                        else -> Color(0xFFCCCCCC)         // Items furthest from center
                    }

                    Text(
                        text = if (hours[it] < 10) "0${hours[it]}" else "${hours[it]}",
                        textAlign = TextAlign.Center,
                        modifier = Modifier.width(50.dp),
                        fontSize = when (distance) {
                            0 -> 24 // Center item (selected)
                            1 -> 20 // Items adjacent to center
                            else -> 16  // Items furthest from center
                        }.sp,
                        color = color
                    )
                })

            Text(
                text = ":",
                style = TextStyle(
                    fontSize = 15.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF191919),
                    textAlign = TextAlign.Center,
                )
            )

            WheelView(
                modifier = Modifier.weight(1f),
                itemSize = DpSize(30.dp, height),
                forceScroll = forceScroll,
                selection = maxOf(0, minOf(minutes.size - 1, minutes.indexOf(selectedTime.minute))),
                itemCount = minutes.size,
                rowOffset = offset,
                selectorOption = SelectorOptions().copy(
                    selectEffectEnabled = true,
                    enabled = false
                ),
                onFocusItem = {
                    // Simply update the minute - we've already ensured valid options in the list
                    selectedTime = selectedTime.copy(minute = minutes[it])
                },
                content = {
                    // Calculate position relative to center (selected) item
                    val position = it % minutes.size
                    val selectedPosition = minutes.indexOf(selectedTime.minute)

                    // Calculate the distance from the selected item (considering circular nature)
                    val distance = minOf(
                        abs(position - selectedPosition),
                        minutes.size - abs(position - selectedPosition)
                    )

                    // Determine color based on distance from center
                    val color = when (distance) {
                        0 -> Color(0xFF191919) // Center item (selected)
                        1 -> Color(0xFFAAAAAA) // Items adjacent to center
                        else -> Color(0xFFCCCCCC)         // Items furthest from center
                    }

                    Text(
                        text = if (minutes[it] < 10) "0${minutes[it]}" else "${minutes[it]}",
                        textAlign = TextAlign.Center,
                        modifier = Modifier.width(100.dp),
                        fontSize = when (distance) {
                            0 -> 24 // Center item (selected)
                            1 -> 20 // Items adjacent to center
                            else -> 16  // Items furthest from center
                        }.sp,
                        color = color
                    )
                })
        }

        SelectorView(darkModeEnabled = false, offset = offset)
    }
}
