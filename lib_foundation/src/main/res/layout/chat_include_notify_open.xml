<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/llNotifyOpen"
    android:visibility="gone"
    android:orientation="vertical">

    <com.lihang.ShadowLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hl_layoutBackground="@color/common_white"
        app:hl_shadowColor="@color/common_color_B2B2B2_10"
        app:hl_shadowLimit="1dp"
        app:hl_shadowHiddenBottom="true"
        app:hl_shadowHiddenLeft="true"
        app:hl_shadowHiddenRight="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/common_white"
            android:gravity="center_vertical"
            android:paddingVertical="10dp"
            android:translationZ="5dp"
            >

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginStart="18dp"
                android:src="@mipmap/chat_ic_notify_tips" />

            <TextView
                android:id="@+id/idText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="打开通知，第一时间收到Ta的消息"
                android:textColor="@color/common_color_292929"
                android:textSize="14dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/idOpenBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:background="@color/common_color_292929"
                android:gravity="center"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                android:text="开启"
                android:textColor="@color/common_white"
                android:textSize="14dp"
                android:textStyle="bold"
                app:qmui_backgroundColor="@color/common_color_292929"
                app:qmui_radius="14dp" />

        </LinearLayout>


    </com.lihang.ShadowLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/common_color_F5F5F5" />
</LinearLayout>
