<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="replyShow"
            type="androidx.databinding.ObservableBoolean" />

        <variable
            name="showGuide"
            type="androidx.databinding.ObservableBoolean" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/common_white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/qmui_voice_answer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@color/common_color_F2F4FB">

            <TextView
                android:id="@+id/tv_title_big"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:fontFamily="sans-serif-medium"
                android:text="@string/me_info_preview_answer"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_20"
                app:layout_constraintStart_toEndOf="@+id/tv_title"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-medium"
                android:text="@string/me_info_preview_voice"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_20"
                app:layout_constraintBaseline_toBaselineOf="@+id/tv_title_big"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/iv_title_icon"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_title_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="11dp"
                android:src="@drawable/common_ic_icon_voice_answer_title"
                app:layout_constraintBottom_toBottomOf="@+id/tv_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_title" />

            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/iv_like"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="36dp"
                android:background="@drawable/common_bg_selector_match_like"
                app:layout_constraintBottom_toBottomOf="@id/tv_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_content"
                tools:visibility="visible">

                <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="13dp"
                    android:src="@{replyShow ? @drawable/common_ic_icon_match_reply : @drawable/common_ic_icon_match_like}" />
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>


            <TextView
                android:id="@+id/tv_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="29dp"
                android:textColor="@color/common_color_545454"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constraintBottom_toTopOf="@+id/iv_icon"
                app:layout_constraintEnd_toStartOf="@+id/iv_like"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title_big"
                tools:text="最难忘的一次相识是最难忘的一次相最难忘的一次相识是最难忘的一次相最难忘的一次相识是最难忘的一次相" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="iv_like,tv_content" />

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="28dp"
                android:src="@drawable/me_ic_icon_info_preview_voice_start"
                app:layout_constraintBottom_toTopOf="@+id/voice_guide_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/barrier"
                app:layout_goneMarginBottom="44dp" />

            <com.kanzhun.common.views.AudioRecorderPlayView
                android:id="@+id/v_voice"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="36dp"
                app:layout_constraintBottom_toBottomOf="@+id/iv_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/iv_icon"
                app:layout_constraintTop_toTopOf="@+id/iv_icon" />

            <ImageView
                android:id="@+id/voice_guide_bg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:background="@mipmap/common_bg_match_voice_gudie"
                app:layout_constraintBottom_toBottomOf="@+id/voice_guide_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/voice_guide_title"
                app:visibleGone="@{showGuide}"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/voice_guide_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                android:paddingLeft="16dp"
                android:paddingTop="16dp"
                android:paddingRight="20dp"
                android:paddingBottom="16dp"
                android:text="@string/common_match_gudie_voice_title"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@+id/voice_guide_bg"
                app:layout_constraintRight_toLeftOf="@+id/voice_guide_btn"
                app:layout_constraintTop_toBottomOf="@+id/iv_icon"
                app:visibleGone="@{showGuide}"
                tools:visibility="visible" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/voice_guide_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="18dp"
                android:paddingLeft="10dp"
                android:paddingTop="5dp"
                android:paddingRight="10dp"
                android:paddingBottom="5dp"
                android:text="@string/common_match_gudie_voice_button"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_14"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/voice_guide_title"
                app:layout_constraintRight_toRightOf="@id/voice_guide_bg"
                app:layout_constraintTop_toTopOf="@+id/voice_guide_title"
                app:qmui_backgroundColor="@color/common_color_7171FF"
                app:qmui_radius="26dp"
                app:visibleGone="@{showGuide}"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>