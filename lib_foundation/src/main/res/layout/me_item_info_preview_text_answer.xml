<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/common_white">

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/qmui_text_answer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="18dp"
            android:layout_marginRight="16dp"
            android:background="@color/common_color_E3EBFF"
            app:qmui_radius="20dp">

            <TextView
                android:id="@+id/tv_q"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:text="Q"
                android:textColor="@color/common_color_AABDED"
                android:textSize="@dimen/common_text_sp_36"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_question_index"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:textColor="@color/common_color_AABDED"
                android:textSize="@dimen/common_text_sp_24"
                app:layout_constraintBottom_toBottomOf="@+id/tv_q"
                app:layout_constraintLeft_toRightOf="@+id/tv_q"
                tools:text="1" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-medium"
                android:gravity="center"
                android:paddingLeft="52dp"
                android:paddingTop="28dp"
                android:paddingRight="52dp"
                android:paddingBottom="17dp"
                android:text="@{item.question}"
                android:textColor="@color/common_color_0D0D1D"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="十年后我的理想生活十十年后我的理想生活十十年后我的理想" />

            <View
                android:id="@+id/v_divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginLeft="26dp"
                android:layout_marginRight="26dp"
                android:background="@color/common_color_C9DBFC"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title" />


            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/iv_like"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:layout_marginBottom="12dp"
                android:background="@drawable/common_bg_selector_match_like"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                tools:visibility="visible">

                <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="13dp"
                    android:src="@{replyShow ? @drawable/common_ic_icon_match_reply : @drawable/common_ic_icon_match_like}" />
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>


            <TextView
                android:id="@+id/tv_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="52dp"
                android:paddingTop="18dp"
                android:paddingRight="52dp"
                android:paddingBottom="32dp"
                android:text="@{item.answer}"
                android:textColor="@color/common_color_0D0D1D_80"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/v_divider"
                tools:text="十年后我的理想" />
        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
    </FrameLayout>

    <data>

        <variable
            name="item"
            type="com.kanzhun.foundation.api.bean.TextAnswerBean" />

        <variable
            name="replyShow"
            type="androidx.databinding.ObservableBoolean" />
    </data>
</layout>