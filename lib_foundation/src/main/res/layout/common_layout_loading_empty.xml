<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <merge xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/iv_error"
            android:layout_width="174dp"
            android:layout_height="174dp"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_fileName="net_error/lost.json"
            app:lottie_imageAssetsFolder="net_error/images"
            app:lottie_loop="false" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/av_load_bg"
            android:layout_width="160dp"
            android:layout_height="160dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@+id/iv_error"
            app:layout_constraintEnd_toEndOf="@+id/iv_error"
            app:layout_constraintStart_toStartOf="@+id/iv_error"
            app:layout_constraintTop_toTopOf="@+id/iv_error"
            app:lottie_autoPlay="true"
            app:lottie_fileName="loading/jiazai.json"
            app:lottie_imageAssetsFolder="loading/images"
            app:lottie_loop="true" />

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/common_loading"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_error" />

        <com.kanzhun.common.views.RoundAlphaButton
            android:id="@+id/btn_retry"
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_marginTop="20dp"
            android:gravity="center_vertical"
            android:paddingLeft="32dp"
            android:paddingRight="32dp"
            android:text="@string/common_click_retry"
            android:textColor="@color/common_color_000000_80"
            android:textSize="@dimen/common_text_sp_18"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_desc"
            app:qmui_borderColor="@color/common_color_979797"
            app:qmui_borderWidth="1dp"
            app:qmui_radius="100dp" />

    </merge>
</layout>