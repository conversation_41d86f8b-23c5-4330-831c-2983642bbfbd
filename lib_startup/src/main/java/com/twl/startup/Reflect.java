package com.twl.startup;

import android.app.Application;
import android.content.pm.ProviderInfo;
import android.os.Handler;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

final class Reflect {
    public static Object gCurrentActivityThread;
    public static Handler gH;
    public static Object gBoundApplication;
    public static Application gApplication;
    public static Handler gMainHandler;
    public static List<ProviderInfo> gProviders;
    public static Method installContentProvidersMethod;

    public static StartupCallback gStartupCallback;
    public static Field gCallback;

    public static Field gPreserveWindow;

}
