package com.kanzhun.marry.lib_share.entity

class Utils {
    companion object {
        /**
         * 获取微信分享描述，微信分享文字描述限制是1024个字符，超过会分享失败
         */
        fun getWxDescription(description: String?): String? {
            return if (description.isNullOrEmpty() || description.length <= 100) {
                description
            } else {
                description.substring(0, 100)
            }

        }
    }
}