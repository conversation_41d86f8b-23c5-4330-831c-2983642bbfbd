<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_top_corner_32_color_white"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rcvShareActions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="24dp"
        tools:itemCount="1"
        tools:listitem="@layout/share_dialog_item" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/common_color_F0F0F0" />

    <TextView
        android:id="@+id/tvCancel"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:gravity="center"
        android:text="@string/common_cancel"
        android:textColor="@color/common_color_858585"
        android:textSize="@dimen/common_text_sp_16" />

</LinearLayout>