plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}
apply from: "$rootDir/gradle/common_library.gradle"

android {
    namespace = "com.kanzhun.marry.lib_share"
    defaultConfig {
//        resourcePrefix 'share_'
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    api deps.wechat
    implementation project(':lib_http_retrofit')
    implementation deps.androidx.app_compat
    implementation deps.androidx.recyclerview
    implementation project(":lib_foundation")

}