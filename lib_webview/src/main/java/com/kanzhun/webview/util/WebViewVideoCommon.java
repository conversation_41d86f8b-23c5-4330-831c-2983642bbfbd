package com.kanzhun.webview.util;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ProgressBar;

import com.kanzhun.common.base.AllBaseActivity;
import com.kanzhun.foundation.SystemConfigInstance;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.util.FileProviderUtils;
import com.kanzhun.common.util.LText;
import com.kanzhun.common.util.MediaFile;
import com.kanzhun.common.util.OpenFileUtils;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.bean.WebVideoPlayBean;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.StringUtils;
import com.kanzhun.webview.HiWebChromeClient;
import com.kanzhun.webview.O2WebViewClient;
import com.kanzhun.webview.WebViewDownloadListener;
import com.kanzhun.webview.WebViewLifecycle;
import com.kanzhun.webview.callback.IWebVideoView;

import java.io.File;
import java.lang.reflect.Method;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * <AUTHOR>
 * @date 2020/10/21.
 */
public class WebViewVideoCommon implements WebViewLifecycle<IWebVideoView> {
    IWebVideoView iWebView;
    private AllBaseActivity activity;
    private String failUrl;
    private String mCurrentUrl;

    @Override
    public AllBaseActivity getActivity() {
        return activity;
    }

    @Override
    public View getContentView() {
        return iWebView.getParentView();
    }

    @Override
    public boolean onCreate(AllBaseActivity activity, IWebVideoView iBaseWebView, Intent intent) {
        this.activity = activity;
        this.iWebView = iBaseWebView;
        WebVideoPlayBean webViewBean = (WebVideoPlayBean) intent.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
        if (activity == null || webViewBean == null || TextUtils.isEmpty(webViewBean.getPath())) {
            return false;
        }
        String path = webViewBean.getLocalPath();
        File file = new File(path);
        boolean exists = file.exists();
        iWebView.onIWebCreate(webViewBean, exists);
        if (file.exists()) {
            if (MediaFile.isVideoFileType(path)) {
                initWebViewOption();
                if (StringUtils.isChinese(path)) {
                    Uri uri = FileProviderUtils.getUriForFile(activity, file);
                    load("" + uri);
                } else {
                    load("file://" + path);
                }
            } else {
                OpenFileUtils.openFile(BaseApplication.getApplication(), file);
                return false;
            }
        }
        return true;
    }

    public boolean playVideo(String path) {
        File file = new File(path);
        if (file.exists()) {
            if (MediaFile.isVideoFileType(path)) {
                initWebViewOption();
                if (StringUtils.isChinese(path)) {
                    Uri uri = FileProviderUtils.getUriForFile(activity, file);
                    load("" + uri);
                } else {
                    load("file://" + path);
                }
                return true;
            } else {
                OpenFileUtils.openFile(BaseApplication.getApplication(), file);
                return false;
            }
        }
        return true;
    }

    private void initWebViewOption() {
        WebView webView = getWebView();
        if (webView == null) return;
        WebSettings settings = webView.getSettings();
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setJavaScriptEnabled(true);
        settings.setDefaultTextEncodingName("UTF-8");
        settings.setBuiltInZoomControls(true);
        settings.setDomStorageEnabled(true);
        settings.setDatabaseEnabled(true);

        settings.setMediaPlaybackRequiresUserGesture(false);
        settings.setPluginState(WebSettings.PluginState.ON);
        settings.setAllowFileAccess(true);
        settings.setTextZoom(100);
//        if(SystemConfigInstance.INSTANCE.getHitWebOpAbExperiment()){
            settings.setCacheMode(android.webkit.WebSettings.LOAD_DEFAULT);
//        }


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            settings.setAllowFileAccessFromFileURLs(false);
            settings.setAllowUniversalAccessFromFileURLs(false);
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }


        String appCachePath = BaseApplication.getApplication().getCacheDir().getAbsolutePath();
//        settings.setAppCachePath(appCachePath);
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            settings.setDatabasePath("/data/data/" + webView.getContext().getPackageName() + "/databases/");
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 在5.0版本及以上，Android默认不允许https站点内访问一个http的资源，设置此属性可以解决
            settings.setMixedContentMode(android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        webView.setWebViewClient(new O2WebViewClient(this,webView));
        setZoomControlGone(settings, new Object[]{false});
        webView.removeJavascriptInterface("searchBoxJavaBridge_");
        webView.setWebChromeClient(new HiWebChromeClient(this));
        webView.setDownloadListener(new WebViewDownloadListener(activity));
    }

    private void setZoomControlGone(WebSettings view, Object[] args) {
        Class classType = view.getClass();
        try {
            Class[] argsClass = new Class[args.length];

            for (int i = 0, j = args.length; i < j; i++) {
                argsClass[i] = args[i].getClass();
            }
            Method[] ms = classType.getMethods();
            for (int i = 0; i < ms.length; i++) {
                if (ms[i].getName().equals("setDisplayZoomControls")) {
                    try {
                        ms[i].invoke(view, false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 加载URL
     *
     * @param url
     */
    void load(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (url.startsWith("file:/") || url.startsWith("content:/")) {
            WebView webview = getWebView();
            if (webview != null) {
                webview.loadUrl(url);
            }
            return;
        }

        syncCookie(url);
        url = handlerWebUrlRule(url);
        WebView webview = getWebView();
        if (webview != null) {
            webview.loadUrl(url);
        }
    }

    private void syncCookie(String url) {
        if (activity == null) {
            return;
        }
        URL cookUrl = null;
        try {
            cookUrl = new URL(url);
        } catch (MalformedURLException e) {
            e.printStackTrace();
            cookUrl = null;
        }
        if (cookUrl == null) return;
        String host = cookUrl.getHost();
        if (AccountHelper.getInstance().getAccount() == null) {
            return;
        }
        CookieSyncManager cookieSyncManager = CookieSyncManager.createInstance(activity);
        cookieSyncManager.sync();
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.setAcceptCookie(true);

        String cookieUrl = cookUrl.getProtocol() + "://" + cookUrl.getHost() + "/";
        cookieManager.setCookie(cookieUrl, "v=" + SettingBuilder.getInstance().getVersionCode());
        cookieManager.setCookie(cookieUrl, "access_token=" + AccountHelper.getInstance().getAccessToken());
        cookieManager.setCookie(cookieUrl, "token_type=" + AccountHelper.getInstance().getTokenType());
        cookieManager.setCookie(cookieUrl, "sk=" + AccountHelper.getInstance().getAccount().getSk());
        cookieManager.setCookie(cookieUrl, "hitWebOpAbExperiment=" + SystemConfigInstance.INSTANCE.getHitWebOpAbExperiment());

        CookieSyncManager.getInstance().sync();
    }

    public String handlerWebUrlRule(String url) {
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "http://" + url;
        }
        return url;
    }

    @Override
    public void onDestroy() {
        CookieSyncManager cookieSyncManager = CookieSyncManager.createInstance(activity);
        cookieSyncManager.sync();
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.removeAllCookie();
        CookieSyncManager.getInstance().sync();
        FrameLayout parent = iWebView != null ? iWebView.getParentView() : null;
        if (parent != null) {
            parent.removeAllViews();
        }
        WebView webView = getWebView();
        if (webView != null) {
            webView.removeAllViews();
            webView.destroyDrawingCache();
            webView.destroy();
        }
    }

    @Override
    public void onResume() {

    }

    @Override
    public void onError(String errorUrl) {
        failUrl = errorUrl;
        iWebView.onWebViewLoadingError();
    }

    @Override
    public void onLoadStart(String url) {

    }

    @Override
    public void onLoadComplete(String url) {
        WebView webview = getWebView();
        if (webview == null) return;
        if (LText.equal(url, failUrl)) return;
        failUrl = null;
        if (LText.empty(url)) return;
        mCurrentUrl = url;
    }

    @Override
    public void initProgressBar(int progress) {
        if (iWebView == null || iWebView.getProgressBar() == null) return;
        ProgressBar progressBar = iWebView.getProgressBar();
        if (progress == 100) {
            progressBar.setVisibility(View.GONE);
        } else {
            progressBar.setVisibility(View.VISIBLE);
            progressBar.setProgress(progress);
        }
    }

    WebView getWebView() {
        return iWebView != null ? iWebView.getWebView() : null;
    }
}
