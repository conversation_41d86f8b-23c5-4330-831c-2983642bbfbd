package com.kanzhun.webview.util

import android.webkit.WebView
import androidx.appcompat.app.AppCompatActivity
import com.kanzhun.common.kotlin.bean.CallWebViewBean
import com.kanzhun.common.kotlin.constract.LivedataWebview
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.utils.L

class WebviewHandler {
    fun liveEventBusObserve(appCompatActivity: AppCompatActivity,webview: WebView) {
        appCompatActivity.liveEventBusObserve(LivedataWebview.WebView_Call_JS) { it: CallWebViewBean ->
            val name = it.methodName
            val params = it.params ?:""
            val js = "javascript:"+name+"("+params+")"
            webview.loadUrl(js)
            L.e("callJS",js)
        }
    }


}

