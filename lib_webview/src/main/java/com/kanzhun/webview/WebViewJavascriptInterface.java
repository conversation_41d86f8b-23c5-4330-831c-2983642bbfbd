package com.kanzhun.webview;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.webkit.CookieManager;
import android.webkit.JavascriptInterface;
import android.webkit.WebStorage;
import android.webkit.WebView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.kotlin.constract.LivedataKeyMe;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.ProtocolHelper;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.bean.MapNavigationLocation;
import com.kanzhun.foundation.constant.ShareKey;
import com.kanzhun.foundation.map.MapNavigationBottomDialog;
import com.kanzhun.foundation.permission.PermissionCallback;
import com.kanzhun.foundation.permission.PermissionData;
import com.kanzhun.foundation.permission.PermissionHelper;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.foundation.router.CommonPageRouter;
import com.kanzhun.foundation.router.MatchingPageRouter;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.foundation.utils.CalendarCommon;
import com.kanzhun.foundation.utils.WebViewCallUtil;
import com.kanzhun.marry.lib_share.ShareAction;
import com.kanzhun.marry.lib_share.bean.ProfileShareBean;
import com.kanzhun.marry.lib_share.bean.ShareInfo;
import com.kanzhun.marry.lib_share.core.SharePlatForm;
import com.kanzhun.marry.zfbapi.ZFBInstallUtil;
import com.kanzhun.marry.zfbapi.ZFBParams;
import com.kanzhun.marry.zfbapi.ZFBPayUtil;
import com.kanzhun.utils.L;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.configuration.Configuration;
import com.kanzhun.utils.rxbus.RxBus;
import com.techwolf.lib.tlog.TLog;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.schedulers.Schedulers;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.Locale;

import static com.kanzhun.common.kotlin.ext.LiveEventBusExtKt.sendBooleanLiveEvent;
import static com.kanzhun.foundation.utils.ImageSaveUtil.saveImageToGallery;
import static com.kanzhun.marry.pay.PayConstants.*;

/**
 * Created by monch on 15/4/25.
 */
public class WebViewJavascriptInterface {

    private static final String TAG = "WebViewJavascriptInterface";

    private WebViewCommon common;

    private boolean isInterceptJsMethodCall = false;

    protected WebViewJavascriptInterface(WebViewCommon common) {
        this.common = common;
    }

    public void setInterceptJsMethodCall(boolean interceptJsMethodCall) {
        isInterceptJsMethodCall = interceptJsMethodCall;
    }

    /**
     * 获取客户端版本
     *
     * @return
     */
    @JavascriptInterface
    public int getVersion() {
        return SettingBuilder.getInstance().getVersionCode();
    }


    /**
     * "javascript:try{isInterceptColseEvent()}catch(e){}";
     */
    public static String tryJsCatch(String methodAndParams) {
        return String.format(Locale.getDefault(), "javascript:try{%s}catch(e){}", methodAndParams == null ? "" : methodAndParams);
    }

    /**
     * 设置h5title
     *
     * @param title 标题
     */
    @JavascriptInterface
    public void setTitle(String title) {
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                common.getiWebView().setTitle(title);
            }
        });
    }

    /**
     * 隐藏标题栏，全屏显示
     */
    @JavascriptInterface
    public void setFullScreen() {
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                common.getiWebView().setFullScreen();
            }
        });
    }

    @JavascriptInterface
    public void setStatusBarStyle(String status) {
        if (TextUtils.isEmpty(status)) {
            return;
        }
        try {
            JSONObject object = new JSONObject(status);
            boolean isLight = TextUtils.equals("light", object.getString("style")); // 值：light，代表文字白色，否则文字黑色
            ExecutorFactory.execMainTask(() -> {
                if (isLight) {
                    common.getiWebView().switchWhiteStatusBar();
                } else {
                    common.getiWebView().switchTranslucent();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 关闭h5界面
     */
    @JavascriptInterface
    public void closeWindow() {
        Intent intent = new Intent();
        common.getActivity().setResult(Activity.RESULT_OK, intent);
        AppUtil.finishActivity(common.getActivity());
    }

    /**
     * 返回到上一个h5页面
     */
    @JavascriptInterface
    public void goBack() {
        if (common.goBack()) {
            return;
        }
        closeWindow();
    }

    /**
     * 缓存到sp中的boolean值方法
     */
    @JavascriptInterface
    public void saveSpBoolean(String json) {
        if (TextUtils.isEmpty(json)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(json);
            String key = jsonObject.getString("key");
            boolean value = jsonObject.getBoolean("value");
            SpManager.putUserBoolean(key, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取sp中的boolean值
     */
    @JavascriptInterface
    public boolean getSpBoolean(String key) {
        return SpManager.get().user().getBoolean(key, false);
    }

    @JavascriptInterface
    public void matchPreferenceDone() {
        //跳转到F1界面去做新手任务
        RxBus.getInstance().post("switch", Constants.POST_TAG_MATCHING_REQUIREMENT);
    }

    @JavascriptInterface
    public int getStatusBarHeight() {
        L.i("JavascriptInterface:", "getStatusBarHeight");
        int barHeightPx = StatusBarUtil.getStatusBarHeight(common.getActivity());
        float density = common.getActivity().getResources().getDisplayMetrics().density;
        return (int) (barHeightPx / density);
    }

    @JavascriptInterface
    public void jumpToMatchPreference() {
        MatchingPageRouter.jumpMatchRequirementActivity(BaseApplication.getApplication().getTopContext());
        closeWindow();
    }

    /**
     * 恋爱要素设置接口成功回调
     */
    @JavascriptInterface
    public void matchPreferenceRequestSuccess() {
        //恋爱要素设置接口成功刷新状态
        RxBus.getInstance().post("switch", Constants.POST_TAG_MATCH_PREFERENCE_REQUEST_SUCCESS);
    }

    /**
     * 获取TemplateCode
     *
     * @return
     */
    @JavascriptInterface
    public String getTemplateCode() {
        return SettingBuilder.getInstance().getConfigurationValue(Configuration.PERSONALITY_TEST_INVITE_CODE_VISIBLE);
    }

    /**
     * 获取性格测试答题内容
     *
     * @return
     */
    @JavascriptInterface
    public String getTestAnswer() {
        if (common != null) {
            return common.getStringParams().get(Constants.PARAM_ANSWER);
        }
        return "";
    }

    /**
     * 性格测试成功后回调
     */
    @JavascriptInterface
    public void onTestSuccess() {
        RxBus.getInstance().post("refresh", Constants.POST_TAG_PERSONALITY_TEST_SUCCESS);
        sendBooleanLiveEvent(LivedataKeyMe.USER_UPDATE_QUESTION_TEST_COMPLETED, true, true, true);
    }

    @JavascriptInterface
    public void reTest() {
        MePageRouter.jumpMBTITestActivity(common.getActivity());
        closeWindow();
    }

    @JavascriptInterface
    public void jumpToChat(String json) {
        try {
            JSONObject jsonObject = new JSONObject(json);
            String chatId = jsonObject.optString("chatId");
            ChatPageRouter.jumpToSingleChatActivityForH5(common.getActivity(), chatId);
            closeWindow();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @JavascriptInterface
    public void share(String paramJson) {
        try {
            JSONObject object = new JSONObject(paramJson);
            //        ProfileShareBean profileShareBean = new Gson().fromJson(params,ProfileShareBean.class);
            new ShareAction(common.getActivity(), null).share(object.optString("userId"), object.optString("type"));
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    @JavascriptInterface
    public void shareOneItem(String paramJson) {
        try {
            JSONObject object = new JSONObject(paramJson);
            ProfileShareBean profileShareBean = new Gson().fromJson(paramJson, ProfileShareBean.class);
            if (LList.getCount(profileShareBean.getShareInfoList()) == 1) {
                ShareInfo shareInfo = profileShareBean.getShareInfoList().get(0);
                if (shareInfo.getSharePlatform() == 1 || shareInfo.getSharePlatform() == 2) {
                    IWXAPI api = WXAPIFactory.createWXAPI(common.getActivity(), ShareKey.getWxAppId());
                    if (!api.isWXAppInstalled()) {
                        WebViewCallUtil.Companion.callShareOneItemResult(common.getActivity(), -1, "未安装微信");
                        T.ss("未安装微信");
                        return;
                    }
                }
            }
            new ShareAction(common.getActivity(), null).shareWithBean(profileShareBean);
            WebViewCallUtil.Companion.callShareOneItemResult(common.getActivity(), 0, "");
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    @JavascriptInterface
    public void jumpToHelpAndFeedBack() {
        if (common != null && common.getActivity() != null) {
            AppUtil.startUri(common.getActivity(), MePageRouter.SETTING_HELP_ACTIVITY);
        }
    }

    @JavascriptInterface
    public void openWeb(String paramJson) {
        try {
            JSONObject object = new JSONObject(paramJson);
            ProtocolHelper.parseProtocol(object.optString("url"));
            if (object.optBoolean("close", false)) {
                closeWindow();
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    @JavascriptInterface
    public void downloadImage(String imageUrl) {
        PermissionHelper.getAlbumHelper(common.getActivity()).setPermissionCallback(new PermissionCallback<PermissionData>() {
            @Override
            public void onResult(boolean yes, @NonNull PermissionData permission) {
                if (yes) {
                    downloadImageHavaPermission(imageUrl);
                } else {
                    if (common.getActivity() instanceof AppCompatActivity) {
                        WebViewCallUtil.Companion.callDownloadImageResult((AppCompatActivity) common.getActivity());
                    }
                }
            }
        }).requestPermission();

    }

    private void downloadImageHavaPermission(String imageUrl) {
        Observable.just(imageUrl).map(new Function<String, Boolean>() {
                    @Override
                    public Boolean apply(String url) {
                        boolean isSuccess = false;
                        try {
                            if (common.getActivity() == null) {
                                return isSuccess;
                            }
                            File file = Glide.with(common.getActivity()).downloadOnly().load(url).submit().get();
                            if (file != null) {
                                saveImageToGallery(common.getActivity(), file, url, true);
                                isSuccess = true;
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        return isSuccess;
                    }
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Boolean>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(Boolean isSuccess) {
                        if (!isSuccess) {
                            T.ss(com.kanzhun.foundation.R.string.common_save_pic_fail);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        T.ss(com.kanzhun.foundation.R.string.common_save_pic_fail);
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    @JavascriptInterface
    public void openLocation(String data) {
        try {
            JSONObject obj = new JSONObject(data);
            double latitude = obj.optDouble("latitude");
            double longitude = obj.optDouble("longitude");
            String address = obj.optString("address");
            String name = obj.optString("name");
            int scale = obj.optInt("scale");
            MapNavigationLocation navigationLocation = new MapNavigationLocation(latitude, longitude, name, null);
            new MapNavigationBottomDialog(common.getActivity()).show(null, navigationLocation, 1);

        } catch (Exception e) {
            TLog.debug(TAG, "openLocation error:" + e.getMessage() + ",data:" + data);
        }
    }

    @JavascriptInterface
    public void openScanQR(String data) {
        L.i("JavascriptInterface:", "openScanQR:" + data);
        try {
            JSONObject obj = new JSONObject(data);
            String sourceType = obj.optString("sourceType", "");
            int needResult = obj.optInt("needResult", 0);
            CommonPageRouter.Companion.jumpToScanActivity(common.getActivity(), PageSource.NONE, "", sourceType, needResult);
        } catch (Exception e) {
            TLog.debug(TAG, "openLocation error:" + e.getMessage() + ",data:" + data);
        }
    }

    @JavascriptInterface
    public void openNewbieTaskGuider() {
        MePageRouter.showNewUserTaskBlockDialog(common.getActivity());
    }

    public static boolean jumpToFaceAuth = false;

    @JavascriptInterface
    public void jumpToFaceAuth(String str) {
        jumpToFaceAuth = true;
        WebViewJSInterface.Companion.jumpToFaceAuthStatic(str, common);
    }

    public static boolean sendSMS = false;

    @JavascriptInterface
    public void sendSMS(String str) {
        sendSMS = true;
        WebViewJSInterface.Companion.sendSMSStatic(str, common);
    }

    @JavascriptInterface
    public void logout() {
        WebViewJSInterface.Companion.logoutStatic(common);
    }

    @JavascriptInterface
    public void securityFrameworkClose() {
        TLog.info("SecurityNoticeManager", "securityFrameworkClose");
        WebViewJSInterface.Companion.securityFrameworkCloseStatic(common);
    }

    @JavascriptInterface
    public String getDuid() {
        return WebViewJSInterface.Companion.getDuidStatic();
    }

    @JavascriptInterface
    public void clearWebviewCache() {
        ExecutorFactory.execLocalTask(() -> {
            WebStorage storage = WebStorage.getInstance();
            storage.deleteAllData();
            CookieManager c = CookieManager.getInstance();
        });
    }


    /**
     * 设置日历提醒 js bridge协议名称
     * storeCalendar
     * <p>
     * 支持参数：
     * title        标题
     * notes        备注
     * startTime   开始时间(时间戳 ms)
     * endTime     开始时间(时间戳 ms)
     * remindMinute 提前提醒时间 (单位分钟)
     * <p>
     * 回调js方法名称
     * storeCalendarCallback
     */
    @JavascriptInterface
    public void storeCalendar(String data) {
        L.i("JavascriptInterface:", "storeCalendar:" + data);
        try {
            JSONObject obj = new JSONObject(data);
            String title = obj.optString("title", "");
            String notes = obj.optString("notes", "");
            long startTime = obj.optLong("startTime", 0);
            long endTime = obj.optLong("endTime", 0);
            int remindMinute = obj.optInt("remindMinute", 0);

            PermissionHelper.getCalendarHelper(common.getActivity())
                    .setPermissionCallback((yes, permission) -> {
                        if (yes) {
                            long eventId = new CalendarCommon(common.getActivity()).addEvent2(title,
                                    notes,
                                    startTime,
                                    endTime,
                                    remindMinute);
                            WebViewCallUtil.Companion.callStoreCalendarResult(common.getActivity(), eventId > 0 ? CODE_SUCCESS : CODE_FAILED, eventId > 0 ? "" : "添加日历失败");
                            TLog.info("CalendarAction", "h5 add calendar has permission, code is %s", eventId > 0 ? CODE_SUCCESS : CODE_FAILED);
                        } else {
                            WebViewCallUtil.Companion.callStoreCalendarResult(common.getActivity(), CODE_UNAUTHORIZED, "用户未授权日历权限");
                            TLog.info("CalendarAction", "h5 add calendar has not  permission, code is %s", CODE_UNAUTHORIZED);
                        }
                    }).requestPermission();

        } catch (Exception e) {
        }

    }

    private static final int CODE_SUCCESS = 0; // 添加日历成功
    private static final int CODE_UNAUTHORIZED = 1; // 用户未授权日历权限
    private static final int CODE_FAILED = 2; // 添加日历失败


    @JavascriptInterface
    public void shareOneMedia(@Nullable String jsonData) { // { "imageUrl": "", "type": "" } // 0: 微信好友，默认值 1: 微信朋友圈
        try {
            JSONObject obj = new JSONObject(jsonData);
            String imageUrl = obj.optString("imageUrl");
            int type = obj.optInt("type");

            SharePlatForm shareType;
            if (type == 0) {
                shareType = SharePlatForm.WECHAT;
            } else if (type == 1) {
                shareType = SharePlatForm.WEMOMENT;
            } else {
                shareType = SharePlatForm.WECHAT;
                TLog.error(TAG, "shareOneMedia error, type is invalid");
            }

            // http://gips3.baidu.com/it/u=3886271102,3123389489&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960
            WebViewJSInterface.shareOneMedia(common, imageUrl, shareType);
        } catch (Throwable e) {
            TLog.error(TAG, "shareOneMedia error", e);
        }
    }

    /**
     * 在 app 内拉起企微获客链接
     *
     * @param json 要拉起的企微获客链接，如，weixin://biz/ww/profile/https%3A%2F%2Fwork.weixin.qq.com%2Fca%2Fcawcded88e653d2833
     */
    @JavascriptInterface
    public void launchWxCustomerAcquisition(@Nullable String json) {
        try {
            // {"url":"weixin://biz/ww/profile/https%3A%2F%2Fwork.weixin.qq.com%2Fca%2Fcawcde8a02bcea9ee8"}
            if (json == null) {
                TLog.error(TAG, "launchWxCustomerAcquisition error: json is null");
                return;
            }

            // 从 json 中解析出 url
            String url;
            try {
                url = new JSONObject(json).optString("url");
            } catch (Throwable e) {
                TLog.error(TAG, "launchWxCustomerAcquisition json error" + e);
                return;
            }

            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_VIEW);
            intent.setData(Uri.parse(url));
            AppUtil.startActivity(common.getActivity(), intent);
        } catch (Throwable e) {
            T.ss("请安装微信最新版！");

            TLog.error(TAG, "launchWxCustomerAcquisition error" + e);
        }
    }

    @JavascriptInterface
    public void initiatePayment(String data) {
        L.i("JavascriptInterface:", "pay:" + data);
        try {
            JSONObject obj = new JSONObject(data);
//            String orderId = obj.optString("orderId", "");
            int type = obj.optInt("paymentType", 0);

            if (type == APP_PAY_PLATFORM_WX) {
                //微信支付
                IWXAPI api = WXAPIFactory.createWXAPI(common.getActivity(), ShareKey.getWxAppId());
                if (!api.isWXAppInstalled()) {
                    TLog.info(TAG, "未安装微信");
                    WebViewCallUtil.Companion.callPayResult(common.getActivity(), APP_PAY_NO_INSTALLED, "", APP_PAY_PLATFORM_WX);
                    return;
                }
                WebViewJavascriptInterfaceKKt.openWeiXinPayPage(common.getActivity(), obj.getString("paramMap"));
            } else if (type == APP_PAY_PLATFORM_ZFB) {
                boolean zfbAppInstalled = ZFBInstallUtil.isZFBAppInstalled(common.getActivity());
                if (!zfbAppInstalled) {
                    TLog.info(TAG, "未安装支付宝");
                    WebViewCallUtil.Companion.callPayResult(common.getActivity(), APP_PAY_NO_INSTALLED, "", APP_PAY_PLATFORM_ZFB);
                    return;
                }

                JSONObject payInfo = obj.getJSONObject("paramMap");
                String orderStr = payInfo.optString("orderStr", "");
                ZFBParams zfbOrderParams = ZFBParams.objPay()
                        .setZfbPayOrderInfo(orderStr);
                if (zfbOrderParams != null && !TextUtils.isEmpty(zfbOrderParams.zfbPayOrderInfo)) {
                    ZFBPayUtil.startDeltaPay(common.getActivity(),
                            zfbOrderParams.zfbPayOrderInfo, zfbOrderParams.isDisableZFBPayLoading);
                } else {
                    TLog.info(TAG, "参数非法");
                    WebViewCallUtil.Companion.callPayResult(common.getActivity(), APP_PAY_PARAM_ERROR, "", APP_PAY_PLATFORM_ZFB);
                }
            }
        } catch (Exception e) {
            TLog.info(TAG, "openLocation error:" + e.getMessage() + ",data:" + data);
            WebViewCallUtil.Companion.callPayResult(common.getActivity(), APP_PAY_PARAM_ERROR, "", APP_PAY_PLATFORM_ZFB);
        }

    }

    /**
     * 发送支付宝支付结果
     * webView.loadUrl("javascript:zfbPayResult(" + resultCode + ")");
     */
    public void postZFBPayResult(int resultCode, String meno) {
        if (common == null) return;
        WebView webView = common.getWebView();
        if (webView == null) return;
        try {
            L.i("postZFBPayResult", "initiatePaymentResult(" + resultCode + ")");
            WebViewCallUtil.Companion.callPayResult(common.getActivity(), resultCode, meno, APP_PAY_PLATFORM_ZFB);
        } catch (Exception e) {
            L.i("postZFBPayResult", "失败调用payZFBResult(" + resultCode + ")");
        }
    }

    public void postWxPayResult(int resultCode, String msg) {
        if (common == null) return;
        WebView webView = common.getWebView();
        if (webView == null) return;
        try {
            L.i("postZFBPayResult", "initiatePaymentResult(" + resultCode + ")");
            WebViewCallUtil.Companion.callPayResult(common.getActivity(), resultCode, msg, APP_PAY_PLATFORM_WX);
            L.i("postWxPayResult", "成功调用payResult(" + resultCode + ")");
        } catch (Exception e) {
            L.i("postWxPayResult", "失败调用payResult(" + resultCode + ")");
        }
    }

}
