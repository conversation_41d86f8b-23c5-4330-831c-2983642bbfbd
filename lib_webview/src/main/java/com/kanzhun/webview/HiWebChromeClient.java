package com.kanzhun.webview;

import android.Manifest;
import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.View;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebView;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;
import androidx.fragment.app.FragmentActivity;

import com.common.AvoidOnResult;
import com.techwolf.lib.tlog.TLog;
import com.kanzhun.common.dialog.BottomListDialog;
import com.kanzhun.common.dialog.model.SelectBottomBean;
import com.kanzhun.foundation.permission.PermissionCallback;
import com.kanzhun.foundation.permission.PermissionData;
import com.kanzhun.foundation.permission.PermissionHelper;
import com.kanzhun.foundation.permission.PermissionManager;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.LText;
import com.kanzhun.foundation.Constants;
import com.kanzhun.utils.base.LList;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by monch on 15/5/20.
 */
public class HiWebChromeClient extends WebChromeClient implements AvoidOnResult.Callback, BottomListDialog.OnBottomItemClickListener, DialogInterface.OnDismissListener {
    private static final String TAG = "HiWebChromeClient";

    private WebViewLifecycle common;
    private ValueCallback<Uri[]> uploadFilesCallback;
    private ValueCallback<Uri> uploadFileCallback;
    private boolean noDismissClean;
    private boolean multiple;
    // Store parameters for delayed file chooser invocation after permission grant
    private String[] pendingAcceptType;
    private boolean pendingIsCapture;
    private boolean pendingMultiple;
    // Store callbacks to preserve them during permission request
    private ValueCallback<Uri[]> pendingUploadFilesCallback;
    private ValueCallback<Uri> pendingUploadFileCallback;
    // Flag to track if we're in the middle of a permission request
    private boolean isRequestingPermission = false;
    private FileChooserIntercept fileChooserIntercept = new FileChooserIntercept() {
        @Override
        public boolean onFileChooserIntercept(boolean isCapture, String[] acceptType) {

            // 要使用摄像机,判断权限 android.permission.CAMERA
            // 要使用照相机,判断权限 android.permission.CAMERA
            String[] permissions = getCameraAndMediaPermissions();
            if (PermissionManager.checkAllSelfPermissions(common.getActivity(), permissions)) {
                return false;
            }

            // Set flag to indicate we're requesting permission
            isRequestingPermission = true;

            // Store parameters and callbacks for later use after permission is granted
            pendingAcceptType = acceptType;
            pendingIsCapture = isCapture;
            pendingMultiple = multiple;
            pendingUploadFilesCallback = uploadFilesCallback;
            pendingUploadFileCallback = uploadFileCallback;

            // We need to clear the current callbacks to prevent them from being called
            // when we call openFileChooseProcess after permission is granted
            uploadFilesCallback = null;
            uploadFileCallback = null;

            PermissionHelper.getH5PermissionHelper((FragmentActivity) common.getActivity(), permissions).setPermissionCallback(new PermissionCallback<PermissionData>() {
                @Override
                public void onResult(boolean yes, @NonNull PermissionData permission) {
                    if (yes) {
                        // Permission granted, now invoke the file chooser process
                        ExecutorFactory.execMainTask(new Runnable() {
                            @Override
                            public void run() {
                                // Restore callbacks before invoking file chooser
                                uploadFilesCallback = pendingUploadFilesCallback;
                                uploadFileCallback = pendingUploadFileCallback;
                                pendingUploadFilesCallback = null;
                                pendingUploadFileCallback = null;

                                // Reset the permission request flag
                                isRequestingPermission = false;

                                // Now invoke the file chooser process
                                openFileChooseProcess(pendingAcceptType, pendingIsCapture, pendingMultiple);
                            }
                        });
                    } else {
                        // Permission denied, reset callbacks
                        if (pendingUploadFilesCallback != null || pendingUploadFileCallback != null) {
                            ValueCallback<Uri[]> tempFilesCallback = pendingUploadFilesCallback;
                            ValueCallback<Uri> tempFileCallback = pendingUploadFileCallback;
                            pendingUploadFilesCallback = null;
                            pendingUploadFileCallback = null;

                            // Reset the permission request flag
                            isRequestingPermission = false;

                            // Now reset the callbacks
                            if (tempFilesCallback != null) {
                                tempFilesCallback.onReceiveValue(null);
                            } else if (tempFileCallback != null) {
                                tempFileCallback.onReceiveValue(null);
                            }
                        }
                    }
                }
            }).requestPermission();
            return true; // 拦截
        }
    };

    private String[] getCameraAndMediaPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return new String[]{Manifest.permission.CAMERA, Manifest.permission.READ_MEDIA_IMAGES, Manifest.permission.READ_MEDIA_VIDEO};
        } else {
            return new String[]{Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
        }
    }

    public HiWebChromeClient(WebViewLifecycle common) {
        this.common = common;
    }

    public void setFileChooserIntercept(FileChooserIntercept fileChooserIntercept) {
        this.fileChooserIntercept = fileChooserIntercept;
    }

    @Override
    public void onReceivedTitle(WebView view, String title) {
        String t = " ";
        if (!LText.empty(title)) {
            t = title;
        }
    }

    @Override
    public void onProgressChanged(WebView view, int newProgress) {
        if (common != null) {
            common.initProgressBar(newProgress);
        }
        super.onProgressChanged(view, newProgress);
    }

    // For Android < 3.0
    public void openFileChooser(ValueCallback<Uri> valueCallback) {
        uploadFileCallback = valueCallback;
        openFileChooseProcess(new String[]{"*/*"}, false);
    }

    // For Android  >= 3.0
    public void openFileChooser(ValueCallback<Uri> valueCallback, String acceptType) {
        uploadFileCallback = valueCallback;
        if (TextUtils.isEmpty(acceptType)) {
            openFileChooseProcess(new String[]{"*/*"}, false);
        } else {
            openFileChooseProcess(new String[]{acceptType}, false);
        }
    }

    //For Android  >= 4.1
    public void openFileChooser(ValueCallback<Uri> valueCallback, String acceptType, String capture) {
        uploadFileCallback = valueCallback;
        boolean isCapture = !TextUtils.isEmpty(capture);
        if (TextUtils.isEmpty(acceptType)) {
            openFileChooseProcess(new String[]{"*/*"}, isCapture);
        } else {
            openFileChooseProcess(new String[]{acceptType}, isCapture);
        }
    }

    // For Android >= 5.0
    @Override
    public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback,
                                     FileChooserParams fileChooserParams) {
        uploadFilesCallback = filePathCallback;
        boolean isCapture = fileChooserParams.isCaptureEnabled();
        boolean multiple = fileChooserParams.getMode() == FileChooserParams.MODE_OPEN_MULTIPLE;
        if (fileChooserParams.getAcceptTypes() != null && fileChooserParams.getAcceptTypes().length > 0) {
            if (fileChooserParams.getAcceptTypes().length == 1 && TextUtils
                    .isEmpty(fileChooserParams.getAcceptTypes()[0])) {
                openFileChooseProcess(new String[]{"*/*"}, isCapture, multiple);
            } else {
                openFileChooseProcess(fileChooserParams.getAcceptTypes(), isCapture, multiple);
            }
        } else {
            openFileChooseProcess(new String[]{"*/*"}, isCapture, multiple);
        }
        return true;
    }

    /**
     * type 类型 :多个 "video/;image/" 单个 "image/*"
     * intent.setType(“video/;image/”);//同时选择视频和图片
     * i.setType("image/*");//图片
     *
     * @param acceptType 类型
     */
    private void openFileChooseProcess(String[] acceptType, boolean isCapture, boolean multiple) {
        this.multiple = multiple;
        StringBuilder typeBuilder = new StringBuilder();
        for (int i = 0; i < acceptType.length; i++) {
            typeBuilder.append(acceptType[i]);
            if (i < acceptType.length - 1) {
                typeBuilder.append(";");
            }
        }
        String acceptTypeString = typeBuilder.toString();
        TLog.info(TAG, "acceptTypeString : %s", acceptTypeString);
        try {
            if (fileChooserIntercept != null) {
                if (fileChooserIntercept.onFileChooserIntercept(isCapture, acceptType)) {
                    //没有权限清空回调状态
                    resetCallBack(null, null);
                    return;
                }
            }
            if (acceptTypeString.contains("video/")) {//默认打开后置摄像头
                List<SelectBottomBean> list = new ArrayList();
                list.add(new SelectBottomBean(Constants.CAPTURE_VIDEO, common.getActivity().getResources().getString(R.string.common_video), R.drawable.common_ic_capture_video));
                list.add(new SelectBottomBean(Constants.SELECT_FILE, common.getActivity().getResources().getString(R.string.common_file), R.drawable.common_ic_upload_file));
                showSelectDialog(list, 2);
            } else if (acceptTypeString.contains("image/")) {//图片或者文件
                ExecutorFactory.execMainTask(new Runnable() {
                    @Override
                    public void run() {
                        List<SelectBottomBean> list = new ArrayList();
                        list.add(new SelectBottomBean(Constants.CAPTURE_PHOTO, common.getActivity().getResources().getString(R.string.common_capture), R.drawable.common_ic_capture_video));
                        list.add(new SelectBottomBean(Constants.SELECT_ALBUM, common.getActivity().getResources().getString(R.string.common_album), R.drawable.common_ic_upload_file));
                        showSelectDialog(list, 2);
                    }
                });
            } else if (acceptTypeString.contains("imagecapture/")) {//自定义capture类型，仅仅调用拍照
                selectFromPhoto();
            } else {
                selectFile(acceptTypeString);
            }
        } catch (Exception e) {//当系统没有相机应用的时候该应用会闪退,所以 try catch
            e.printStackTrace();
            //h5的动作要有一致性, uploadFiles 赋值之后必须要有 onReceiveValue(),不然会影响其他功能
            resetCallBack(null, null);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode != Activity.RESULT_OK) {//取消操作，重置，否则会影响下次调用
            resetCallBack(null, null);
            return;
        }
        if (null == uploadFileCallback && null == uploadFilesCallback) {
            clean();
            return;
        }
        Uri result = data == null ? null : data.getData();
        Uri[] uris = result == null ? null : new Uri[]{result};
        resetCallBack(uris, result);
    }

    /**
     * type 类型 :多个 "video/;image/" 单个 "image/*"
     * intent.setType(“video/;image/”);//同时选择视频和图片
     * i.setType("image/*");//图片
     *
     * @param acceptType 类型
     */
    private void openFileChooseProcess(String[] acceptType, boolean isCapture) {
        openFileChooseProcess(acceptType, isCapture, false);
    }


    /**
     * 重新设置callback回调
     *
     * @param uris 为空代表清空
     * @param uri  为空代表清空
     */
    private void resetCallBack(Uri[] uris, Uri uri) {
        // If we're in the middle of a permission request, don't reset the callbacks
        // as they will be handled by the permission callback
        if (isRequestingPermission) {
            return;
        }

        // Make local copies and clear the original references to prevent multiple calls
        ValueCallback<Uri[]> tempFilesCallback = uploadFilesCallback;
        ValueCallback<Uri> tempFileCallback = uploadFileCallback;
        uploadFilesCallback = null;
        uploadFileCallback = null;

        // Now call the callbacks with the provided values
        if (tempFilesCallback != null) {
            try {
                tempFilesCallback.onReceiveValue(uris);
                TLog.info(TAG, "resetUris : %s", uris == null ? "null" : uris.toString());
            } catch (Exception e) {
                TLog.error(TAG, "Error calling uploadFilesCallback: %s", e.getMessage());
            }
        } else if (tempFileCallback != null) {
            try {
                tempFileCallback.onReceiveValue(uri);
                TLog.info(TAG, "resetUri : %s", uri == null ? "null" : uri.toString());
            } catch (Exception e) {
                TLog.error(TAG, "Error calling uploadFileCallback: %s", e.getMessage());
            }
        }

        clean();
    }

    /**
     * 清除旧状态操作
     */
    private void clean() {
        uploadFilesCallback = null;
        uploadFileCallback = null;
        noDismissClean = false;
        multiple = false;
        // Don't clear pending callbacks here as they might be needed after permission grant
        // Only reset the permission request flag if we're not in the middle of a permission callback
        if (!isRequestingPermission) {
            pendingUploadFilesCallback = null;
            pendingUploadFileCallback = null;
        }
    }

    private Uri getUri(File photoFile) {
        Uri fileUri = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                fileUri = FileProvider
                        .getUriForFile(common.getActivity(),
                                common.getActivity().getPackageName() + ".fileprovider", photoFile);
            } catch (Exception e) {

            }
        } else {
            fileUri = Uri.fromFile(photoFile);
        }
        return fileUri;
    }

    /**
     * 显示选择操作弹窗
     *
     * @param list      item数据
     * @param spanCount 列数
     */
    private void showSelectDialog(List<SelectBottomBean> list, int spanCount) {
        BottomListDialog dialog = new BottomListDialog.Builder(common.getActivity())
                .setItemLayout(R.layout.common_item_bottom_select_dialog)
                .setData(list)
                .setSpanCount(2)
                .setCanceledOnTouchOutside(true)
                .setOnBottomItemClickListener(this)
                .create();
        dialog.setOnDismissListener(this);
        dialog.show();
    }

    @Override
    public void onBottomItemClick(View view, int pos, SelectBottomBean bottomBean) {
        switch (bottomBean.type) {
            case Constants.CAPTURE_VIDEO:
                noDismissClean = true;
                Intent intent = new Intent();
                intent.setAction(MediaStore.ACTION_VIDEO_CAPTURE);
                intent.putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 1);
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                AvoidOnResult.of(common.getActivity()).startForResult(intent, 0, this);
                break;
            case Constants.SELECT_FILE:
                noDismissClean = true;
                selectFile("*/*");
                break;
            case Constants.CAPTURE_PHOTO:
                selectFromPhoto();
                break;
            case Constants.SELECT_ALBUM:
                selectFromAlbum();
                break;
        }
    }

    /**
     * 手机中选择文件
     *
     * @param acceptTypeString 文件类型
     */
    private void selectFile(String acceptTypeString) {
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_GET_CONTENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType(acceptTypeString);
        intent = Intent.createChooser(intent, "File Chooser");
        AvoidOnResult.of(common.getActivity()).startForResult(intent, 0, this);
    }

    /**
     * 相册中选择图片
     */
    public void selectFromAlbum() {
        noDismissClean = true;
        PhotoSelectManager.jumpForGalleryFromResult(common.getActivity(), multiple ? 9 : 1, new PhotoSelectManager.OnGalleryCountCallBack() {
            @Override
            public void onGalleryListener(List<Uri> fileList, boolean originalEnable) {
                if (LList.isEmpty(fileList)) {
                    resetCallBack(null, null);
                    return;
                }
                Uri[] uris = new Uri[fileList.size()];
                for (int i = 0; i < fileList.size(); i++) {
                    uris[i] = fileList.get(i);
                    try {
                        if (uris[i] != null) {
                            TLog.info(TAG, "uri : %s , pos : %d", uris[i].toString(), i);
                        } else {
                            TLog.info(TAG, "uri : %s , pos : %d", "null", i);
                        }
                    } catch (Exception e) {

                    }
                }
                resetCallBack(uris, uris[0]);
            }
        });
    }

    /**
     * 调用相机拍照
     */
    public void selectFromPhoto() {
        noDismissClean = true;
        PhotoSelectManager.jumpForCameraResult(common.getActivity(), new PhotoSelectManager.OnCameraCallBack() {
            @Override
            public void onCameraCallback(File cameraFilePath) {
                if (cameraFilePath == null) {
                    resetCallBack(null, null);
                    return;
                }
                Uri fileUri = getUri(cameraFilePath);
                TLog.info(TAG, "cameraUri : %s", fileUri == null ? "null" : fileUri.toString());
                resetCallBack(new Uri[]{fileUri}, fileUri);
            }
        });
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        if (!noDismissClean) {
            resetCallBack(null, null);
        }
    }

    public interface FileChooserIntercept {

        /**
         * @param isCapture  是否是照相功能
         * @param acceptType input标签 acceptType的属性
         * @return 是否要拦截
         */
        boolean onFileChooserIntercept(boolean isCapture, String[] acceptType);
    }
}

