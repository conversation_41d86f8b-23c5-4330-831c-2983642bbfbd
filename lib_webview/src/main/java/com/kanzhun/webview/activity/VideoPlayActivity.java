package com.kanzhun.webview.activity;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;

import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.foundation.permission.PermissionCallback;
import com.kanzhun.foundation.permission.PermissionData;
import com.kanzhun.foundation.permission.PermissionHelper;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.OpenFileUtils;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.bean.WebVideoPlayBean;
import com.kanzhun.foundation.router.AppPageRouter;
import com.kanzhun.foundation.viewmodel.FileDownLoadViewModel;
import com.kanzhun.utils.T;
import com.kanzhun.webview.BR;
import com.kanzhun.webview.R;
import com.kanzhun.webview.callback.IWebVideoView;
import com.kanzhun.webview.callback.VideoPlayCallback;
import com.kanzhun.webview.databinding.WebviewActivityVideoPlayBinding;
import com.kanzhun.webview.util.WebViewVideoCommon;
import com.kanzhun.webview.viewmodel.VideoPlayViewModel;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2020/10/21.
 */
@RouterUri(path = AppPageRouter.VIDEO_PLAY_ACTIVITY)
public class VideoPlayActivity extends FoundationVMActivity<WebviewActivityVideoPlayBinding, VideoPlayViewModel> implements IWebVideoView, VideoPlayCallback {
    private WebViewVideoCommon common;
    private WebVideoPlayBean mBean;

    @Override
    public int getContentLayoutId() {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        return R.layout.webview_activity_video_play;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mBean = (WebVideoPlayBean) getIntent().getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE);
        common = new WebViewVideoCommon();
        if (!common.onCreate(this, this, getIntent())) {
            AppUtil.finishActivity(this);
        }
        getViewModel().getSaveFileLiveData().observe(this, new Observer<File>() {
            @Override
            public void onChanged(File file) {
                if (file != null && file.exists()) {
                    sendToPhone(file);
                }
            }
        });
    }

    @Override
    public void onIWebCreate(WebVideoPlayBean webViewBean, boolean downLoad) {
        getViewModel().init(webViewBean);
        if (downLoad) {
            getViewModel().setDownloadStatus(FileDownLoadViewModel.STATUS_DOWNLOAD);
        } else {
            if (!TextUtils.isEmpty(getViewModel().getThumbnailUrl())) {
                getDataBinding().imgThumbnail.load(getViewModel().getThumbnailUrl());
            }
            getViewModel().downloadFile();
            getViewModel().getSuccessLiveData().observe(this, new Observer<Boolean>() {
                @Override
                public void onChanged(Boolean aBoolean) {
                    if (aBoolean) {
                        if (!common.playVideo(getViewModel().getLocalPath())) {
                            AppUtil.finishActivity(VideoPlayActivity.this);
                        }
                    }
                }
            });
        }
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(VideoPlayActivity.this);
    }

    @Override
    public void clickRight(View view) {
        String name = mBean.getName();
        File saveFile = new File(mBean.getPath());
        if (saveFile.exists()) {
            if (TextUtils.isEmpty(name)) {
                name = saveFile.getName();
            }
            String finalName = name;
            PermissionHelper.getStorageHelper(VideoPlayActivity.this).setPermissionCallback(new PermissionCallback<PermissionData>() {
                @Override
                public void onResult(boolean yes, @NonNull PermissionData permission) {
                    if (yes) {
                        getViewModel().saveFile(saveFile, finalName);
                    } else {
                        T.ss(R.string.common_no_storage_permission);
                    }
                }
            }).requestPermission();
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        common.onDestroy();
    }


    public void sendToPhone(File file) {
        Uri localUri = Uri.fromFile(file);
        Intent localIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, localUri);
        sendBroadcast(localIntent);
        T.ss(R.string.saved_to_album);
    }

    @Override
    public ProgressBar getProgressBar() {
        return getDataBinding().progress;
    }

    @Override
    public FrameLayout getParentView() {
        return getDataBinding().parent;
    }

    @Override
    public WebView getWebView() {
        return getDataBinding().webview;
    }

    @Override
    public void onWebViewLoadingError() {
        File file = new File(mBean.getPath());
        if (file.exists()) {
            OpenFileUtils.openFile(VideoPlayActivity.this, file);
            AppUtil.finishActivity(this);
        }
    }

    @Override
    public void downloadOrOpenFile(int status) {
        switch (status) {
            case FileDownLoadViewModel.STATUS_NONE:
                getViewModel().downloadFile();
                break;
            case FileDownLoadViewModel.STATUS_DOWNLOADING:
                break;
            case FileDownLoadViewModel.STATUS_DOWNLOAD:
                break;
        }

    }

    @Override
    public void openFile() {
        //用第三方打开
        File file = new File(mBean.getPath());
        if (file.exists()) {
            OpenFileUtils.openFile(VideoPlayActivity.this, file);
            AppUtil.finishActivity(this);
        }
    }
}
