<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.kanzhun.foundation.viewmodel.FileDownLoadViewModel" />
        <variable
            name="viewModel"
            type="com.kanzhun.webview.viewmodel.VideoPlayViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.webview.callback.VideoPlayCallback" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/common_black">

        <FrameLayout
            android:id="@+id/parent"
            android:layout_width="match_parent"
            android:layout_height="500dp"
            android:layout_gravity="center_vertical"
            app:visibleGone="@{viewModel.downloadStatus == FileDownLoadViewModel.STATUS_DOWNLOAD}">

            <WebView
                android:id="@+id/webview"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ProgressBar
                android:id="@+id/progress"
                style="@android:style/Widget.ProgressBar.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:progressDrawable="@drawable/webview_bg_progressbar_horizontal" />
        </FrameLayout>


        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/img_thumbnail"
            android:layout_width="match_parent"
            android:layout_height="500dp"
            android:layout_gravity="center"
            android:scaleType="fitXY"
            app:visibleGone="@{viewModel.downloadStatus != FileDownLoadViewModel.STATUS_DOWNLOAD}" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@android:color/transparent"
            app:visibleGone="@{viewModel.downloadStatus == FileDownLoadViewModel.STATUS_DOWNLOAD}">

            <ImageView
                android:id="@+id/iv_video_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:onClick="@{(view)->callback.clickRight(view)}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/webview_ic_icon_video_save"/>

            <ImageView
                android:id="@+id/iv_video_open"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:onClick="@{()->callback.openFile()}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/iv_video_right"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/webview_ic_icon_video_open"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/iv_video_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/webview_ic_icon_video_close"
            android:onClick="@{(view)->callback.clickLeft(view)}" />

        <com.kanzhun.foundation.views.VideoDownloadProgressbar
            android:id="@+id/d_progress"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            app:videoDownloadProgress="@{viewModel.progress}"
            app:visibleGone="@{viewModel.downloadStatus == FileDownLoadViewModel.STATUS_DOWNLOADING}" />

        <ImageView
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:onClick="@{()->callback.downloadOrOpenFile(viewModel.downloadStatus)}"
            android:src="@drawable/webview_ic_download"
            app:visibleGone="@{viewModel.downloadStatus == FileDownLoadViewModel.STATUS_NONE}"
            tools:visibility="visible" />
    </FrameLayout>
</layout>

