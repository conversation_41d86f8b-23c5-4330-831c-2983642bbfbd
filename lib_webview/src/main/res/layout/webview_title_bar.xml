<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="hasDivider"
            type="boolean" />

        <variable
            name="title"
            type="String" />

        <variable
            name="callback"
            type="com.kanzhun.common.callback.TitleBarCallback" />

        <variable
            name="noBackground"
            type="Boolean" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_title_height"
        android:background="@{noBackground ? @android:color/transparent : @color/common_white}">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:onClick="@{(view)->callback.clickLeft(view)}"
            android:paddingLeft="16dp"
            android:paddingRight="6dp"
            android:src="@drawable/common_ic_black_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_finish"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:onClick="@{(view)->callback.clickLeft(view)}"
            android:paddingLeft="10dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:src="@drawable/common_ic_black_cancel"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_back"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 为保证title居中，右侧隐藏一对和左侧一样的布局 -->
        <ImageView
            android:id="@+id/iv_right_finish"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingRight="10dp"
            android:src="@drawable/common_ic_black_cancel"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toLeftOf="@id/iv_back_right"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_back_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/common_ic_black_back"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:ellipsize="end"
            android:gravity="center"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:singleLine="true"
            android:text='@{title,default = "" }'
            android:textColor="@color/common_color_0D0D1A"
            android:textSize="17dp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/barrier"
            app:layout_constraintStart_toEndOf="@+id/barrier_left"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="这里显示的是WebView的标题哈哈哈" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:onClick="@{(view)->callback.clickRight(view)}"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:src="@drawable/common_ic_setting"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier_left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="end"
            app:constraint_referenced_ids="iv_finish"
            app:layout_constrainedWidth="true" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="start"
            app:constraint_referenced_ids="iv_right_finish"
            app:layout_constrainedWidth="true" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/common_divider_height"
            android:background="@color/webview_color_CFCFCF"
            android:visibility="@{hasDivider ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
