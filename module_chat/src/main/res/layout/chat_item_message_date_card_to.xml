<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.kanzhun.common.views.layout.InterceptConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/chat_message_item_padding_left_right"
        android:paddingTop="@dimen/chat_message_item_padding_top_bottom"
        android:paddingRight="@dimen/chat_message_item_padding_left_right"
        android:paddingBottom="@dimen/chat_message_item_padding_top_bottom">

        <include
            android:id="@+id/ic_avatar"
            layout="@layout/chat_view_message_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:msg="@{msg}"
            app:listener="@{listener}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/message_content"
            layout="@layout/chat_view_message_date_card_to"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/chat_message_item_avatar_content_margin"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ic_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:listener="@{listener}"
            app:msg="@{msg}" />

    </com.kanzhun.common.views.layout.InterceptConstraintLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForDateCard" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel" />
    </data>

</layout>