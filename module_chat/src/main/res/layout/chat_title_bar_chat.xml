<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

        <variable
            name="noBackground"
            type="boolean" />

        <variable
            name="title"
            type="String" />

        <variable
            name="icon"
            type="String" />

        <variable
            name="imgUrl"
            type="String" />

        <variable
            name="moodTitle"
            type="String" />

        <variable
            name="moodUrl"
            type="String" />

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.ChatTitleCallback" />

        <variable
            name="right"
            type="boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/idBarRoot"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@{noBackground ? @android:color/transparent : @color/common_white}">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:onClick="@{(view)->callback.clickLeft(view)}"
            android:paddingLeft="18dp"
            android:paddingRight="5dp"
            android:src="@drawable/common_ic_black_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="100dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:onClick="@{(view)->callback.clickTitle(view)}"
            android:text="@{title}"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@+id/llMood"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_goneMarginEnd="0dp"
            tools:text="这是一个超级超级超级长的昵称这是一个超级超级超级长的昵称这是一个超级超级超级长的昵称" />

        <LinearLayout
            android:id="@+id/llMood"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="1dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/tv_name"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            app:visibleGone="@{!TextUtils.isEmpty(moodUrl) &amp;&amp; !TextUtils.isEmpty(moodTitle)}"
            tools:visibility="visible">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/ivMood"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:alpha="0.3"
                android:scaleType="centerCrop"
                app:imageUrl="@{moodUrl}"
                tools:background="@color/common_color_FF7847" />

            <TextView
                android:id="@+id/tvMoodName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{moodTitle}"
                android:textColor="@color/common_color_B8B8B8"
                android:textSize="@dimen/common_text_sp_11"
                tools:text="Emo中" />

        </LinearLayout>


        <FrameLayout
            android:id="@+id/flIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/tv_name"
            app:layout_constraintStart_toEndOf="@+id/tv_name"
            app:layout_constraintTop_toTopOf="@+id/tv_name">

            <TextView
                android:id="@+id/tv_official"
                android:layout_width="wrap_content"
                android:layout_height="17dp"
                android:layout_marginStart="4dp"
                android:background="@drawable/chat_bg_corner_9_color_4c9cf8"
                android:gravity="center"
                android:maxLines="1"
                android:paddingLeft="6dp"
                android:paddingTop="1dp"
                android:paddingRight="6dp"
                android:paddingBottom="1dp"
                android:text="@string/chat_official"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_10"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="官方活动"
                tools:visibility="visible" />

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/ivLoveStatus"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:onClick="@{(view)->callback.clickLoveIcon(view)}"
                android:padding="8dp"
                android:visibility="gone"
                app:imageUrl="@{icon}"
                app:visibleGone="@{icon}"
                tools:src="@mipmap/chat_ic_same_tag" />
        </FrameLayout>

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/compose_view_days_spent"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/iv_right"
            app:layout_constraintStart_toEndOf="@+id/flIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@color/color_black_70" />

        <ImageView
            android:id="@+id/iv_right"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:onClick="@{(view)->callback.clickRight(view)}"
            android:paddingLeft="5dp"
            android:paddingRight="18dp"
            android:src="@drawable/common_ic_icon_right_more"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleGone="@{right}" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
