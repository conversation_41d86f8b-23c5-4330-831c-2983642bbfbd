<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRootView"
    android:layout_width="70dp"
    android:layout_height="100dp"
    android:background="@mipmap/bg_emotion_preview"
    android:clipChildren="false"
    tools:ignore="ResourceName">

    <ImageView
        android:id="@+id/iv_preview"
        android:layout_marginTop="8dp"
        android:layout_centerHorizontal="true"
        tools:background="@color/common_color_E03641"
        android:layout_width="35dp"
        android:layout_height="35dp" />

    <TextView
        android:id="@+id/tv_preview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_preview"
        android:layout_marginTop="2dp"
        android:gravity="center"
        tools:text="[微笑]"
        android:textColor="@color/common_color_B8B8B8"
        android:textSize="10dp" />

</RelativeLayout>


