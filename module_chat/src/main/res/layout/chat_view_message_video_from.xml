<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:chatListener="@{listener}"
        app:chatMessage="@{msg}">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="100dp"
            android:layout_height="100dp">

            <com.kanzhun.common.views.image.OImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="fitXY"
                app:common_radius="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:showVideoScale="@{msg}" />


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/chat_icon_chat_video_play"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:layout_marginBottom="6dp"
                android:shadowColor="@color/common_color_000000_50"
                android:shadowRadius="3.0"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_12"
                app:duration="@{msg.duration}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="00:14" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForVideo" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />
    </data>
</layout>