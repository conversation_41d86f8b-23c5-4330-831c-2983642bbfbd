<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="item"
            type="com.kanzhun.foundation.api.bean.TopicGameOptionBean" />

    </data>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="62dp"
        android:layout_marginStart="6dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="6dp"
        android:layout_marginBottom="8dp">

        <FrameLayout
            android:id="@+id/fl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@{item.content}"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_16"
                android:textStyle="bold"
                tools:text="以父之小名以父之小名" />

        </FrameLayout>

    </FrameLayout>

</layout>