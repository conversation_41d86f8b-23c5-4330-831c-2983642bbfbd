<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.text.TextUtils" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForDateCard" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:chatListener="@{listener}"
        app:chatMessage="@{msg}">
        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/cl_card"
            android:layout_width="250dp"
            android:layout_height="160dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:qmui_radius="12dp"
            tools:background="@mipmap/chat_bg_date_card_message_success"
            app:dateCardBackground="@{msg.dateCardInfo.status}">
            <com.kanzhun.common.views.textview.BoldTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_margin="12dp"
                android:lineHeight="20dp"
                app:dateCardStatusContentColor="@{msg.dateCardInfo.status}"
                android:textSize="@dimen/common_text_sp_20"
                android:text="@{TextUtils.isEmpty(msg.dateCardInfo.text) ? @string/chat_would_you_like_date_with_me : msg.dateCardInfo.text}"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="@string/chat_would_you_like_date_with_me"/>


        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="11dp"
            app:layout_constraintTop_toBottomOf="@id/cl_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginRight="15dp"
            android:gravity="start"
            android:layout_marginTop="4dp"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_12"
            app:dateCardStatusDescReceive="@{msg.dateCardInfo.status}"
            tools:text="你欣然接受"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>