<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".LoveCardSenderFinishedFragment">

    <data>

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.chat.viewmodel.LoveCardDetailViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.LoveCardFinishedCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@mipmap/chat_bg_love_letter"/>
        
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:paddingRight="5dp"
                android:paddingBottom="5dp"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="25dp"
                android:onClick="@{(view)->callback.clickClose()}"
                android:src="@mipmap/common_ic_close_black"
                android:fitsSystemWindows="true"/>

            <ImageView
                android:layout_marginTop="91dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="48dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/confession_letter"/>
            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_gei"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_close"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="16dp"
                android:text="给"
                android:textSize="@dimen/common_text_sp_18"
                android:textColor="@color/common_color_191919"/>

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_sender_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toRightOf="@+id/tv_gei"
                app:layout_constraintRight_toLeftOf="@+id/tv_de"
                app:layout_constraintTop_toTopOf="@+id/tv_gei"
                app:layout_constraintBottom_toBottomOf="@+id/tv_gei"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintHorizontal_bias="0"
                android:maxLines="1"
                android:ellipsize="end"
                android:text="@{activityViewModel.statusBeanField.friendName}"
                android:textSize="@dimen/common_text_sp_18"
                android:textColor="@color/common_color_191919"/>

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_de"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toRightOf="@+id/tv_sender_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_gei"
                app:layout_constraintBottom_toBottomOf="@+id/tv_gei"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintHorizontal_bias="0"
                android:layout_marginRight="28dp"
                android:text="的"
                android:textSize="@dimen/common_text_sp_18"
                android:textColor="@color/common_color_191919"/>

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_leave_a_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_sender_title"
                android:layout_marginTop="8dp"
                android:layout_marginLeft="28dp"
                android:layout_marginRight="28dp"
                android:textColor="@color/common_black"
                android:textStyle="bold"
                android:textSize="@dimen/common_text_sp_28"
                android:text="@string/chat_love_card_a_letter"/>

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_leave_a_message"
                app:layout_constraintBottom_toTopOf="@+id/tv_accept_or_refuse"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="20dp">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:text="@{@string/chat_love_card_dear(activityViewModel.statusBeanField.friendName)}"
                        android:id="@+id/tv_content_top"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="28dp"
                        android:layout_marginRight="28dp"
                        android:lineSpacingMultiplier="1.3"
                        android:textColor="@color/common_black"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Dear fkdkajlf" />

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/tv_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        app:layout_constraintTop_toBottomOf="@+id/tv_content_top"
                        android:layout_marginLeft="28dp"
                        android:layout_marginRight="28dp"
                        android:lineHeight="32dp"
                        android:textColor="@color/common_black"
                        android:textSize="@dimen/common_text_sp_16"
                        tools:text="@string/common_long_placeholder"
                        android:text="@{activityViewModel.statusBeanField.msg}" />

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/tv_love_card_from"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toBottomOf="@+id/tv_content"
                        android:gravity="right"
                        android:layout_marginTop="24dp"
                        android:layout_marginLeft="28dp"
                        android:layout_marginRight="28dp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:textStyle="bold"
                        android:textColor="@color/common_black"
                        android:textSize="@dimen/common_text_sp_16"
                        tools:text="From: XXX"
                        android:text="@{@string/chat_love_card_from(activityViewModel.statusBeanField.name)}"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.core.widget.NestedScrollView>

            <TextView
                android:id="@+id/tv_accept_or_refuse"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/cl_continue"
                android:layout_marginBottom="20dp"
                android:textColor="@{activityViewModel.statusBeanField.reply == 1 ? @color/common_color_5B43C2 : @color/common_color_E040E7}"
                android:textSize="@dimen/common_text_sp_16"
                android:textStyle="bold"
                app:msgSenderId="@{activityViewModel.msgSenderIdField}"
                app:reply="@{activityViewModel.statusBeanField.reply}"
                tools:text="@string/chat_accept_you_love"
                tools:textColor="@color/common_color_5B43C2"/>

            <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                android:id="@+id/cl_continue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="20dp"
                android:background="@color/common_color_191919"
                app:qmui_radius="25dp"
                android:onClick="@{(view)->callback.clickContinue()}">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:gravity="center"
                    android:text="@string/chat_continue_chat"
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_16"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />
            </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>