<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:background="@drawable/chat_bg_message_from3"
            android:paddingLeft="16dp"
            android:paddingTop="10dp"
            android:paddingRight="16dp"
            android:paddingBottom="10dp"
            android:text="@{msg.getLikeInfo().likeReason}"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_16"
            android:visibility="@{TextUtils.isEmpty(msg.getLikeInfo().likeReason) ? View.GONE : View.VISIBLE}"
            app:chatListener="@{chatListener}"
            app:chatMessage="@{msg}"
            tools:ignore="SpUsage"
            tools:text="喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的喜欢了你的" />

    </FrameLayout>

    <data>

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForLike" />

        <variable
            name="chatListener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

    </data>

</layout>