<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.kanzhun.common.views.layout.InterceptConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/chat_message_item_padding_left_right"
        android:paddingTop="@dimen/chat_message_item_padding_top_bottom"
        android:paddingRight="@dimen/chat_message_item_padding_left_right"
        android:paddingBottom="@dimen/chat_message_item_padding_top_bottom">

        <include
            android:id="@+id/ic_avatar"
            layout="@layout/chat_view_message_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:listener="@{listener}"
            app:msg="@{msg}" />

        <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
            xmlns:tools="http://schemas.android.com/tools"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/chat_message_item_avatar_content_margin"
            android:background="@drawable/chat_bg_message_to3"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ic_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_max="wrap"
            app:layout_constraintWidth_percent="0.65">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:paddingLeft="16dp"
                android:paddingTop="10dp"
                android:paddingRight="16dp"
                android:paddingBottom="10dp"
                android:text="@{msg.illegalText}"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_16"
                tools:text="我公司？" />
        </LinearLayout>


    </com.kanzhun.common.views.layout.InterceptConstraintLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.ChatMessage" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel" />
    </data>

</layout>