<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.TopicGameDetailViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.TopicGameDetailCallback" />

    </data>

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#B3000000"
        android:fitsSystemWindows="true"
        android:focusable="true"
        app:qmui_shadowElevation="16dp"
        tools:ignore="ContentDescription,SpUsage,UseCompatTextViewDrawableXml,Autofill,HardcodedText">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="116dp"
            android:background="@drawable/common_bg_corner_20_top_2_color_white"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@mipmap/chat_bg_topic_game3"
                app:layout_constraintDimensionRatio="375:162"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="140dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.kanzhun.common.views.textview.BoldTextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="54dp"
                    android:layout_marginBottom="32dp"
                    android:gravity="center_horizontal"
                    android:textColor="@color/common_color_191919"
                    android:textSize="@dimen/common_text_sp_24"
                    android:textStyle="bold"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/iv_right"
                    app:layout_constraintStart_toEndOf="@+id/iv_left"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="你最喜欢的音你最你你最喜欢的音你最你你最喜欢的音你最你你最喜欢的音你最你你最喜欢的音你最你" />

                <ImageView
                    android:id="@+id/iv_left"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="5dp"
                    android:src="@mipmap/chat_icon_left_quiot2"
                    app:layout_constraintEnd_toStartOf="@+id/tv_title"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_title" />

                <ImageView
                    android:id="@+id/iv_right"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="10dp"
                    android:src="@mipmap/chat_icon_right_quiot2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toEndOf="@+id/tv_title"
                    app:layout_constraintTop_toTopOf="@+id/tv_title" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_margin="20dp"
                android:onClick="@{v->callback.clickClose()}"
                android:src="@mipmap/common_ic_close"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_content"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="18dp"
                android:layout_marginEnd="18dp"
                android:layout_marginBottom="16dp"
                app:layout_constraintBottom_toTopOf="@+id/fl_input_voice"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cl_title">

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/scroll_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal" />

                    </LinearLayout>

                </androidx.core.widget.NestedScrollView>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_tip"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginEnd="30dp"
                android:layout_marginBottom="10dp"
                android:drawableStart="@drawable/me_ic_fail_small"
                android:drawablePadding="5dp"
                android:text="@{viewModel.errorObservable}"
                android:textColor="@color/common_color_FF3F4B"
                android:textSize="@dimen/common_text_sp_12"
                app:layout_constraintBottom_toTopOf="@+id/fl_input_voice"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:visibleGone="@{!TextUtils.isEmpty(viewModel.errorObservable) &amp;&amp; viewModel.observableType == 1}" />

            <ImageView
                android:id="@+id/iv_voice"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_marginStart="20dp"
                android:enabled="@{viewModel.enableSendObservable}"
                android:onClick="@{v->callback.clickSwitchVoiceInput()}"
                android:src="@drawable/chat_selector_topic_game_voice_new"
                app:layout_constraintBottom_toBottomOf="@+id/fl_input_voice"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/fl_input_voice" />

            <FrameLayout
                android:id="@+id/fl_input_voice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="20dp"
                app:layout_constraintBottom_toTopOf="@+id/btn_next"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/iv_voice"
                app:layout_constraintTop_toBottomOf="@+id/cl_content">

                <RelativeLayout
                    android:id="@+id/fl_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/chat_bg_topic_game_send_input"
                    tools:visibility="visible">

                    <EditText
                        android:id="@+id/et_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="0dp"
                        android:layout_toStartOf="@+id/iv_input_del"
                        android:background="@null"
                        android:enabled="@{viewModel.enableSendObservable}"
                        android:focusable="true"
                        android:hint="对你的选择还有补充嘛？"
                        android:imeOptions="actionSend"
                        android:inputType="textMultiLine"
                        android:maxLength="150"
                        android:maxLines="3"
                        android:minHeight="41dp"
                        android:paddingHorizontal="16dp"
                        android:paddingTop="11dp"
                        android:paddingBottom="11dp"
                        android:text="@={viewModel.inputObservable}"
                        android:textColor="@color/common_black"
                        android:textColorHint="@color/common_color_B7B7B7"
                        android:textSize="@dimen/common_text_sp_16"
                        tools:ignore="RtlSymmetry"
                        tools:text="测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试" />

                    <ImageView
                        android:id="@+id/iv_input_del"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignTop="@+id/et_input"
                        android:layout_alignBottom="@+id/et_input"
                        android:layout_alignParentEnd="true"
                        android:layout_marginEnd="4dp"
                        android:onClick="@{v->callback.clickClearInput()}"
                        android:src="@mipmap/chat_icon_topic_game_delete"
                        app:visibleGone="@{viewModel.inputObservable.length()>0}" />

                </RelativeLayout>

                <FrameLayout
                    android:id="@+id/fl_voice_input"
                    android:layout_width="match_parent"
                    android:layout_height="46dp"
                    android:visibility="gone"
                    tools:visibility="gone">

                    <RelativeLayout
                        android:id="@+id/fl_voice_play"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:background="@drawable/chat_bg_topic_game_send_input"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <LinearLayout
                            android:id="@+id/ll_voice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="34dp"
                            android:onClick="@{v->callback.startVoice()}"
                            android:orientation="horizontal"
                            android:paddingLeft="20dp"
                            android:paddingRight="20dp"
                            tools:ignore="RelativeOverlap">

                            <com.kanzhun.common.views.AudioRecorderPlayView
                                android:id="@+id/record_play_view"
                                android:layout_width="0dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center_vertical"
                                android:layout_weight="1"
                                app:common_lump_color="@color/common_black" />

                            <TextView
                                android:id="@+id/tv_time"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="12dp"
                                android:textColor="@color/common_black"
                                android:textSize="@dimen/common_text_sp_16"
                                tools:text="60s" />

                        </LinearLayout>

                        <ImageView
                            android:id="@+id/iv_voice_del"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="10dp"
                            android:onClick="@{v->callback.clickClearVoice()}"
                            android:src="@mipmap/chat_icon_topic_game_delete" />

                    </RelativeLayout>

                    <TextView
                        android:id="@+id/tv_voice_record"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/chat_bg_topic_game_send_voice"
                        android:gravity="center"
                        android:soundEffectsEnabled="true"
                        android:text="@string/chat_record_voice"
                        android:textColor="@color/common_color_323337"
                        android:textSize="@dimen/common_text_sp_16"
                        tools:visibility="gone" />

                </FrameLayout>

            </FrameLayout>

            <FrameLayout
                android:id="@+id/fl_voice"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/chat_bg_topic_game_voice"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/cl_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/cl_content"
                tools:visibility="visible">

                <com.kanzhun.marry.chat.views.TopicGameVoiceView
                    android:id="@+id/voice_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </FrameLayout>

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/btn_next"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="28dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="32dp"
                android:enabled="@{viewModel.enableSendObservable &amp;&amp; (TextUtils.isEmpty(viewModel.errorObservable) || viewModel.observableType == 2)}"
                android:gravity="center"
                android:onClick="@{v->callback.clickSend()}"
                android:text="@string/chat_topic_game_send"
                android:textSize="@dimen/common_text_sp_20"
                android:textStyle="bold"
                app:greyDisabledStyle="@{viewModel.enableSendObservable &amp;&amp; (TextUtils.isEmpty(viewModel.errorObservable) || viewModel.observableType == 2)}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/fl_input_voice"
                app:qmui_radius="27dp"
                tools:qmui_borderColor="@color/color_black_70"
                tools:qmui_borderWidth="1dp" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.qmuiteam.qmui.layout.QMUIFrameLayout>

</layout>