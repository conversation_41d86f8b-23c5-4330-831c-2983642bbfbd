<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.TopicGameCallback" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.TopicGameViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/common_bg_corner_20_top_2_color_white"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_header_bg"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@mipmap/chat_bg_topic_game2"
                app:layout_constraintDimensionRatio="375:230"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <LinearLayout
                android:id="@+id/ll_title_bar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:layout_marginVertical="10dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingTop="32dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:onClick="@{v->callback.close()}"
                    android:src="@mipmap/common_ic_close"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <ImageView
                    android:id="@+id/iv_desc"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:onClick="@{v->callback.clickTip()}"
                    android:src="@mipmap/chat_topic_game_tip2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="ContentDescription" />

            </LinearLayout>

            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/magic_indicator"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="-16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_header_bg"
                tools:background="@color/common_color_000000_30"
                tools:layout_height="56dp" />

            <androidx.viewpager.widget.ViewPager
                android:id="@+id/view_pager"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/magic_indicator" />

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/magic_indicator">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <FrameLayout
                        android:id="@+id/fl_content"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="10dp"
                        tools:ignore="UselessParent">

                        <com.scwang.smart.refresh.layout.SmartRefreshLayout
                            android:id="@+id/smart_refresh_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:srlEnableAutoLoadMore="true">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerView"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:layoutManager="com.kanzhun.common.views.LinearLayoutManagerWrapper" />
                        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

                        <include
                            android:id="@+id/ic_empty"
                            layout="@layout/common_include_loading_empty_view"
                            app:viewModel="@{viewModel}" />
                    </FrameLayout>

                </LinearLayout>


            </androidx.core.widget.NestedScrollView>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/fl_info"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/fl_detail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </FrameLayout>

</layout>