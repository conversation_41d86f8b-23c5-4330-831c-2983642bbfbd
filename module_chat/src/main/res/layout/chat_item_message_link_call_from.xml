<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForLinkCall" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel" />
    </data>

    <com.kanzhun.common.views.layout.InterceptConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/chat_message_item_padding_left_right"
        android:paddingTop="@dimen/chat_message_item_padding_top_bottom"
        android:paddingRight="@dimen/chat_message_item_padding_left_right"
        android:paddingBottom="@dimen/chat_message_item_padding_top_bottom">

        <include
            android:id="@+id/ic_avatar"
            layout="@layout/chat_view_message_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:msg="@{msg}"
            app:listener="@{listener}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/chat_message_item_avatar_content_margin"
            app:chatItemBackground="@{msg}"
            android:drawablePadding="6dp"
            android:paddingLeft="16dp"
            android:paddingTop="10dp"
            android:paddingRight="16dp"
            android:paddingBottom="10dp"
            android:text="@{msg.showContent}"
            android:textColor="@color/common_black"
            android:textIsSelectable="false"
            android:textSize="@dimen/common_text_sp_16"
            app:chatListener="@{listener}"
            app:chatMessage="@{msg}"
            app:isSelf="@{false}"
            android:gravity="center_vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ic_avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:linkMediaType="@{msg.linkCallMediaType}"
            tools:text="帮忙看一下收帮忙看一下收" />

    </com.kanzhun.common.views.layout.InterceptConstraintLayout>
</layout>