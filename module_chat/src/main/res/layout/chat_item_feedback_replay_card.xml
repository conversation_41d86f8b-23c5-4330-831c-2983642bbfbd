<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.kanzhun.common.views.layout.InterceptConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/chat_message_item_padding_left_right"
        android:paddingTop="@dimen/chat_message_item_padding_top_bottom"
        android:paddingRight="@dimen/chat_message_item_padding_left_right"
        android:paddingBottom="@dimen/chat_message_item_padding_top_bottom"
        app:chatListener="@{listener}"
        app:chatMessage="@{msg}">

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/feedback_replay_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:messageForFeedbackReplayCard="@{msg}"
            tools:composableName="com.kanzhun.marry.chat.bindadapter.FeedbackReplayComposableKt.PreviewFeedbackReplay" />

    </com.kanzhun.common.views.layout.InterceptConstraintLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForFeedbackReplayCard" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel" />

    </data>

</layout>