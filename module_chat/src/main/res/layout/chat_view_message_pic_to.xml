<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:chatListener="@{listener}"
        app:chatMessage="@{msg}">

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_gravity="right"
            app:qmui_radius="10dp">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/iv_pic"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:showPicScale="@{msg}" />

            <FrameLayout
                android:id="@+id/fl_error"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@color/common_color_EBEBEB"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <include
                    layout="@layout/chat_layout_image_load_failed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center" />
            </FrameLayout>

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

    </LinearLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForPic" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />
    </data>
</layout>