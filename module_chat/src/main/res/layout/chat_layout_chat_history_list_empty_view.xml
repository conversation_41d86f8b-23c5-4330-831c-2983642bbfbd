<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="emptyDesc"
            type="java.lang.String" />

        <variable
            name="callback"
            type="com.kanzhun.common.callback.TitleBarCallback" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <include
                android:id="@+id/title_bar"
                layout="@layout/common_title_bar"
                app:callback="@{callback}"/>

            <com.airbnb.lottie.LottieAnimationView
                app:lottie_imageAssetsFolder="replay_like/images"
                app:lottie_fileName="replay_like/reply.json"
                app:lottie_autoPlay="true"
                app:lottie_loop="false"
                android:layout_width="174dp"
                android:layout_height="174dp"
                android:id="@+id/iv_pic"
                android:layout_marginTop="161dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_bar" />

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@{emptyDesc}"
                android:textColor="@color/common_color_7F7F7F"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="@id/iv_pic"
                app:layout_constraintRight_toRightOf="@id/iv_pic"
                app:layout_constraintTop_toBottomOf="@id/iv_pic" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/btn_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:height="50dp"
                android:gravity="center"
                android:paddingLeft="24dp"
                android:paddingRight="24dp"
                android:text="@string/chat_back_to_chat_page"
                android:textColor="@color/common_white"
                app:layout_constraintLeft_toLeftOf="@id/tv_desc"
                app:layout_constraintRight_toRightOf="@id/tv_desc"
                app:layout_constraintTop_toBottomOf="@id/tv_desc"
                app:qmui_radius="25dp"
                app:qmui_backgroundColor="@color/common_black" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>