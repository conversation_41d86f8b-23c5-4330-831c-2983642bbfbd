<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".LoveCardReceiveFragment">

    <data>

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.chat.viewmodel.LoveCardDetailViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.LoveCardReceiveCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@mipmap/chat_bg_love_letter"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="25dp"
                android:fitsSystemWindows="true"
                android:onClick="@{(view)->callback.clickClose()}"
                android:paddingRight="5dp"
                android:paddingBottom="5dp"
                android:src="@mipmap/common_ic_close_black"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_gei"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="16dp"
                android:text="给"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_close" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_sender_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{activityViewModel.statusBeanField.name}"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintLeft_toRightOf="@+id/tv_gei"
                app:layout_constraintRight_toLeftOf="@+id/tv_de"
                app:layout_constraintTop_toBottomOf="@+id/iv_close" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_de"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginRight="28dp"
                android:text="的"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_18"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintLeft_toRightOf="@+id/tv_sender_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_close" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_leave_a_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="28dp"
                android:fontFamily="sans-serif-medium"
                android:text="@string/chat_love_card_a_letter"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_28"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_sender_title" />

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="28dp"
                android:layout_marginBottom="16dp"
                app:layout_constraintBottom_toTopOf="@+id/cl_description"
                app:layout_constraintTop_toBottomOf="@+id/tv_leave_a_message">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:text="@{@string/chat_love_card_dear(activityViewModel.statusBeanField.name)}"
                        android:id="@+id/tv_content_top"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="28dp"
                        android:layout_marginRight="28dp"
                        android:lineSpacingMultiplier="1.3"
                        android:textColor="@color/common_black"
                        android:textSize="@dimen/common_text_sp_16"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Dear fkdkajlf" />

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/tv_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        app:layout_constraintTop_toBottomOf="@+id/tv_content_top"
                        android:layout_marginLeft="28dp"
                        android:layout_marginRight="28dp"
                        android:lineSpacingMultiplier="1.3"
                        android:text="@{activityViewModel.statusBeanField.msg}"
                        android:textColor="@color/common_black"
                        android:textSize="@dimen/common_text_sp_16"
                        tools:text="房间看到拉法基阿喀琉斯发动机咖啡粉进度款拉法基方式大姐夫考虑到司法局立卡" />

                    <com.kanzhun.common.views.textview.BoldTextView
                        android:id="@+id/tv_love_card_from"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="28dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginRight="28dp"
                        android:ellipsize="end"
                        android:gravity="right"
                        android:maxLines="1"
                        android:text="@{@string/chat_love_card_from(activityViewModel.statusBeanField.friendName)}"
                        android:textColor="@color/common_black"
                        android:textSize="@dimen/common_text_sp_16"
                        android:textStyle="bold"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_content"
                        tools:text="Fom xxx" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.core.widget.NestedScrollView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                app:layout_constraintBottom_toTopOf="@+id/cl_refuse">

                <TextView
                    android:id="@+id/tv_sub_title_pre"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="28dp"
                    android:layout_marginTop="20dp"
                    android:text="@string/chat_love_card_continue_subtitle_pre"
                    android:textColor="@color/common_black_70"
                    android:textSize="@dimen/common_text_sp_16"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_unlock_voice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="28dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginRight="28dp"
                    android:text="@string/chat_love_card_continue_unlock"
                    android:textColor="@color/common_black_70"
                    android:textSize="@dimen/common_text_sp_16"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_sub_title_pre" />

                <TextView
                    android:id="@+id/tv_love_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:drawableRight="@drawable/chat_ic_date_card_date_mode"
                    android:drawablePadding="4dp"
                    android:fontFamily="sans-serif-medium"
                    android:onClick="@{(view)->callback.clickLoveMode()}"
                    android:text="@string/chat_love_mode"
                    android:textColor="@color/chat_love_card_mode"
                    android:textSize="@dimen/common_text_sp_16"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_sub_title_pre"
                    app:layout_constraintTop_toBottomOf="@+id/tv_unlock_voice" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                android:id="@+id/cl_refuse"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="5dp"
                android:layout_marginBottom="20dp"
                android:background="@color/common_color_191919"
                android:onClick="@{(view)->callback.clickRefuse()}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/cl_receive"
                app:qmui_radius="25dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@drawable/chat_ic_date_card_btn_refuse"
                    android:drawablePadding="6dp"
                    android:gravity="center"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="@string/chat_date_card_refuse"
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_16"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />
            </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

            <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                android:id="@+id/cl_receive"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="20dp"
                android:background="@{activityViewModel.statusBeanField.status == 2 ? @color/common_color_CCCCCC : @color/common_color_292929}"
                android:onClick="@{(view)->callback.clickReceive()}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/cl_refuse"
                app:layout_constraintRight_toRightOf="parent"
                app:qmui_radius="25dp"
                tools:background="@color/common_color_292929">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawableLeft="@mipmap/chat_ic_date_card_btn_receive"
                    android:drawablePadding="6dp"
                    android:gravity="center"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="@string/chat_date_card_receive"
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_16"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />
            </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_refuse_anim"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/common_white"
            android:visibility="gone"
            tools:visibility="gone">

            <ImageView
                android:id="@+id/iv_anim_top_refuse"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="115dp"
                android:src="@mipmap/chat_bg_love_card_refuse_man"
                app:layout_constraintDimensionRatio="h,375:262"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="26dp"
                android:layout_marginEnd="48dp"
                android:src="@mipmap/confession_letter"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_anim_top_refuse" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_to_like"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="43dp"
                android:text="@string/chat_to_like"
                android:textColor="@color/common_color_191919"
                android:textSize="28dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_anim_top_refuse" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tv_leave_some_space"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="1dp"
                android:text="@string/chat_leave_some_space"
                android:textColor="@color/common_color_191919"
                android:textSize="40dp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_to_like" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="16dp"
                android:lineHeight="24dp"
                android:text="@string/chat_ta_must_be_depressed"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_leave_some_space" />

            <com.kanzhun.common.views.RoundAlphaButton
                android:id="@+id/btn_continue"
                style="@style/common_blue_button_style"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="20dp"
                android:onClick="@{(view)->callback.clickContinue()}"
                android:text="@string/chat_continue_chat"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>