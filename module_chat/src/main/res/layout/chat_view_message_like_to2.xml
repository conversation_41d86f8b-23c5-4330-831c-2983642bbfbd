<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.kanzhun.common.views.layout.InterceptConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/chat_message_item_padding_left_right"
        android:paddingTop="@dimen/chat_message_item_padding_top_bottom"
        android:paddingRight="@dimen/chat_message_item_padding_left_right"
        android:paddingBottom="@dimen/chat_message_item_padding_top_bottom"
        tools:ignore="SpUsage,ContentDescription">

        <include
            android:id="@+id/ic_avatar"
            layout="@layout/chat_view_message_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:listener="@{listener}"
            app:msg="@{msg}" />

        <LinearLayout
            android:id="@+id/ll_you_like_me"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/chat_message_item_avatar_content_margin"
            android:gravity="end"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ic_avatar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:text="@{msg.getLikeInfo().getLikeModuleToNameShow()}"
                android:textColor="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_14"
                tools:text="@string/chat_like_has_send" />

            <include
                layout="@layout/chat_view_message_like_reason_to"
                app:chatListener="@{listener}"
                app:msg="@{msg}" />

        </LinearLayout>

    </com.kanzhun.common.views.layout.InterceptConstraintLayout>

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel" />

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <import type="com.kanzhun.foundation.Constants" />

        <import type="com.kanzhun.foundation.model.message.MessageForLike.LikeInfo" />

        <import type="com.kanzhun.foundation.kotlin.me.MeInfoExtKt" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForLike" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

    </data>

</layout>