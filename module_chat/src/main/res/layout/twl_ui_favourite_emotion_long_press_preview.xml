<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRootView"
    android:layout_width="178dp"
    android:layout_height="221dp"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:background="@mipmap/bg_express_preview"
    android:clipChildren="false">

    <ImageView
        android:id="@+id/iv_preview"
        tools:background="@color/common_color_FF7847"
        android:layout_marginTop="30dp"
        android:layout_centerHorizontal="true"
        android:layout_width="120dp"
        android:layout_height="120dp" />

    <View
        android:id="@+id/mDividerView"
        android:layout_marginRight="15dp"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="10dp"
        android:background="@color/common_color_F5F5F5"
        android:layout_width="match_parent"
        android:layout_height="1dp" />

    <TextView
        android:text="删除"
        android:layout_marginTop="2dp"
        android:id="@+id/mDeleteView"
        android:paddingTop="7dp"
        android:paddingBottom="10dp"
        android:textSize="14dp"
        android:gravity="center"
        android:textColor="#FF292929"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>


