<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30dp"
        android:layout_marginTop="88dp"
        android:layout_marginRight="30dp"
        android:layout_marginBottom="120dp"
        android:scrollbars="none">

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_white"
            app:qmui_radius="10dp">

            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/fl_video"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="18dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="18dp"
                app:layout_constraintDimensionRatio="h,3:4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/tv_content"
                app:layout_goneMarginBottom="60dp"
                app:qmui_radius="10dp">

                <com.kanzhun.foundation.player.OViewRenderer
                    android:id="@+id/sv_video"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <ImageView
                    android:id="@+id/iv_voice"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:src="@drawable/me_selector_story_video_voice"
                    app:viewSelected="@{item.silence}"
                    app:visibleGone="@{item.type != ProfileInfoModel.Story.TYPE_LIVE_PHOTO}" />

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:src="@drawable/me_ic_icon_live_photo"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:visibleGone="@{item.type == ProfileInfoModel.Story.TYPE_LIVE_PHOTO}" />

                <com.kanzhun.common.views.image.OImageView
                    android:id="@+id/iv_photo"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="fitXY"
                    app:imageUrl="@{item.photo}" />

            </com.qmuiteam.qmui.layout.QMUIFrameLayout>

            <com.kanzhun.common.views.OTextView
                android:id="@+id/tv_content"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="18dp"
                android:layout_marginBottom="20dp"
                android:gravity="center"
                android:text="@{item.text}"
                android:textColor="@color/common_color_333333"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/fl_video"
                app:visibleGone="@{item.text}"
                tools:text="朋友乔迁送的花花朋友乔迁送的花花朋友乔迁送的花花" />


        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
    </ScrollView>

    <data>

        <import type="com.kanzhun.foundation.api.model.ProfileInfoModel" />

        <variable
            name="item"
            type="com.kanzhun.foundation.api.bean.VideoStoryBean" />
    </data>
</layout>