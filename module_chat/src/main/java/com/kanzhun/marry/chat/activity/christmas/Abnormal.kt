package com.kanzhun.marry.chat.activity.christmas

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.foundation.model.Contact
import com.kanzhun.marry.chat.R

@Composable
fun AbnormalHidden(
    modifier: Modifier = Modifier,
    onBtnClick: () -> Unit = {},
    contact: Contact? = Contact(),
) {
    Abnormal(
        modifier = modifier,
        desc = "关闭隐身模式，获取任务卡片",
        btnTxt = "去关闭",
        onBtnClick = onBtnClick,
        contact = contact
    )
}

@Composable
fun AbnormalInformal(
    modifier: Modifier = Modifier,
    onBtnClick: () -> Unit = {},
    contact: Contact? = Contact(),
) {
    Abnormal(
        modifier = modifier,
        desc = "成为正式用户，获取任务卡片",
        btnTxt = "去完善资料",
        onBtnClick = onBtnClick,
        contact = contact
    )
}

@Composable
fun Abnormal(
    modifier: Modifier = Modifier,
    desc: String = "",
    btnTxt: String = "",
    onBtnClick: () -> Unit = {},
    contact: Contact? = Contact(),
) {
    SantaMessage(contact = contact, modifier = modifier) {
        ConstraintLayout(
            modifier = Modifier.Companion
                .fillMaxWidth()
                .wrapContentHeight()
                .background(
                    color = Color(0xFFFFF8EB),
                    shape = RoundedCornerShape(20.dp)
                )
        ) {
            val (content, border, cover) = createRefs()

            Image(
                painter = painterResource(id = R.mipmap.chat_ic_cover_hide),
                contentDescription = "image description",
                modifier = Modifier.Companion
                    .constrainAs(cover) {
                        start.linkTo(content.start)
                        end.linkTo(content.end)
                        top.linkTo(content.top)
                        bottom.linkTo(content.bottom)

                        width = Dimension.Companion.fillToConstraints
                        height = Dimension.Companion.fillToConstraints
                    },
                contentScale = ContentScale.Companion.FillBounds
            )

            Column(
                modifier = Modifier.Companion
                    .fillMaxWidth()
                    .padding(16.dp)
                    .constrainAs(content) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top)
                    },
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_eye_hide),
                    contentDescription = "image description",
                )

                Text(
                    text = desc,
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF292929),
                    )
                )

                Spacer(modifier = Modifier.Companion.height(9.dp))

                Text(
                    text = btnTxt,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFFFFFFFF),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .noRippleClickable {
                            onBtnClick()
                        }
                        .background(
                            color = Color(0xFF292929),
                            shape = RoundedCornerShape(size = 72.dp)
                        )
                        .padding(horizontal = 24.dp, vertical = 9.dp)
                )
            }

            Image(
                painter = painterResource(id = R.mipmap.chat_ic_border),
                contentDescription = "image description",
                modifier = Modifier.Companion
                    .constrainAs(border) {
                        start.linkTo(content.start)
                        end.linkTo(content.end)
                        top.linkTo(content.top)
                        bottom.linkTo(content.bottom)

                        width = Dimension.Companion.fillToConstraints
                        height = Dimension.Companion.fillToConstraints
                    },
                contentScale = ContentScale.Companion.FillBounds
            )
        }
    }
}

@Preview
@Composable
private fun PreviewAbnormal() {
    Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
        AbnormalHidden()

        AbnormalInformal()
    }
}