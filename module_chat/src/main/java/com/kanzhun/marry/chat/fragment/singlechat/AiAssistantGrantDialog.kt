package com.kanzhun.marry.chat.fragment.singlechat

import android.annotation.SuppressLint
import android.app.Dialog
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.foundation.kotlin.ktx.userSp
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.ai.ConversationSettingsViewModel
import com.kanzhun.marry.chat.ai.hasChatRecordStateOn
import com.kanzhun.utils.T
import com.kanzhun.utils.ui.ActivityUtils

private const val KEY_HAS_SHOW_AI_ASSISTANT_GRANT_DIALOG = "key_has_show_ai_assistant_grant_dialog"

@SuppressLint("UseKtx", "ApplySharedPref")
private fun setHasShowAiAssistantGrantDialog() {
    userSp().edit().putBoolean(KEY_HAS_SHOW_AI_ASSISTANT_GRANT_DIALOG, true).commit()
}

private fun hasShowAiAssistantGrantDialog(): Boolean {
    return userSp().getBoolean(KEY_HAS_SHOW_AI_ASSISTANT_GRANT_DIALOG, false)
}

class AiAssistantGrantDialog(val activity: FragmentActivity) {
    fun shouldShow(callback: ((Boolean) -> Unit)? = null) {
        if (hasChatRecordStateOn()) {
            return
        }

        // 当前用户，有且只弹一次
        if (hasShowAiAssistantGrantDialog()) {
            return
        }

        if (ActivityUtils.isValid(activity)) {
            Dialog(activity, R.style.common_dialog).apply {
                setContentView(ComposeView(activity).apply {
                    setViewTreeLifecycleOwner(activity)
                    setViewTreeSavedStateRegistryOwner(activity)
                    onSetWindowContent {
                        val csViewModel: ConversationSettingsViewModel =
                            viewModel(viewModelStoreOwner = activity)

                        GrantDialogContent(
                            onAgree = {
                                csViewModel.changeChatRecordState(
                                    targetState = true,
                                    callback = { result ->
                                        callback?.invoke(result)

                                        if (result) {

                                            if (ActivityUtils.isValid(activity)) {
                                                dismiss()
                                            }
                                        } else {
                                            T.ss("请求失败，请稍候重试")
                                        }
                                    }
                                )

                                reportPoint("chat-ssistant-permission-click") {
                                    actionp1 = "同意"
                                }
                            },
                            onDisagree = {
                                if (ActivityUtils.isValid(activity)) {
                                    dismiss()

                                    reportPoint("chat-ssistant-permission-click") {
                                        actionp1 = "拒绝"
                                    }
                                }
                            },
                            onClose = {
                                if (ActivityUtils.isValid(activity)) {
                                    dismiss()
                                }
                            }
                        )
                    }
                })
            }.show()

            setHasShowAiAssistantGrantDialog()
        }
    }
}