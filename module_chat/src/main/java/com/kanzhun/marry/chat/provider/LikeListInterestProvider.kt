package com.kanzhun.marry.chat.provider

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.common.util.ProtocolHelper.Companion.parseProtocol
import com.kanzhun.foundation.api.model.ResourceTextBean
import com.kanzhun.foundation.api.model.ThumbBean
import com.kanzhun.foundation.router.MePageRouter.jumpToInfoPreviewActivity
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.chat.adapter.LikeListTagAdapter
import com.kanzhun.marry.chat.databinding.ChatLikeListItemInterestBinding
import com.kanzhun.marry.chat.ext.getReadStatusBg
import com.kanzhun.marry.chat.ext.toLikeListResourceType
import com.kanzhun.marry.chat.item.LikeListInterestBean
import com.kanzhun.marry.chat.likelist.ILikeList
import com.kanzhun.utils.GsonUtils

class LikeListInterestProvider(val activity: FragmentActivity,val iLikeList: ILikeList) : BaseItemProvider<BaseListItem, ChatLikeListItemInterestBinding>() {
    override fun onBindItem(binding: ChatLikeListItemInterestBinding, item: BaseListItem) {
        if (item is LikeListInterestBean) {
            binding.run {
                val thumbBean = item.thumbBean
                flAvatar.ivAvatar.load(thumbBean.tinyAvatar)
                setInfo(thumbBean)
                setResourceType(thumbBean)
                root.clickWithTrigger {
                    if(iLikeList.canDo(item.thumbBean.userStatus,item.thumbBean.phase))jumpToInfoPreviewActivity(activity, thumbBean.userId, "", PageSource.LIKE_LIST, "", "", "", thumbBean.securityId)

                    reportPoint("thumbsup-list-click") {
                        type = item.mLocalItemType.toLikeListResourceType()
                        peer_id = thumbBean.userId
                    }
                }

                clResourceTypeClickLayout.onClick {
                    if(item.thumbBean.jumpUrl?.isNotEmpty() == true)    {
                        parseProtocol(item.thumbBean.jumpUrl)
                        return@onClick
                    }
//                    jumpToInfoPreviewActivity(activity, AccountHelper.getInstance().userId, "", PageSource.LIKE_LIST)
                }
            }
        }

    }

    private fun ChatLikeListItemInterestBinding.setInfo(thumbBean: ThumbBean) {
        root.setBackgroundResource(thumbBean.getReadStatusBg())
        clInfo.tvUserName.text = thumbBean.nickName
        clInfo.tvStatus.textOrGone(thumbBean.statusTag)
        if (thumbBean.recommendTag.isNullOrEmpty()) {
            clInfo.tvTags.gone()
        } else {
            clInfo.tvTags.visible()
            clInfo.tvTags.setAdapter(LikeListTagAdapter(root.context, thumbBean.recommendTag))
        }
        clInfo.tvResourceType.text = thumbBean.thumbModuleContent
        clInfo.tvTime.text = thumbBean.timeStr
    }

    private fun ChatLikeListItemInterestBinding.setResourceType(thumbBean: ThumbBean) {
        val infoBean = GsonUtils.fromJson(thumbBean.resourceInfo, ResourceTextBean::class.java)
        clResourceType.tvText.text = infoBean.text
    }
}

