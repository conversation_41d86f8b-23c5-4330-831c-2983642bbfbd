package com.kanzhun.marry.chat.dialog;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;

import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.api.bean.ChatTopicGameAnswer;
import com.kanzhun.foundation.api.bean.ChatTopicGameQuestion;
import com.kanzhun.foundation.base.fragment.FoundationVMDialogFragment;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.message.MessageForAudio;
import com.kanzhun.foundation.model.message.MessageForTopicGame;
import com.kanzhun.foundation.utils.MessageUtils;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.bindadapter.ChatBindingAdapter;
import com.kanzhun.marry.chat.callback.ChatTopicGameDetailCallback;
import com.kanzhun.marry.chat.databinding.ChatDialogChatTopicGameDetailBinding;
import com.kanzhun.marry.chat.viewmodel.ChatTopicGameDetailViewModel;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/8/17
 * 聊天话题游戏消息详情弹框
 */
public class ChatTopicGameDetailDialog extends FoundationVMDialogFragment<ChatDialogChatTopicGameDetailBinding, ChatTopicGameDetailViewModel> implements ChatTopicGameDetailCallback {

    public static ChatTopicGameDetailDialog newInstance(FragmentActivity activity, MessageForTopicGame.TopicGameInfo topicGameInfo, String chatId, String senderId) {
        ChatTopicGameDetailDialog detailDialog = new ChatTopicGameDetailDialog(activity);
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA, topicGameInfo);
        bundle.putString(BundleConstants.BUNDLE_CHAT_ID, chatId);
        bundle.putString(BundleConstants.BUNDLE_USER_ID, senderId);
        detailDialog.setArguments(bundle);
        return detailDialog;
    }


    protected ChatTopicGameDetailDialog(FragmentActivity activity) {
        super(new Builder(activity).setPadding(24, 40,24,40).setCanceledOnTouchOutside(false));
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_dialog_chat_topic_game_detail;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        if (getArguments() != null) {
            MessageForTopicGame.TopicGameInfo gameInfo = (MessageForTopicGame.TopicGameInfo) getArguments().getSerializable(BundleConstants.BUNDLE_DATA);
            String chatId = getArguments().getString(BundleConstants.BUNDLE_CHAT_ID);
            String senderId = getArguments().getString(BundleConstants.BUNDLE_USER_ID);
            getViewModel().setGameQuestion(gameInfo.getQuestion());
            getViewModel().setSenderAnswer(gameInfo.getSenderAnswers());
            getViewModel().setReplyAnswer(gameInfo.getReceiverAnswers());
            getViewModel().setTopicGameStatus(gameInfo.getStatus());
            getViewModel().setChatId(chatId);
            getViewModel().setSenderId(senderId);
            initView();
            initData();
        }
    }

    private void initData() {
        getViewModel().getVoicePlayingLiveData().observe(this, new Observer<Float>() {
            @Override
            public void onChanged(Float aFloat) {
                if (getViewModel().getCurrVoiceType() == 1) {
                    getDataBinding().recordPlayView.setReadPercent(aFloat);
                } else {
                    getDataBinding().replyRecordPlayView.setReadPercent(aFloat);
                }
            }
        });

        getViewModel().getSendVoiceStatusLiveData().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer == MessageForAudio.STATUS_VOICE_START) {
                    if(objectAnimator !=null && objectAnimator.isRunning())objectAnimator.cancel();
                    getDataBinding().ivSendVoice.setImageResource(R.mipmap.chat_topic_game_icon_voice_start);
                }else if (integer == MessageForAudio.STATUS_VOICE_ERROR) {
                    if(objectAnimator !=null && objectAnimator.isRunning())objectAnimator.cancel();
                    getDataBinding().ivSendVoice.setImageResource(R.mipmap.chat_topic_game_icon_voice_start);
                } else  if(integer == MessageForAudio.STATUS_VOICE_DOWNLOADING){
                    getDataBinding().ivSendVoice.setImageResource(R.mipmap.chat_topic_game_icon_voice_downloading);
                    if(objectAnimator !=null && !objectAnimator.isRunning()){
                        objectAnimator.setAutoCancel(false);
                        objectAnimator.setDuration(500);
                        objectAnimator.start();
                    }
                }else {
                    if(objectAnimator !=null && objectAnimator.isRunning())objectAnimator.cancel();
                    getDataBinding().ivSendVoice.setImageResource(R.mipmap.chat_topic_game_icon_voice_pause);
                }
            }
        });

        getViewModel().getReplyVoiceStatusLiveData().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer == MessageForAudio.STATUS_VOICE_START) {
                    if(objectAnimatorReplay !=null && objectAnimatorReplay.isRunning())objectAnimatorReplay.cancel();
                    getDataBinding().ivReplyVoice.setImageResource(R.mipmap.chat_topic_game_icon_voice_start);
                }else if (integer == MessageForAudio.STATUS_VOICE_ERROR) {
                    if(objectAnimatorReplay !=null && objectAnimatorReplay.isRunning())objectAnimatorReplay.cancel();
                    getDataBinding().ivReplyVoice.setImageResource(R.mipmap.chat_topic_game_icon_voice_start);
                } else  if(integer == MessageForAudio.STATUS_VOICE_DOWNLOADING){
                    getDataBinding().ivReplyVoice.setImageResource(R.mipmap.chat_topic_game_icon_voice_downloading);
                    if(objectAnimatorReplay !=null && !objectAnimatorReplay.isRunning()){
                        objectAnimatorReplay.setAutoCancel(false);
                        objectAnimatorReplay.setDuration(500);
                        objectAnimatorReplay.start();
                    }
                }else {
                    if(objectAnimatorReplay !=null && objectAnimatorReplay.isRunning())objectAnimatorReplay.cancel();
                    getDataBinding().ivReplyVoice.setImageResource(R.mipmap.chat_topic_game_icon_voice_pause);
                }
            }
        });
    }

    ObjectAnimator objectAnimator =null;
    ObjectAnimator objectAnimatorReplay =null;

    private void initView() {
        ChatTopicGameQuestion gameQuestion = getViewModel().getGameQuestion();
        getDataBinding().tvTitle.setText(gameQuestion.title);

        ChatTopicGameAnswer senderAnswer = getViewModel().getSenderAnswer();
        getDataBinding().tvSendContent.setText(getViewModel().getSenderContent());
        boolean friend = !MessageUtils.isAuthor(getViewModel().getSenderId());
        if (friend) {
            ChatBindingAdapter.setAvatarUrl(getDataBinding().ivSend, getViewModel().getChatId());
        } else {
            ChatBindingAdapter.setAvatarUrl(getDataBinding().ivSend, AccountHelper.getInstance().getUserId());
        }
        if (TextUtils.equals(senderAnswer.type, "1")) {
            //文字
            getDataBinding().tvSendText.setVisibility(View.VISIBLE);
            getDataBinding().tvSendText.setText(senderAnswer.content);
        } else if (TextUtils.equals(senderAnswer.type, "2")) {
            //语音
            getDataBinding().llSendVoice.setVisibility(View.VISIBLE);
            setSendVoiceWidth(senderAnswer);
            getDataBinding().tvTime.setText(senderAnswer.duration + "s");
        }

        if (getViewModel().getTopicGameStatus() == MessageForTopicGame.TopicGameInfo.STATUS_OTHER_DO) {
            ChatTopicGameAnswer replyAnswer = getViewModel().getReplyAnswer();
            getDataBinding().clReply.setVisibility(View.VISIBLE);
            getDataBinding().tvReplyContent.setText(getViewModel().getReplyContent());
            if (friend) {
                ChatBindingAdapter.setAvatarUrl(getDataBinding().ivReply, AccountHelper.getInstance().getUserId());
            } else {
                ChatBindingAdapter.setAvatarUrl(getDataBinding().ivReply, getViewModel().getChatId());
            }
            if (TextUtils.equals(replyAnswer.type, "1")) {
                //文字
                getDataBinding().tvReplyText.setVisibility(View.VISIBLE);
                getDataBinding().tvReplyText.setText(replyAnswer.content);
            } else if (TextUtils.equals(replyAnswer.type, "2")) {
                //语音
                getDataBinding().llReplyOptionVoice.setVisibility(View.VISIBLE);
                setReplyVoiceWidth(replyAnswer);
                getDataBinding().tvReplyTime.setText(replyAnswer.duration + "s");
            }

            getDataBinding().tvChoseNoTitle.setText(R.string.chat_topic_game_chose_no_title_2);
        }

        String noChoseOptions = getViewModel().getNoChoseOptions();
        if (!TextUtils.isEmpty(noChoseOptions)) {
            getDataBinding().tvChoseNoTitle.setVisibility(View.VISIBLE);
            getDataBinding().tvChoseNoContent.setVisibility(View.VISIBLE);
            getDataBinding().tvChoseNoContent.setText(noChoseOptions);
        }
        objectAnimator = ObjectAnimator.ofFloat(getDataBinding().ivSendVoice,"rotation",0f,360f,0f);

        objectAnimator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if(getViewModel() != null && getViewModel().getSendVoiceStatusLiveData() != null &&
                        getViewModel().getSendVoiceStatusLiveData().getValue() == MessageForAudio.STATUS_VOICE_DOWNLOADING){
                    objectAnimator.start();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                ObjectAnimator.ofFloat(getDataBinding().ivSendVoice,"rotation",0f).start();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
        objectAnimatorReplay = ObjectAnimator.ofFloat(getDataBinding().ivReplyVoice,"rotation",0f,360f);
        objectAnimatorReplay.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if(getViewModel() != null && getViewModel().getReplyVoiceStatusLiveData() != null &&
                        getViewModel().getReplyVoiceStatusLiveData().getValue() == MessageForAudio.STATUS_VOICE_DOWNLOADING){
                    objectAnimatorReplay.start();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {
                ObjectAnimator.ofFloat(getDataBinding().ivReplyVoice,"rotation",0f).start();
            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });
    }

    private void setReplyVoiceWidth(ChatTopicGameAnswer replyAnswer) {
        ViewGroup.LayoutParams layoutParams = getDataBinding().llReplyOptionVoice.getLayoutParams();
        int minWidth = QMUIDisplayHelper.dp2px(activity, 80);
        int maxWidth = (QMUIDisplayHelper.getScreenWidth(activity) - QMUIDisplayHelper.dp2px(activity, 170));
        int length = replyAnswer.wave.length;
        int width = (int) (((float) (length - 6) / 29) * (maxWidth - minWidth) + minWidth);
        layoutParams.width = width;
        getDataBinding().llReplyOptionVoice.setLayoutParams(layoutParams);

        getDataBinding().replyRecordPlayView.setLumpCount(replyAnswer.wave.length);
        getDataBinding().replyRecordPlayView.setWaveData(replyAnswer.wave);
        getDataBinding().replyRecordPlayView.setReadPercent(getViewModel().getSendReadPercent());
    }

    private void setSendVoiceWidth(ChatTopicGameAnswer senderAnswer) {
        ViewGroup.LayoutParams layoutParams = getDataBinding().llSendVoice.getLayoutParams();
        int minWidth = QMUIDisplayHelper.dp2px(activity, 80);
        int maxWidth = (QMUIDisplayHelper.getScreenWidth(activity) - QMUIDisplayHelper.dp2px(activity, 170));
        int length = senderAnswer.wave.length;
        int width = (int) (((float) (length - 6) / 29) * (maxWidth - minWidth) + minWidth);
        layoutParams.width = width;
        getDataBinding().llSendVoice.setLayoutParams(layoutParams);

        getDataBinding().recordPlayView.setLumpCount(senderAnswer.wave.length);
        getDataBinding().recordPlayView.setWaveData(senderAnswer.wave);
        getDataBinding().recordPlayView.setReadPercent(getViewModel().getSendReadPercent());
    }

    @Override
    public void clickClose() {
        dismiss();
    }

    @Override
    public void startSendVoice() {
        getViewModel().voicePlay(1);
    }

    @Override
    public void startReplyVoice() {
        getViewModel().voicePlay(2);
    }

    @Override
    public void onPause() {
        super.onPause();
        getViewModel().onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getViewModel().release();
    }
}
