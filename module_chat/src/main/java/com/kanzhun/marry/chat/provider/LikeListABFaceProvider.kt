package com.kanzhun.marry.chat.provider

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.common.util.ProtocolHelper.Companion.parseProtocol
import com.kanzhun.common.util.load
import com.kanzhun.foundation.api.model.ResourceABFaceBean
import com.kanzhun.foundation.api.model.ThumbBean
import com.kanzhun.foundation.router.MePageRouter.jumpToInfoPreviewActivity
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.chat.adapter.LikeListTagAdapter
import com.kanzhun.marry.chat.databinding.ChatLikeListItemAbFaceBinding
import com.kanzhun.marry.chat.ext.getReadStatusBg
import com.kanzhun.marry.chat.ext.toLikeListResourceType
import com.kanzhun.marry.chat.item.LikeListABFaceBean
import com.kanzhun.marry.chat.likelist.ILikeList
import com.kanzhun.utils.GsonUtils

class LikeListABFaceProvider(val activity: FragmentActivity,val iLikeList: ILikeList) : BaseItemProvider<BaseListItem, ChatLikeListItemAbFaceBinding>() {
    override fun onBindItem(binding: ChatLikeListItemAbFaceBinding, item: BaseListItem) {
        if (item is LikeListABFaceBean) {
            binding.run {
                val thumbBean = item.thumbBean
                flAvatar.ivAvatar.load(thumbBean.tinyAvatar)
                setInfo(thumbBean)
                setResourceType(thumbBean)
                root.clickWithTrigger {
                    if(iLikeList.canDo(item.thumbBean.userStatus,item.thumbBean.phase))jumpToInfoPreviewActivity(activity, thumbBean.userId, "", PageSource.LIKE_LIST, "", "", "", thumbBean.securityId)

                    reportPoint("thumbsup-list-click") {
                        type = item.mLocalItemType.toLikeListResourceType()
                        peer_id = thumbBean.userId
                    }
                }

                clResourceTypeClickLayout.onClick {
                    if(item.thumbBean.jumpUrl?.isNotEmpty() == true)    {
                        parseProtocol(item.thumbBean.jumpUrl)
                        return@onClick
                    }
//                    jumpToInfoPreviewActivity(activity, AccountHelper.getInstance().userId, "", PageSource.LIKE_LIST)
                }
            }
        }

    }

    private fun ChatLikeListItemAbFaceBinding.setInfo(thumbBean: ThumbBean) {
        root.setBackgroundResource(thumbBean.getReadStatusBg())
        clInfo.tvUserName.text = thumbBean.nickName
        clInfo.tvStatus.textOrGone(thumbBean.statusTag)
        if (thumbBean.recommendTag.isNullOrEmpty()) {
            clInfo.tvTags.gone()
        } else {
            clInfo.tvTags.visible()
            clInfo.tvTags.setAdapter(LikeListTagAdapter(root.context, thumbBean.recommendTag))
        }
        clInfo.tvResourceType.text = thumbBean.thumbModuleContent
        clInfo.tvTime.text = thumbBean.timeStr
    }

    private fun ChatLikeListItemAbFaceBinding.setResourceType(thumbBean: ThumbBean) {
        val infoBean = GsonUtils.fromJson(thumbBean.resourceInfo, ResourceABFaceBean::class.java)
        clResourceType.ivAPhoto.load(infoBean?.photoA)
        clResourceType.ivBPhoto.load(infoBean?.photoB)
    }
}

