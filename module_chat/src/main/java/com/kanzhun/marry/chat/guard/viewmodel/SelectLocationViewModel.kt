package com.kanzhun.marry.chat.guard.viewmodel

import androidx.lifecycle.viewModelScope
import com.amap.api.maps.model.LatLng
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.foundation.kotlin.ktx.format
import com.kanzhun.foundation.ui.moment.toast
import com.kanzhun.http.createApi
import com.kanzhun.marry.chat.api.KChatApi
import com.kanzhun.marry.chat.api.model.AddressModel
import com.kanzhun.marry.chat.api.model.NearbySearchRequest
import com.kanzhun.marry.chat.api.model.NearbySearchResponse.PoiItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.UUID


/**
 * ViewModel for the location selection screen
 */
class SelectLocationViewModel : BaseViewModel() {

    // Current selected location
    private val _selectedLocation = MutableStateFlow<PoiItem?>(null)
    val selectedLocation: StateFlow<PoiItem?> = _selectedLocation.asStateFlow()

    // Current map center location
    private val _currentMapLocation = MutableStateFlow<LatLng?>(null)
    val currentMapLocation: StateFlow<LatLng?> = _currentMapLocation.asStateFlow()

    // List of nearby POIs
    private val _nearbyPois = MutableStateFlow<List<PoiItem>>(emptyList())
    val nearbyPois: StateFlow<List<PoiItem>> = _nearbyPois.asStateFlow()

    // Search results
    private val _searchResults = MutableStateFlow<List<PoiItem>>(emptyList())
    val searchResults: StateFlow<List<PoiItem>> = _searchResults.asStateFlow()

    // Loading state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    // Search query
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    // Is in search mode
    private val _isSearchMode = MutableStateFlow(false)
    val isSearchMode: StateFlow<Boolean> = _isSearchMode.asStateFlow()

    private val _addressModel = MutableStateFlow<AddressModel?>(null)
    val addressModel: StateFlow<AddressModel?> = _addressModel.asStateFlow()

    init {
        // 获取推荐的地址信息
        getMeetingRecommendAddressInfo()
    }

    /**
     * Update the current map location
     */
    fun updateMapLocation(
        latLng: LatLng,
        isMapInteractProgrammatically: Boolean = false,
        myLocation: LatLng? = null
    ) {
        _currentMapLocation.value = latLng

        if (isMapInteractProgrammatically) {
            return
        }

        // Search for POIs near this location
        performSearch(myLocation)
    }

    /**
     * Select a POI item
     */
    fun selectLocation(poiItem: PoiItem) {
        _selectedLocation.value = poiItem
    }

    /**
     * Set initial selected location based on coordinates and address
     * This is used when the user has already selected a location and wants to change it
     */
    fun setInitialSelectedLocation(
        latitude: Double?,
        longitude: Double?,
        address: String?,
        id: String? = null
    ) {
        if (latitude != null && longitude != null && address != null) {
            // Create a PoiItem with the provided information
            val poiItem = PoiItem(
                id = id ?: UUID.randomUUID().toString(), // 使用传入的ID，如果没有则生成新的
                name = address,
                address = "", // We don't have the full address here
                distance = "0",
                latitude = latitude.toString(),
                longitude = longitude.toString()
            )

            // Set as selected location
            _selectedLocation.value = poiItem

            // Also update map location
            _currentMapLocation.value = LatLng(latitude, longitude)
        }
    }

    /**
     * Update search query
     */
    fun updateSearchQuery(query: String, myLocation: LatLng? = null) {
        _searchQuery.value = query
        if (query.isBlank()) {
            _isSearchMode.value = false
            _searchResults.value = emptyList()
        } else {
            _isSearchMode.value = true

            performSearch(myLocation)
        }
    }

    /**
     * Get recommended address information from the server
     */
    fun getMeetingRecommendAddressInfo() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val recAddressResp = withContext(Dispatchers.IO) {
                    createApi(KChatApi::class.java).getMeetingRecommendAddressInfo()
                }

                if (recAddressResp.isSuccess) {
                    _addressModel.value = recAddressResp.data
                } else {
                    toast(recAddressResp.msg ?: "获取推荐地址信息失败")
                }
            } catch (e: Exception) {
                toast(e.message ?: "获取推荐地址信息失败")
            } finally {
                _isLoading.value = false

                if (isQaDebugUser()) {
                    _addressModel.value = AddressModel(
                        selectedAddressName = "星巴克咖啡(三里屯店)",
                        selectedAddress = "星巴克咖啡(三里屯店)详细地址",
                        selectedDistrict = "朝阳区",
                        selectedCity = "北京市",

                        id = UUID.randomUUID().toString(),
                        gps = "116.455154,39.934219",
                        addressName = "星巴克咖啡(三里屯店)",
                        address = "北京市朝阳区三里屯路19号",
                        showCoupon = 1,
                        recommendPics = listOf(
                            "http://gips1.baidu.com/it/u=1971954603,2916157720&fm=3028&app=3028&f=JPEG&fmt=auto?w=1920&h=2560",
                            "http://gips3.baidu.com/it/u=119870705,2790914505&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=720",
                            "http://gips3.baidu.com/it/u=4283915297,3700662292&fm=3028&app=3028&f=JPEG&fmt=auto?w=1440&h=2560",
                        )
                    )
                }
            }
        }
    }

    /**
     * Perform search
     */
    fun performSearch(myLocation: LatLng? = null) {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                // 获取当前位置
                val currentLatLng = _currentMapLocation.value ?: return@launch

                val query = _searchQuery.value

                // 创建请求参数
                val request = NearbySearchRequest(
                    longitude =
                        // 经度，范围[-180,180]，小数点后不超过6位
                        currentLatLng.longitude.coerceIn(-180.0, 180.0).format(6).toString(),
                    latitude =
                        // 纬度，范围[-180,180]，小数点后不超过6位
                        currentLatLng.latitude.coerceIn(-180.0, 180.0).format(6).toString(),
                    keywords = query,  // 搜索关键词
                    userGps =
                        if (myLocation != null) {
                            "${
                                myLocation.longitude.coerceIn(-180.0, 180.0).format(6)
                            },${
                                myLocation.latitude.coerceIn(-180.0, 180.0).format(6)
                            }"
                        } else "", // 用户当前位置的经纬度，格式为："经度,纬度"，例如："116.4551,39.9296"
                )

                // 调用API
                val response = withContext(Dispatchers.IO) {
                    createApi(KChatApi::class.java).nearbySearch(request = request.toQueryMap())
                }

                if (response.isSuccess) {
                    if (query.isEmpty()) {
                        _nearbyPois.value = response.data.pois
                    } else {
                        _searchResults.value = response.data.pois
                    }
                } else {
                    toast(response.msg ?: "搜索失败")
                }
            } catch (e: Exception) {
                toast("搜索失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clear search
     */
    fun clearSearch() {
        _searchQuery.value = ""
        _isSearchMode.value = false
        _searchResults.value = emptyList()
    }
}
