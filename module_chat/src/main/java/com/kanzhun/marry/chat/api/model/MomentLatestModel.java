package com.kanzhun.marry.chat.api.model;

import com.kanzhun.foundation.api.bean.PictureListItemBean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/14.
 */
public class MomentLatestModel implements Serializable {
    private static final long serialVersionUID = -400968643429579942L;
    public static final int READ_MOMENT = 1;//阅读过

    public String userId;// 用户id
    public int age;//年龄
    public int constellation;// 星座: 1-摩羯座; 2-水瓶座; 3-双鱼座; 4-白羊座; 5-金牛座; 6-双子座; 7-巨蟹座; 8-狮子座; 9-处女座; 10-天秤座; 11-天蝎座; 12-射手座
    public int height;// 身高 cm
    public String school;//学校名称
    public String industry;// 行业
    public String career;// 职业
    public String addressLevel1;// 现居地 省/直辖市
    public String addressLevel2;// 现居地 市/直辖市的区
    public String momentId;// 动态id
    public int read;// 是否阅读过，1-是，0-否
    public long createTime;// 创建时间
    public String content;// 内容
    public List<PictureListItemBean> pictures;// 图片列表


}
