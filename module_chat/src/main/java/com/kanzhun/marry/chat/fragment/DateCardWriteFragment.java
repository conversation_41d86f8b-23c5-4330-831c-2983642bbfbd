package com.kanzhun.marry.chat.fragment;

import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.databinding.ChatFragmentDateCardWriteBinding;

import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.kanzhun.foundation.dialog.DateModeDescriptionDialog;
import com.kanzhun.marry.chat.viewmodel.DateCardViewModel;
import com.kanzhun.marry.chat.viewmodel.DateCardWriteViewModel;
import com.kanzhun.marry.chat.callback.DateCardWriteCallback;
import com.kanzhun.utils.views.MultiClickUtil;

/**
 * 相识卡 - 写张相识卡
 * <p>
 * Created by <PERSON><PERSON> on 2022/5/27
 */
public class DateCardWriteFragment extends FoundationVMShareFragment<ChatFragmentDateCardWriteBinding, DateCardWriteViewModel, DateCardViewModel> implements DateCardWriteCallback {

    @Override
    protected void initFragment() {
        super.initFragment();
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_fragment_date_card_write;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickSubmit(View view) {
        if (MultiClickUtil.isMultiClick()) return;
        ChatPageRouter.jumpToDateCardInputActivity(activity, getActivityViewModel().chatId);
    }

    @Override
    public void clickDateMode() {
        // 显示相识模式说明页
        new DateModeDescriptionDialog().show(((FragmentActivity) activity).getSupportFragmentManager(),
                DateModeDescriptionDialog.TAG);
    }
}