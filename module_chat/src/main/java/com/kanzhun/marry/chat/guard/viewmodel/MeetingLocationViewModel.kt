package com.kanzhun.marry.chat.guard.viewmodel

import androidx.lifecycle.viewModelScope
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.foundation.ui.moment.toast
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.createApi
import com.kanzhun.marry.chat.api.KChatApi
import com.kanzhun.marry.chat.api.model.MeetingPlanSettingDetailResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * ViewModel for the MeetingLocationActivity
 */
class MeetingLocationViewModel : BaseViewModel() {
    private val _settingDetail = MutableStateFlow<MeetingPlanSettingDetailResponse?>(null)
    val settingDetail: StateFlow<MeetingPlanSettingDetailResponse?> = _settingDetail.asStateFlow()

    // Loading state
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    /**
     * Fetch meeting plan setting detail from the server
     * @param recordId The record ID to fetch details for
     */
    fun getMeetingPlanSettingDetail(recordId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = withContext(Dispatchers.IO) {
                    createApi(KChatApi::class.java).getMeetingPlanSettingDetail(recordId)
                }

                if (response.isSuccess) {
                    _settingDetail.value = response.data

                    reportPoint("O2-seeyou-seeyouhint-show") {
                        actionp2 =
                            if (response.data.protectOpenStatus == true) "看准安全守护中" else "建议开启安全守护功能，见面有保障" // 引导条文案
                        peer_id = response.data.peerId ?: ""
                    }
                } else {
                    toast(response.msg ?: "获取见面计划设置详情失败")
                }
            } catch (e: Exception) {
                toast(e.message ?: "获取见面计划设置详情失败")
            } finally {
                _isLoading.value = false

                if (isQaDebugUser()) {
                    mockAfter15Minutes()
                }
            }
        }
    }

    fun mockAfter15Minutes() {
        viewModelScope.launch {
            _settingDetail.emit(
                MeetingPlanSettingDetailResponse(
                    recordId = "12345",
                    meetTime = System.currentTimeMillis() - 20 * 60 * 1000,
                    addressName = "星巴克(三里屯太古里店)",
                    address = "北京市朝阳区三里屯路19号三里屯太古里北区N3-B1",
                    recommendAddress = 1,
                    showCoupon = 1,
                    recommendPics = listOf(
                        "http://gips1.baidu.com/it/u=1971954603,2916157720&fm=3028&app=3028&f=JPEG&fmt=auto?w=1920&h=2560",
                        "http://gips3.baidu.com/it/u=119870705,2790914505&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=720",
                        "http://gips3.baidu.com/it/u=4283915297,3700662292&fm=3028&app=3028&f=JPEG&fmt=auto?w=1440&h=2560",
                    ),
                    addressPicUrl = "http://gips3.baidu.com/it/u=119870705,2790914505&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=720",
                    province = "北京市",
                    city = "北京市",
                    district = "朝阳区",
                    gps = "116.455154,39.934219",

                    protectOpenStatus = true,
                    arriveStatus = false,
                )
            )
        }
    }

    fun mockBefore15Minutes() {
        viewModelScope.launch {
            _settingDetail.emit(
                MeetingPlanSettingDetailResponse(
                    recordId = "12345",
                    meetTime = System.currentTimeMillis() + 20 * 60 * 1000,
                    addressName = "星巴克(三里屯太古里店)",
                    address = "北京市朝阳区三里屯路19号三里屯太古里北区N3-B1",
                    recommendAddress = 1,
                    showCoupon = 1,
                    recommendPics = listOf(
                        "http://gips1.baidu.com/it/u=1971954603,2916157720&fm=3028&app=3028&f=JPEG&fmt=auto?w=1920&h=2560",
                        "http://gips3.baidu.com/it/u=119870705,2790914505&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=720",
                        "http://gips3.baidu.com/it/u=4283915297,3700662292&fm=3028&app=3028&f=JPEG&fmt=auto?w=1440&h=2560",
                    ),
                    addressPicUrl = "http://gips3.baidu.com/it/u=119870705,2790914505&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=720",
                    province = "北京市",
                    city = "北京市",
                    district = "朝阳区",
                    gps = "116.455154,39.934219",

                    protectOpenStatus = true,
                    arriveStatus = false,
                )
            )
        }
    }
}
