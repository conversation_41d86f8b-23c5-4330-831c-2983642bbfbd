package com.kanzhun.marry.chat.viewmodel;

import static com.kanzhun.foundation.TestKt.isQaDebugUser;
import static com.kanzhun.foundation.model.message.MessageConstants.MSG_SEQ_STARTING_VALUE;
import static com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_KZ_ACTIVITY;

import android.app.Activity;
import android.app.Application;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.Nullable;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.dialog.model.SelectBottomBean;
import com.kanzhun.common.recorder.MediaPlayerManager;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.views.image.DraggableParamsInfo;
import com.kanzhun.foundation.SystemConfigInstance;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.bean.MessageResponse;
import com.kanzhun.foundation.api.callback.MessagePbApiRequestCallback;
import com.kanzhun.foundation.imageviewer.ImageExtraBean;
import com.kanzhun.foundation.imageviewer.ImageExtraIndexBean;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.MessageService;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.Draft;
import com.kanzhun.foundation.model.ReplyBean;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageForAudio;
import com.kanzhun.foundation.model.message.MessageForLike;
import com.kanzhun.foundation.model.message.MessageForText;
import com.kanzhun.foundation.model.message.MessageForTopicGame;
import com.kanzhun.foundation.model.message.MessageForVideo;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.foundation.utils.HiMessage;
import com.kanzhun.foundation.utils.MessageUtils;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.ICommonCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.api.ChatApi;
import com.kanzhun.marry.chat.api.bean.ClearMessagesBean;
import com.kanzhun.marry.chat.model.CardProcessBean;
import com.kanzhun.marry.chat.model.ChatClickOptionBean;
import com.kanzhun.marry.chat.model.ImageViewerResult;
import com.kanzhun.marry.chat.model.MsgCountBean;
import com.kanzhun.marry.chat.model.PanelFunc;
import com.kanzhun.marry.chat.util.ChatAudioManager;
import com.kanzhun.marry.chat.util.ChatHelper;
import com.kanzhun.marry.chat.util.ChatRecordUtil;
import com.kanzhun.marry.chat.util.ChatUtils;
import com.kanzhun.utils.GsonUtils;
import com.kanzhun.utils.T;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

/**
 * Created by guofeng
 * on 2019/3/12.
 */

public abstract class ChatBaseViewModel extends SendMessageViewModel {
    private static final String TAG = "ChatBaseViewModel";
    protected boolean isSysChatType = false; //是否是系统会话
    private String chatId;
    private String nickName = "Ta";
    private List<SelectBottomBean> mVideoChatList = new ArrayList<>();
    private List<SelectBottomBean> mFileChatList = new ArrayList<>();
    protected MutableLiveData<ImageExtraBean> imageExtraLiveData = new MutableLiveData<>();
    private boolean mHasLastestMessage = false;
    protected Draft mDraft = new Draft();
    private ReplyBean mReplyBean;
    private CardProcessBean mCardProcessBean;
    private Conversation mConversation;

    private MutableLiveData<Boolean> delSuccessLiveData = new MutableLiveData<>();
    protected ObservableField<String> inputField = new ObservableField<>();
    protected ObservableBoolean voiceMode = new ObservableBoolean(false);
    private ObservableBoolean isFriend = new ObservableBoolean(false);//是否是朋友关系，不是朋友关系无法发送消息
    private ObservableBoolean isReply = new ObservableBoolean(false);//是否在reply
    private ObservableBoolean showEmotionTips = new ObservableBoolean(false);//是否展示底部emotion提示
    private boolean showVoiceItem = false;//是否展示语音通话入口，默认不展示，进入相识模式、情侣模式才显示
    private int status = -1; // 用户状态1 正常 2 封禁 3 待注销 4 已注销

    protected MutableLiveData<ImageViewerResult> imagePreviewLiveData = new MutableLiveData<>(); //图片点击跳转监听
    protected ObservableBoolean userLocked = new ObservableBoolean(false);

    protected MutableLiveData<MessageForTopicGame> topicGameClickLiveData = new MutableLiveData<>();
    private long systemId;

    /**
     * constructor
     *
     * @param application
     */
    public ChatBaseViewModel(Application application) {
        super(application);
    }

    public abstract int getType();

    /**
     * 获取chatId
     *
     * @return
     */
    public String getChatId() {
        return chatId;
    }

    /**
     * init
     *
     * @param chatId
     */
    public void init(String chatId, long systemId) {
        this.chatId = chatId;
        this.systemId = systemId;
    }

    @Override
    protected void initData() {
        super.initData();
    }

    public boolean isSysChatType() {
        return isSysChatType;
    }

    public ReplyBean getReplyBean() {
        return mReplyBean;
    }

    public void setReplyBean(ReplyBean mReplyBean) {
        this.mReplyBean = mReplyBean;
    }

    public boolean isShowVoiceItem() {
        return showVoiceItem;
    }

    /**
     * 更新已读状态
     */
    public void updateRead() {
        if (Thread.currentThread() == Looper.getMainLooper().getThread()) {
            ExecutorFactory.execLocalTask(new Runnable() {
                @Override
                public void run() {
                    excultRead();
                }
            });
        } else {
            excultRead();
        }
    }

    public void excultRead() {
        Conversation conversation = ServiceManager.getInstance().getConversationService().getByUid(getChatId(), getType());
        if (conversation != null) {
            if (conversation.getUnreadCount() > 0 || conversation.getReadSyncId() == 0) {//conversation.getReadSyncId() == 0 没有同步会话，先来的消息，本地生成的会话，但是可以从喜欢我或者我喜欢等列表进入，该情形下在线时候收到消息，则unreadcount > 0.否则为0
                MessageService service = ServiceManager.getInstance().getMessageService();
                service.sendRead(conversation.getChatId(), getType());
            }
        } else {
            MessageService service = ServiceManager.getInstance().getMessageService();
            service.sendRead(getChatId(), getType());
        }
    }

    public Contact getContact(ChatMessage chatMessage) {
        return getContact(chatMessage.getSender());
    }

    public Contact getContact(String userId) {
        return ServiceManager.getInstance().getContactService().getContactById(userId);
    }

    public String getSecurityId() {
        if (TextUtils.isEmpty(chatId)) {
            return "";
        }
        if (getContact(chatId) == null) {
            return "";
        }
        return getContact(chatId).getSecurityId();
    }

    public boolean isContactLocked() {
        Contact contact = getContact(chatId);
        if (contact == null) {
            return false;
        }
        return contact.getProfileLocked() == 1;
    }

    public String getTinyAvatar(ChatMessage chatMessage) {
        String avatar = "";
        if (TextUtils.equals(chatMessage.getSender(), AccountHelper.getInstance().getUserId())) {
            User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
            if (user != null) {
                avatar = user.getTinyAvatar();
            }
        } else {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender());
            if (contact != null) {
                avatar = contact.getTinyAvatar();
            }
        }
        return avatar;
    }

    /**
     * 根据seq获取HiMessage
     *
     * @return
     */
    public LiveData<HiMessage> getMessage() {
        LiveData<HiMessage> hiMessageLiveData = ServiceManager.getInstance().getMessageService().register(getChatId(), getType());
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                ServiceManager.getInstance()
                        .getMessageService().getContactMessages(getChatId(), getType(), null, isMeetingPlan(), getModeMinSeq());
            }
        });
        return hiMessageLiveData;
    }

    public void requestMessages(Boolean isEarlier) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                ServiceManager.getInstance()
                        .getMessageService().getContactMessages(getChatId(), getType(), isEarlier, isMeetingPlan(), getModeMinSeq());
            }
        });
    }

    public void applyDraft(EditText input, @Nullable Runnable onDraftSetDone) {
        ExecutorFactory.execLocalTask(() -> {
            Conversation byUid = ServiceManager.getInstance().getConversationService().getByUid(getChatId(), getType());
            if (byUid == null || TextUtils.isEmpty(byUid.getDraftContent())) {
                postOnDraftSetDone(onDraftSetDone);
                return;
            }

            Draft draft = byUid.getDraft();
            if (draft == null) {
                postOnDraftSetDone(onDraftSetDone);
                return;
            }

            mDraft.setContent(draft.getContent());
            new Handler(Looper.getMainLooper()).post(() -> {
                input.setText(byUid.getDraftContent());
                input.setSelection(byUid.getDraftContent().length());

                postOnDraftSetDone(onDraftSetDone);
            });
        });
    }

    private void postOnDraftSetDone(@Nullable Runnable onDraftSet) {
        new Handler(Looper.getMainLooper()).post(() -> {
            if (onDraftSet != null) {
                onDraftSet.run();
            }
        });
    }

    public boolean isHasLastestMessage() {
        return mHasLastestMessage;
    }

    public void setHasLastestMessage(boolean hasLastestMessage) {
        this.mHasLastestMessage = hasLastestMessage;
    }


    /**
     * 获取聊天长按option的操作列表
     *
     * @param chatMessage 消息
     * @return
     */
    public List<ChatClickOptionBean> getChatClickOptions(ChatMessage chatMessage) {
        List<ChatClickOptionBean> chatClickOptionBeans = new ArrayList<>();
        boolean sendSuccess = chatMessage.getStatus() >= MessageConstants.MSG_STATE_DELIVERY;
        if (chatMessage instanceof MessageForText) { //复制
            chatClickOptionBeans.add(new ChatClickOptionBean(R.string.chat_copy, R.mipmap.chat_option_copy2, chatMessage));
        }
        if (isSysChatType()) {
            return chatClickOptionBeans;
        }
        if (!userLocked.get() && sendSuccess && isFriend.get() && MessageUtils.canOperate(chatMessage)) {//回复
            ChatClickOptionBean chatClickOptionBean = new ChatClickOptionBean(R.string.chat_quote2, R.mipmap.chat_option_reply2, chatMessage);
            chatClickOptionBeans.add(chatClickOptionBean);
        }
        boolean isOverTime = System.currentTimeMillis() - chatMessage.getTime() > 120 * 60 * 1000;
        if (!userLocked.get() && sendSuccess && MessageUtils.isAuthor(chatMessage) && isFriend.get() && !isOverTime && MessageUtils.canOperate(chatMessage)) {//撤回
            ChatClickOptionBean chatClickOptionBean = new ChatClickOptionBean(R.string.chat_withdrawn, R.mipmap.chat_option_withdrawn2, chatMessage);
            chatClickOptionBeans.add(chatClickOptionBean);
        }
        if (MessageUtils.canOperate(chatMessage)) {
            ChatClickOptionBean chatClickOptionBean = new ChatClickOptionBean(R.string.chat_delete, R.mipmap.chat_option_delete2, chatMessage);
            chatClickOptionBeans.add(chatClickOptionBean);
        }
        return chatClickOptionBeans;
    }

    public void resetPatches(String chatId, int chatType) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                ServiceManager.getInstance().getMessageService().resetPatches(chatId, chatType);
            }
        });
    }

    public List<PanelFunc> getPanelFuncs() {
        List<PanelFunc> panelFuncs = new ArrayList<>();
        panelFuncs.add(new PanelFunc(R.mipmap.chat_ic_take_photo, "拍照", PanelFunc.TAKE_PHOTO));
        panelFuncs.add(new PanelFunc(R.mipmap.chat_ic_from_galary, "图片", PanelFunc.CHOOSE_GALLERY));
        if (showVoiceItem) {
            panelFuncs.add(new PanelFunc(R.mipmap.chat_ic_voice_talk, "语音通话", PanelFunc.VOICE_CALL));
        }

        if (!isMeetingPlan()) { // 见面计划聊天没有这个功能
            panelFuncs.add(new PanelFunc(R.mipmap.chat_ic_topic_game, "话题游戏", PanelFunc.TOPIC_GAME));

            CardProcessBean cardProcessBean = mCardProcessBean;
            if (cardProcessBean != null && cardProcessBean.tacitTest != null || isQaDebugUser()) {
                if (cardProcessBean != null && cardProcessBean.tacitTest != null && cardProcessBean.tacitTest.status == 2 || isQaDebugUser()) { // 已解锁时展示
                    panelFuncs.add(new PanelFunc(R.mipmap.chat_ic_privity_entry, "默契考验", PanelFunc.TACIT_UNDERSTANDING_TEST));
                }
            }
        }

        checkRenderDateLoveCard(panelFuncs);
        return panelFuncs;
    }

    public LiveData<Integer> onPlayAudio(MessageForAudio messageForAudio) {
        MutableLiveData<Integer> liveData = new MutableLiveData<>();
        if (ChatAudioManager.getInstance().isPlaying()
                && !TextUtils.isEmpty(messageForAudio.getAudioInfo().getUrl())
                && messageForAudio.getMid() == ChatAudioManager.getInstance().getMid()
                && messageForAudio.getAudioInfo().getUrl().equals(ChatAudioManager.getInstance().getPlayingUrl())) {
            ChatAudioManager.getInstance().pause();
            liveData.setValue(MessageForAudio.STATUS_VOICE_PAUSE);
        } else {
            ChatAudioManager.getInstance().setMid(messageForAudio.getMid());
            if (messageForAudio.getVoiceStatus() == MessageForAudio.STATUS_VOICE_PAUSE) {
                ChatAudioManager.getInstance().rePlay((int) (messageForAudio.getReadPercent() * 1000 * messageForAudio.getDuration()), messageForAudio.getAudioInfo().getUrl(), messageForAudio.getAudioInfo().getLocalPath(), new MediaPlayerManager.PlayingListener() {
                    @Override
                    public void playCallback() {
                        liveData.setValue(MessageForAudio.STATUS_VOICE_PLAYING);
                    }

                    @Override
                    public void onComplete() {
                        liveData.setValue(MessageForAudio.STATUS_VOICE_START);
                    }

                });
            } else {
                ChatAudioManager.getInstance().play(messageForAudio.getAudioInfo().getUrl(), messageForAudio.getAudioInfo().getLocalPath(), new MediaPlayerManager.PlayingListener() {
                    @Override
                    public void playCallback() {
                        liveData.setValue(MessageForAudio.STATUS_VOICE_PLAYING);
                    }

                    @Override
                    public void onComplete() {
                        liveData.setValue(MessageForAudio.STATUS_VOICE_START);
                    }
                });
            }
        }
        return liveData;
    }

    public LiveData<Integer> onPlayAudio(MessageForLike messageForLike) {
        MutableLiveData<Integer> liveData = new MutableLiveData<>();
        if (ChatAudioManager.getInstance().isPlaying()
                && !TextUtils.isEmpty(messageForLike.getLikeInfo().answer.answer)
                && messageForLike.getMid() == ChatAudioManager.getInstance().getMid()
                && messageForLike.getLikeInfo().answer.answer.equals(ChatAudioManager.getInstance().getPlayingUrl())) {
            ChatAudioManager.getInstance().pause();
            liveData.setValue(MessageForAudio.STATUS_VOICE_PAUSE);
        } else {
            ChatAudioManager.getInstance().setMid(messageForLike.getMid());
            if (messageForLike.getVoiceStatus() == MessageForAudio.STATUS_VOICE_PAUSE) {
                ChatAudioManager.getInstance().rePlay((int) (messageForLike.getReadPercent() * 1000 * messageForLike.getLikeInfo().voiceExtInfoBean.duration),
                        messageForLike.getLikeInfo().answer.answer, null,
                        new MediaPlayerManager.PlayingListener() {
                            @Override
                            public void playCallback() {
                                liveData.setValue(MessageForAudio.STATUS_VOICE_PLAYING);
                            }

                            @Override
                            public void onComplete() {
                                liveData.setValue(MessageForAudio.STATUS_VOICE_START);
                            }

                        });
            } else {
                ChatAudioManager.getInstance().play(messageForLike.getLikeInfo().answer.answer, null, new MediaPlayerManager.PlayingListener() {
                    @Override
                    public void playCallback() {
                        liveData.setValue(MessageForAudio.STATUS_VOICE_PLAYING);
                    }

                    @Override
                    public void onComplete() {
                        liveData.setValue(MessageForAudio.STATUS_VOICE_START);
                    }
                });
            }
        }
        return liveData;
    }

    // 相识卡、表白信按钮是否显示过，用于拦截第一次默认请求时设置sp
    public boolean dateLoveCardHasShown = false;

    /**
     * 相识卡、表白信，按钮的显示判断
     */
    private void checkRenderDateLoveCard(List<PanelFunc> panelFuncs) {
        if (!isMeetingPlan()) {
            if (mCardProcessBean != null) {
                MsgCountBean dateCard = mCardProcessBean.dateCard;
                if (dateCard != null) {
                    int progress = (int) ((dateCard.msgCount * 1.0f / dateCard.maxCount) * 100);
                    int icon = dateCard.status == 1 ? R.mipmap.chat_ic_date_card_lock : R.mipmap.chat_ic_date_card;
                    panelFuncs.add(new PanelFunc(icon, "相识卡",
                            PanelFunc.APPOINTMENT_CARD, progress, dateCard.status == 1,
                            dateCard.status == 2 ? 0 : dateCard.status));
                }

                MsgCountBean wechatExchange = mCardProcessBean.wechatExchange;
                if (wechatExchange != null) {
                    int progress = (int) ((wechatExchange.msgCount * 1.0f / wechatExchange.maxCount) * 100);
                    int icon = wechatExchange.status == 2 ? R.mipmap.chat_ic_change_wx : R.mipmap.chat_ic_change_wx_unlock;
                    panelFuncs.add(new PanelFunc(icon, "交换微信",
                            PanelFunc.CHANGE_WX, progress, wechatExchange.status == 1,
                            wechatExchange.status == 2 ? 0 : wechatExchange.status));
                }

                MsgCountBean loveCard = mCardProcessBean.loveCard;
                if (loveCard != null) {
                    int progress = (int) ((loveCard.msgCount * 1.0f / loveCard.maxCount) * 100);
                    int icon = loveCard.status == 1 ? R.mipmap.chat_ic_love_card_lock : R.mipmap.chat_ic_love_card;
                    panelFuncs.add(new PanelFunc(icon, "表白信",
                            PanelFunc.CONFESSION_LETTER, progress, loveCard.status == 1,
                            loveCard.status == 2 ? 0 : loveCard.status));
                }


            }

            if (SystemConfigInstance.INSTANCE.isOpenChatAssistantGray() || isQaDebugUser()) {
                // 见面计划聊天没有这个功能
                panelFuncs.add(new PanelFunc(R.mipmap.chat_ic_ai_entry, "聊天助手", PanelFunc.CHAT_ASSISTANT));
            }
        }
    }

    /**
     * 获取相识卡、表白信状态和进度
     */
    public void getCardProcess(ICommonCallback callback) {
        if (isSysChatType()) {
            return;
        }

        Observable<BaseResponse<CardProcessBean>> baseResponseObservable = RetrofitManager.getInstance().createApi(ChatApi.class).getCardProcess(chatId);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<CardProcessBean>(true) {
            @Override
            public void onSuccess(CardProcessBean data) {
                mCardProcessBean = data;
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }

            @Override
            public void showFailed(ErrorReason reason) {
            }
        });
    }

    public void getContactImages(ImageExtraIndexBean imageExtraIndexBean, DraggableParamsInfo draggableParamsInfo) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                List<ChatMessage> listPicList = getChatMessagesList();
                final ImageExtraBean imageList = ChatUtils.filterImageAndVideo(listPicList, imageExtraIndexBean);
                if (draggableParamsInfo != null) {
                    imageList.setDraggableParamsInfo(draggableParamsInfo);
                }
                imageExtraLiveData.postValue(imageList);
            }
        });
    }

    public List<ChatMessage> getChatMessagesList() {
        return ServiceManager.getInstance().getMessageService().getContactPicAndVideoMessages(getChatId(), getType());
    }

    public CardProcessBean getCardProcessBean() {
        return mCardProcessBean;
    }

    public Conversation getConversation() {
        return mConversation;
    }

    public void setConversation(Conversation conversation) {
        this.mConversation = conversation;
    }

    public MutableLiveData<ImageExtraBean> getImageExtraLiveData() {
        return imageExtraLiveData;
    }

    public void onVideo(Activity context, MessageForVideo messageForVideo) {
        ChatRecordUtil.toOpenVideoForChatRecord(context, messageForVideo);
    }

    //取消匹配
    public void deleteMatchFriend() {
        Map<String, Object> map = new HashMap<>();
        map.put("chatId", chatId);
        HttpExecutor.requestSimplePost(URLConfig.URL_CHAT_DEL_FRIEND, map, new SimpleRequestCallback(true) {

            @Override
            public void onSuccess() {
                ExecutorFactory.execLocalTask(new Runnable() {
                    @Override
                    public void run() {
                        ServiceManager.getInstance().getConversationService().delete(chatId, getType());
                        delSuccessLiveData.postValue(true);
                    }
                });
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }
        });
    }

    //结束情侣模式
    public void endLove() {
        Map<String, Object> map = new HashMap<>();
        map.put("chatId", chatId);
        HttpExecutor.requestSimplePost(URLConfig.URL_CHAT_END_LOVE, map, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                SpManager.putUserInt("match_card", -1);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });
    }

    //结束相识模式
    public void endDate() {
        Map<String, Object> map = new HashMap<>();
        map.put("chatId", chatId);
        HttpExecutor.requestSimplePost(URLConfig.URL_CHAT_END_DATE, map, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                SpManager.putUserInt("match_card", -1);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });
    }

    public MutableLiveData<Boolean> getDelSuccessLiveData() {
        return delSuccessLiveData;
    }

    /**
     * 撤回消息
     */
    public void revertMessage(ChatMessage chatMessage) {
        long msgId = chatMessage.getMid();
        Map<String, Object> map = new HashMap<>();
        map.put("msgId", msgId);
        HttpExecutor.requestSimplePost(URLConfig.URL_CHAT_WITHDRAW, map, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                ExecutorFactory.execLocalTask(() -> {
                    ServiceManager.getInstance().getMessageService().insertWithdrawnMessage(chatMessage);

                    if (chatMessage.getMediaType() == MessageConstants.MSG_TEXT) {
                        ServiceManager.getInstance().getMessageService().getLocalWithDrawMsg().put(-msgId, System.currentTimeMillis());
                    }
                });
            }

            @Override
            public void dealFail(ErrorReason reason) {
                TLog.error(TAG, "revertMessage: " + reason.getErrReason());
            }
        });
    }

    public ObservableField<String> getInputField() {
        return inputField;
    }

    public void setInputField(ObservableField<String> inputField) {
        this.inputField = inputField;
    }

    public ObservableBoolean getVoiceMode() {
        return voiceMode;
    }

    public void setVoiceMode(ObservableBoolean voiceMode) {
        this.voiceMode = voiceMode;
    }

    public ObservableBoolean getIsFriend() {
        return isFriend;
    }

    public void setIsFriend(boolean isFriend) {
        this.isFriend.set(isFriend);
    }

    public ObservableBoolean getIsReply() {
        return isReply;
    }

    public void setIsReply(boolean isReply) {
        this.isReply.set(isReply);
    }

    public ObservableBoolean getShowEmotionTips() {
        return showEmotionTips;
    }

    public void setShowEmotionTips(ObservableBoolean showEmotionTips) {
        this.showEmotionTips = showEmotionTips;
    }

    public List<PanelFunc> refreshPanelFunc(boolean showVoiceItem) {
        this.showVoiceItem = showVoiceItem;
        return getPanelFuncs();
    }


    /**
     * 获取大图预览所需数据
     *
     * @param chatMessage
     * @param view
     */
    public void getPreviewImages(ChatMessage chatMessage, View view) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                List<ChatMessage> listPicList = ServiceManager.getInstance().getMessageService().getContactPicAndVideoMessages(getChatId(), getType());
                ImageViewerResult imageViewerBean = ChatHelper.filterPreviewImages(listPicList, chatMessage, view);
                imagePreviewLiveData.postValue(imageViewerBean);
            }
        });
    }

    public String getNickName(String userId) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contact != null) {
            return contact.getNickName();
        }
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public MutableLiveData<ImageViewerResult> getImagePreviewLiveData() {
        return imagePreviewLiveData;
    }

    public void saveDraft(String content) {
        boolean isSave = !TextUtils.equals(content, mDraft.getContent());
        if (isSave) {
            ExecutorFactory.execLocalTask(new Runnable() {
                @Override
                public void run() {
                    mDraft.setContent(content);
                    String draftContent = GsonUtils.getGson().toJson(mDraft);
                    ServiceManager.getInstance().getConversationService().saveDraft(getChatId(), getType(), draftContent);
                }
            });
        }
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void getTopicGameMessage(long msgId) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                ChatMessage chatMessage = ServiceManager.getInstance().getMessageService().getChatMessage(msgId);
                if (chatMessage != null && chatMessage.getMediaType() == MessageConstants.MSG_TOPIC_GAME) {
                    MessageForTopicGame messageForTopicGame = (MessageForTopicGame) chatMessage;
                    topicGameClickLiveData.postValue(messageForTopicGame);
                } else {
                    HashMap<String, Object> messageKey = new HashMap<>();
                    messageKey.put("msgIds", msgId);
                    HttpExecutor.executeJsonGet(URLConfig.URL_GET_MSG_BY_ID, messageKey, new MessagePbApiRequestCallback() {
                        @Override
                        public void handleInChildThread(MessageResponse data) {
                            super.handleInChildThread(data);
                            if (data.messages != null && data.messages.size() > 0) {
                                ChatMessage message = data.messages.get(0);
                                if (message.getMediaType() == MessageConstants.MSG_TOPIC_GAME) {
                                    MessageForTopicGame messageForTopicGame = (MessageForTopicGame) message;
                                    topicGameClickLiveData.postValue(messageForTopicGame);
                                } else if (message.isDeleted()) {
                                    T.ss("消息已被删除");
                                } else {
                                    T.ss("消息不存在");
                                }
                            }
                        }

                        @Override
                        public void onComplete() {
                            super.onComplete();
                        }
                    });
                }
            }
        });
    }

    public MutableLiveData<MessageForTopicGame> getTopicGameClickLiveData() {
        return topicGameClickLiveData;
    }

    public abstract boolean contactLocked();

    public void deleteMessage(ChatMessage chatMessage) {
        if (chatMessage == null) {
            return;
        }
        if (chatMessage.isFailed()) {
            ServiceManager.getInstance().getMessageService().delete(chatMessage, getChatId(), getType());
            return;
        }
        long msgId = chatMessage.getMid();
        Map<String, Object> map = new HashMap<>();
        map.put("msgId", msgId);
        map.put("encChatId", chatId);
        HttpExecutor.requestSimplePost(URLConfig.URL_CHAT_DEL_MESSAGE, map, new SimpleRequestCallback(true) {

            @Override
            public void onSuccess() {

            }

            @Override
            public void dealFail(ErrorReason reason) {

            }
        });
    }

    public void deleteAllMessages() {

        long maxShowSeq = ServiceManager.getInstance().getMessageService().getMaxShowSeq(getChatId(), getType());
        Observable<BaseResponse<ClearMessagesBean>> baseResponseObservable = RetrofitManager.getInstance().createApi(ChatApi.class).clearMessages(chatId, maxShowSeq);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<ClearMessagesBean>() {
            @Override
            public void onSuccess(ClearMessagesBean data) {
                long minSeq = MessageConstants.MSG_SEQ_STARTING_VALUE;
                if (data != null) {
                    minSeq = data.getMinSeq();
                }
                //重置startSeq
                ServiceManager.getInstance().getMessageService().setStartSeq(chatId, minSeq);
                //清空本地聊天记录
                ServiceManager.getInstance().getMessageService().clearMessages(chatId, getType(), maxShowSeq);
                //重新拉取消息更新页面
                getMessage();
                //更新联系人
                Conversation conversation = ServiceManager.getInstance().getConversationService().getConversation(chatId, getType());
                //重置联系人显示内容等
                conversation.resetShowInfo(minSeq);
                ArrayList<Conversation> conversations = new ArrayList<>();
                conversations.add(conversation);
                //更新联系人
                ServiceManager.getInstance().getConversationService().update(conversations);
                hideShowProgressBar();

            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                if (reason != null && !TextUtils.isEmpty(reason.getErrReason())) {
                    T.ss(reason.getErrReason());
                }
            }

            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar("正在清空聊天记录...");
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });

    }

    public void sendEnterChat() {
        if (isSysChatType()) {//系统聊天，不发inChat消息
            return;
        }
        MessageService service = ServiceManager.getInstance().getMessageService();
        if (service != null) {
            service.sendInChat(getChatId());
        }
    }

    public long getSystemId() {
        return systemId;
    }

    public boolean isOfficialSystemActivity() {
        return getSystemId() == SYS_ID_OFFICIAL_KZ_ACTIVITY;
    }

    public boolean isMeetingPlan() {
        if (TextUtils.isEmpty(chatId)) {
            return false;
        }
        if (getContact(chatId) == null) {
            return false;
        }
        return getContact(chatId).getModeType() == 2;
    }

    public long getModeMinSeq() {
        if (TextUtils.isEmpty(chatId)) {
            return MSG_SEQ_STARTING_VALUE;
        }
        if (getContact(chatId) == null) {
            return MSG_SEQ_STARTING_VALUE;
        }
        return getContact(chatId).getModeMinSeq();
    }
}
