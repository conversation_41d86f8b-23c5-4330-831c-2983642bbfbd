package com.kanzhun.marry.chat.guard.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.guard.model.SafetyContact
import com.kanzhun.utils.T

/**
 * Dialog for adding a new safety guardian
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddSafetyGuardian(
    onConfirm: (phoneNumber: String, name: String?) -> Unit = { _, _ -> },
    onSelectContact: () -> Unit = {},
    pickedSafetyContact: SafetyContact? = null,
) {
    var phoneNumber by remember(pickedSafetyContact) {
        mutableStateOf(pickedSafetyContact?.phoneNumber ?: "")
    }
    var name by remember(pickedSafetyContact) {
        val contactName = pickedSafetyContact?.name ?: ""
        mutableStateOf(if (contactName.length > 10) contactName.substring(0, 10) else contactName)
    }

    // Pattern for phone numbers with optional country prefix
    val pattern = """(\+\d{1,4})?((12|13|14|15|16|17|18|19)\d{9}|400[0-9]{7})""".toRegex()

    // Validate phone number
    var isPhoneValid by remember(pickedSafetyContact) { mutableStateOf(pattern.matches(phoneNumber)) }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 20.dp)
    ) {
        // Phone number input with contact selection
        Text(
            text = "守护人手机号码",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF7F7F7F),
            ),
            modifier = Modifier.padding(horizontal = 20.dp)
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextField(
                value = phoneNumber,
                onValueChange = { newValue ->
                    // Allow digits and + symbol
                    if (newValue.all { it.isDigit() || it == '+' }) {
                        phoneNumber = newValue

                        // Update isPhoneValid based on the pattern
                        isPhoneValid = pattern.matches(newValue)
                    }
                },
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 4.dp),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                singleLine = true,
                colors = TextFieldDefaults.colors(
                    unfocusedContainerColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent,
                    focusedIndicatorColor = Color.Transparent,
                    cursorColor = Color(0xFF3D7EFF)
                ),
                placeholder = {
                    Text(
                        text = "请填写",
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFFB8B8B8),
                        )
                    )
                },
                textStyle = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF292929),
                )
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Contact selection button
            Row(
                modifier = Modifier
                    .clickable { onSelectContact() }
                    .padding(horizontal = 20.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_safty_guard_contact),
                    contentDescription = "image description",
                    modifier = Modifier
                        .size(16.dp)
                )

                Text(
                    text = "通讯录",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF292929),
                    )
                )
            }
        }

        HorizontalDivider(
            color = Color(0xFFEEEEEE),
            modifier = Modifier.padding(horizontal = 20.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Name input (optional)
        Text(
            text = "守护人姓名 (选填)",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF7F7F7F),
            ),
            modifier = Modifier.padding(horizontal = 20.dp)
        )

        TextField(
            value = name,
            onValueChange = { newValue ->
                // 限制最多输入10个字
                if (newValue.length <= 10) {
                    name = newValue
                } else {
                    // 超过10个字给toast提示
                    T.ss("姓名最多输入10个字")
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 4.dp),
            singleLine = true,
            colors = TextFieldDefaults.colors(
                unfocusedContainerColor = Color.Transparent,
                focusedContainerColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                cursorColor = Color(0xFF3D7EFF)
            ),
            placeholder = {
                Text(
                    text = "请填写（最多10个字）",
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFB8B8B8),
                    )
                )
            },
            textStyle = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF292929),
            )
        )

        Spacer(modifier = Modifier.height(10.dp))

        // Action buttons
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            // Confirm button
            Button(
                onClick = {
                    if (isPhoneValid) {
                        onConfirm(phoneNumber, name.ifEmpty { null })
                    }
                },
                modifier = Modifier
                    .padding(horizontal = 20.dp)
                    .weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFC5EEF9),
                    contentColor = Color.White,
                    disabledContainerColor = Color(0x80C5EEF9)
                ),
                shape = RoundedCornerShape(22.dp),
                contentPadding = PaddingValues(0.dp),
                enabled = isPhoneValid
            ) {
                Text(
                    text = "添加",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(if (isPhoneValid) 0xFF0A4859 else 0x800A4859),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier.padding(vertical = 12.dp)
                )
            }
        }
    }
}

@Preview(name = "Add Safety Guardian Dialog - Empty", showBackground = true)
@Composable
fun AddSafetyGuardianDialogEmptyPreview() {
    AddSafetyGuardian()
}

@Preview(name = "Add Safety Guardian Dialog - Prefilled", showBackground = true)
@Composable
fun AddSafetyGuardianDialogPrefilledPreview() {
    AddSafetyGuardian(
        pickedSafetyContact = SafetyContact(
            name = "张三",
            phoneNumber = "18612345678",
            maskedPhoneNumber = "186****5678"
        )
    )
}
