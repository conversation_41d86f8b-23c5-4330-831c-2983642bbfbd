package com.kanzhun.marry.chat.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.views.flowlayout.FlowAdapter
import com.kanzhun.marry.chat.databinding.ChatLikeListItemTagsBinding


class LikeListTagAdapter(val context: Context, val datas: List<String>?) : FlowAdapter<String>(datas) {

    private val layoutInflater = LayoutInflater.from(context)
    override fun getView(position: Int, parent: ViewGroup?): View {
        ChatLikeListItemTagsBinding.inflate(layoutInflater).also {
            it.divider11.visible(position != 0)
            it.tvTagName.text = datas?.get(position)
            return it.root
        }
    }
}