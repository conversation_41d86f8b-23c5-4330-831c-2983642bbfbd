package com.kanzhun.marry.chat.bindadapter

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.foundation.api.bean.MomentListItemBean
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.message.CommonCardInfo
import com.kanzhun.foundation.model.message.MessageForCommonCard
import com.kanzhun.foundation.utils.MessageUtils
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.viewmodel.SingleChatViewModel
import com.kanzhun.utils.GsonUtils
import com.kanzhun.utils.T

fun bindChangeWXCard(
    composeView: ComposeView,
    message: MessageForCommonCard,
) {

    message.commonCardInfo?.let { commonCardInfo ->
        composeView.onSetWindowContent {
            val singleChatViewModel = viewModel<SingleChatViewModel>()
            val contact by singleChatViewModel.contactLiveData.observeAsState(Contact().apply {})
            val avatar = if (!MessageUtils.isAuthor(message)) getFriendAvatar(contact) else getMyAvatar()

            ChangeWXCardView(
                bean = commonCardInfo,
                avatar = avatar ,
                isLeft = !MessageUtils.isAuthor(message)
            ) { str ->
                //将str 复制到剪切板
                val clipboardManager =
                    composeView.context.getSystemService(android.content.Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                val clipData = android.content.ClipData.newPlainText("WeChat ID", str)
                clipboardManager.setPrimaryClip(clipData)
                T.ss("已复制微信号至剪切板")
            }
        }
    }
}


@Composable
private fun ChangeWXCardView(
    bean: CommonCardInfo,
    avatar: String = "",
    isLeft: Boolean = false,
    onCopy: (str: String) -> Unit = {},
) {
    ConstraintLayout {
        val (refAvatar, refTitle, refContent, refBg) = createRefs()

        AsyncImage(
            model = avatar ?: "",
            placeholder = painterResource(R.drawable.common_female_level_two_guide),
            contentDescription = null,
            modifier = Modifier
                .size(40.dp)
                .constrainAs(refAvatar) {
                    top.linkTo(parent.top)
                    if (isLeft) {
                        start.linkTo(parent.start)
                    } else {
                        end.linkTo(parent.end)
                    }
                }
                .clip(shape = CircleShape)
        )


        Image(
            painter = painterResource(id = R.drawable.chat_change_wx_msg_card_bg),
            contentDescription = null,
            modifier = Modifier
                .size(243.dp, 150.dp)
                .constrainAs(refBg) {
                    top.linkTo(parent.top)
                    if (isLeft) {
                        start.linkTo(refAvatar.end, 10.dp)
                    } else {
                        end.linkTo(refAvatar.start, 10.dp)
                    }

                },
        )
        Column(
            modifier = Modifier
                .padding(16.dp)
                .constrainAs(refTitle) {
                    top.linkTo(parent.top)
                    start.linkTo(refBg.start)
                    end.linkTo(refBg.end)
                    width = Dimension.fillToConstraints
                }
                .noRippleClickable {
                    onCopy(bean.content ?: "")
                }
        ) {

            Text(
                text = bean.title ?: "",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontFamily = boldFontFamily(),
                    color = Color(0xFF191919),
                ),
                maxLines = 2,
                overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(20.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .background(
                        color = Color(0xFFF4F4F6),
                        shape = RoundedCornerShape(12.dp)
                    )
                    .padding(12.dp)
            ) {
                Text(
                    text = bean.content ?: "",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF7F7F7F),
                    ),
                    modifier = Modifier.weight(1f)
                )

                Icon(
                    painter = painterResource(id = R.drawable.chat_change_wx_msg_card_icon),
                    contentDescription = "Copy",
                    modifier = Modifier.width(20.dp),
                )
            }
        }


        Text(
            text = "平台已核验对方身份，但交换微信后请注意隐私安全",
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF7F7F7F),
            ),
            modifier = Modifier
                .constrainAs(refContent) {
                    top.linkTo(refBg.bottom, 8.dp)
                    start.linkTo(refBg.start, 4.dp)
                    end.linkTo(refBg.end, 4.dp)
                    width = Dimension.fillToConstraints
                }
        )
    }
}


@Preview(widthDp = 500, showBackground = true, backgroundColor = 0xFFB8B8B8)
@Composable
private fun PreviewChangeWXCardViewRight() {
    //title 为上方描述文案 content 为微信号信息
    ChangeWXCardView(
        CommonCardInfo(
            title = "和你的聊天很愉快，想进一步交流，可以添加我的微信哦",
            content = "abc123",
            sourceId = "abc123"
        )
    )
}

@Preview(widthDp = 500, showBackground = true, backgroundColor = 0xFFB8B8B8)
@Composable
private fun PreviewChangeWXCardViewLeft() {
    //title 为上方描述文案 content 为微信号信息
    ChangeWXCardView(
        CommonCardInfo(
            title = "和你的聊天很愉快，想进一步交流，可以添加我的微信哦",
            content = "abc123",
            sourceId = "abc123"
        ), isLeft = true
    )
}