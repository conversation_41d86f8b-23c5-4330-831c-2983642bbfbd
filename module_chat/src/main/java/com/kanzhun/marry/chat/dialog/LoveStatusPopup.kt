package com.kanzhun.marry.chat.dialog

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.util.widget.PopupUtils
import com.kanzhun.marry.chat.databinding.ChatLoveStatusPopLayoutBinding
import kotlin.math.abs

class LoveStatusPopup(val context: Context,val targetView: View) {
    fun show(){
        val binding = ChatLoveStatusPopLayoutBinding.inflate(LayoutInflater.from(context), null, false)
        val popupUtils = PopupUtils.Builder(context).setContentView(binding.root)
            .setWidth(ViewGroup.LayoutParams.WRAP_CONTENT).setHeight(ViewGroup.LayoutParams.WRAP_CONTENT).build()
        val xOff: Int = -abs(targetView.measuredWidth / 2 - 210.dpI / 2)
        popupUtils.showAsDropDown(targetView,xOff, 4.dpI,Gravity.CENTER)
    }
}