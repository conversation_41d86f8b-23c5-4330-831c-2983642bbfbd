package com.kanzhun.marry.chat.activity.christmas

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants.IterateForever
import com.airbnb.lottie.compose.rememberLottieComposition
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.message.OgChristmasCard
import com.kanzhun.marry.chat.R

// 2 任务前置提示卡片
@Composable
fun TaskPre(
    taskPreCard: OgChristmasCard, modifier: Modifier = Modifier,
    contact: Contact? = Contact(),
) {
    SantaMessage(contact = contact, modifier = modifier) {
        ConstraintLayout(
            modifier = Modifier.Companion
                .fillMaxWidth()
                .wrapContentHeight()
                .background(
                    color = Color(0xFFFFF8EB),
                    shape = RoundedCornerShape(20.dp)
                )
        ) {
            val (content, border, leaf) = createRefs()

            Column(
                modifier = Modifier.Companion
                    .fillMaxWidth()
                    .padding(16.dp)
                    .constrainAs(content) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top)
                    },
            ) {
                Row(verticalAlignment = Alignment.Companion.CenterVertically) {
                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_today_task),
                        contentDescription = "image description",
                    )

                    Spacer(modifier = Modifier.Companion.width(4.dp))

                    Text(
                        text = "你的今日任务是：",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(600),
                            color = Color(0xFF0D5737),
                        )
                    )
                }

                Spacer(modifier = Modifier.Companion.height(10.dp))

                Text(
                    text = taskPreCard.title ?: "",
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF0D5737),
                    )
                )

                Spacer(modifier = Modifier.Companion.height(4.dp))

                Text(
                    text = taskPreCard.content ?: "我将会为你提供一些线索帮你寻找",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF0D5737),
                    ),
                    modifier = Modifier.alpha(0.5f)
                )
            }

            Image(
                painter = painterResource(id = R.mipmap.chat_ic_border),
                contentDescription = "image description",
                modifier = Modifier.Companion
                    .constrainAs(border) {
                        start.linkTo(content.start)
                        end.linkTo(content.end)
                        top.linkTo(content.top)
                        bottom.linkTo(content.bottom)

                        width = Dimension.Companion.fillToConstraints
                        height = Dimension.Companion.fillToConstraints
                    },
                contentScale = ContentScale.Companion.FillBounds
            )

            val composition by rememberLottieComposition(spec = LottieCompositionSpec.Asset("christmas/chat/chat_jiangguo.json"))
            LottieAnimation(
                composition = composition,
                iterations = IterateForever,
                modifier = Modifier
                    .size(66.dp)
                    .constrainAs(leaf) {
                        top.linkTo(content.top)
                        end.linkTo(content.end)
                    }
            )
        }
    }
}

@Preview
@Composable
private fun PreviewTaskPre() {
    TaskPre(taskPreCard = OgChristmasCard().apply {
        title = "找到今日CP"
        content = "我将会为你提供一些线索帮你寻找"
    })
}