package com.kanzhun.marry.chat.emotion;

import com.kanzhun.marry.chat.emotion.data.EmotionItem;

/**
 * create by guofeng
 * date on 2022/3/10
 */ //业务层回掉接口
public interface IEmotionCallBack {
    //收藏点击添加表情
    void onAddEmotionClickListener();

    //内置emotion表情点击删除按钮
    void onInnerEmotionDeleteListener();

    //点击表情
    void onItemEmotionClickListener(EmotionItem emotionUIBean);

    //点击删除收藏表情
    default void onDeleteFavouriteEmotionListener(EmotionItem extendEmotion) {
    }

    default void onFirstDeleteFavouriteEmotionListener(EmotionItem extendEmotion) {
    }

    default void onShowFavouritePreviewEmotion(EmotionItem extendEmotion) {
    }

    // 点击表情分组
    default void onItemEmotionGroupClickListener(String groupName) {
    }
}