package com.kanzhun.marry.chat.views;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewParent;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.customview.widget.ViewDragHelper;

/**
 * A custom layout that allows swiping to reveal menu options on the right side.
 * This layout contains two children: the main content view and the menu view.
 */
public class SwipeLayout extends FrameLayout {

    private ViewDragHelper mDragHelper;
    private View mContentView;
    private View mMenuView;
    private int mMenuWidth;
    private float mInitialMotionX;
    private float mInitialMotionY;
    private int mTouchSlop;
    private boolean mIsOpen = false;
    private SwipeListener mSwipeListener;
    private OnClickListener mContentClickListener;
    private OnLongClickListener mContentLongClickListener;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private boolean mIsLongPressed = false;
    private boolean mIsClickHandled = false;
    private Runnable mLongPressRunnable;
    private boolean mSwipeEnabled = true; // 默认启用侧滑功能

    public interface SwipeListener {
        void onSwipeStart();

        void onSwipeEnd(boolean isOpen);
    }

    public interface OnClickListener {
        void onClick();
    }

    public interface OnLongClickListener {
        void onLongClick();
    }

    public SwipeLayout(@NonNull Context context) {
        this(context, null);
    }

    public SwipeLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SwipeLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        // 使用较低的敏感度值（0.5f）使 ViewDragHelper 更容易捕获水平滑动
        mDragHelper = ViewDragHelper.create(this, 0.5f, new DragHelperCallback());
        mTouchSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();

        // 初始化长按检测Runnable
        mLongPressRunnable = new Runnable() {
            @Override
            public void run() {
                if (!mIsLongPressed && !mIsClickHandled) {
                    mIsLongPressed = true;
                    mIsClickHandled = true; // 标记为已处理，防止后续触发点击事件
                    if (mContentLongClickListener != null) {
                        mContentLongClickListener.onLongClick();
                    }
                }
            }
        };
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        if (getChildCount() != 2) {
            throw new IllegalStateException("SwipeLayout must have exactly 2 children: content and menu");
        }
        mContentView = getChildAt(0);
        mMenuView = getChildAt(1);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mMenuWidth = mMenuView.getMeasuredWidth();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        // Position the menu view to the right of the content view
        mMenuView.layout(right - left, 0, right - left + mMenuWidth, bottom - top);
    }

    private boolean mIsBeingDragged = false;
    private boolean mIsMenuAreaTouched = false;
    private int mActivePointerId = -1;

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        final int action = ev.getActionMasked();

        // 如果侧滑功能被禁用，不拦截触摸事件，但仍然允许点击和长按
        if (!mSwipeEnabled) {
            return false;
        }

        // 如果点击事件已经在dispatchTouchEvent中处理过，则不再处理
        if (action == MotionEvent.ACTION_UP && mIsClickHandled) {
            return false;
        }

        if (action == MotionEvent.ACTION_CANCEL || action == MotionEvent.ACTION_UP) {
            mDragHelper.cancel();
            mIsBeingDragged = false;
            mActivePointerId = -1;
            // 取消长按检测
            mHandler.removeCallbacks(mLongPressRunnable);
            // 重置，允许父视图处理触摸事件
            getParent().requestDisallowInterceptTouchEvent(false);
            return false;
        }

        if (action == MotionEvent.ACTION_DOWN) {
            mInitialMotionX = ev.getX();
            mInitialMotionY = ev.getY();
            mActivePointerId = ev.getPointerId(0);
            mIsLongPressed = false;
            mIsClickHandled = false;

            // 启动长按检测
            if (mContentLongClickListener != null) {
                mHandler.postDelayed(mLongPressRunnable, ViewConfiguration.getLongPressTimeout());
            }

            // 如果菜单已打开，检查是否点击在菜单区域
            if (mIsOpen) {
                // 如果点击在菜单区域内，不拦截，让事件传递给菜单视图
                if (mInitialMotionX >= getWidth() - mMenuWidth && mInitialMotionX <= getWidth()) {
                    mIsMenuAreaTouched = true;
                    return false;
                } else {
                    // 如果点击在菜单区域外，拦截事件以处理关闭菜单
                    getParent().requestDisallowInterceptTouchEvent(true);
                    mIsMenuAreaTouched = false;
                    return true;
                }
            }
        } else if (action == MotionEvent.ACTION_MOVE) {
            if (mActivePointerId == -1) {
                return false;
            }

            // 如果是在菜单区域内的触摸，不拦截
            if (mIsOpen && mIsMenuAreaTouched) {
                return false;
            }

            final int pointerIndex = ev.findPointerIndex(mActivePointerId);
            if (pointerIndex == -1) {
                return false;
            }

            final float x = ev.getX(pointerIndex);
            final float y = ev.getY(pointerIndex);
            final float xDiff = Math.abs(x - mInitialMotionX);
            final float yDiff = Math.abs(y - mInitialMotionY);

            // 只有在明显是水平滑动时才拦截
            if (xDiff > mTouchSlop && xDiff > yDiff * 1.2) {
                mIsBeingDragged = true;
                // 取消长按检测，因为用户开始拖动
                mHandler.removeCallbacks(mLongPressRunnable);
                // 请求父视图不要拦截触摸事件
                getParent().requestDisallowInterceptTouchEvent(true);

                // 如果是从右向左滑动（显示菜单）或菜单已经打开
                if (mInitialMotionX > x || mIsOpen) {
                    return true;
                }
            } else if (yDiff > mTouchSlop && yDiff > xDiff) {
                // 如果是明显的垂直滑动，不拦截
                mIsBeingDragged = false;
                getParent().requestDisallowInterceptTouchEvent(false);
                return false;
            }
        }

        boolean intercept = mDragHelper.shouldInterceptTouchEvent(ev);
        if (!intercept && action == MotionEvent.ACTION_MOVE && !mIsBeingDragged) {
            // 如果ViewDragHelper不拦截且不是水平拖动，允许父视图处理
            getParent().requestDisallowInterceptTouchEvent(false);
        }
        return intercept;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        final int action = event.getActionMasked();

        // 如果点击事件已经在dispatchTouchEvent中处理过，则不再处理
        if (action == MotionEvent.ACTION_UP && mIsClickHandled) {
            return true;
        }

        // 如果侧滑功能被禁用，但菜单已经打开，则关闭菜单
        if (!mSwipeEnabled && mIsOpen) {
            closeMenu();
        }

        // 如果菜单已打开，并且点击在菜单区域内，且内容视图没有移动，则将事件传递给菜单视图
        if (mSwipeEnabled && mIsOpen && action == MotionEvent.ACTION_UP) {
            float x = event.getX();
            float y = event.getY();

            // 如果点击在菜单区域内
            if (x >= getWidth() - mMenuWidth && x <= getWidth() && y >= 0 && y <= getHeight()) {
                // 检查内容视图是否在完全打开的位置（左边缘在-mMenuWidth处）
                // 如果内容视图已经移动，说明用户正在尝试关闭菜单，不应该将事件传递给菜单视图
                if (mContentView.getLeft() == -mMenuWidth) {
                    // 将事件坐标转换为菜单视图的坐标系
                    event.offsetLocation(-(getWidth() - mMenuWidth), 0);
                    return mMenuView.dispatchTouchEvent(event);
                }
            }
        }

        if (action == MotionEvent.ACTION_DOWN) {
            mActivePointerId = event.getPointerId(0);
            mInitialMotionX = event.getX();
            mInitialMotionY = event.getY();
            mIsLongPressed = false;
            mIsClickHandled = false;

            // 启动长按检测
            if (mContentLongClickListener != null) {
                mHandler.postDelayed(mLongPressRunnable, ViewConfiguration.getLongPressTimeout());
            }

            // 如果点击在菜单区域内，且菜单完全打开
            if (mIsOpen && mInitialMotionX >= getWidth() - mMenuWidth && mInitialMotionX <= getWidth() && mContentView.getLeft() == -mMenuWidth) {
                // 将事件坐标转换为菜单视图的坐标系
                event.offsetLocation(-(getWidth() - mMenuWidth), 0);
                return mMenuView.dispatchTouchEvent(event);
            }
        } else if (action == MotionEvent.ACTION_MOVE) {
            if (mActivePointerId == -1) {
                return false;
            }

            final int pointerIndex = event.findPointerIndex(mActivePointerId);
            if (pointerIndex == -1) {
                return false;
            }

            final float x = event.getX(pointerIndex);
            final float y = event.getY(pointerIndex);
            final float xDiff = Math.abs(x - mInitialMotionX);
            final float yDiff = Math.abs(y - mInitialMotionY);

            // 如果是水平拖动，确保父视图不拦截
            if (xDiff > mTouchSlop && xDiff > yDiff * 1.2) {
                mIsBeingDragged = true;
                // 取消长按检测，因为用户开始拖动
                mHandler.removeCallbacks(mLongPressRunnable);
                getParent().requestDisallowInterceptTouchEvent(true);
            } else if (yDiff > mTouchSlop && yDiff > xDiff) {
                // 如果是垂直滑动，允许父视图拦截
                mIsBeingDragged = false;
                getParent().requestDisallowInterceptTouchEvent(false);
                // 如果是明显的垂直滑动，可以考虑返回false让父视图处理
                if (!mIsOpen) {
                    return false;
                }
            }
        } else if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL) {
            mIsBeingDragged = false;
            mActivePointerId = -1;
            // 重置，允许父视图处理触摸事件
            getParent().requestDisallowInterceptTouchEvent(false);

            // 取消长按检测
            mHandler.removeCallbacks(mLongPressRunnable);

            // 处理点击事件，但只有在没有拖动且不是长按的情况下
            if (action == MotionEvent.ACTION_UP && !mIsBeingDragged && !mIsLongPressed && !mIsClickHandled) {
                float x = event.getX();
                float y = event.getY();
                float xDiff = Math.abs(x - mInitialMotionX);
                float yDiff = Math.abs(y - mInitialMotionY);

                // 如果移动距离小于触摸阈值，认为是点击
                if (xDiff < mTouchSlop && yDiff < mTouchSlop) {
                    // 如果菜单未打开或点击在内容区域，触发点击事件
                    if (!mIsOpen || x < getWidth() - mMenuWidth) {
                        mIsClickHandled = true;
                        if (mContentClickListener != null) {
                            mContentClickListener.onClick();
                        }
                    }
                }
            }
        }

        // 如果侧滑功能启用，才处理拖动事件
        if (mSwipeEnabled) {
            try {
                mDragHelper.processTouchEvent(event);
            } catch (Exception e) {
                // 防止可能的异常
            }
            return true;
        } else {
            // 如果侧滑功能禁用，但仍然返回true以处理点击和长按事件
            return true;
        }
    }

    @Override
    public void computeScroll() {
        if (mDragHelper.continueSettling(true)) {
            ViewCompat.postInvalidateOnAnimation(this);
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        final int action = ev.getActionMasked();

        // 如果菜单已打开，并且点击在菜单区域内
        if (mIsOpen) {
            float x = ev.getX();
            // 如果点击在菜单区域内
            if (x >= getWidth() - mMenuWidth && x <= getWidth()) {
                // 对于菜单区域的点击，我们需要确保事件能正确传递给菜单视图
                if (action == MotionEvent.ACTION_DOWN) {
                    // 在菜单区域内按下时，记录状态
                    mIsMenuAreaTouched = true;
                }

                // 如果内容视图已经移动，说明用户正在尝试关闭菜单，不应该将事件传递给菜单视图
                if (mIsMenuAreaTouched && (mContentView.getLeft() == -mMenuWidth || action != MotionEvent.ACTION_UP)) {
                    // 将事件坐标转换为菜单视图的坐标系
                    MotionEvent menuEvent = MotionEvent.obtain(ev);
                    menuEvent.offsetLocation(-(getWidth() - mMenuWidth), 0);
                    boolean handled = mMenuView.dispatchTouchEvent(menuEvent);
                    menuEvent.recycle();

                    // 如果是按键抬起或取消，重置状态
                    if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL) {
                        mIsMenuAreaTouched = false;
                    }

                    if (handled) {
                        return true;
                    }
                }
            } else if (action == MotionEvent.ACTION_DOWN) {
                // 如果在菜单区域外按下，重置状态
                mIsMenuAreaTouched = false;
            }
        }

        // Handle initial touch down
        if (action == MotionEvent.ACTION_DOWN) {
            mInitialMotionX = ev.getX();
            mInitialMotionY = ev.getY();
            mActivePointerId = ev.getPointerId(0);
            mIsBeingDragged = false;
            mIsLongPressed = false;
            mIsClickHandled = false;

            // 启动长按检测
            if (mContentLongClickListener != null) {
                mHandler.postDelayed(mLongPressRunnable, ViewConfiguration.getLongPressTimeout());
            }

            // 允许父视图处理触摸事件
            getParent().requestDisallowInterceptTouchEvent(false);
        }
        // Handle move events to detect horizontal dragging early
        else if (action == MotionEvent.ACTION_MOVE) {
            if (mActivePointerId != -1) {
                final int pointerIndex = ev.findPointerIndex(mActivePointerId);
                if (pointerIndex != -1) {
                    final float x = ev.getX(pointerIndex);
                    final float y = ev.getY(pointerIndex);
                    final float xDiff = Math.abs(x - mInitialMotionX);
                    final float yDiff = Math.abs(y - mInitialMotionY);

                    // 只有在明显是水平滑动时才阻止父视图拦截
                    if (xDiff > mTouchSlop && xDiff > yDiff * 1.2) {
                        getParent().requestDisallowInterceptTouchEvent(true);
                        mIsBeingDragged = true;
                        // 取消长按检测，因为用户开始拖动
                        mHandler.removeCallbacks(mLongPressRunnable);
                    } else if (yDiff > mTouchSlop && yDiff > xDiff) {
                        // 如果是明显的垂直滑动，确保父视图可以拦截
                        getParent().requestDisallowInterceptTouchEvent(false);
                        mIsBeingDragged = false;
                    }
                }
            }
        }
        // Reset state on up/cancel
        else if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_CANCEL) {
            mIsBeingDragged = false;
            mActivePointerId = -1;
            // 取消长按检测
            mHandler.removeCallbacks(mLongPressRunnable);
            // 重置，允许父视图处理触摸事件
            getParent().requestDisallowInterceptTouchEvent(false);

            // 处理点击事件，但只有在没有拖动且不是长按的情况下
            if (action == MotionEvent.ACTION_UP && !mIsBeingDragged && !mIsLongPressed && !mIsClickHandled) {
                float x = ev.getX();
                float y = ev.getY();
                float xDiff = Math.abs(x - mInitialMotionX);
                float yDiff = Math.abs(y - mInitialMotionY);

                // 如果移动距离小于触摸阈值，认为是点击
                if (xDiff < mTouchSlop && yDiff < mTouchSlop) {
                    // 如果菜单未打开或点击在内容区域，触发点击事件
                    if (!mIsOpen || x < getWidth() - mMenuWidth) {
                        mIsClickHandled = true;
                        if (mContentClickListener != null) {
                            mContentClickListener.onClick();
                        }
                    }
                }
            }
        }

        return super.dispatchTouchEvent(ev);
    }

    public void closeMenu() {
        if (mIsOpen) {
            mDragHelper.smoothSlideViewTo(mContentView, 0, 0);
            mIsOpen = false;
            if (mSwipeListener != null) {
                mSwipeListener.onSwipeEnd(false);
            }
            ViewCompat.postInvalidateOnAnimation(this);
            // 允许父视图处理触摸事件
            ViewParent parent = getParent();
            if (parent != null) {
                parent.requestDisallowInterceptTouchEvent(false);
            }
        }
    }

    public void openMenu() {
        if (!mIsOpen) {
            mDragHelper.smoothSlideViewTo(mContentView, -mMenuWidth, 0);
            mIsOpen = true;
            if (mSwipeListener != null) {
                mSwipeListener.onSwipeEnd(true);
            }
            ViewCompat.postInvalidateOnAnimation(this);
            // 菜单打开时不允许父视图拦截触摸事件
            getParent().requestDisallowInterceptTouchEvent(true);
        }
    }

    public boolean isMenuOpen() {
        return mIsOpen;
    }

    public void setSwipeListener(SwipeListener listener) {
        mSwipeListener = listener;
    }

    /**
     * 设置内容区域的点击监听器
     *
     * @param listener 点击监听器
     */
    public void setOnClickListener(OnClickListener listener) {
        mContentClickListener = listener;
    }

    /**
     * 设置内容区域的长按监听器
     *
     * @param listener 长按监听器
     */
    public void setOnLongClickListener(OnLongClickListener listener) {
        mContentLongClickListener = listener;
    }

    /**
     * 设置是否启用侧滑功能
     *
     * @param enabled true表示启用，false表示禁用
     */
    public void setSwipeEnabled(boolean enabled) {
        if (!enabled && mIsOpen) {
            // 如果禁用侧滑且菜单当前已打开，则关闭菜单
            closeMenu();
        }
        mSwipeEnabled = enabled;
    }

    /**
     * 获取当前侧滑功能是否启用
     *
     * @return true表示启用，false表示禁用
     */
    public boolean isSwipeEnabled() {
        return mSwipeEnabled;
    }

    private class DragHelperCallback extends ViewDragHelper.Callback {

        @Override
        public boolean tryCaptureView(@NonNull View child, int pointerId) {
            // 如果侧滑功能禁用，不允许捕获视图
            if (!mSwipeEnabled) {
                return false;
            }
            return child == mContentView;
        }

        @Override
        public int clampViewPositionHorizontal(View child, int left, int dx) {
            // Restrict horizontal movement between -menuWidth and 0
            return Math.max(-mMenuWidth, Math.min(left, 0));
        }

        @Override
        public int clampViewPositionVertical(View child, int top, int dy) {
            // No vertical movement
            return 0;
        }

        @Override
        public int getViewHorizontalDragRange(View child) {
            return mMenuWidth;
        }

        @Override
        public void onViewCaptured(@NonNull View capturedChild, int activePointerId) {
            super.onViewCaptured(capturedChild, activePointerId);
            // Ensure parent doesn't intercept touch events while dragging
            getParent().requestDisallowInterceptTouchEvent(true);
        }

        @Override
        public void onViewReleased(View releasedChild, float xvel, float yvel) {
            // Determine whether to open or close the menu based on position and velocity
            final int childWidth = releasedChild.getWidth();
            int left;
            boolean isOpen;

            if (xvel < 0 || (xvel == 0 && releasedChild.getLeft() < -mMenuWidth / 2)) {
                // Open menu if swiping left or if more than half open
                left = -mMenuWidth;
                isOpen = true;
            } else {
                // Close menu
                left = 0;
                isOpen = false;
            }

            if (mDragHelper.settleCapturedViewAt(left, 0)) {
                ViewCompat.postInvalidateOnAnimation(SwipeLayout.this);
            }

            if (mIsOpen != isOpen) {
                mIsOpen = isOpen;
                if (mSwipeListener != null) {
                    mSwipeListener.onSwipeEnd(isOpen);
                }
            }
        }

        @Override
        public void onViewDragStateChanged(int state) {
            if (state == ViewDragHelper.STATE_DRAGGING && mSwipeListener != null) {
                mSwipeListener.onSwipeStart();
            }
        }

        @Override
        public void onViewPositionChanged(View changedView, int left, int top, int dx, int dy) {
            // Update the position of the menu view as the content view moves
            mMenuView.offsetLeftAndRight(dx);
        }
    }
}
