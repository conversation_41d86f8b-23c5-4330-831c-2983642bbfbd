package com.kanzhun.marry.chat.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.ICommonCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.chat.api.ChatApi;
import com.kanzhun.marry.chat.model.DateCardStatusBean;
import com.kanzhun.utils.T;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

public class DateCardDetailViewModel extends FoundationViewModel {

    private MutableLiveData<Boolean> loadingLiveData = new MutableLiveData<>();
    private MutableLiveData<DateCardStatusBean> dateCardLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> finishLiveData = new MutableLiveData<>();
    public ObservableField<DateCardStatusBean> statusBeanField = new ObservableField<>();
    public String msgId;
    public ObservableField<String> msgSenderId = new ObservableField<>();

    /**
     * 查询相识卡内容
     */
    public void getDateCard() {
        Observable<BaseResponse<DateCardStatusBean>> baseResponseObservable = RetrofitManager.getInstance().createApi(ChatApi.class).getDateCard(msgId);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<DateCardStatusBean>() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar("正在加载");
            }

            @Override
            public void onSuccess(DateCardStatusBean data) {
                if (data == null) return;
                if (TextUtils.isEmpty(data.friendName)) {
                    data.friendName = "Ta";
                }
                dateCardLiveData.setValue(data);
                statusBeanField.set(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                T.ss(reason.getErrReason());
                ExecutorFactory.execMainTaskDelay(new Runnable() {
                    @Override
                    public void run() {
                        finishLiveData.postValue(true);
                    }
                }, 200);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    /**
     * @param reply 1:收下2:拒绝
     */
    public void replyDateCard(int reply, ICommonCallback callback) {
        if (userProfileLocked()) {
            return;
        }
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(ChatApi.class).replyDateCard(reply, msgId);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                loadingLiveData.postValue(true);
            }

            @Override
            public void onSuccess() {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if(reason != null){
                    T.ss(reason.getErrReason());
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                loadingLiveData.postValue(false);
            }
        });
    }

    public MutableLiveData<Boolean> getLoadingLiveData() {
        return loadingLiveData;
    }

    public MutableLiveData<DateCardStatusBean> getDateCardLiveData() {
        return dateCardLiveData;
    }

    public MutableLiveData<Boolean> getFinishLiveData() {
        return finishLiveData;
    }

    public DateCardDetailViewModel(Application application) {
        super(application);
    }

}