package com.kanzhun.marry.chat.views;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;

/**
 * <AUTHOR>
 * @date 2022/10/18.
 */
public class ScrollLimitFrameLayout extends FrameLayout {
    float lowestPointY;
    int scrollLimit = QMUIDisplayHelper.dpToPx(24);
    ScrollLimitListener scrollLimitListener;
    boolean enableScrollLimit;

    public ScrollLimitFrameLayout(@NonNull Context context) {
        this(context, null);
    }

    public ScrollLimitFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScrollLimitFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (getVisibility() == VISIBLE && enableScrollLimit) {
            switch (ev.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    lowestPointY = ev.getY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    float moveY = ev.getY();
                    Log.e("&&&&&&", "lowestPointY=" + lowestPointY + " moveY=" + moveY);
                    if (lowestPointY - moveY >= scrollLimit && scrollLimitListener != null) {
                        enableScrollLimit = false;//隐藏view防止继续回调
                        scrollLimitListener.overScrollLimit();
                        Log.e("&&&&&&", "---------------------");
                        break;
                    }
                    if (moveY > lowestPointY) {
                        lowestPointY = moveY;
                    }
                    break;
            }
        }
        return super.dispatchTouchEvent(ev);
    }

    public void setScrollLimitListener(ScrollLimitListener scrollLimitListener) {
        this.scrollLimitListener = scrollLimitListener;
    }

    public void setEnableScrollLimit(boolean enableScrollLimit) {
        this.enableScrollLimit = enableScrollLimit;
    }

    public boolean isEnableScrollLimit() {
        return enableScrollLimit;
    }

    public interface ScrollLimitListener {
        void overScrollLimit();
    }
}
