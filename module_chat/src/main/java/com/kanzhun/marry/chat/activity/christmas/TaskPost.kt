package com.kanzhun.marry.chat.activity.christmas

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.message.OgccTaskPost
import com.kanzhun.marry.chat.R

// 5 任务后提示卡片
@Composable
fun TaskPost(
    taskPost: OgccTaskPost, modifier: Modifier = Modifier,
    contact: Contact? = Contact(),
) {
    SantaMessage(contact = contact, modifier = modifier) {
        Column(
            modifier = Modifier.Companion
                .fillMaxWidth()
                .background(
                    color = Color(0xFFFFF8EB),
                    shape = RoundedCornerShape(20.dp)
                )
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(14.dp)
        ) {
            Text(
                text = taskPost.title ?: "你的今日CP可能会在这些页面出现哦～",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF191919),
                )
            )

            Column(modifier = Modifier.Companion.align(Alignment.Companion.CenterHorizontally)) {
                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_gold_arrow_down),
                    contentDescription = "image description",
                    contentScale = ContentScale.Companion.None
                )

                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_gold_arrow_down),
                    contentDescription = "image description",
                    contentScale = ContentScale.Companion.None
                )
            }

            Box(
                modifier = Modifier.Companion
                    .fillMaxWidth()
                    .wrapContentHeight()
            ) {
                Row(
                    modifier = Modifier.Companion
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .border(
                            width = 3.dp,
                            color = Color(0xFFF9EACD),
                            shape = RoundedCornerShape(size = 16.dp)
                        )
                        .padding(1.5.dp)
                        .padding(top = 12.dp)
                        .padding(vertical = 16.dp),
                    verticalAlignment = Alignment.Companion.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    TaskPostItem(
                        name = "探索",
                        icon = R.mipmap.chat_ic_explore,
                    )
                    TaskPostItem(
                        name = "互动",
                        icon = R.mipmap.chat_ic_interact,
                    )
                    TaskPostItem(
                        name = "消息",
                        icon = R.mipmap.chat_ic_communicate,
                    )
                    TaskPostItem(
                        name = "我的",
                        icon = R.mipmap.chat_ic_me,
                    )
                }

                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_like_btn),
                    contentDescription = "image description",
                    modifier = Modifier.Companion
                        .align(Alignment.Companion.TopCenter)
                        .padding(top = 3.dp)
                )
            }
        }
    }
}

@Composable
fun TaskPostItem(
    name: String,
    @DrawableRes icon: Int,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Image(
            painter = painterResource(id = icon),
            contentDescription = "image description",
            modifier = Modifier.size(24.dp)
        )

        Text(
            text = name,
            style = TextStyle(
                fontSize = 11.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF292929),
            )
        )
    }
}

@Preview
@Composable
private fun PreviewTaskPost() {
    TaskPost(taskPost = OgccTaskPost().apply {
        title = "你的今日CP可能会在这些页面出现哦～"
    })
}