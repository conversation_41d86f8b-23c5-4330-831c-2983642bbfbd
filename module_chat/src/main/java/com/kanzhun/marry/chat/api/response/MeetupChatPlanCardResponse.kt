package com.kanzhun.marry.chat.api.response

import java.io.Serializable

/*

    {
        "taskTotalNum": 5, // 总任务数
        "taskCompleteNum": 5, // 任务完成数
        "spentDays": 2, // 相处天数
        "spentHour": 97, // 小时
        "currentTaskInfo": {
            "type": 1, // 任务类型    1 初次打招呼 2 默契考验 3 交换照片 4 互发语音 5 解锁完成匹配报告 10 见面邀约
            "taskNum": 1, // 任务序号
            "maleUserId": "xxx", // 男用户id
            "femaleUserId": "xxx", // 女用户id
            "maleUserStatus": 1, // 男用户完成状态
            "femaleUserStatus": 1, // 女用户完成状态
            "taskStatus": 0, // 任务状态 0 未完成 1 已完成。 2 默契考验时，无双方完成状态 只判断整体任务状态 10 见面邀约时 1 标识见面成功
            "msgId": 1333221, // 对应任务消息 id
            "subtitle": "再聊30句解锁语音任务", // 子标题
            "completeMsgNum": 30, // 完成当前任务需要消息总数
            "currentMsgNum": 20, // 当前任务下已发送消息数
        },
        "taskList": [{
            "type": 1, // 任务类型    1 初次打招呼 2 默契考验 3 交换照片 4 互发语音 5 解锁完成匹配报告 10 见面邀约
            "taskNum": 1, // 任务序号
            "subtitle": "再聊30句解锁语音任务", // 子标题
            "completeMsgNum": 30, // 完成当前任务需要消息总数
        }]

    }

 */

data class MeetupChatPlanCardResponse(
    var taskTotalNum: Int? = 5, // 总任务数
    var taskCompleteNum: Int? = 0, // 任务完成数
    var spentDays: Int? = 0, // 相处天数
    var spentHours: Int? = 0, // 小时
    var currentTaskInfo: CurrentTaskInfo? = null,
    var taskList: List<TaskInfo>? = null
) : Serializable {
    data class CurrentTaskInfo(
        var type: Int? = null, // 任务类型    1 初次打招呼 2 默契考验 3 交换照片 4 互发语音 5 解锁完成匹配报告 10 见面邀约
        var taskNum: Int? = null, // 任务序号
        var maleUserId: String? = null, // 男用户id
        var femaleUserId: String? = null,// 女用户id
        var maleUserStatus: Int? = null, // 男用户完成状态
        var femaleUserStatus: Int? = null, // 女用户完成状态
        var taskStatus: Int? = null, // 任务状态 0 未完成 1 已完成。 2 默契考验时，无双方完成状态 只判断整体任务状态；10 见面邀约时 1 待确认 2 确认 3 拒绝
        var msgId: Long? = null, // 对应任务消息 id
        var subtitle: String? = null, // 子标题
        var completeMsgNum: Int? = null, // 完成当前任务需要消息总数
        var currentMsgNum: Int? = null // 当前任务下已发送消息数
    ) : Serializable

    //region 若≤48h：相识XX小时；若48h以上：相识XX天
    fun getSpentNum(): Int {
        return if (isLessThan48Hours()) (spentHours ?: 0) else (spentDays ?: 0)
    }

    fun getSpentUnit(): String {
        return if (isLessThan48Hours()) "小时" else "天"
    }

    private fun isLessThan48Hours(): Boolean {
        return (spentDays ?: 0) <= 2 || (spentHours ?: 0) <= 48
    }
    //endregion

    data class TaskInfo(
        var type: Int? = null, // 任务类型    1 初次打招呼 2 默契考验 3 交换照片 4 互发语音 5 解锁完成匹配报告 10 见面邀约
        var taskNum: Int? = null, // 任务序号
        var subtitle: String? = null, // 子标题
        var completeMsgNum: Int? = null, // 完成当前任务需要消息总数
    ) : Serializable
}


data class ProtectMeetChatCard(
    val recordId: String?,//记录id
    val meetTime: Long,//见面时间
    val address: String?,//见面地址
    val securityGuardian: SecurityGuardianBean?
) : Serializable

data class SecurityGuardianBean(
    val openStatus: Int,//0-未开启，1-已开启
    val count: Int,//安全守护人数量
) : Serializable