package com.kanzhun.marry.chat.activity.christmas

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.api.response.GetLimitedActivityQuotaResponse
import com.kanzhun.utils.T

@Preview
@Composable
fun SantaTitleBar(
    modifier: Modifier = Modifier,
    contact: Contact? = Contact().apply { nickName = "圣诞限定" },
    activityQuotaState: GetLimitedActivityQuotaResponse = GetLimitedActivityQuotaResponse(),
    onBackPressed: () -> Unit = {},
    onTitleClick: () -> Unit = {}
) {
    Box(modifier = modifier.fillMaxWidth()) {
        Text(
            text = contact?.nickName ?: "圣诞限定",
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFFF7F7F7),
            ),
            modifier = Modifier
                .align(Alignment.Center)
                .fillMaxWidth()
                .noRippleClickable {
                    onTitleClick()
                },
            textAlign = TextAlign.Center,
            overflow = TextOverflow.Ellipsis,
        )

        Row(
            modifier = Modifier.height(48.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(R.drawable.icon_img_back_white),
                modifier = Modifier
                    .noRippleClickable { onBackPressed() }
                    .padding(start = 20.dp, end = 5.dp)
                    .size(24.dp),
                contentDescription = ""
            )

            Spacer(modifier = Modifier.weight(1f))

            Row(
                modifier = Modifier
                    .noRippleClickable {
                        // 用户处于异常状态：非正式用户，隐身用户，被限制开聊用户等状态下，点击时不唤起积分弹
                        // 窗，展示toast文案：当前状态下不可兑换礼物
                        if (activityQuotaState.userStatus != 0) {
                            T.ss("当前状态下不可兑换礼物")
                        } else {
                            ProtocolHelper.parseProtocol(activityQuotaState.jumpUrl)
                        }

                        reportPoint("christ-store-chat-click") {
                            // 记录点击时用户有多少积分
                            actionp2 = activityQuotaState.userQuota?.toString()
                        }
                    }
                    .padding(end = 16.dp)
                    .background(
                        color = Color(0xFFFFF8EB),
                        shape = RoundedCornerShape(63.dp)
                    )
                    .padding(horizontal = 10.dp, vertical = 7.dp),
                horizontalArrangement = Arrangement.spacedBy(2.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_candies),
                    contentDescription = "image description",
                    contentScale = ContentScale.None
                )

                Text(
                    text = "糖果商店·${activityQuotaState.userQuota}",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF292929),
                    )
                )
            }
        }
    }
}