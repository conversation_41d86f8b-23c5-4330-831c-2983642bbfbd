package com.kanzhun.marry.chat.util

import com.kanzhun.foundation.facade.ConversationProvider
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_KZ_ACTIVITY
import com.kanzhun.foundation.model.message.MessageForEmpty
import com.kanzhun.foundation.utils.MessageUtils
import com.kanzhun.utils.L

class ChatListHandler {
    companion object {
        fun getList(conversations: List<Conversation>?): List<Conversation>? {
            var hasChristmas = false

            val mutableConversations = conversations?.toMutableList()

            mutableConversations?.forEach { it ->
                if (it.unreadCount > 0 && !hasChristmas) {
                    hasChristmas = true
                    it.showChristmas = true
                    L.e("ChatListHandler", "showChristmas:" + it.nickName)
                } else {
                    it.showChristmas = false
                }
                val chatMessage =
                    ServiceManager.getInstance().getMessageService().queryLatestMessage(it.chatId)

                if (chatMessage != null) {
                    if (chatMessage.mid != it.msgId) {
                        val conversationsMessage =
                            ServiceManager.getInstance().getMessageService().query(it.msgId)
                        if (conversationsMessage is MessageForEmpty) {
                            ConversationProvider.updateConversationByMessage(it, chatMessage, true)
                        } else if (it.lastSeq < chatMessage.seq) {
                            ConversationProvider.updateConversationByMessage(it, chatMessage, true)
                        }
                    } else {
                        if (it.lastSeq < chatMessage.seq) {
                            ConversationProvider.updateConversationByMessage(it, chatMessage, true)
                        } else if (it.lastSeq == chatMessage.seq && !it.content.equals(
                                MessageUtils.createContent(
                                    chatMessage
                                )
                            )
                        ) {
                            ConversationProvider.updateConversationByMessage(it, chatMessage, true)
                        }
                    }

                    val systemId = it.systemId
                    if (systemId == SYS_ID_OFFICIAL_KZ_ACTIVITY.toLong() && com.kanzhun.marry.chat.BuildConfig.DEBUG) {
                        L.e("ChatListHandler", "conversations:$systemId")
                        L.e("ChatListHandler", "conversations lastSeq:" + it.lastSeq)
                        L.e("ChatListHandler", "conversations lastTime:" + it.lastTime)
                        L.e("ChatListHandler", "conversations lastMsgStatus:" + it.lastMsgStatus)
                        L.e("ChatListHandler", "conversations modifyTime:" + it.modifyTime)
                        L.e("ChatListHandler", "conversations content:" + it.content)
                        L.e("ChatListHandler", "chatMessage:" + chatMessage.content)
                        L.e("ChatListHandler", "chatMessage seq:" + chatMessage.seq)
                        L.e("ChatListHandler", "chatMessage time:" + chatMessage.time)
                        L.e("ChatListHandler", "chatMessage status:" + chatMessage.status)
                        L.e("ChatListHandler", "chatMessage updateTime:" + chatMessage.updateTime)
                        L.e("ChatListHandler", "chatMessage content:" + chatMessage.content)
                        L.e("ChatListHandler", "chatMessage summary:" + chatMessage.summary)
                    }
                }
            }

            return mutableConversations
        }
    }
}