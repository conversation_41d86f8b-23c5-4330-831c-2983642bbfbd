package com.kanzhun.marry.chat.activity.yueyue

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.api.ChatApi
import com.kanzhun.marry.chat.dialog.DeleteRelationDialog
import com.kanzhun.marry.chat.dialog.DeleteRelationType
import com.kanzhun.utils.T

@Composable
fun YueYueFeedbackSelectDialog(
    yueyueChatId: String,
    conversation: Conversation,
    onFeedback: () -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    // Determine if the conversation is currently shielded and pinned
    val isShielded = conversation.shield == 1
    val isPinned = conversation.sort > 0

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomCenter
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Pin/Unpin option
            Text(
                text = if (isPinned) "取消置顶" else "置顶聊天",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF292929),
                    textAlign = TextAlign.Center,
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .fillMaxWidth()
                    .noRippleClickable {
                        // Handle pin/unpin action
                        val pinType =
                            if (isPinned) 0 else 1 // 1 for pin (top), 0 for unpin (cancel top)

                        // Call the API to update pin status
                        HttpExecutor.requestSimple(
                            RetrofitManager.getInstance().createApi(ChatApi::class.java)
                                .updateTop(yueyueChatId, pinType),
                            object : SimpleRequestCallback(true) {
                                override fun onSuccess() {
                                    ExecutorFactory.execLocalTask {
                                        // Update local conversation sort status
                                        conversation.sort = if (pinType > 0) 1 else 0
                                        ServiceManager.getInstance().conversationService.updateConversation(
                                            conversation
                                        )

                                        ExecutorFactory.runOnUiThread {
                                            // Show toast message
                                            if (pinType == 1) {
                                                T.ss("已置顶")
                                            } else {
                                                T.ss("已取消聊天置顶")
                                            }
                                            // Dismiss the dialog
                                            onDismiss()
                                        }
                                    }
                                }

                                override fun dealFail(reason: ErrorReason) {
                                    ExecutorFactory.runOnUiThread {
                                        if (pinType == 1) {
                                            T.ss("置顶失败，请稍后重试")
                                        } else {
                                            T.ss("取消置顶失败，请稍后重试")
                                        }
                                    }
                                }
                            }
                        )

                        // Report analytics
                        reportPoint("chat-list-swipe-action") {
                            type = if (pinType == 1) "pin" else "cancel_pin"
                            peer_id = yueyueChatId
                        }
                    }
            )

            HorizontalDivider(
                thickness = 0.5.dp,
                color = Color(0xFFE5E5E5),
                modifier = Modifier
                    .fillMaxWidth()
            )

            Text(
                text = "评价月月",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF292929),
                    textAlign = TextAlign.Center,
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .fillMaxWidth()
                    .noRippleClickable {
                        // Call the feedback callback
                        onFeedback()

                        // Report analytics
                        reportPoint("chat-list-swipe-action") {
                            type = "feedback"
                            peer_id = yueyueChatId
                        }
                    }
            )

            HorizontalDivider(
                thickness = 0.5.dp,
                color = Color(0xFFE5E5E5),
                modifier = Modifier
                    .fillMaxWidth()
            )

            val context = LocalContext.current
            Text(
                text = if (isShielded) "取消屏蔽" else "屏蔽月月",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF292929),
                    textAlign = TextAlign.Center,
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp)
                    .noRippleClickable {
                        fun requestShield(shieldType: Int) {
                            // Call the API to update shield status
                            HttpExecutor.requestSimple(
                                RetrofitManager.getInstance().createApi(ChatApi::class.java)
                                    .updateShield(yueyueChatId, shieldType),
                                object : SimpleRequestCallback(true) {
                                    override fun onSuccess() {
                                        ExecutorFactory.execLocalTask {
                                            // Update local conversation shield status
                                            conversation.shield = shieldType
                                            ServiceManager.getInstance().conversationService.updateConversation(
                                                conversation
                                            )

                                            ExecutorFactory.runOnUiThread {
                                                // Show toast message
                                                if (shieldType == 1) {
                                                    T.ss("已屏蔽")
                                                } else {
                                                    T.ss("已取消屏蔽")
                                                }
                                                onDismiss()
                                            }
                                        }
                                    }

                                    override fun dealFail(reason: ErrorReason) {
                                        ExecutorFactory.runOnUiThread {
                                            if (shieldType == 1) {
                                                T.ss("屏蔽失败，请稍后重试")
                                            } else {
                                                T.ss("取消屏蔽失败，请稍后重试")
                                            }
                                        }
                                    }
                                }
                            )
                        }

                        if (isShielded) { // 用户取消屏蔽
                            requestShield(shieldType = 0)
                        } else { // 用户打算屏蔽月月，弹框阻止
                            DeleteRelationDialog(
                                context = context,
                                relationType = DeleteRelationType.SHIELD
                            ).show { confirm: Boolean ->
                                if (confirm) {
                                    requestShield(shieldType = 1)
                                }
                                null
                            }
                        }
                    }
            )

            HorizontalDivider(
                thickness = 6.dp,
                color = Color(0xFFF5F5F5),
                modifier = Modifier.fillMaxWidth()
            )

            Text(
                text = "取消",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFB2B2B2),
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .fillMaxWidth()
                    .noRippleClickable { onDismiss() }
            )
        }
    }
}

@Preview
@Composable
fun PreviewYueYueFeedbackSelectDialog() {
    YueYueFeedbackSelectDialog(
        yueyueChatId = "123456789",
        conversation = Conversation().apply {
            sort = 1024
            shield = 1
        },
        onFeedback = {},
        onDismiss = {}
    )
}

@Composable
fun YueYueFeedbackDialog(
    overallRatingInit: Int = 0,
    authenticityRatingInit: Int = 0,
    communicationRatingInit: Int = 0,
    commentInit: String = "",
    selectable: Boolean = true,
    onDismiss: () -> Unit = {},
    onSubmit: (Int, Int, Int, String) -> Unit = { _, _, _, _ -> },
) {
    var overallRating by remember { mutableIntStateOf(overallRatingInit) }
    var authenticityRating by remember { mutableIntStateOf(authenticityRatingInit) }
    var communicationRating by remember { mutableIntStateOf(communicationRatingInit) }
    var comment by remember { mutableStateOf(commentInit) }

    // Track keyboard visibility and height
    val imeHeight = WindowInsets.ime.getBottom(LocalDensity.current)
    val imeVisible = imeHeight > 0

    // Create scroll state for scrolling when keyboard appears
    val scrollState = rememberScrollState()

    // Track if any text field is focused
    var isTextFieldFocused by remember { mutableStateOf(false) }

    // Scroll to bottom when keyboard appears or when comment changes
    LaunchedEffect(imeVisible, comment) {
        if (imeVisible && isTextFieldFocused) {
            // Small delay to let the keyboard animation start
            kotlinx.coroutines.delay(100)
            scrollState.animateScrollTo(scrollState.maxValue)

            // Additional scroll after keyboard is fully visible
            kotlinx.coroutines.delay(300)
            scrollState.animateScrollTo(scrollState.maxValue)
        }
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomCenter
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 88.dp)
                .background(Color.White, RoundedCornerShape(topStart = 32.dp, topEnd = 32.dp))
                .padding(horizontal = 20.dp)
                // Add vertical scrolling capability
                .verticalScroll(scrollState)
                // Add padding for IME (keyboard)
                .imePadding()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
            ) {
                Text(
                    text = "给月月一些建议吧",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF292929)
                    ), modifier = Modifier.align(Alignment.Center)
                )
                Image(
                    painter = R.drawable.common_black_close.painterResource(),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = "Close",
                    modifier = Modifier
                        .size(24.dp)
                        .align(Alignment.TopEnd)
                        .noRippleClickable { onDismiss() },
                )
            }

            // Overall Rating
            SelectFiveItem(
                title = "整体见面评价",
                leftStr = "不满意",
                rightStr = "很满意",
                selectable = selectable,
                overallRating, onClick1 = {
                    overallRating = it
                })

            SelectFiveItem(
                title = "身份是否真实",
                leftStr = "不真实",
                rightStr = "很真实",
                selectable = selectable, rating = authenticityRating, onClick1 = {
                    authenticityRating = it
                })

            SelectFiveItem(
                title = "交流是否愉快",
                leftStr = "不愉快",
                rightStr = "很愉快", selectable = selectable,
                rating = communicationRating, onClick1 = {
                    communicationRating = it
                })

            if (selectable || comment.isNotEmpty()) {
                // Comment Field
                Column(modifier = Modifier.padding(vertical = 8.dp)) {
                    Text(
                        text = "说说整体的见面感受吧",
                        style = TextStyle(fontSize = 14.sp, color = Color(0xFF292929))
                    )
                    BasicTextField(
                        value = comment,
                        // only allow editing when selectable == true
                        onValueChange = { newText ->
                            if (selectable && newText.length <= 500) {
                                comment = newText
                            }
                        },
                        // make field read-only when selectable == false
                        readOnly = !selectable,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(100.dp)
                            .padding(top = 8.dp)
                            .background(Color(0xFFF7F7F7), RoundedCornerShape(8.dp))
                            .padding(12.dp)
                            .onFocusChanged { focusState ->
                                isTextFieldFocused = focusState.isFocused
                            },
                        textStyle = TextStyle(fontSize = 14.sp, color = Color(0xFF292929)),
                        decorationBox = { innerTextField ->
                            Box {
                                if (comment.isEmpty()) {
                                    Text(
                                        text = "请输入",
                                        style = TextStyle(
                                            fontSize = 14.sp,
                                            color = Color(0xFFB8B8B8)
                                        )
                                    )
                                }
                                innerTextField()
                            }


                        }
                    )
                    if (selectable) {
                        Row(
                            modifier = Modifier
                                .align(Alignment.End)
                                .padding(top = 4.dp)
                        ) {

                            Text(
                                text = buildAnnotatedString {
                                    val len = comment.length
                                    withStyle(
                                        style = SpanStyle(
                                            color = when {
                                                len == 0 -> Color(0xFFB2B2B2)
                                                len > 100 -> Color.Red
                                                else -> Color(0xFF292929)
                                            }
                                        )
                                    ) {
                                        append(len.toString())
                                    }
                                    withStyle(style = SpanStyle(color = Color(0xFFB2B2B2))) {
                                        append("/500")
                                    }
                                },
                                style = TextStyle(fontSize = 12.sp)
                            )


                        }
                    }
                }
            }



            if (selectable) {
                // Submit Button
                Text(
                    text = "提交",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                        .background(
                            color = if (overallRating == 0 || authenticityRating == 0 || communicationRating == 0) Color(
                                0x4D191919
                            ) else Color(0xFF191919),
                            shape = RoundedCornerShape(25.dp)
                        )
                        .padding(vertical = 12.dp)
                        .noRippleClickable {
                            if (overallRating == 0 || authenticityRating == 0 || communicationRating == 0) {
                                return@noRippleClickable
                            }
                            onSubmit(
                                overallRating,
                                authenticityRating,
                                communicationRating,
                                comment
                            )
                        }
                )
            }

        }
    }
}

@Composable
private fun SelectFiveItem(
    title: String,
    leftStr: String,
    rightStr: String,
    selectable: Boolean = true,
    rating: Int, onClick1: (Int) -> Unit = {},
) {
    Column(modifier = Modifier.padding(vertical = 8.dp)) {
        Text(
            text = title,
            style = TextStyle(fontSize = 14.sp, color = Color(0xFF292929))
        )

        Row(
            modifier = Modifier.padding(top = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = leftStr,
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF292929),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier.padding(end = 12.dp)
            )

            repeat(5) { index ->
                Image(
                    painter = if (index == (rating - 1)) {
                        when (index) {
                            0 -> R.drawable.image_meeting_protect_item_revert_select_0.painterResource()
                            1 -> R.drawable.image_meeting_protect_item_revert_select_1.painterResource()
                            2 -> R.drawable.image_meeting_protect_item_revert_select_2.painterResource()
                            3 -> R.drawable.image_meeting_protect_item_revert_select_3.painterResource()
                            4 -> R.drawable.image_meeting_protect_item_revert_select_4.painterResource()
                            else -> R.drawable.image_meeting_protect_item_revert_select_0.painterResource()
                        }
                    } else {
                        when (index) {
                            0 -> R.drawable.image_meeting_protect_item_revert_unselect_0.painterResource()
                            1 -> R.drawable.image_meeting_protect_item_revert_unselect_1.painterResource()
                            2 -> R.drawable.image_meeting_protect_item_revert_unselect_2.painterResource()
                            3 -> R.drawable.image_meeting_protect_item_revert_unselect_3.painterResource()
                            4 -> R.drawable.image_meeting_protect_item_revert_unselect_4.painterResource()
                            else -> R.drawable.image_meeting_protect_item_revert_unselect_0.painterResource()
                        }
                    },
                    contentDescription = "Star ${index + 1}",
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .size(40.dp)
                        .noRippleClickable {
                            if (!selectable) {
                                return@noRippleClickable
                            }
                            onClick1(index + 1)
                        }
                )
            }

            Text(
                text = rightStr,
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF292929),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier.padding(start = 12.dp)
            )
        }
    }
}

@Preview
@Composable
fun PreviewYueYueFeedbackDialog() {
    YueYueFeedbackDialog()
}

@Preview
@Composable
fun PreviewYueYueFeedbackDialog2() {
    YueYueFeedbackDialog(selectable = false)
}