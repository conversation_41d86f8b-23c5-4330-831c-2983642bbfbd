package com.kanzhun.marry.chat.activity.yueyue

import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.lifecycle.Observer
import androidx.lifecycle.viewmodel.compose.viewModel
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.model.message.MessageConstants
import com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_YUEYUE
import com.kanzhun.foundation.model.message.MessageForRecommendOpenChat
import com.kanzhun.foundation.model.message.MessageForRecommendUserCard
import com.kanzhun.foundation.model.message.MessageForRecommendWelcome
import com.kanzhun.foundation.model.message.MessageForText
import com.kanzhun.foundation.model.message.MessageForTime
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.utils.HiMessage
import com.kanzhun.marry.chat.activity.yueyue.ui.YueYueChatScreen
import com.kanzhun.marry.chat.viewmodel.ChatViewModel
import com.kanzhun.utils.base.LList
import com.sankuai.waimai.router.annotation.RouterUri

/**
 * 小红娘月月
 */
@RouterUri(path = [ChatPageRouter.YUE_YUE_ACTIVITY])
class YueYueActivity : BaseComposeActivity(), Observer<HiMessage> {
    override fun enableSafeDrawingPadding() = false

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge()

        super.onCreate(savedInstanceState)
        immersionBar {
            statusBarDarkFont(true)
            transparentStatusBar()
        }

        val chatViewModel by viewModels<ChatViewModel>()
        chatViewModel.init(
            intent.getStringExtra(BundleConstants.BUNDLE_USER_ID),
            intent.getLongExtra(BundleConstants.BUNDLE_SYSTEM_ID, SYS_ID_OFFICIAL_YUEYUE.toLong())
        )
    }

    override fun onResume() {
        super.onResume()
        val chatViewModel by viewModels<ChatViewModel>()
        chatViewModel.message.observe(this, this)
    }

    override fun onChanged(@Suppress("PARAMETER_NAME_CHANGED_ON_OVERRIDE") hiMessage: HiMessage) {
        val chatViewModel by viewModels<ChatViewModel>()

        val messages = hiMessage.chatMessages

        val size = messages.size

        if (!LList.isEmpty(messages)) { // 初次初始化时，hiMessage = new HiMessage(); 传递的latest为false
            if (!hiMessage.isLatest) {
                hiMessage.isLatest =
                    messages[size - 1]
                        .seq >= ServiceManager.getInstance().getMessageService()
                        .getMaxShowSeq(
                            chatViewModel.chatId,
                            MessageConstants.MSG_SINGLE_CHAT
                        )
            }
        } else {
            hiMessage.isLatest = true
        }

        if (size > 0) {
            chatViewModel.updateRead()
        }

        val yueYueViewModel by viewModels<YueYueViewModel>()

        // 过滤无效的数据
        val chatMessages = hiMessage.chatMessages.filter { message ->
            when (message) {
                is MessageForRecommendUserCard -> true

                is MessageForRecommendOpenChat -> true

                is MessageForRecommendWelcome -> true

                is MessageForText -> true

                is MessageForTime -> true

                else -> false
            }
        }

        yueYueViewModel.updateChatMessages(chatMessages)
    }

    override fun onPause() {
        val chatViewModel by viewModels<ChatViewModel>()
        chatViewModel.message.removeObserver(this)
        super.onPause()
    }

    @Composable
    override fun OnSetContent() {
        val chatViewModel = viewModel<ChatViewModel>()
        val yueYueViewModel = viewModel<YueYueViewModel>()
        val yueyueChatId = chatViewModel.chatId ?: ""

        // Initialize YueYueViewModel with chatId and type
        LaunchedEffect(yueyueChatId) {
            yueYueViewModel.init(yueyueChatId, MessageConstants.MSG_SINGLE_CHAT)
        }

        val yueyue by chatViewModel.contactLiveData.observeAsState(Contact().apply {
            nickName = "专属小红娘月月"
        })
        val conversation by ServiceManager.getInstance().getConversationService()
            .findByUid(yueyueChatId, MessageConstants.MSG_SINGLE_CHAT)
            .observeAsState(Conversation())

        YueYueChatScreen(
            yueyueChatId = yueyueChatId,
            contact = yueyue,
            conversation = conversation,
            onRefresh = {
                yueYueViewModel.inPullToRequestMessages()
                chatViewModel.requestMessages(true)
            },
            yueYueViewModel = yueYueViewModel, // Pass the existing ViewModel instance
            onSendMessage = {
                chatViewModel.sendTextMessage(
                    it,
                    yueyueChatId,
                    chatViewModel.type,
                    null
                )
            },
            onResendMessage = { message ->
                chatViewModel.resendMessage(message)
            },
            onBackClick = {
                AppUtil.finishActivity(this)
            },
        )
    }
}