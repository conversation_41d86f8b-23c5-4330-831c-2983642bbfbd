package com.kanzhun.marry.chat.activity.christmas

import android.widget.ImageView
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants.IterateForever
import com.airbnb.lottie.compose.rememberLottieComposition
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.message.CardType
import com.kanzhun.foundation.model.message.OgccGuessCp
import com.kanzhun.foundation.model.message.OgccGuessCp.UserInfo
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.AppTheme
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.chat.R
import com.kanzhun.utils.T
import com.techwolf.lib.tlog.TLog

private const val TAG = "GuessCp"

// 4 猜cp卡片
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun GuessCp(
    modifier: Modifier = Modifier,
    guessCp: OgccGuessCp,
    onGuessCp: (taskId: String, chooseUserId: String) -> Unit = { _, _ -> },
    onGuessCpRight: () -> Unit = {},
    contact: Contact? = Contact(),
) {
    var shouldReportExpose by rememberSaveable { mutableStateOf(true) }
    LaunchedEffect(shouldReportExpose) {
        if (shouldReportExpose) {
            reportPoint("christ-guessclue-publish-expo") {

                actionp2 = guessCp.hitUserId ?: "" // 记录答案的用户的id
                actionp3 =
                    guessCp.tags?.joinToString(",") ?: "" // 记录线索提供的标签文案，多个标签拼在一起用,号隔开
                actionp4 =
                    guessCp.users?.filter { info -> info.userStatus == 1 }?.joinToString(",") {
                        it.userId ?: ""
                    } // 记录提供的选项用户的id，根据abc顺序拼接，用,号隔开（只埋正常用户）
                type =
                    (guessCp.users?.filter { info -> info.userStatus == 1 }?.size
                        ?: 0).toString() // 记录线索展示个数，1：一个选项，2：两个选项，3：三个选项（只埋正常用户）
            }
            shouldReportExpose = false
        }
    }

    SantaMessage(contact = contact, modifier = modifier) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color(0xFFFFF8EB),
                    shape = RoundedCornerShape(20.dp)
                )
        ) {
            val composition by rememberLottieComposition(spec = LottieCompositionSpec.Asset("christmas/chat/chat_qiu.json"))
            LottieAnimation(
                composition = composition,
                iterations = IterateForever,
                modifier = Modifier
                    .size(56.dp)
                    .align(Alignment.TopEnd)
                    .offset(y = (-3).dp)
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            ) {
                Text(
                    text = guessCp.title
                        ?: "解锁了一个盲盒任务，请你认真查看ta的主页，以下是一些线索",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF191919),
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(end = 50.dp),
                )

                Spacer(modifier = Modifier.height(16.dp))

                Column(
                    Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color(0xFF0D5737),
                            shape = RoundedCornerShape(size = 24.dp)
                        )
                        .padding(1.5.dp)
                ) {
                    val tags = guessCp.tags
                    if (!tags.isNullOrEmpty()) {
                        FlowRow(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    color = Color(0xFFFFFFFF),
                                    shape = RoundedCornerShape(size = 24.dp)
                                )
                                .padding(horizontal = 10.dp, vertical = 12.dp),
                            horizontalArrangement = Arrangement.spacedBy(10.dp),
                            verticalArrangement = Arrangement.spacedBy(10.dp)
                        ) {
                            tags.forEach { tag ->
                                LabelItem(label = tag)
                            }
                        }
                    }

                    Row(
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tips),
                            contentDescription = "image description",
                            modifier = Modifier
                                .padding(top = 2.dp)
                                .size(18.dp)
                        )

                        Text(
                            text = guessCp.content ?: "",
                            style = TextStyle(
                                fontSize = 13.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFFFFF8EB),
                            )
                        )
                    }
                }

                Spacer(modifier = Modifier.height(20.dp))

                val users = guessCp.users
                if (!users.isNullOrEmpty()) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight(),
                        horizontalArrangement = Arrangement.spacedBy(30.dp)
                    ) {
                        users.forEach { user ->
                            CpItem(
                                guessCp = guessCp,
                                userInfo = user,
                                onGuessCp = { userId ->
                                    guessCp.chooseUserId = userId

                                    onGuessCp(guessCp.taskId ?: "", userId)

                                    if (AppTheme.christmasShinShow == 1) {
                                        if (guessCp.hitUserId == userId) {
                                            // 刷新页面右上角糖果商店糖果券
                                            onGuessCpRight()
                                        } else {
                                            // 选择错误
                                            T.ss("阿哦～猜错啦～")
                                        }
                                    } else {
                                        TLog.info(TAG, "活动已结束")
                                    }
                                },
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun LabelItem(
    label: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = label,
        modifier = modifier
            .background(color = Color(0xFFF5F5F5), shape = RoundedCornerShape(32.dp))
            .padding(horizontal = 16.dp, vertical = 8.dp),
        style = TextStyle(
            fontSize = 13.sp,
            fontWeight = FontWeight(500),
            color = Color(0xFF292929),
        )
    )
}

@Composable
fun RowScope.CpItem(
    guessCp: OgccGuessCp,
    userInfo: UserInfo,
    modifier: Modifier = Modifier,
    onGuessCp: (chooseUserId: String) -> Unit = { },
) {
    val userId = userInfo.userId
    val isChooseUser = !userId.isNullOrEmpty() && userId == guessCp.chooseUserId
    val isRightUser = userId == guessCp.hitUserId

    // 已选用户，禁用该用户（不可点击）；当前用户不是选中的（置灰）
    val hasChosen = !guessCp.chooseUserId.isNullOrEmpty()
    val isDisabledUser = (hasChosen && !isChooseUser) || userInfo.userStatus == 2

    Column(
        modifier = modifier
            .weight(1f)
            .alpha(if (isDisabledUser) 0.5f else 1f),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box {
            val context = LocalContext.current
            OImageView2(
                imageUrl = userInfo.avatar ?: "",
                inPreview = guessCp.inPreview,
                modifier = Modifier
                    .noRippleClickable {
                        // 跳转用户首页
                        val securityId = userInfo.securityId
                        if (!userId.isNullOrEmpty() && !securityId.isNullOrEmpty()) {
                            MePageRouter.jumpToInfoPreviewActivity(
                                context = context,
                                userId = userId,
                                lid = null,
                                thirdId = "",
                                thirdType = "",
                                sourceType = "",
                                securityId = securityId,
                                protocolFrom = "",
                                sendLikedToFinish = false
                            )
                        }
                    }
                    .size(60.dp)
                    .border(width = 2.dp, color = Color(0xFF0D5737), shape = CircleShape)
                    .aspectRatio(1f)
                    .conditional(guessCp.inPreview) {
                        background(color = Color.LightGray, shape = CircleShape)
                    }
                    .clip(CircleShape),
                scaleType = ImageView.ScaleType.CENTER_CROP
            )

            if (isChooseUser) {
                Image(
                    painter = painterResource(id = if (isRightUser) R.mipmap.chat_ic_choose else R.mipmap.chat_ic_choose_wrong),
                    contentDescription = "image description",
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .size(20.dp)
                )
            }
        }

        Text(
            text = userInfo.nickname ?: "",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF0D5737),
                textAlign = TextAlign.Center,
            ),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )

        Text(
            text = if (isChooseUser) "已选" else "选Ta",
            modifier = Modifier
                .noRippleClickable {
                    if (!hasChosen && !isDisabledUser) {
                        val chooseId = userId ?: ""
                        onGuessCp(chooseId)

                        reportPoint("christ-guessclue-publish-click") {
                            actionp2 = guessCp.hitUserId ?: "" // 记录答案的用户的id
                            actionp3 =
                                guessCp.tags?.joinToString(",") ?: "" // 记录线索提供的标签文案，多个标签拼在一起用,号隔开
                            actionp4 =
                                guessCp.users?.filter { info -> info.userStatus == 1 }
                                    ?.joinToString(",") {
                                        it.userId ?: ""
                                    } // 记录提供的选项用户的id，根据abc顺序拼接，用,号隔开
                            actionp5 = chooseId // 记录用户选择的id
                            type =
                                (guessCp.users?.filter { info -> info.userStatus == 1 }?.size
                                    ?: 0).toString() // 记录线索展示个数，1：一个选项，2：两个选项，3：三个选项
                        }
                    }
                }
                .background(color = Color(0xFF292929), shape = RoundedCornerShape(size = 74.dp))
                .padding(start = 12.dp, top = 4.dp, end = 12.dp, bottom = 4.dp),
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFFFFFFFF),
            )
        )
    }
}

@Preview
@Composable
private fun PreviewGuessCpWrong() {
    GuessCp(guessCp = MOCK_GUESS_CP.apply {
        content = "点击头像可以查看他的个人主页，猜猜Ta是谁，猜对了可以获得一个积分✨"
    })
}

@Preview
@Composable
private fun PreviewGuessCpRight() {
    GuessCp(guessCp = MOCK_GUESS_CP.apply {
//        chooseUserId = ""
        chooseUserId = "zzz"
        content = "点击头像可以查看他的个人主页，猜猜Ta是谁，猜对了可以获得一个积分✨"
    })
}

val MOCK_GUESS_CP = OgccGuessCp().apply {
    christmasCardType = CardType.GUESS_CP.type

    title = "解锁了一个盲盒任务，请你认真查看ta的主页，以下是一些线索"

    tags = listOf(
        "慢热小宇宙\uD83D\uDEF8",
        "新鲜体验派\uD83D\uDEF8",
        "游戏迷\uD83C\uDFAE",
        "音乐\uD83C\uDFB5",
        "爱自由\uD83D\uDE0A",
    )
    users = mutableListOf<UserInfo>().apply {
        add(UserInfo().apply {
            nickname = "我是谁我是谁我是谁我是谁我是谁"
            userId = "xxx"
            userStatus = 2
        })

        add(UserInfo().apply {
            nickname = "周杰伦"
            userId = "yyy"
            userStatus = 2
        })

        add(UserInfo().apply {
            nickname = "派大星派大星派大星派大星派大星"
            userId = "zzz"
            userStatus = 1
        })
    }
    chooseUserId = "xxx"
    hitUserId = "yyy"

    inPreview = true
}