package org.eclipse.paho.client.mqttv3.internal.http;

import org.apache.http.HttpStatus;
import org.apache.http.ProtocolException;
import org.apache.http.impl.io.ChunkedInputStream;
import org.apache.http.impl.io.ContentLengthInputStream;
import org.apache.http.impl.io.IdentityInputStream;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.CharArrayBuffer;
import org.eclipse.paho.client.mqttv3.logging.Logger;
import org.eclipse.paho.client.mqttv3.logging.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.PipedInputStream;
import java.io.PipedOutputStream;

/**
 * Created by yuchaofei on 2017/5/23.
 */

public class HttpReceiver implements Runnable {
    private static final String TAG = "HttpReceiver";

    private static final String CLASS_NAME = HttpReceiver.class.getName();
    private static final Logger log = LoggerFactory.getLogger(LoggerFactory.MQTT_CLIENT_MSG_CAT, CLASS_NAME);

    private boolean running = false;
    private Object lifecycle = new Object();
    private Thread receiverThread = null;
    private PipedOutputStream pipedOutputStream;
    private SocketInputBuffer mSocketInputBuffer;
    private int mBufferSize;

    public HttpReceiver(InputStream input, PipedInputStream pipedInputStream, int bufferSize) throws IOException {
        mBufferSize = bufferSize;
        mSocketInputBuffer = HttpRespParse.createSesssionBuffer(input, mBufferSize);
        this.pipedOutputStream = new PipedOutputStream();
        pipedInputStream.connect(pipedOutputStream);
    }

    /**
     * Starts up the WebSocketReceiver's thread
     */
    public void start(String threadName){
        final String methodName = "start";
        //@TRACE 855=starting
        log.fine(CLASS_NAME, methodName, "855");
        synchronized (lifecycle) {
            if(!running) {
                running = true;
                receiverThread = new Thread(this, threadName);
                receiverThread.start();
            }
        }
    }

    /**
     * Stops this WebSocketReceiver's thread.
     * This call will block.
     */
    public void stop() {
        final String methodName = "stop";
        synchronized (lifecycle) {
            //@TRACE 850=stopping
            log.fine(CLASS_NAME,methodName, "850");
            if(running) {
                running = false;
                closeOutputStream();
                if( !Thread.currentThread().equals(receiverThread)) {
                    try {
                        // Wait for the thread to finish
                        receiverThread.join(100);
                    } catch (InterruptedException ex) {
                        // Interrupted Exception
                    }
                }
            }
        }
        receiverThread = null;
        //@TRACE 851=stopped
        log.fine(CLASS_NAME, methodName, "851");
    }

    public void run() {
        final String methodName = "run";

        byte[] readBuffer = new byte[mBufferSize];
        CharArrayBuffer charArrayBuffer = new CharArrayBuffer(128);
        while (running && (mSocketInputBuffer != null)) {
            try {
                //@TRACE 852=network read message
                log.fine(CLASS_NAME, methodName, "852");
                while (!mSocketInputBuffer.isDataAvailable(30)) {
                    if (!running) {
                        return;
                    }
                }
                if (!running) {
                    return;
                }
                charArrayBuffer.clear();
                HttpResponse respone = new HttpRespParse(mSocketInputBuffer, charArrayBuffer).parse();
                if (HttpRespParse.canResponseHaveBody(respone)) {
                    if (respone.getContentLen() != -1) {
                        respone.setContent(new ContentLengthInputStream(mSocketInputBuffer, respone.getContentLen()));
                    } else if (respone.getTransferEncoding().equalsIgnoreCase(HTTP.CHUNK_CODING)) {
                        respone.setContent(new ChunkedInputStream(mSocketInputBuffer));
                    } else {
                        respone.setContent(new IdentityInputStream(mSocketInputBuffer));
                    }
                } else {
                    log.warning(CLASS_NAME, methodName, respone.toString());
                }
                int status = respone.getStatusLine().getStatusCode();
                if (status < HttpStatus.SC_OK) {
                    if (status != HttpStatus.SC_CONTINUE) {
                        throw new ProtocolException("Unexpected response: " + respone.getStatusLine());
                    }
                } else {
                    int readCount;
                    while (true) {
                        readCount = respone.getContent().read(readBuffer);
                        if (readCount > 0) {
                            pipedOutputStream.write(readBuffer, 0, readCount);
                        } else {
                            break;
                        }
                    }
                    pipedOutputStream.flush();
                }
                mSocketInputBuffer.reset();

            } catch (Exception ex) {
                ex.printStackTrace();
                // Exception occurred whilst reading the stream.
                this.stop();
            }
        }
    }

    private void closeOutputStream(){
        try {
            pipedOutputStream.close();
        } catch (IOException e) {
        } finally {
            try {
                if (mSocketInputBuffer != null) {
                    InputStream inputStream = mSocketInputBuffer.instream;
                    if (inputStream != null) {
                        inputStream.close();
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    public boolean isRunning() {
        return running;
    }
}
