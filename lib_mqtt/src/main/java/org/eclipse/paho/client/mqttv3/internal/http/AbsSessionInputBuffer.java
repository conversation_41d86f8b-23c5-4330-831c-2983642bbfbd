package org.eclipse.paho.client.mqttv3.internal.http;

import org.apache.http.impl.io.HttpTransportMetricsImpl;
import org.apache.http.io.HttpTransportMetrics;
import org.apache.http.io.SessionInputBuffer;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.ByteArrayBuffer;
import org.apache.http.util.CharArrayBuffer;

import java.io.IOException;

/**
 * Created by yuchaofei on 2017/5/23.
 */

public abstract class AbsSessionInputBuffer implements SessionInputBuffer {
    protected byte[] buffer;
    protected int bufferpos;
    protected int bufferlen;
    protected ByteArrayBuffer linebuffer = null;


    protected String charset = HTTP.US_ASCII;
    protected boolean ascii = true;
    protected int maxLineLen = -1;
    protected HttpTransportMetricsImpl metrics;

    public boolean hasBufferedData() {
        return this.bufferpos < this.bufferlen;
    }


    public int read() throws IOException {
        int noRead = 0;
        while (!hasBufferedData()) {
            noRead = fillBuffer();
            if (noRead == -1) {
                return -1;
            }
        }
        return this.buffer[this.bufferpos++] & 0xff;
    }


    public int read(final byte[] b, int off, int len) throws IOException {
        if (b == null) {
            return 0;
        }
        int noRead = 0;
        while (!hasBufferedData()) {
            noRead = fillBuffer();
            if (noRead == -1) {
                return -1;
            }
        }
        int chunk = this.bufferlen - this.bufferpos;
        if (chunk > len) {
            chunk = len;
        }
        System.arraycopy(this.buffer, this.bufferpos, b, off, chunk);
        this.bufferpos += chunk;
        return chunk;
    }


    public int read(final byte[] b) throws IOException {
        if (b == null) {
            return 0;
        }
        return read(b, 0, b.length);
    }


    private int locateLF() {
        for (int i = this.bufferpos; i < this.bufferlen; i++) {
            if (this.buffer[i] == HTTP.LF) {
                return i;
            }
        }
        return -1;
    }


    public int readLine(final CharArrayBuffer charbuffer) throws IOException {
        if (charbuffer == null) {
            throw new IllegalArgumentException("Char array buffer may not be null");
        }
        this.linebuffer.clear();
        int noRead = 0;
        boolean retry = true;
        while (retry) {
            // attempt to find end of line (LF)
            int i = locateLF();
            if (i != -1) {
                // end of line found.
                if (this.linebuffer.isEmpty()) {
                    // the entire line is preset in the read buffer
                    return lineFromReadBuffer(charbuffer, i);
                }
                retry = false;
                int len = i + 1 - this.bufferpos;
                this.linebuffer.append(this.buffer, this.bufferpos, len);
                this.bufferpos = i + 1;
            } else {
                // end of line not found
                if (hasBufferedData()) {
                    int len = this.bufferlen - this.bufferpos;
                    this.linebuffer.append(this.buffer, this.bufferpos, len);
                    this.bufferpos = this.bufferlen;
                }
                noRead = fillBuffer();
                if (noRead == -1) {
                    retry = false;
                }
            }
            if (this.maxLineLen > 0 && this.linebuffer.length() >= this.maxLineLen) {
                throw new IOException("Maximum line length limit exceeded");
            }
        }
        if (noRead == -1 && this.linebuffer.isEmpty()) {
            // indicate the end of stream
            return -1;
        }
        return lineFromLineBuffer(charbuffer);
    }


    private int lineFromLineBuffer(final CharArrayBuffer charbuffer) throws IOException {
        // discard LF if found
        int l = this.linebuffer.length();
        if (l > 0) {
            if (this.linebuffer.byteAt(l - 1) == HTTP.LF) {
                l--;
                this.linebuffer.setLength(l);
            }
            // discard CR if found
            if (l > 0) {
                if (this.linebuffer.byteAt(l - 1) == HTTP.CR) {
                    l--;
                    this.linebuffer.setLength(l);
                }
            }
        }
        l = this.linebuffer.length();
        if (this.ascii) {
            charbuffer.append(this.linebuffer, 0, l);
        } else {
            // This is VERY memory inefficient, BUT since non-ASCII charsets are
            // NOT meant to be used anyway, there's no point optimizing it
            String s = new String(this.linebuffer.buffer(), 0, l, this.charset);
            charbuffer.append(s);
        }
        return l;
    }


    private int lineFromReadBuffer(final CharArrayBuffer charbuffer, int pos) throws IOException {
        int off = this.bufferpos;
        int len;
        this.bufferpos = pos + 1;
        // BEGIN android-changed
        // The first test below was fixed to not try to skip beyond the
        // start of the live part of the buffer.
        if (pos > off && this.buffer[pos - 1] == HTTP.CR) {
            // skip CR if found
            pos--;
        }
        // END android-changed
        len = pos - off;
        if (this.ascii) {
            charbuffer.append(this.buffer, off, len);
        } else {
            // This is VERY memory inefficient, BUT since non-ASCII charsets are
            // NOT meant to be used anyway, there's no point optimizing it
            String s = new String(this.buffer, off, len, this.charset);
            charbuffer.append(s);
        }
        return len;
    }


    public String readLine() throws IOException {
        CharArrayBuffer charbuffer = new CharArrayBuffer(64);
        int l = readLine(charbuffer);
        if (l != -1) {
            return charbuffer.toString();
        } else {
            return null;
        }
    }


    public HttpTransportMetrics getMetrics() {
        return this.metrics;
    }


    public byte[] getBuffer() {
        return buffer;
    }


    public int getBufferpos() {
        return bufferpos;
    }


    public int getBufferlen() {
        return bufferlen;
    }

    protected abstract int fillBuffer() throws IOException ;
}
