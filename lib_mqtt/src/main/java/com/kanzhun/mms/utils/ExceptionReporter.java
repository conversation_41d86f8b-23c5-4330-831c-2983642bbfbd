package com.kanzhun.mms.utils;

import android.os.Message;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/3/24.
 */

public class ExceptionReporter extends ThreadManager.MMSHandler {
    private static final String TAG = "ExceptionReporter";
    private final static int HANDLE_EXECPTION = 1;
    private static ExceptionReporter sInstance = new ExceptionReporter();

    private ExceptionReporter() {
        super(ThreadManager.getReportThreadLooper());
    }

    public void postRelyNetExecption(TWLException ex) {
        postRelyNetExecption(ex, null);
    }

    public void postRelyNetExecption(TWLException ex, Runnable runnable) {
        long time = System.currentTimeMillis();
        Message message = obtainMessage(HANDLE_EXECPTION);
        message.arg1 = (int) (0xFFFFFFFFl & time);
        message.arg2 = (int) ((0xFFFFFFFF00000000l & time) >> 32);
        message.obj = new InnerBean(ex, runnable);
        message.sendToTarget();
    }

    private static class InnerBean {
        public TWLException mTWLException;
        public Runnable mRunnable;

        public InnerBean(TWLException TWLException, Runnable runnable) {
            mTWLException = TWLException;
            mRunnable = runnable;
        }
    }

    @Override
    public void handleMessage(Message msg) {
        long time = ((long) msg.arg1 & 0xFFFFFFFFl) | (((long) msg.arg2 << 32) & 0xFFFFFFFF00000000l);
        long stratTime = System.currentTimeMillis();
        if (stratTime - time > 2000){
            BLog.e(TAG, "postCatchedException timeout : current = [%d], submit = [%d]", stratTime, time);
            return;
        }
        switch (msg.what) {
            case HANDLE_EXECPTION:
                if (isPingHost()) {
                    if (System.currentTimeMillis() - stratTime < 2000){
                        InnerBean innerBean = (InnerBean) msg.obj;
//                        String url = MMSServiceNative.getServerProfile().getServerInfo().getServerUrl();
//                        TWLTraceRoute.getTWLTraceRoute().ping(url);
                        ExceptionUtils.postCatchedException(innerBean.mTWLException);
                        if (innerBean.mRunnable != null) {
                            String ipInfo = getIPInfo();
                            if (ipInfo != null) {
                                BLog.d(TAG, "ipInfo = [%s]", ipInfo);
                            }

                            checkNetwork();
                            innerBean.mRunnable.run();
                        }
                    } else {
                        BLog.e(TAG, "postCatchedException timeout2 : current = [%d], start = [%d]", System.currentTimeMillis(), stratTime);
                    }
                } else {
                    BLog.e(TAG, "No network");
                }
                break;
        }
    }

    public static ExceptionReporter getInstance() {
        return sInstance;
    }

    private static final String PING_URL = "http://3gimg.qq.com/ping.html";

    /**
     * 阻塞方法 探测网络是否可用
     */
    public static boolean isPingHost() {
        boolean ret = false;
        try {
            HttpURLConnection httpConn = getConnection(PING_URL);
            httpConn.connect();
            byte[] content = new byte[8];
            httpConn.getInputStream().read(content);
            httpConn.getInputStream().close();
            httpConn.disconnect();
            String resp = new String(content);
            if ("Poduct3G".equals(resp)) {
                ret = true;
            }
        } catch (Exception e) {
        }
        return ret;
    }

    private static String getIPInfo() {
        String ip = null;
        try {
            HttpURLConnection httpConn = getConnection("http://ip.taobao.com/service/getIpInfo2.php?ip=myip");
            httpConn.setRequestProperty("User-Agent", "Mozilla/5.0");
            if (httpConn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(httpConn.getInputStream()));
                String line;
                StringBuilder jsonRespone = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    jsonRespone.append(line);
                }
                JSONObject jsonObject = new JSONObject(jsonRespone.toString());
                String code = jsonObject.getString("code");
                if (code.equals("0")) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    StringBuilder result = new StringBuilder(26);
                    result.append(data.getString("ip")).append(" ");
                    result.append(data.getString("city")).append(data.getString("isp"));
                    ip = result.toString();
                }
            }
            httpConn.getInputStream().close();
            httpConn.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ip;
    }

    private static HttpURLConnection getConnection(String httpUrl) throws IOException {
        URL url = new URL(httpUrl);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        httpConn.setDoOutput(true);
        httpConn.setDoInput(true);
        httpConn.setRequestMethod("GET");
        httpConn.setUseCaches(false);
        httpConn.setInstanceFollowRedirects(false);
        httpConn.setReadTimeout(60000);
        return httpConn;
    }

    private static boolean isCanCheck = true;
    private static void checkNetwork() {
        try {
            if (isCanCheck) {
                isCanCheck = false;

//                List<String> list = MMSServiceNative.getServerProfile().getServerInfo().getHttpEndpoint().getBackupIPList();
//                if (list == null) {
//                    list = MMSServiceNative.getServerProfile().getServerInfo().getEndpoint().getBackupIPList();
//                }
//                String url = null;
//                if (list != null && list.size() > 0) {
//                    url = list.get(0);
//                }
//                if (url == null) {
//                    url = MMSServiceNative.getServerProfile().getServerInfo().getEndpoint().getServerUrl();
//                }
//                if (url != null)
//                    TWLTraceRoute.getTWLTraceRoute().traceRoute(url, 80);
            }
        } catch (Throwable e) {
            BLog.printErrStackTrace(TAG, e, "checkNetwork");
        }
    }


}
