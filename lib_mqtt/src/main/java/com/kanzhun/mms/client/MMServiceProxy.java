package com.kanzhun.mms.client;

import static com.kanzhun.mms.common.MMSConstants.PROCESS_MULTI;
import static com.kanzhun.mms.common.MMSConstants.PROCESS_SINGLETON;
import static com.kanzhun.mms.utils.TWLException.MMS_CLIENT_CHANGETOSINGLETON;

import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.DeadObjectException;
import android.os.IBinder;
import android.os.Process;
import android.os.RemoteException;
import android.os.SystemClock;

import com.kanzhun.mms.ConnectionInfo;
import com.kanzhun.mms.IMMService;
import com.kanzhun.mms.IMMServicePushFilter;
import com.kanzhun.mms.MMSMessage;
import com.kanzhun.mms.ServerResponse;
import com.kanzhun.mms.common.Shakehands;
import com.kanzhun.mms.common.UserInfo;
import com.kanzhun.mms.service.AppStatus;
import com.kanzhun.mms.service.MMSServiceNative;
import com.kanzhun.mms.service.process.ProcessGuard;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.mms.utils.ExceptionUtils;
import com.kanzhun.mms.utils.MqttUtil;
import com.kanzhun.mms.utils.TWLException;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Created by yuchaofei on 2017/3/1.
 * MMService 客户端代理类
 */
class MMServiceProxy implements IDeliveredListener, ConnectionService.IServiceConnection, IKickListener {
    private static final String TAG = "MMServiceProxy";
    private static final long MULTI_PROCESS_DEATH_MAX_COUNT = 3; //通信进程死亡次数
    private static final long START_TIME_INIT = 0; //通信进程启动时间初始值
    private static final long DEFAULT_CHANAGE_PROCESS_TIME = 30 * 1000;//通信进程启动等待最大时间，超过此时间，认为服务启动失败，启用单进程模式。
    private static final long WORKER_DEFAULT_WAIT_TIME = 50;//worker默认sleep时间
    private static final long WORKER_START_WAIT_TIME = 500;//worker启动服务sleep时间
    private Context mContext;
    private volatile IMMService mIMMService;
    private Worker mWorker;
    private LinkedBlockingQueue<MMSMessage> mMsgQueue = new LinkedBlockingQueue<>();
    private TimeoutChecker mTimeoutChecker;
    private IMMServicePushFilter mMMServicePushFilter;
    private ConnectionService mService = null;
    private UserInfo mUserInfo;
    /**
     * 记录服务启动耗时
     */
    private AtomicLong mStartTime = new AtomicLong(START_TIME_INIT);
    private int mProcessType;

//    private CancelHelper mCancelHelper = new CancelHelper();

    MMServiceProxy(Context context, int processType) {
        this.mContext = context;
        mProcessType = processType;
        mService = createService(processType);
        Shakehands.registerShakehands(mContext, mShakehandsReceiver);
    }

    private BroadcastReceiver mShakehandsReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            BLog.d(TAG, "mShakehandsReceiver onReceive() called");
            getIMMService();
        }
    };

    /**
     * 返回当前是否是多进程模式
     *
     * @return
     */
    public boolean isMultiModel(){
        return mService instanceof MultiProcessService;
    }

    public boolean isMMSLiving() {
        return mService instanceof MultiProcessService && mIMMService != null;
    }

    /**
     * 启动worker线程，建议setMMServicePushFilter后启动
     */
    public void start() {
        if (mWorker == null) {
            mWorker = new Worker();
            mWorker.start();
        }
    }

    /**
     * 创建通信服务
     * @param processType
     * @return
     */
    private ConnectionService createService(int processType){
        ConnectionService connectionService;
        switch (processType) {
            case PROCESS_MULTI:
                connectionService = new MultiProcessService(mContext, this);
                break;
            case PROCESS_SINGLETON:
                connectionService = new SingletonProcessService(mContext, this);
                break;
            default:
                throw new IllegalArgumentException("Error processType!");
        }
        return connectionService;
    }

    /**
     * 通信进程启动成功回调
     * @param mmService
     */
    @Override
    public void onServiceConnected(IMMService mmService) {
        UserInfo userInfo = null;
        try {
            mmService.registerPushFilter(mMMServicePushFilter);
            userInfo = mUserInfo;
            if (userInfo != null) {
                mmService.setAccountInfo(userInfo.getClientId(), userInfo.getUserName(), userInfo.getPassword());
            } else {
                BLog.e(TAG, "userinfo is null");
            }
        } catch (RemoteException e) {
            BLog.printErrStackTrace(TAG, e, "onServiceConnected");
        } finally {
            mIMMService = mmService;
            if (userInfo == null && mUserInfo != null) {
                userInfo = mUserInfo;
                if (userInfo != null) {
                    try {
                        mmService.setAccountInfo(userInfo.getClientId(), userInfo.getUserName(), userInfo.getPassword());
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }
            }
            long time = SystemClock.elapsedRealtime() - mStartTime.getAndSet(START_TIME_INIT);//重置启动时间初始值
            BLog.d(TAG, "start connection service time = [%d]", time);
            setForeground(AppStatus.isForeground());
        }
    }

    /**
     * 通信服务端口回调
     * @param mmService
     */
    @Override
    public void onServiceDisconnected(IMMService mmService, boolean isDied) {
        BLog.d(TAG, "onServiceDisconnected() called with: mmService = [%s], mIMMService = [%s]", mmService, mIMMService);
        if (isDied) {
            resendAllTimeoutTask();
        }
        if (mmService == mIMMService && mIMMService != null) {
            mIMMService = null;
        }
    }

    /**
     * 返回通信服务代理
     * @return
     */
    private IMMService getIMMService(){
        IMMService immService = mIMMService;
        if (mIMMService == null) {
            synchronized (this) {
                if (mIMMService == null) {
                    ConnectionService service = mService;
                    if (service == null) {
                        return null;
                    }
                    mStartTime.compareAndSet(START_TIME_INIT, SystemClock.elapsedRealtime());//更新启动时间初始值
                    if (service.startService()) {
                        return mIMMService;
                    } else {
                        return null;
                    }
                } else {
                    immService = mIMMService;
                }
            }
        }
        return immService;
    }

    /**
     * 注册Server
     *
     * @param MMServicePushFilter
     */
    public void setMMServicePushFilter(IMMServicePushFilter MMServicePushFilter) {
        BLog.d(TAG, "setMMServicePushFilter");
        mMMServicePushFilter = MMServicePushFilter;
    }

    /**
     * 设置通信用户，连接
     * @param userInfo
     */
    public void setUserInfo(UserInfo userInfo) {
        UserInfo info = mUserInfo;
        if (info != null && info.equals(userInfo)){
            BLog.d(TAG, "Userinfo equal");
            return;
        }
        mUserInfo = userInfo;
        IMMService immService = getIMMService();
        if (immService != null) {
            try {
                BLog.d(TAG, "setUserInfo");
                immService.setAccountInfo(userInfo.getClientId(), userInfo.getUserName(), userInfo.getPassword());
            } catch (Exception e) {
                BLog.printErrStackTrace(TAG, e, "setUserInfo");
            }
        } else {
            BLog.d(TAG, "immService is null, userinfo = [%s]", mUserInfo);
        }
    }

    /**
     * 发送消息
     *
     * @param mmsMessage
     */
    public void send(MMSMessage mmsMessage) {
        BLog.d(TAG, "send");
        mMsgQueue.offer(mmsMessage);
    }

    /**
     * 断开连接，队列中的所有消息发送设置为失败
     */
    public void disconnect() {
        mUserInfo = null;
        cancelAllWaitTask();
        cancelAllTimeoutTask();
        if (mIMMService == null) {
            return;
        }
        IMMService immService = getIMMService();
        if (immService != null) {
            try {
                immService.disconnect();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        BLog.d(TAG, "disconnect");

    }

    /**
     * 通信服务退出
     */
    public void exit() {
        cancelAllWaitTask();
        cancelAllTimeoutTask();
        if (mIMMService == null) {
            return;
        }
        IMMService immService = getIMMService();
        if (immService != null) {
            try {
                immService.exit();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        BLog.d(TAG, "exit");

    }

    private boolean mShadow = false;
    /**
     * 设置APP前后台
     *
     * @param isForeground
     */
    public void setForeground(boolean isForeground) {
        AppStatus.setForeground(isForeground);
        if (isForeground == mShadow) {
            return;
        }
        if (mIMMService == null) {
            return;
        }
        IMMService immService = getIMMService();
        if (immService != null) {
            try {
                immService.setForeground(isForeground ? 1 : 0);
                mShadow = isForeground;
            } catch (Exception e) {
                BLog.e(TAG, "setForeground error %s", e.toString());
                e.printStackTrace();
            }
        }
    }

//    public void cancel(int id) {
//        if (mIMMService == null) {
//            mCancelHelper.addCancel(id);
//            return;
//        }
//        internalCancel(id);
//    }

    /**
     * 取消对应ID的发送任务
     * @param id
     */
    private void internalCancel(int id){
        IMMService immService = mIMMService;
        if (immService != null) {
            try {
                immService.cancel(id);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 目前为log日志flush
     */
    public void flush(){
        if (mIMMService == null) {
            return;
        }
        IMMService immService = getIMMService();
        if (immService != null) {
            try {
                immService.flush();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public ConnectionInfo dumpConnectionInfo(){
        if (mIMMService == null) {
            return null;
        }
        IMMService immService = getIMMService();
        if (immService != null) {
            try {
                return immService.dumpConnection();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 删除所有等待的发送任务
     */
    private void cancelAllWaitTask() {
        try {
            Object[] objects = mMsgQueue.toArray();
            mMsgQueue.clear();
            for (Object object : objects) {
                if (object instanceof MMSMessage) {
                    onMessageFailure((MMSMessage) object);
                }
            }
            BLog.d(TAG, "cancelAllWaitTask size = [%d]", objects.length);
        } catch (Exception e) {
            BLog.printErrStackTrace(TAG, e, "cancelAllWaitTask");
        }
    }

    /**
     * 重新发送超时队列中所有数据
     */
    private void resendAllTimeoutTask() {
        TimeoutChecker timeoutChecker = mTimeoutChecker;
        if (timeoutChecker != null) {
            List<MMSMessage> list = timeoutChecker.getMMSMessages();
            if (!list.isEmpty()) {
                BLog.d(TAG, "resendAllTimeoutTask size = [%d]", list.size());
                mMsgQueue.addAll(list);
            }
        }
    }

    /**
     * 取消所有超时任务
     */
    private void cancelAllTimeoutTask(){
        TimeoutChecker timeoutChecker = mTimeoutChecker;
        if (timeoutChecker != null) {
            timeoutChecker.removeAll();
        }
    }

    /**
     * 处理发送消息工具类
     * @param message
     */
    private static void onMessageFailure(MMSMessage message) {
        if (message != null) {
            ISendCallback sendCallback = message.getSendCallback();
            if (sendCallback != null) {
                sendCallback.onFailure();
            }
        }
    }

    /**
     * 将服务切换为单进程
     */
    private void changeToSingleton(long time){
        if (mService instanceof MultiProcessService) {
            mService.removeConnection();
            mService.stopService();

            mIMMService = null;
            mService = createService(PROCESS_SINGLETON);
            if (AppStatus.isForeground()) {
                StringBuffer info = new StringBuffer("ChangeToSingleton, Only Statistics! uid = ");
                UserInfo userInfo = mUserInfo;
                if (userInfo != null) {
                    info.append(userInfo.getUserName());
                }
                info.append(", WaitTime = ").append(time);
                info.append(", gDeathCount = ").append(MultiProcessService.gDeathCount);
                ExceptionUtils.postCatchedException(new TWLException(MMS_CLIENT_CHANGETOSINGLETON, new Exception(info.toString())));
            }
        } else {
            BLog.e(TAG, "mService is not MultiProcessService!");
        }
    }

    private MultiProcessService mGuardProcess = null;

    /**
     * 临时方法，在单进程模式中启动通信进程
     */
    private void startMMSProcess() {
        try {
            if (mService instanceof SingletonProcessService && AppStatus.isForeground() && mProcessType == PROCESS_MULTI) {
                if (mGuardProcess == null) {
                    mGuardProcess = new MultiProcessService(mContext, null);
                }
                if (!mGuardProcess.isBind()) {
                    BLog.d(TAG, "SingletonProcess start MultiProcessService");
                    mGuardProcess.startBaseServiceConn();
                }
            }
        } catch (Throwable e) {
        }
    }

    /**
     * 处理消息发送
     * 1.发送前检查服务是否启动，如果超时，启动单进程模式
     * 2.等待过程中，mms服务挂掉，启动单进程模式
     * 3.加入超时队列，发送消息。
     * @return
     */
    private long continueProcessMsgQueue() {
        MMSMessage mmsMessage = null;
        IMMService immService = null;
        try {
            immService = getIMMService();
            if (immService == null) {
                boolean isTimeout = false;
                long startTime = mStartTime.get();
                long time = -1;
                if (startTime > START_TIME_INIT) {
                    time =  SystemClock.elapsedRealtime() - startTime;
                    isTimeout = time > DEFAULT_CHANAGE_PROCESS_TIME;
                    if (isTimeout && !AppStatus.isActive() && MqttUtil.isMMSRuning(mContext)) {
                        isTimeout = false;
                    }
                }
                if (isTimeout //如果多进程模式Service启动超过默认时间，切换为单进程模式
                        || MultiProcessService.isOftenKilled()) {//如果通信进程被干死，启动单进程模式
                    BLog.e(TAG, "changeToSingleton wait time = [%d], deathCount = [%d]", time, MultiProcessService.gDeathCount);
                    changeToSingleton(time);
                }
                return WORKER_START_WAIT_TIME;
            }

            mStartTime.set(START_TIME_INIT);//重置启动时间
            startMMSProcess();
            mmsMessage = mMsgQueue.take();
            immService = getIMMService();//更新bind
            if (immService == null){//如果启动成功，在等待消息的过程中，服务Disconnected，重新bind服务
                mMsgQueue.offer(mmsMessage);
                BLog.e(TAG, "mIMMService is null on take() after!");
                return WORKER_DEFAULT_WAIT_TIME;
            }

            ISendCallback sendCallback = mmsMessage.getSendCallback();
            if (mTimeoutChecker == null) {
                mTimeoutChecker = new TimeoutChecker();
            }

            if (sendCallback != null) {
                int id = mmsMessage.getId();
                mTimeoutChecker.addMessage(id, new TimeoutChecker.TimeoutCheckCallback(mmsMessage){
                    @Override
                    public void onTimeout(int id) {
                        super.onTimeout(id);
                        MMServiceProxy.this.internalCancel(id);
                    }
                });
            }
            immService.send(mmsMessage);
        } catch (Throwable e) {
            BLog.printErrStackTrace(TAG, e, "continueProcessMsgQueue");
            if (e instanceof DeadObjectException) {// 如果通信进程挂了，重新加入队列
                mTimeoutChecker.removeMessage(mmsMessage.getId());
                mMsgQueue.offer(mmsMessage);
                if (mIMMService == immService) {
                    mIMMService = null;
                }
                return WORKER_DEFAULT_WAIT_TIME;
            }
            try {
                if (mmsMessage != null
                        && mTimeoutChecker != null) {
                    mTimeoutChecker.removeMessage(mmsMessage.getId());
                    onMessageFailure(mmsMessage);
                }
            } catch (Throwable e1) {
                BLog.printErrStackTrace(TAG, e1, "continueProcessMsgQueue removeMessage");
            }
        } finally {
            if (mmsMessage != null) {
                MMSMessage.relaseMessage(mmsMessage);
            }
        }
        return WORKER_DEFAULT_WAIT_TIME;
    }

    @Override
    public void onDelivered(int id, ServerResponse result) {
        TimeoutChecker timeoutChecker = mTimeoutChecker;
        if (timeoutChecker != null) {
            try {
                ISendCallback sendCallback = timeoutChecker.removeMessage(id);
                if (sendCallback != null) {
                    if (result != null && result.success) {
                        sendCallback.onSuccess(result);
                    } else {
                        sendCallback.onFailure();
                    }
                }
            } catch (Exception e) {
                BLog.printErrStackTrace(TAG, e, "onDelivered");
            }
        }
    }

    @Override
    public void onKick() {
        mUserInfo = null;
    }


    private class Worker extends Thread {

        @Override
        public void run() {
            try {
                if (mService instanceof SingletonProcessService) {
                    Thread.sleep(10);
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
            while (true) {
                try {
                    long waitTime = continueProcessMsgQueue();
                    Thread.sleep(waitTime);
                } catch (Throwable e) {
                    BLog.printErrStackTrace(TAG, e, "Worker run()");
                }
            }
        }
    }

    /**
     * 多进程模式
     */
    private static class MultiProcessService extends ConnectionService implements IBinder.DeathRecipient, ServiceConnection {
        private static final String SERVICE = "com.kanzhun.mms.service.MMSServiceNative";

        private static int gDeathCount = 0;
        private static long gServiceRunTime = 0;

        public static boolean isOftenKilled(){
            return gDeathCount > MULTI_PROCESS_DEATH_MAX_COUNT //如果death
                    || (gServiceRunTime < 30 * 1000 && gServiceRunTime != 0);
        }

        private ProcessGuard mProcessGuard = new ProcessGuard(this);
        private volatile IMMService mIMMService;
        protected volatile long lastStartSerivceTime = -1;
        private long mStartTime = 0;

        public MultiProcessService(Context context, IServiceConnection serviceConnection) {
            super(context, serviceConnection);
        }

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            BLog.d(TAG, "onServiceConnected() called with: name = [%s], service = [%s]", name, service);
            if (service != null) {
                mStartTime = SystemClock.elapsedRealtime();
                IMMService mmService;
                try {
                    mmService = IMMService.Stub.asInterface(service);
                    if (mmService != null) {
                        mIMMService = mmService;
                        mProcessGuard.setBinder(mmService);
                        IServiceConnection connection = mConnection;
                        if (connection != null) {
                            connection.onServiceConnected(mmService);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            IServiceConnection connection = mConnection;
            if (connection != null) {
                connection.onServiceDisconnected(mIMMService, false);
            }
        }

        /**
         * 启动服务并绑定
         */
        private void startBaseServiceConn() {
            try {
                ComponentName componentName = new ComponentName(mContext.getPackageName(),
                        SERVICE);
                Intent intent = new Intent();
                intent.setComponent(componentName);
                mContext.startService(intent);
                if (!mContext.bindService(intent, this, Application.BIND_AUTO_CREATE)) {
                    BLog.e(TAG, "MMS service bind failed!!!");
                }
            } catch (Throwable e) {
                BLog.printErrStackTrace(TAG, e, "startBaseServiceConn");
            }
        }

        @Override
        public boolean startService() {
            long now = System.currentTimeMillis();
            if (lastStartSerivceTime == -1 || (now - lastStartSerivceTime > 10000)) {
                lastStartSerivceTime = now;
                startBaseServiceConn();
            }
            return false;
        }

        @Override
        public void stopService() {
            try {
                ComponentName componentName = new ComponentName(mContext.getPackageName(),
                        SERVICE);
                Intent intent = new Intent();
                intent.setComponent(componentName);
                mContext.unbindService(this);
                mContext.stopService(intent);

                int pid = MqttUtil.getMMSPid(mContext);
                if (pid != -1) {
                    BLog.e(TAG, "kill mms process");
                    Process.killProcess(pid);
                }
            } catch (Throwable e) {
                BLog.printErrStackTrace(TAG, e, "stopService");
            }
        }

        @Override
        public void removeConnection() {
            mConnection = null;
        }

        @Override
        public void binderDied() {
            BLog.d(TAG, "binderDied() called gDeathCount = [%d]", gDeathCount);
            IServiceConnection connection = mConnection;
            if (connection != null) {
                connection.onServiceDisconnected(mIMMService, true);
            }
            stopService();
            mIMMService = null;
            gServiceRunTime = SystemClock.elapsedRealtime() - mStartTime;
            if (gServiceRunTime < 10 * 60 * 1000) {
                gDeathCount ++;
            } else {
                startService();
            }
        }

        public boolean isBind(){
            return mIMMService != null;
        }
    }

    /**
     * 单进程模式
     */
    private static class SingletonProcessService extends ConnectionService {

        public SingletonProcessService(Context context, IServiceConnection serviceConnection) {
            super(context, serviceConnection);
        }

        @Override
        public boolean startService() {
            IMMService service = MMSServiceNative.init(mContext);
            IServiceConnection connection = mConnection;
            if (connection != null) {
                connection.onServiceConnected(service);
            }
            return true;
        }

        @Override
        public void stopService() {

        }

        @Override
        public void removeConnection() {
            mConnection = null;
        }
    }
}
