package com.kanzhun.mms.client;

import static com.kanzhun.mms.common.MMSConstants.PROCESS_MULTI;
import static com.kanzhun.mms.common.MMSConstants.PROCESS_SINGLETON;

import android.app.Application;
import android.content.Context;

import androidx.annotation.NonNull;

import com.kanzhun.mms.ConnectionInfo;
import com.kanzhun.mms.MMSMessage;
import com.kanzhun.mms.common.IServerProfile;
import com.kanzhun.mms.common.MMSMessageFactory;
import com.kanzhun.mms.common.UserInfo;
import com.kanzhun.mms.service.MMSServiceNative;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.mms.utils.ExceptionUtils;

/**
 * Created by yuchaofei on 2017/3/3.
 */

public class MMSServiceSDK {
    private static final String TAG = "MMSServiceSDK";
    /**
     * 通信进程名称
     */
    public static final String PROCESS_NAME = "mms";
    private static Context sContext;
    private static int sProcessType = PROCESS_MULTI;
    private static ForegroundLifecycle sForegroundLifecycle;

    /**
     * 初始化通信SDK，默认多进程模式
     * @param context
     * @return
     */
    private static void initialize(Context context) {
        if (context != null && sContext == null) {
            sContext = context.getApplicationContext();
            if (context instanceof Application) {
                Application application = (Application) context;
                sForegroundLifecycle = new ForegroundLifecycle();
                application.registerActivityLifecycleCallbacks(sForegroundLifecycle);
            }
        }
    }

    public static void startLifecycle() {
        sForegroundLifecycle.setForegroundChangeStart(true);
    }

    /**
     * 初始化通信SDK，建议在Application.onCrate方法初始化，没有耗时操作
     * @param context ApplicationContex
     * @param strategy 初始化策略
     */
    public static void initialize(@NonNull Context context, MMSStrategy strategy) {
        sProcessType = strategy.isMultiProcess ? PROCESS_MULTI : PROCESS_SINGLETON;
        initialize(context);
    }

    /**
     * 返回MMS SDK 实例，调用之前先调用initialize
     * @return
     */
    public static MMSServiceSDK get() {
        if (sContext == null) {
            throw new NullPointerException("Please call the initialize() first");
        }
        return MMSServiceSDKProxy.sSDK;
    }

    private static final class MMSServiceSDKProxy {
        private static final MMSServiceSDK sSDK = new MMSServiceSDK();
    }

    private MMServiceProxy mMMServiceProxy;
    private Receiver mReceiver;

    private MMSServiceSDK() {
        mMMServiceProxy = new MMServiceProxy(sContext, sProcessType);
        mReceiver = new Receiver(sContext);
        mMMServiceProxy.setMMServicePushFilter(mReceiver);
        mReceiver.setDeliveredListener(mMMServiceProxy);
        mReceiver.setKickListener(mMMServiceProxy);
        mMMServiceProxy.start();
    }

    /**
     * 设置连接监听，建议在连接与发送消息之前
     * @param connectionListener
     */
    public void setConnectionListener(IConnectionListener connectionListener){
        mReceiver.setConnectionListener(connectionListener);
    }

    /**
     * 设置消息接收监听，建议在连接与发送消息之前
     * @param receiveListener
     */
    public void setReceiveListener(IReceiveListener receiveListener){
        mReceiver.setReceiveListener(receiveListener);
    }

    /**
     * 连接服务器
     * @param clientId
     * @param userName
     * @param password
     */
    public void connect(String clientId, String userName, String password) {
        mMMServiceProxy.setUserInfo(new UserInfo(clientId, userName, password));
    }

    /**
     * 发送消息
     * @param data
     * @param callback
     */
    public void send(byte[] data, ISendCallback callback) {
        send(data, callback, true);
    }

    /**
     * 发送消息
     * @param data
     * @param callback
     * @param isRetry
     */
    public void send(byte[] data, ISendCallback callback, boolean isRetry) {
        MMSMessage mmsMessage = MMSMessageFactory.createMqttMessage(data, callback);
        if (!isRetry) {
            mmsMessage.setTryCount((byte) 0);
        }else{
            mmsMessage.setTryCount((byte) 2);
        }
        mMMServiceProxy.send(mmsMessage);
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        mMMServiceProxy.disconnect();
    }

    /**
     * 通信进程退出
     */
    public void exit(){
        mMMServiceProxy.exit();
    }

    /**
     * App 前后台状态设置
     * @param isForeground
     */
    void setForeground(boolean isForeground) {
        try {
            mMMServiceProxy.setForeground(isForeground);
        } catch (Throwable e) {
            BLog.e(TAG, "setForeground error %s", e.toString());
        }
    }

    public void flushLog(){
        mMMServiceProxy.flush();
    }

    public boolean isMultiModel() {
        return mMMServiceProxy.isMultiModel();
    }

    public boolean isMMSLiving() {
        return mMMServiceProxy.isMMSLiving();
    }


    public ConnectionInfo dumpConnection(){
        return mMMServiceProxy.dumpConnectionInfo();
    }

    /**
     * mms 策略
     */
    public static final class MMSStrategy {
        private boolean isMultiProcess = true;

        public MMSStrategy(IServerProfile serverProfile){
            setServerProfile(serverProfile);
        }

        public MMSStrategy(IServerProfile serverProfile, BLog.LogImp logImpl){
            setServerProfile(serverProfile);
            setLogImpl(logImpl);
        }

        public MMSStrategy(IServerProfile serverProfile, BLog.LogImp logImpl, ExceptionUtils.PostExecption report){
            setServerProfile(serverProfile);
            setLogImpl(logImpl);
            setReportExecption(report);
        }

        public void setMultiProcess(boolean multiProcess) {
            isMultiProcess = multiProcess;
        }

        public void setLogImpl(BLog.LogImp logImpl){
            BLog.setLogImp(logImpl);
        }

        public void setServerProfile(IServerProfile serverProfile){
            MMSServiceNative.setServerProfile(serverProfile);
        }

        public void setReportExecption(ExceptionUtils.PostExecption report){
            ExceptionUtils.setPostExecption(report);
        }
    }
}
