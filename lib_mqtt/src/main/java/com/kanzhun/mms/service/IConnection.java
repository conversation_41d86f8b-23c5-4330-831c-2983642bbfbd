package com.kanzhun.mms.service;

import com.kanzhun.mms.ConnectionInfo;
import com.kanzhun.mms.IMMServicePushFilter;
import com.kanzhun.mms.MMSMessage;
import com.kanzhun.mms.common.UserInfo;

/**
 * Created by y<PERSON>aofei on 2017/3/6.
 */

public interface IConnection {
    void onCreate();
    void onDestroy();
    void connect(UserInfo info);
    void send(MMSMessage message);
    void cancel(int id);
    void disconnect();
    void setServicePushFilter(IMMServicePushFilter servicePushFilter);
    void onAppStatusChanage(boolean isForeground);
    ConnectionInfo dumpConnection();
}
