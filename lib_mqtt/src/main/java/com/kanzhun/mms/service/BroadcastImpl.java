package com.kanzhun.mms.service;

import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteException;

import com.kanzhun.mms.IMMServicePushFilter;
import com.kanzhun.mms.MMSMessage;
import com.kanzhun.mms.ServerResponse;
import com.kanzhun.mms.common.MMSConstants;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.mms.utils.ExceptionUtils;
import com.kanzhun.mms.utils.TWLException;

import static com.kanzhun.mms.common.MMSConstants.ACTION_CONNECT;
import static com.kanzhun.mms.common.MMSConstants.ACTION_DELIVERED;
import static com.kanzhun.mms.common.MMSConstants.ACTION_LOST;
import static com.kanzhun.mms.common.MMSConstants.ACTION_PUSH;
import static com.kanzhun.mms.common.MMSConstants.EXTRA_DATA;
import static com.kanzhun.mms.common.MMSConstants.EXTRA_RESULT;
import static com.kanzhun.mms.utils.TWLException.MMS_SERVER_2_CLIENT_THROWABLE;

/**
 * Created by yuchaofei on 2017/3/25.
 */

public class BroadcastImpl implements IMMServicePushFilter {
    private static final String TAG = "BroadcastImpl";
    private Context mContext;

    public BroadcastImpl(Context context) {
        mContext = context;
    }

    @Override
    public byte[] getIdentifyData() throws RemoteException {
         return null;
    }

    @Override
    public void onConnected(int status) throws RemoteException {
        Intent intent = new Intent(ACTION_CONNECT);
        intent.putExtra(EXTRA_RESULT, status);
        sendBroadcast(intent);
    }

    @Override
    public void onPush(MMSMessage message) throws RemoteException {
        Intent intent = new Intent(ACTION_PUSH);
        intent.putExtra(MMSConstants.EXTRA_ID, message.getId());
        intent.putExtra(EXTRA_DATA, message.getData());
        sendBroadcast(intent);
    }

    @Override
    public void onDelivered(int id, ServerResponse result) throws RemoteException {
        Intent intent = new Intent(ACTION_DELIVERED);
        intent.putExtra(MMSConstants.EXTRA_ID,id);
        intent.putExtra(EXTRA_RESULT,result);
        sendBroadcast(intent);
    }

    @Override
    public void onConnectionLost(int code) throws RemoteException {
        Intent intent = new Intent(ACTION_LOST);
        intent.putExtra(EXTRA_RESULT, code);
        sendBroadcast(intent);
    }

    @Override
    public IBinder asBinder() {
        return null;
    }

    private void sendBroadcast(Intent intent){
        try {
            mContext.sendBroadcast(intent);
        } catch (Exception e) {
            BLog.printErrStackTrace(TAG, e, "sendBroadcast");
            ExceptionUtils.postCatchedException(new TWLException(MMS_SERVER_2_CLIENT_THROWABLE, e));
        }
    }
}
