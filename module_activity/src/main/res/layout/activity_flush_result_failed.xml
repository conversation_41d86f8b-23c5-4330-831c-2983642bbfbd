<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/common_black"
    tools:ignore="HardcodedText,SpUsage,ContentDescription,UseCompatTextViewDrawableXml,UnusedAttribute">

    <ImageView
        android:id="@+id/iv_photo"
        android:layout_width="50dp"
        android:layout_height="100dp"
        android:layout_marginBottom="16dp"
        android:src="@drawable/activity_ff_kodak"
        app:layout_constraintBottom_toBottomOf="@+id/iv_frame"
        app:layout_constraintEnd_toEndOf="@+id/iv_frame"
        app:layout_constraintStart_toStartOf="@+id/iv_frame"
        app:layout_constraintTop_toTopOf="@+id/iv_frame" />

    <ImageView
        android:id="@+id/iv_frame"
        android:layout_width="375dp"
        android:layout_height="354dp"
        android:layout_gravity="center"
        android:layout_marginLeft="@dimen/common_sp_20"
        android:src="@drawable/activity_ff_ic_photo_frame"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_failed_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center|top"
        android:textColor="#99F7F1E7"
        tools:text="附件看到啦见风使舵李开发较大萨卡拉法基阿达山卡拉受打击了咔哒手机卡啦"
        android:textFontWeight="400"
        android:textSize="@dimen/common_text_sp_14"
        android:minHeight="128dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_frame"
         />

</androidx.constraintlayout.widget.ConstraintLayout>