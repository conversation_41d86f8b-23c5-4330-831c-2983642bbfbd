<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!--自定义Dialog样式-->
    <style name="style_shake_shake_dialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowSoftInputMode">adjustResize|stateHidden</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@color/common_translate</item>
        <item name="android:windowBackground">@color/common_translate</item>
        <item name="android:statusBarColor">@color/common_translate</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.9</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>


</resources>