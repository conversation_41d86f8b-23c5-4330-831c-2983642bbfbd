package com.anbetter.danmuku.control.speed;

public final class RandomSpeedController implements SpeedController {

    private final static int RATE = 1000;

    private static final float MAX_SPEED = 3.5f;

    private static final float MIN_SPEED = 8.5f;

    private float width;

    @Override
    public void setWidthPixels(int width) {
        this.width = width;
    }

    @Override
    public float getSpeed() {
        return (float) (((Math.random() * (MAX_SPEED - MIN_SPEED) + MIN_SPEED)) / RATE) * width;
    }

    public float getMaxSpeed() {
        return MAX_SPEED;
    }

    public float getMinSpeed() {
        return MIN_SPEED;
    }


}
