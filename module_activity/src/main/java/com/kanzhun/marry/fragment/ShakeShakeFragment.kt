package com.kanzhun.marry.fragment

import android.animation.Animator
import android.content.Context
import android.content.Context.VIBRATOR_SERVICE
import android.content.DialogInterface
import android.graphics.BlurMaskFilter
import android.os.Bundle
import android.os.Vibrator
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import com.gyf.immersionbar.ImmersionBar
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.app.AppThreadFactory
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.dialog.BaseDialogFragment
import com.kanzhun.common.kotlin.constant.LivedataKeyMatching
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.invisible
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ext.sendBooleanLiveEvent
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.foundation.SystemConfigInstance
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.bean.ThumbAddBean
import com.kanzhun.foundation.api.callback.SendLikeBean
import com.kanzhun.foundation.api.callback.SendLikeBeanItemData
import com.kanzhun.foundation.facade.SyncDispatch
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.matching.MatchingLikeModel
import com.kanzhun.foundation.model.message.MessageConstants
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.SendLikeHandler
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.views.PreviewLikeView
import com.kanzhun.foundation.views.SendLikeButton.SendButtonStatus
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.activity.R
import com.kanzhun.marry.activity.databinding.ActivityFlushResultFailedBinding
import com.kanzhun.marry.activity.databinding.ActivityFlushResultPicBinding
import com.kanzhun.marry.activity.databinding.ActivityFlushResultTxtBinding
import com.kanzhun.marry.activity.databinding.FragmentShakeShakeBinding
import com.kanzhun.marry.activity.film.KEY_REFRESH_ROOM_INFO
import com.kanzhun.marry.activity.film.api.ActivityApi
import com.kanzhun.marry.bean.FilmExtractBean
import com.kanzhun.marry.bean.FilmGetBean
import com.kanzhun.marry.bean.FilmWashBean
import com.kanzhun.marry.util.OnShakeListenerCallBack
import com.kanzhun.marry.util.ShakeListener
import com.kanzhun.marry.util.Sound
import com.kanzhun.utils.T
import com.techwolf.lib.tlog.TLog


class ShakeShakeFragment(val initFilmProcessId:String? = ""):BaseDialogFragment<FragmentShakeShakeBinding>(),ImpReloadState {
    var mShakeListener:ShakeListener? = null


    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): FragmentShakeShakeBinding {
        val binding = FragmentShakeShakeBinding.inflate(inflater)
        immersionBar{
            titleBarMarginTop(binding.llContentRoot)
        }
        return binding
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog
        if (dialog != null) {
            val window = dialog.window
            window?.let {
                it.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                it.setBackgroundDrawableResource(android.R.color.transparent)
                it.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            }
        }
    }

    override fun registerListener() {
    }

    private fun getFileDetail(mFilmProcessId:String,showShakeBtn:Boolean = true,isInit: Boolean = false) {
        val observable = RetrofitManager.getInstance().createApi(
            ActivityApi::class.java
        ).filmGet(mFilmProcessId)
        HttpExecutor.execute(observable, object : BaseRequestCallback<FilmGetBean>() {
            override fun onSuccess(data: FilmGetBean?) {
                if (isDestroy) return
                binding.idRootLayout.visible()
                if (data != null && data.img?.isNotEmpty() == true) {
                    TLog.print("ShakeShakeFragment","wash FlushedPicScene")
                    val scene = FlushedPicScene(mFilmProcessId,filmWashBean = FilmWashBean(quota = data.quota, securityId = data.securityId,
                        userId = data.userId, content = data.content, nickName = data.nickName, avatar = data.avatar,img = data.img,
                        resourceId = data.resourceId, resourceType = data.resourceType, thumbId = data.thumbId, relationStatus = data.relationStatus, canSendLike = data.canSendLike),
                        this@ShakeShakeFragment)
                    scene.showShakeBtn = showShakeBtn
                    reloadState(scene)
                } else if (data != null && data.content?.isNotEmpty() == true) {
                    TLog.print("ShakeShakeFragment","wash FlushedTxtScene")
                    val scene = FlushedTxtScene(mFilmProcessId,filmWashBean = FilmWashBean(quota = data.quota, securityId = data.securityId,
                        userId = data.userId, content = data.content, nickName = data.nickName, avatar = data.avatar,img = data.img,
                        resourceId = data.resourceId, resourceType = data.resourceType, thumbId = data.thumbId, relationStatus = data.relationStatus, canSendLike = data.canSendLike),this@ShakeShakeFragment)
                    scene.showShakeBtn = showShakeBtn
                    reloadState(scene)
                } else {
                    val scene = FlushedLostFailedScene(this@ShakeShakeFragment)
                    scene.showShakeBtn = showShakeBtn
                    reloadState(scene)
                }
            }

            override fun dealFail(reason: ErrorReason?) {
                if(reason?.errCode == 2501 || reason?.errCode == 2502){
                    binding.idRootLayout.visible()
                    reloadState(FlushedLostFailedScene(this@ShakeShakeFragment))
                }else{
                    if(isInit){
                        dismissAllowingStateLoss()
                        T.ss(reason?.errReason)
                        return
                    }
                    binding.idRootLayout.visible()
                    T.ss(reason?.errReason)
                }
            }

            override fun onComplete() {
                super.onComplete()
                ExecutorFactory.getMainHandler().postDelayed({
                    TLog.print("ShakeShakeFragment","wash shakeStart")
                    shakeStart()
                }, 1000)
            }

        })
    }

    override fun initData() {
        if(initFilmProcessId?.isNotEmpty() == true){
            getFileDetail(initFilmProcessId,false,true)
        }else{
            Sound.initPool(requireContext())
            mShakeListener = ShakeListener(context,object : OnShakeListenerCallBack {
                override fun onShake() {
                    if (canShake) {
                        reportPoint("receive-film-click"){
                            type = if(scene is ReceivedPicScene || scene is FlushedPicScene) "照片" else if (scene is ReceivedTxtScene || scene is FlushedTxtScene) "文字" else ""
                            status = if(scene is ReceivedPicScene || scene is ReceivedTxtScene) "未解锁" else if (scene is FlushedPicScene || scene is FlushedTxtScene) "已解锁" else ""
                            peer_id =  if (scene is FlushedPicScene) (scene as FlushedPicScene).filmWashBean.userId  else if (scene is FlushedTxtScene) (scene as FlushedTxtScene).filmWashBean.userId else ""
                            actionp3 = "摇晃抽一张"
                        }
                        reloadState(ShakeScene(this@ShakeShakeFragment))
                    }

                }
            })
            shakeStop()
            getFilmFromNet(false,true)
        }
        liveEventBusObserve<com.kanzhun.foundation.model.matching.MatchingLikeModel?>(key = LivedataKeyMatching.MATCH_SEND_LIKE_SUCCESS) {
            if (it == null) {
                return@liveEventBusObserve
            }

            TLog.print("MATCH_SEND_LIKE_SUCCESS",it.toString())
            AppThreadFactory.getMainHandler().post {
                if(scene is FlushedTxtScene){
                    (scene as FlushedTxtScene).refreshLove(it)
                }else if(scene is FlushedPicScene){
                    (scene as FlushedPicScene).refreshLove(it)
                }
            }

        }
    }

    var scene: Scene? = null
    override fun reloadState(scene: Scene) {
        if (isDestroy) return
        this.scene = scene
        binding.run {
            if (scene.showShake()){
                idShakeLayout.visible()
                shakeStart()
//                idResultLayout.gone()
                shakeAnim()
            }else{
                idResultLayout.visible()
                idShakeLayout.gone()
                tvFlushTitle.text = scene.title()

                tvFlushDesc.apply {
                    text = scene.desc()
                    visibility = if (scene.desc().isEmpty()) View.INVISIBLE else View.VISIBLE
                }

                flFlushContent.apply {
                    removeAllViews()

                    // 显示内容
                    val view = scene.content(flFlushContent)
                    if (view != null) {
                        addView(
                            view,
                            FrameLayout.LayoutParams(
                                FrameLayout.LayoutParams.WRAP_CONTENT,
                                FrameLayout.LayoutParams.WRAP_CONTENT
                            ).apply { gravity = Gravity.CENTER }
                        )
                    }
                }

                llFlush.onClick {
                    scene.buttonAction()
                }
                tvFlushButtonText.text = scene.buttonText()
                if(scene.hashFlushButtonIcon()){
                    tvFlushButtonText.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.activity_ff_2, 0)
                }else{
                    tvFlushButtonText.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
                }
                idShakeBtn.invisible(scene.shakeBtnVisible())
                idShakeBtn.onClick { scene.shakeBtnAction() }
            }

        }
    }

    override fun getBaseContext(): Context {
        return requireContext()
    }

    override fun initView(binding: FragmentShakeShakeBinding) {
        binding.ivClose.onClick {
            reportPoint("receive-film-click"){
                type = if(scene is ReceivedPicScene || scene is FlushedPicScene) "照片" else if (scene is ReceivedTxtScene || scene is FlushedTxtScene) "文字" else ""
                status = if(scene is ReceivedPicScene || scene is ReceivedTxtScene) "未解锁" else if (scene is FlushedPicScene || scene is FlushedTxtScene) "已解锁" else ""
                peer_id =  if (scene is FlushedPicScene) (scene as FlushedPicScene).filmWashBean.userId  else if (scene is FlushedTxtScene) (scene as FlushedTxtScene).filmWashBean.userId else ""
                actionp3 = "关闭页面"
            }
            dismissAllowingStateLoss()
        }
    }

    private fun shakeAnim() {
        if (canShake) {
            startShakeAnim() //开始执行动画
            getFilmFromNet()
        }
        shakeStop() //取消摇动监听,动画结束之前不再监听
    }

    //胶片抽取
    private fun getFilmFromNet(noQuotaDismiss:Boolean = true,isInit:Boolean = false) {
        val startTime = System.currentTimeMillis()
        val observable = RetrofitManager.getInstance().createApi(
            ActivityApi::class.java
        ).postFilmExtract()
        HttpExecutor.execute(observable, object : BaseRequestCallback<FilmExtractBean>() {
            override fun onSuccess(data: FilmExtractBean?) {
                if (isDestroy) return
                val ca = 1500-(System.currentTimeMillis() - startTime)
                if(ca < 100){
                    startShakeResult(data)
                }else{
                    ExecutorFactory.getMainHandler().postDelayed({
                        startShakeResult(data)
                    }, ca)
                }
            }

            override fun dealFail(reason: ErrorReason?) {
                if (isDestroy) return
                if(isInit){
                    dismissAllowingStateLoss()
                    T.ss(reason?.errReason)
                }else{
                    binding.idRootLayout.visible()
                    if(reason?.errCode == 1901 && noQuotaDismiss){
                        T.ss(reason?.errReason)
                        dismissAllowingStateLoss()
                    }else if(reason?.errCode == 2501 || reason?.errCode == 2502 ){
                        reloadState(FlushedLostFailedScene(this@ShakeShakeFragment))
                    }else {
                        reloadState(FlushedLostFailedScene(this@ShakeShakeFragment))
                        T.ss(reason?.errReason)
                    }
                    ExecutorFactory.getMainHandler().postDelayed({
                        shakeStart()
                    }, 1000)
                }

            }

        })
    }

    private fun startShakeResult(data: FilmExtractBean?) {
        if (isDestroy) return
        binding.idRootLayout.visible()
        if (data != null && data.img?.isNotEmpty() == true) {
            TLog.print("ShakeShakeFragment", "Extract ReceivedPicScene")
            reloadState(ReceivedPicScene(data, this@ShakeShakeFragment))
        } else if (data != null && data.content?.isNotEmpty() == true) {
            TLog.print("ShakeShakeFragment", "Extract ReceivedTxtScene")
            reloadState(ReceivedTxtScene(data, this@ShakeShakeFragment))
        } else {
            reloadState(FlushedLostFailedScene(this@ShakeShakeFragment))
        }
        ExecutorFactory.getMainHandler().postDelayed({
            TLog.print("ShakeShakeFragment", "Extract shakeStart")
            shakeStart()
        }, 1000)
    }

    override fun onResume() {
        super.onResume()
        shakeStart()
    }

    private fun shakeStart() {
        if (isDestroy) return
        canShake = true
        mShakeListener?.start()
    }

    override fun onPause() {
        shakeStop()
        super.onPause()
    }

    var isDestroy = false

    override fun onDestroy() {
        isDestroy = true
        shakeStop()
        ImmersionBar.destroy(this)
        super.onDestroy()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        shakeStop()
    }

    var canShake = false
    private fun shakeStop() {
        canShake = false
        mShakeListener?.stop()
    }

    /**震动 */
    private fun vibrate(milliseconds: Long) {
        val vibrator = activity?.getSystemService(VIBRATOR_SERVICE) as Vibrator?
        vibrator?.vibrate(milliseconds)
    }

    private fun startShakeAnim() {
        vibrate(500)
//        Sound.playStartSound()

        if(shakeListener == null){
            shakeListener = object :Animator.AnimatorListener{
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {

                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }

            }

            binding.idShakeIcon.addAnimatorListener(shakeListener)
        }

        binding.idShakeIcon.playAnimation()
    }

    var shakeListener:Animator.AnimatorListener? = null
}

interface ImpReloadState{
    fun reloadState(scene: Scene)
    fun getBaseContext():Context
}
data class StateModel(val content: String = "")

interface Scene {
    fun showShake():Boolean = false
    fun data(): StateModel = StateModel()

    fun title(): String = ""
    fun desc(): String = ""
    fun content(parent: ViewGroup): View? = null
    fun buttonText(): String = ""
    fun buttonAction(): () -> Unit = {}
    fun shakeBtnAction(): () -> Unit = {}
    fun shakeBtnVisible(): Boolean
    fun hashFlushButtonIcon():Boolean
}

abstract class BaseScene(impReloadState:ImpReloadState,var showShakeBtn:Boolean = true) : Scene{

    override fun shakeBtnVisible(): Boolean {
        return showShakeBtn
    }

    override fun hashFlushButtonIcon(): Boolean {
        return false
    }
}

abstract class ReceivedScene(private val filmExtractBean:FilmExtractBean,private val  impReloadState:ImpReloadState) : BaseScene(impReloadState) {
    override fun title(): String = "收到一张新胶片"

    abstract fun flushSuccess(filmProcessId:String,data: FilmWashBean?)

    override fun shakeBtnAction(): () -> Unit {
        impReloadState.reloadState(ShakeScene(impReloadState))
        return super.shakeBtnAction()
    }

    override fun buttonAction(): () -> Unit {
        val observable = RetrofitManager.getInstance().createApi(
            ActivityApi::class.java
        ).filmWash(filmExtractBean.filmProcessId?:"")
        HttpExecutor.execute(observable, object : BaseRequestCallback<FilmWashBean>() {
            override fun onSuccess(data: FilmWashBean?) {
                flushSuccess(filmExtractBean.filmProcessId?:"",data)

                sendBooleanLiveEvent(KEY_REFRESH_ROOM_INFO, true)
            }

            override fun dealFail(reason: ErrorReason?) {
                if(reason?.errCode == 2501 || reason?.errCode == 2502){
                    impReloadState.reloadState(FlushedLostFailedScene(impReloadState))
                }else{
                    T.ss(reason?.errReason)
                }

            }

            override fun onComplete() {
                super.onComplete()

            }

        })
        return super.buttonAction()
    }

    override fun hashFlushButtonIcon(): Boolean {
        return true
    }

}

abstract class FlushedScene(private val filmWashBean:FilmWashBean,private val impReloadState:ImpReloadState) : BaseScene(impReloadState) {
    override fun title(): String = "你的胶片冲洗完成"

    abstract fun refreshLove(oUid:MatchingLikeModel)

    override fun shakeBtnAction(): () -> Unit {
        impReloadState.reloadState(ShakeScene(impReloadState))
        return super.shakeBtnAction()
    }

    override fun buttonAction(): () -> Unit {
        MePageRouter.jumpToInfoPreviewActivity(context = impReloadState.getBaseContext(),userId = filmWashBean.userId, securityId = filmWashBean.securityId?:"", pageSource = PageSource.FILM_ACTIVITY)
        return super.buttonAction()
    }

    fun likeClick(uid:String,resourceType:String,resourceId:String,filmProcessId:String,onSuccessed:()->Unit){
        if(AccountHelper.getInstance().userId == uid){
            return
        }
        if("500".equals(resourceType)){
            val params: MutableMap<String, Any> = HashMap()
            params["filmProcessId"] = filmProcessId
            params["momentId"] = resourceId
            params["source"] = 0
            HttpExecutor.requestSimplePost(
                URLConfig.URL_MOMENT_THUMB_ADD,
                params,
                object : SimpleRequestCallback() {
                    override fun onSuccess() {
                        onSuccessed()
                    }

                    override fun dealFail(reason: ErrorReason) {

                    }
                })
        }else{
            //去点赞
            val baseResponseObservable = RetrofitManager.getInstance().createApi(
                FoundationApi::class.java
            ).thumbAdd(uid,resourceType, resourceId,filmProcessId)
            HttpExecutor.execute(baseResponseObservable, object : BaseRequestCallback<ThumbAddBean>(true) {
                override fun onSuccess(data: ThumbAddBean) {
                    onSuccessed()
                }

                override fun dealFail(reason: ErrorReason) {
                }
            })
        }
    }

    fun loveClick(context: Context,uid: String,resourceType:String,resourceId:String,isSendModel:SendButtonStatus,sendLikeBean: SendLikeBean){
        if (sendLikeBean.canSend == 0 && sendLikeBean.relationStatus == 11) {
            T.ss("对方回复前只能发送1条消息哦")
            return
        }
        if(isSendModel != SendButtonStatus.NORMAL && !AccountHelper.getInstance().isFormalUser){
            gotoSingleChatActivity(context,uid)
            return
        }
        if(isSendModel == SendButtonStatus.FRIEND){
            ChatPageRouter.jumpToSingleChatActivity(context, uid,PageSource.NONE)
            return
        }

        SendLikeHandler().sendLike(context = context,PageSource.NONE,sendLikeBean.userName,resourceType,uid = uid){
            MatchingPageRouter.jumpSendLikeChatActivity(
                context,uid, PreviewLikeView.LIKE_TYPE.fromResourceType(resourceType),PageSource.ME_USER_INFO_PREVIEW_ACTIVITY,it.chatWordList,
                resourceId,sendLikeBean)
        }
    }

    fun gotoSingleChatActivity(context: Context,userId: String?) {
        ExecutorFactory.execLocalTask {
            val conversation =
                ServiceManager.getInstance().conversationService.getConversation(
                    userId,
                    MessageConstants.MSG_SINGLE_CHAT
                )
            if (conversation != null) {
                ExecutorFactory.execMainTaskDelay({
                    ChatPageRouter.jumpToSingleChatActivity(
                        context,
                        userId,
                        conversation.systemId
                    )
                },300)
            }
        }
    }

}

abstract class FlushedFailedScene (val impReloadState:ImpReloadState) : BaseScene(impReloadState, showShakeBtn = false) {
    override fun title(): String = "很抱歉"

    override fun shakeBtnAction(): () -> Unit {
        impReloadState.reloadState(ShakeScene(impReloadState))
        return super.shakeBtnAction()
    }


}

abstract class AbsShakeScene (impReloadState:ImpReloadState) : BaseScene(impReloadState,showShakeBtn = false) {
    override fun title(): String = "很抱歉"
}

class ShakeScene(impReloadState:ImpReloadState) : AbsShakeScene(impReloadState) {
    override fun showShake(): Boolean {
        return true
    }
}

// 图片打码
class ReceivedPicScene(val filmExtractBean:FilmExtractBean,val impReloadState:ImpReloadState) : ReceivedScene(filmExtractBean,impReloadState) {
    override fun content(parent: ViewGroup): View {
        val binding = ActivityFlushResultPicBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )

        reportPoint("receive-film-expo"){
            type = "照片"
            status = "未解锁"
            actionp2 = filmExtractBean.quota.toString()
        }
        binding.run {
            tvFlushUnlock.visibility = View.VISIBLE
            if(filmExtractBean.imgStyle == 2){
                ivPhoto.loadBlur(filmExtractBean.img)
            }else{
                ivPhoto.load(filmExtractBean.img)
            }

            idName.text = filmExtractBean.nickName
            idText.gone()
            if(filmExtractBean.content.isNullOrEmpty()){
                idTextSpan.gone()
            }else{
                idTextSpan.visible()
                idTextSpan.setContent(filmExtractBean.content)
            }
            idAvatar.loadBlur(filmExtractBean.avatar)
        }

        return binding.root
    }

    override fun buttonText(): String = "冲洗这张（消耗1次 余 ${filmExtractBean.quota} 次）"

    override fun shakeBtnAction(): () -> Unit {
        reportPoint("receive-film-click"){
            type = "照片"
            status = "未解锁"
            actionp3 = "重新抽一张"
        }
        return super.shakeBtnAction()
    }

    override fun buttonAction(): () -> Unit {
        reportPoint("receive-film-click"){
            type = "照片"
            status = "未解锁"
            actionp3 = "冲洗这张"
        }
        return super.buttonAction()
    }

    override fun flushSuccess(filmProcessId:String,data: FilmWashBean?) {
        if(data != null){
            impReloadState.reloadState(FlushedPicScene(filmProcessId,data,impReloadState))
        }else{
            impReloadState.reloadState(FlushedLostFailedScene(impReloadState))
        }
    }
}

// 图片展示
class FlushedPicScene (val filmProcessId:String,val filmWashBean:FilmWashBean,impReloadState: ImpReloadState) : FlushedScene(filmWashBean,impReloadState) {
    var binding:ActivityFlushResultPicBinding? = null
    override fun desc(): String = "冲洗后的照片会通过站内信发送给你"

    override fun content(parent: ViewGroup): View {
        binding = ActivityFlushResultPicBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )

        binding!!.run {
            idLikeBtn.visible()
            idLoveBtn.visible()
            var isSendModel = SendButtonStatus.NORMAL
            if (filmWashBean.canSendLike) {
                isSendModel = SendButtonStatus.NORMAL
            } else {
                when (filmWashBean.relationStatus) {
                    11 -> { //已发送
                        isSendModel = SendButtonStatus.LIKE
                    }
                    20, 30, 40 -> { //好友
                        isSendModel = SendButtonStatus.FRIEND
                    }
                    else -> { //隐藏
                        isSendModel = SendButtonStatus.NORMAL
                    }
                }
            }
            var isLike = !TextUtils.isEmpty(filmWashBean.thumbId)
            idLoveBtn.onClick {
                val sendLikeBean = SendLikeBean()
                sendLikeBean.userName = filmWashBean.nickName ?: ""
                sendLikeBean.relationStatus = filmWashBean.relationStatus
                sendLikeBean.canSend = if (filmWashBean.canSendLike) 1 else 0
                sendLikeBean.securityId = filmWashBean.securityId
                sendLikeBean.itemDta = SendLikeBeanItemData(
                    url = filmWashBean.img,
                    content = filmWashBean.content,
                )
                loveClick(
                    context = binding!!.root.context,
                    filmWashBean.userId ?: "",
                    filmWashBean.resourceType.toString(),
                    filmWashBean.resourceId ?: "",
                    isSendModel,
                    sendLikeBean
                )
                reportPoint("receive-film-click"){
                    type = "照片"
                    status = "已解锁"
                    peer_id = filmWashBean.userId
                    actionp3 = "发送喜欢"
                }
            }
            if (isSendModel == SendButtonStatus.NORMAL) {
                idLoveBtn.setImageResource(R.drawable.image_film_shake_result_love)
            } else {
                idLoveBtn.setImageResource(R.drawable.image_film_shake_result_loved)
            }
            if (isLike) {
                idLikeBtn.setImageResource(R.drawable.image_film_shake_result_liked)
            } else {
                idLikeBtn.setImageResource(R.drawable.image_film_shake_result_like)
            }
            if (filmWashBean.resourceType == 30 || filmWashBean.resourceType == 50) {
                idLoveBtn.visible()
                if(filmWashBean.userId.equals(AccountHelper.getInstance().getUserId()) || SystemConfigInstance.likeResourceClose == 1){
                    idLoveBtn.gone()
                    TLog.print("shakeshakefragment","命中自己或关闭实验")
                }else{
                    if(filmWashBean.canSendLike){
                        idLoveBtn.visible()
                    }else{
                        when(filmWashBean.relationStatus){
                            11 -> { //已发送
                                idLoveBtn.visible()
                            }

                            20, 30, 40 -> { //好友
                                idLoveBtn.visible()
                            }

                            else -> { //隐藏
                                TLog.print("shakeshakefragment","隐藏:${filmWashBean.relationStatus}")
                                idLoveBtn.gone()
                            }
                        }
                    }
                }
            } else {
                TLog.print("shakeshakefragment","命中不支持资源类型")
                idLoveBtn.gone()
            }
            idLikeBtn.onClick {
                if(isLike){
                    return@onClick
                }
                likeClick(
                    filmWashBean.userId ?: "",
                    filmWashBean.resourceType.toString(),
                    filmWashBean.resourceId ?: "",
                    filmProcessId
                ) {
                    isLike = true
                    idLikeBtn.setImageResource(R.drawable.image_film_shake_result_liked)
                    idLikeBtn.onClick {  }
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_NOTICE_LIST_SYNC)
                }
                reportPoint("receive-film-click"){
                    type = "照片"
                    status = "已解锁"
                    peer_id = filmWashBean.userId
                    actionp3 = "点赞胶片"
                }
            }


            tvFlushUnlock.gone()
            ivPhoto.load(filmWashBean.img)
            idName.text = filmWashBean.nickName
            idTextSpan.gone()
            if (filmWashBean.content.isNullOrEmpty()) {
                idText.gone()
            } else {
                idText.visible()
                idText.text = filmWashBean.content
            }
            idAvatar.load(filmWashBean.avatar)
        }

        reportPoint("receive-film-expo"){
            type = "照片"
            status = "已解锁"
            peer_id = filmWashBean.userId
            actionp2 = filmWashBean.quota.toString()
        }


        return binding!!.root
    }

    override fun refreshLove(mMatchingLikeModel: MatchingLikeModel) {
        if(filmWashBean.userId.equals(mMatchingLikeModel.originToUserId)){
            binding?.idLoveBtn?.setImageResource(R.drawable.image_film_shake_result_loved)
            binding?.idLoveBtn?.onClick {
                if (mMatchingLikeModel.likeEachOther?.otherInfo != null && mMatchingLikeModel.likeEachOther?.myInfo != null) {
                    ChatPageRouter.jumpToSingleChatActivity( it.context, mMatchingLikeModel.likeEachOther?.otherInfo?.userId,PageSource.NONE)
                }else{
                    T.ss("对方回复前只能发送1条消息哦")
                }

            }
        }

    }

    override fun shakeBtnAction(): () -> Unit {
        reportPoint("receive-film-click"){
            type = "照片"
            status = "已解锁"
            peer_id = filmWashBean.userId
            actionp3 = "重新抽一张"
        }
        return super.shakeBtnAction()
    }

    override fun buttonAction(): () -> Unit {
        reportPoint("receive-film-click"){
            type = "照片"
            status = "已解锁"
            peer_id = filmWashBean.userId
            actionp3 = "去看看Ta"
        }
        return super.buttonAction()
    }

    override fun buttonText(): String = "去看看Ta"
}

// 文字打码
class ReceivedTxtScene(val filmExtractBean:FilmExtractBean,val impReloadState: ImpReloadState) : ReceivedScene(filmExtractBean,impReloadState) {
    override fun desc(): String = ""

    override fun content(parent: ViewGroup): View {
        val binding = ActivityFlushResultTxtBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )

        reportPoint("receive-film-expo"){
            type = "文字"
            status = "未解锁"
            actionp2 = filmExtractBean.quota.toString()
        }
        binding.run {
            tvFlushUnlock.visibility = View.VISIBLE
            idAvatar.loadBlur(filmExtractBean.avatar)
            idName.text = filmExtractBean.nickName
            TLog.print("ShakeShakeFragment","idText blur")
            idText.paint.setMaskFilter(BlurMaskFilter(20.0f,BlurMaskFilter.Blur.NORMAL))
            idText.setText(filmExtractBean.content)
            tvFlushUnlock.visible()
        }

        return binding.root
    }

    override fun shakeBtnAction(): () -> Unit {
        reportPoint("receive-film-click"){
            type = "文字"
            status = "未解锁"
            actionp3 = "重新抽一张"
        }
        return super.shakeBtnAction()
    }

    override fun buttonAction(): () -> Unit {
        reportPoint("receive-film-click"){
            type = "文字"
            status = "未解锁"
            actionp3 = "冲洗这张"
        }
        return super.buttonAction()
    }

    override fun buttonText(): String = "冲洗这张（消耗1次 余${filmExtractBean.quota}次）"
    override fun flushSuccess(filmProcessId:String,data: FilmWashBean?) {
        if(data != null){
            impReloadState.reloadState(FlushedTxtScene(filmProcessId,data,impReloadState))
        }else{
            impReloadState.reloadState(FlushedLostFailedScene(impReloadState))
        }
    }
}

// 文字展示
class FlushedTxtScene(val filmProcessId:String,val filmWashBean:FilmWashBean,impReloadState: ImpReloadState) : FlushedScene(filmWashBean,impReloadState)  {

    val binding:ActivityFlushResultTxtBinding? = null
    override fun content(parent: ViewGroup): View {
        val binding = ActivityFlushResultTxtBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )

        binding!!.apply {
            binding.idLikeBtn.visible()
            binding.idLoveBtn.visible()
            var isSendModel = SendButtonStatus.NORMAL
            if (filmWashBean.canSendLike) {
                isSendModel = SendButtonStatus.NORMAL
            } else {
                when (filmWashBean.relationStatus) {
                    11 -> { //已发送
                        isSendModel = SendButtonStatus.LIKE
                    }
                    20, 30, 40 -> { //好友
                        isSendModel = SendButtonStatus.FRIEND
                    }
                    else -> { //隐藏
                        isSendModel = SendButtonStatus.NORMAL
                    }
                }
            }
            var isLike = !TextUtils.isEmpty(filmWashBean.thumbId)
            binding.idLoveBtn.onClick {
                val sendLikeBean = SendLikeBean()
                sendLikeBean.userName = filmWashBean.nickName?:""
                sendLikeBean.relationStatus = filmWashBean.relationStatus
                sendLikeBean.canSend = if(filmWashBean.canSendLike) 1 else 0
                sendLikeBean.securityId = filmWashBean.securityId
                sendLikeBean.itemDta = SendLikeBeanItemData(
                    url = filmWashBean.img,
                    content = filmWashBean.content,
                )
                loveClick(context = binding.root.context,filmWashBean.userId?:"",filmWashBean.resourceType.toString(),
                    filmWashBean.resourceId?:"",isSendModel,sendLikeBean)
                reportPoint("receive-film-click"){
                    type = "文字"
                    status = "已解锁"
                    peer_id = filmWashBean.userId
                    actionp3 = "发送喜欢"
                }
            }
            if(isSendModel == SendButtonStatus.NORMAL){
                binding.idLoveBtn.setImageResource(R.drawable.image_film_shake_result_loved)
            }else{
                binding.idLoveBtn.setImageResource(R.drawable.image_film_shake_result_love)
            }
            if(isLike){
                binding.idLikeBtn.setImageResource(R.drawable.image_film_shake_result_liked)
            }else{
                binding.idLikeBtn.setImageResource(R.drawable.image_film_shake_result_like)
            }
            if(filmWashBean.resourceType == 30 || filmWashBean.resourceType == 50){
                binding.idLoveBtn.visible()
                if(filmWashBean.userId.equals(AccountHelper.getInstance().getUserId()) || SystemConfigInstance.likeResourceClose == 1){
                    idLoveBtn.gone()
                    TLog.print("shakeshakefragment","命中自己或关闭实验")
                }else{
                    if(filmWashBean.canSendLike){
                        idLoveBtn.visible()
                    }else{
                        when(filmWashBean.relationStatus){
                            11 -> { //已发送
                                idLoveBtn.visible()
                            }

                            20, 30, 40 -> { //好友
                                idLoveBtn.visible()
                            }

                            else -> { //隐藏
                                idLoveBtn.gone()
                                TLog.print("shakeshakefragment","隐藏:${filmWashBean.relationStatus}")
                            }
                        }
                    }
                }
            }else{
                binding.idLoveBtn.gone()
                TLog.print("shakeshakefragment","资源不支持")
            }
            binding.idLikeBtn.onClick {
                if(isLike){
                    return@onClick
                }else{
                    likeClick(filmWashBean.userId?:"",filmWashBean.resourceType.toString(),
                        filmWashBean.resourceId?:"",filmProcessId){
                        isLike = true
                        binding.idLikeBtn.setImageResource(R.drawable.image_film_shake_result_liked)
                        binding.idLikeBtn.onClick {  }
                        SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_NOTICE_LIST_SYNC)
                    }
                    reportPoint("receive-film-click"){
                        type = "文字"
                        status = "已解锁"
                        peer_id = filmWashBean.userId
                        actionp3 = "点赞胶片"
                    }
                }
            }
            tvFlushUnlock.gone()
            idAvatar.load(filmWashBean.avatar)
            idName.text = filmWashBean.nickName
            idText.setText(filmWashBean.content)
            tvFlushUnlock.gone()
        }


        reportPoint("receive-film-expo"){
            type = "文字"
            status = "已解锁"
            peer_id = filmWashBean.userId
            actionp2 = filmWashBean.quota.toString()
        }

        return binding.root
    }

    override fun buttonText(): String = "去看看Ta"
    override fun refreshLove(mMatchingLikeModel: MatchingLikeModel) {
        if(filmWashBean.userId.equals(mMatchingLikeModel.originToUserId)){
            binding?.idLoveBtn?.setImageResource(R.drawable.image_film_shake_result_loved)
            binding?.idLoveBtn?.onClick {
                if (mMatchingLikeModel.likeEachOther?.otherInfo != null && mMatchingLikeModel.likeEachOther?.myInfo != null) {
                    ChatPageRouter.jumpToSingleChatActivity( it.context, mMatchingLikeModel.likeEachOther?.otherInfo?.userId,PageSource.NONE)
                }else{
                    T.ss("对方回复前只能发送1条消息哦")
                }

            }
        }
    }

    override fun shakeBtnAction(): () -> Unit {
        reportPoint("receive-film-click"){
            type = "文字"
            status = "已解锁"
            peer_id = filmWashBean.userId
            actionp3 = "重新抽一张"
        }
        return super.shakeBtnAction()
    }

    override fun buttonAction(): () -> Unit {
        reportPoint("receive-film-click"){
            type = "文字"
            status = "已解锁"
            peer_id = filmWashBean.userId
            actionp3 = "去看看Ta"
        }
        return super.buttonAction()
    }

}

// 胶片在向你飞奔而来的路上走失了
class FlushedLostFailedScene(impReloadState: ImpReloadState) : FlushedFailedScene(impReloadState) {
    override fun desc(): String = "胶片在向你飞奔而来的路上走失了"

    override fun content(parent: ViewGroup): View {
        val binding = ActivityFlushResultFailedBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )

        reportPoint("receive-film-expo"){
            msg = "胶片在向你飞奔而来的路上走失了"
        }
        return binding.root
    }

    override fun buttonText(): String = "重新抽一张"

    override fun buttonAction(): () -> Unit {
        reportPoint("receive-film-click"){
            actionp3 = "重新抽一张"
        }
        return super.shakeBtnAction()
    }
}

