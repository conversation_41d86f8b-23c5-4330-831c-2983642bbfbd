package com.kanzhun.marry.activity.film.api.bean

import java.io.Serializable

/**
 * https://api.weizhipin.com/project/1975/interface/api/719104
 *
 * {
 *         "openTime": "19:00:00", // 活动每日开放时间
 *         "closeTime": "24:00:00", // 活动每日关闭时间
 *         "quotaTips": "x个冲洗机会将于今日过期",
 *         "openTimeCountDown":2233, // 活动剩余开始时间 已开始则为 0
 *         "closeTimeCountDown":0, // 活动结束时间 未开始则为 0
 *         "tutorialUrl":"http:url.com", // 活动教程地址，为空不展示
 *         "recommendUser": [{
 *             "securityId": "xxxxx",
 *             "userId":"xxxx",
 *             "content": "刚刚有人偷偷看了你的一张胶片，点击看看他是谁"
 *         }],
 *         "userQuota": {
 *             "quota": 10, //用户总权益
 *             "todayQuota": 1, //用户今天获取的权益
 *             "userTaskList": [ //任务列表
 *                  {
 *                     "type": 3, //1:点赞评论 3:动态 4:分享 5 个人资料
 *                     "goalQuota": 5, //任务每天总权益
 *                     "quota": 1, // 任务单次获取权益数
 *                     "completeQuota": 1, //当天完成的权益
 *                     "icon": "url", // 图标
 *                     "title": "发布生活碎片动态",
 *                     "subtitle": "今日",
 *                     "btnText": "去参与",
 *                     "jumpProto": "og://" // 跳转协议
 *                 },
 *                 {
 *                     "type": 3, //1:点赞评论 3:动态 4:分享
 *                     "goalQuota": 5, //任务每天总权益
 *                     "completeQuota": 1 //当天完成的权益
 *                 },
 *                 {
 *                     "type": 1, //1:点赞评论 3:动态 4:分享
 *                     "goalQuota": 4, //任务每天总权益
 *                     "completeQuota": 0 //当天完成的权益
 *                 },
 *                 {
 *                     "type": 4, //1:点赞评论 3:动态 4:分享
 *                     "goalQuota": 1, //任务每天总权益
 *                     "completeQuota": 0 //当天完成的权益
 *                 }
 *             ]
 *         },
 *         "recommendFilmList": [{ // 胶片推荐列表
 *             "content": "今天出去玩了", // 胶片文案
 *             "img": "url", // 胶片图片
 *             "imgStyle": 1, //客户端图片处理动作 1原样展示 2模糊处理
 *             "nickName": "用户昵称",
 *             "avatar": "url", //头像地址
 *             "filmProcessId": "xxx", // 胶片冲洗id
 *             "securityId": "xxxx", // 用户 securityId
 *             "userId": "xxx", // 用户id
 *         }]
 *     }
 */
data class FilmRoomInfoResponse(
    val openTime: String? = null,
    val openTimeCountDown: Long? = -1, // 秒
    val closeTime: String? = null,
    val quotaTips: String? = null,
    val closeTimeCountDown: Long? = -1, // 秒
    val tutorialUrl: String? = null,
    val recommendUser: List<RecommendUser>? = null,
    val userQuota: UserQuota? = null,
    val recommendFilmList: List<RecommendFilm>? = null,
) : Serializable

data class RecommendUser(
    val securityId: String? = null,
    val userId: String? = null,
    val content: String? = null
) : Serializable

data class UserQuota(
    val quota: Int = -1,
    val todayQuota: Int = -1,
    val userTaskList: List<UserTask>? = null
) : Serializable

data class UserTask(
    val type: Int? = 0,
    val goalQuota: Int? = 0,
    val quota: Int? = 0,
    val completeQuota: Int? = 0,
    val icon: String? = null,
    val title: String? = null,
    val subtitle: String? = null,
    val btnText: String? = null,
    val jumpProto: String? = null
) : Serializable

/**
 *             "content": "今天出去玩了", // 胶片文案
 *             "img": "url", // 胶片图片
 *             "imgStyle":1 , //客户端图片处理动作 1原样展示 2模糊处理
 *             "nickName": "用户昵称",
 *             "avatar": "url", //头像地址
 *             "filmProcessId": "xxx", // 胶片冲洗id
 *             "securityId": "xxxx", // 用户 securityId
 *             "userId": "xxx", // 用户id
 */
data class RecommendFilm(
    var content: String? = null,
    var img: String? = null,
    var imgStyle: Int? = 1,
    var nickName: String? = null,
    var avatar: String? = null,
    var filmProcessId: String? = null,
    var securityId: String? = null,
    var userId: String? = null
) : Serializable {
    fun cantSeeTa(): Boolean {
        return userId.isNullOrEmpty() || securityId.isNullOrEmpty()
    }
}

enum class UserTaskType(val type: Int) {
    LikeComment(1),
    EditInfo(5),
    Dynamic(3),
    Share(4)
}