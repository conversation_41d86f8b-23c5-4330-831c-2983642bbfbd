package com.kanzhun.marry.activity.film

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.core.content.ContextCompat
import androidx.core.text.buildSpannedString
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.anbetter.danmuku.DanMuView
import com.anbetter.danmuku.model.DanMuModel
import com.anbetter.danmuku.model.utils.DimensionUtil
import com.chad.library.adapter.base.BaseBinderAdapter
import com.kanzhun.common.adpter.BaseDataBindingItemBinder
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.onSetViewTreeContent
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.statusbar.addStatusMargin
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.foundation.facade.TempTaskType
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.ModuleActivityRouter
import com.kanzhun.foundation.router.SocialPageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.activity.R
import com.kanzhun.marry.activity.databinding.ActivityFilmFlushMoreItemBinding
import com.kanzhun.marry.activity.databinding.FragmentActivityFilmFlushBinding
import com.kanzhun.marry.activity.film.api.bean.RecommendUser
import com.kanzhun.marry.activity.film.api.bean.UserTask
import com.kanzhun.marry.activity.film.api.bean.UserTaskType
import com.kanzhun.marry.fragment.ShakeShakeFragment
import com.kanzhun.marry.lib_share.ShareAction
import com.kanzhun.marry.lib_share.core.IShare
import com.kanzhun.marry.lib_share.core.SharePlatForm
import com.kanzhun.utils.T
import com.kanzhun.utils.base.LText
import com.kanzhun.utils.platform.Utils
import com.kanzhun.utils.rxbus.RxBus
import com.kanzhun.utils.string.appendClickable
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

private const val LOG_TAG = "FilmFlushFragment"

class FilmFlushFragment :
    BaseBindingFragment<FragmentActivityFilmFlushBinding, FilmFlushRoomViewModel>(),
    UserTaskItemBinder.Callback {

    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.llContentRoot.apply {
            addStatusMargin()
            val lp = layoutParams as ViewGroup.MarginLayoutParams
            lp.topMargin += DimensionUtil.dpToPx(context, 52f)
            layoutParams = lp
        }

        mBinding.composeView.onSetViewTreeContent {
            val roomInfo by mViewModel.roomInfo.observeAsState()
            roomInfo?.recommendFilmList?.let { recommendFilmList ->
                if (recommendFilmList.isNotEmpty()) {
                    RecommendFilmListContent(recommendFilmList) { film, canSeeTa ->
                        if (canSeeTa) {
                            activity?.let {
                                MePageRouter.jumpToInfoPreviewActivity(
                                    context = it,
                                    userId = film.userId ?: "",
                                    securityId = film.securityId ?: "",
                                    pageSource = PageSource.NONE
                                )
                            }

                            // type参数增加：最受欢迎的照片冲洗、最受欢迎的照片看看Ta
                            reportClick(
                                typeStr = "最受欢迎的照片看看Ta",
                                p2 = film.img,
                                p3 = film.userId
                            )
                        } else {
                            film.filmProcessId?.let { mViewModel.flushDirectly(it) }

                            // type参数增加：最受欢迎的照片冲洗、最受欢迎的照片看看Ta
                            reportClick(
                                typeStr = "最受欢迎的照片冲洗",
                                p2 = film.img,
                                p3 = film.userId
                            )
                        }
                    }
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initData() {
        mViewModel.roomInfo.observe(this) { roomInfo ->
            reportPoint("film-processing-room-expo") {
                actionp2 = // 记录当前是否有弹幕：有弹幕、无弹幕
                    if ((roomInfo.recommendUser?.size ?: 0) > 0) {
                        "有弹幕"
                    } else {
                        "无弹幕"
                    }
                actionp3 = // 记录当前可冲洗次数，记录数字，没有时记0
                    (roomInfo.userQuota?.quota ?: 0).toString()

                // 当actionp3≠0表示有可冲洗次数时，底部会增加最受欢迎的照片模块
                if (actionp3 !== "0") {
                    val recommendFilmList = roomInfo.recommendFilmList
                    if (!recommendFilmList.isNullOrEmpty()) {
                        actionp4 =
                            recommendFilmList.joinToString(";") {
                                it.img ?: ""
                            } // 记录曝光的照片链接，多个链接用英文;隔开
                        actionp5 =
                            recommendFilmList.joinToString(";") {
                                it.userId ?: ""
                            } // 记录曝光的照片的uid，多个uid用英文;隔开
                        actionp6 =
                            recommendFilmList.joinToString(";") { if (it.cantSeeTa()) "未冲洗" else "已冲洗" } // 记录曝光的照片是否已冲洗，记成文本“已冲洗”“未冲洗”，用英文;隔开
                    }
                }
            }

            mBinding.run {
                val recommendUsers = roomInfo.recommendUser
                if (recommendUsers?.isNotEmpty() == true) {
                    // 启动弹幕引擎
                    danmuContainer.apply {
                        // 首次进入页面，启动弹幕
                        if (getTag(R.id.tag_state_description) == null) {
                            setOnCompleteListener {
                                addTitleSequentiallyDelayed(recommendUsers)
                            }
                            prepare()
                            addTitleSequentiallyDelayed(recommendUsers)

                            setTag(R.id.tag_state_description, true)
                        }
                    }
                }

                idShakeBtn.onClick {
                    reportClick(typeStr = "随机抽取一张")

                    if (mViewModel.inActivity.value == true) {
                        startShakeFragment()
                    } else {
                        T.ss(
                            "今日 ${
                                parseToUserReadableTime(roomInfo.openTime ?: "19:00:00")
                            }-${
                                parseToUserReadableTime(roomInfo.closeTime ?: "24:00:00")
                            } 开启抽取功能，敬请期待\uD83D\uDE1A"
                        )
                    }
                }

                val quota = roomInfo.userQuota?.quota ?: -1
                llRemainFlushCount.apply {
                    visibility = if (quota > 0) View.VISIBLE else View.GONE
                }

                tvRemainFlushCount.apply {
                    text = "你还有 $quota 次冲洗机会"
                }

                tvRemainFlushCountPast.apply {
                    val quotaTips = roomInfo.quotaTips
                    text = quotaTips
                    visibility = if (quotaTips.isNullOrEmpty()) View.GONE else View.VISIBLE
                }

                llNoFlushCount.apply {
                    visibility = if (quota <= 0) View.VISIBLE else View.GONE
                }

                ServiceManager.getInstance().tempTask.getTempTaskRepository()?.handlerView(
                    idLottieAnimationView,
                    TempTaskType.Film
                )
            }
        }

        mViewModel.countDown.observe(this) { countDown ->
            mBinding.tvCountDown.visibility = View.VISIBLE
            mBinding.tvCountDown.text = countDown
        }

        mViewModel.userTasks.observe(this) { userTaskModels ->
            mBinding.rvFlushMore.adapter = BaseBinderAdapter().apply {
                addItemBinder(
                    UserTask::class.java,
                    UserTaskItemBinder(this@FilmFlushFragment)
                )

                setList(userTaskModels)
            }
        }

        mViewModel.flushFilmWashProcessIdLiveData.observe(this) { filmProcessId ->
            if (!filmProcessId.isNullOrEmpty()) {
                activity?.let { fragmentActivity ->
                    ModuleActivityRouter.showShakeDialogFragment(
                        filmProcessId,
                        fragmentActivity
                    )
                }
            }
        }

        val activityViewModel by activityViewModels<FilmFlushViewModel>()
        activityViewModel.refreshData.observe(
            viewLifecycleOwner
        ) { value ->
            if (value == true) {
                initRoomInfo()
            }
        }
    }

    private fun parseToUserReadableTime(timeString: String): String {
        val timeParts = timeString.split(":")
        val hour = try {
            LText.getInt(timeParts[0])
        } catch (_: Throwable) {
            0
        }
        return "${hour}点"
    }

    private fun DanMuView.addTitleSequentiallyDelayed(titles: List<RecommendUser>) {
        lifecycleScope.launch {
            val marginEnd = DimensionUtil.dpToPx(activity, 70)
            titles.forEach {
                add(createDanmuView(recommendUser = it).apply {
                    setStartPositionX((marginEnd).toFloat())
                })
                delay(1000)
            }
        }
    }

    private fun createDanmuView(
        recommendUser: RecommendUser,
    ): DanMuModel {
        return DanMuModel().apply {
            tag = recommendUser

            displayType = DanMuModel.RIGHT_TO_LEFT
            priority = DanMuModel.NORMAL

            // 显示的文本内容
            textSize = DimensionUtil.spToPx(activity, 14).toFloat()
            textColor = ContextCompat.getColor(Utils.getApp(), R.color.color_white_50)
            text = buildSpannedString {
                append(recommendUser.content)
                append("  ")
                appendClickable(
                    "点击去看Ta",
                    R.color.common_white.toResourceColor(),
                    false
                ) {}
            }

            // 弹幕文本背景
            textBackground =
                ContextCompat.getDrawable(
                    Utils.getApp(),
                    R.drawable.activity_ff_corners_danmu
                )
            textBackgroundMarginLeft = DimensionUtil.dpToPx(activity, 16)
            textBackgroundPaddingLeft = DimensionUtil.dpToPx(activity, 16)
            textBackgroundPaddingRight = DimensionUtil.dpToPx(activity, 16)
            textBackgroundPaddingTop = DimensionUtil.dpToPx(activity, 8)
            textBackgroundPaddingBottom = DimensionUtil.dpToPx(activity, 8)

            enableTouch(true)
            setOnTouchCallBackListener { danMuModel ->
                val tag = danMuModel.tag
                if (tag is RecommendUser) {
                    reportClick(typeStr = "弹幕")

                    // 点击后跳该用户个人主页
                    MePageRouter.jumpToInfoPreviewActivity(
                        activity,
                        recommendUser.userId,
                        null,
                        PageSource.FILM_ACTIVITY,
                        "",
                        "",
                        "",
                        recommendUser.securityId,
                        ""
                    )
                }
            }
        }
    }

    private fun startShakeFragment() {
        val shakeShakeFragment = ShakeShakeFragment()
        shakeShakeFragment.show(childFragmentManager, ShakeShakeFragment::class.java.toString())
    }

    override fun onResume() {
        super.onResume()
        initRoomInfo()
    }

    override fun onRetry() {
        initRoomInfo()
    }

    private fun initRoomInfo() {
        mViewModel.initData()
    }

    override fun getStateLayout() = mBinding.stateLayout

    override fun onDestroy() {
        mBinding.danmuContainer.release()
        super.onDestroy()
    }

    override fun onJoin(task: UserTask) {
        TLog.info(LOG_TAG, "UserTask: $task")

        when (task.type) {
            UserTaskType.EditInfo.type -> {
                reportClick(typeStr = "完善个人资料")

                MePageRouter.jumpToMeInfoEditActivity(activity)
            }

            UserTaskType.LikeComment.type -> {
                T.ss("去首页找找你感兴趣的嘉宾，大胆的送出你的赞吧～")

                reportClick(typeStr = "点赞或评论别人动态")

                mViewModel.viewModelScope.launch {
                    delay(1000)

                    // 点赞评论
                    RxBus.getInstance().post(0, Constants.POST_TAG_GO_MAIN_TAB)

                    // 选中推荐第一个
                    RxBus.getInstance().post(0, Constants.POST_TAG_MATCH_TAB_SELECT_INDEX)
                }
            }

            UserTaskType.Dynamic.type -> {
                reportClick(typeStr = "发布生活碎片动态")

                // 动态
                SocialPageRouter.jumpToPublishActivity(activity, "", Constants.PUBLISH_TYPE_SQUARE)
            }

            UserTaskType.Share.type -> {
                reportClick(typeStr = "分享活动给好友")

                // 分享
                val host = activity
                if (host is FragmentActivity) {
                    ShareAction(host, object : IShare.Callback {
                        override fun onStart(type: SharePlatForm?, desc: String?) {
                            TLog.info(LOG_TAG, "onStart, type: $type, desc: $desc")
                        }

                        override fun onSuccess(type: SharePlatForm?) {
                            TLog.info(LOG_TAG, "onSuccess, type: $type")
                            mViewModel.finishShare()
                        }

                        override fun dealFail(type: SharePlatForm?, desc: String?) {
                            TLog.error(LOG_TAG, "dealFail, type: $type, desc: $desc")
                        }

                    }).share(AccountHelper.getInstance().account.userId, "5")
                }
            }

            else -> {
                reportClick(typeStr = task.title ?: "")

                ProtocolHelper.parseProtocol(task.jumpProto)
            }
        }
    }

    private fun reportClick(
        action: String = "film-processing-room-click",
        typeStr: String,
        p2: String? = null,
        p3: String? = null
    ) {
        reportPoint(action) {
            type = typeStr // 记录点击的位置：弹幕、随机抽取一张、去参与发布生活碎片动态、去参与点赞或评论别人动态、去参与分享活动给好友

            actionp2 = p2 // 记录当type=‘最受欢迎的照片’时，点击的照片链接
            actionp3 = p3 // 记录当type=‘最受欢迎的照片’时，点击的照片用户uid
        }
    }
}

class UserTaskItemBinder(private val callback: Callback) :
    BaseDataBindingItemBinder<UserTask, ActivityFilmFlushMoreItemBinding>() {
    override fun getResLayoutId(): Int {
        return R.layout.activity_film_flush_more_item
    }

    override fun bind(
        holder: BinderDataBindingHolder<ActivityFilmFlushMoreItemBinding>?,
        binding: ActivityFilmFlushMoreItemBinding?,
        item: UserTask?
    ) {
        binding?.item = item
        binding?.run {
            item?.let {
                ivIcon.setImageURI(it.icon)

                val isEditInfoTask = it.type == UserTaskType.EditInfo.type

                tvFfToday.text = buildSpannedString {
                    append(if (isEditInfoTask) "已获得" else "今日")
                    append("  ")

                    appendClickable(
                        item.completeQuota.toString(),
                        R.color.activity_FC9825.toResourceColor(),
                        false
                    ) {}

                    append("/")
                    append(item.goalQuota.toString())
                }

                tvFfJoin.apply {
                    val completeFull = item.completeQuota == item.goalQuota
                    alpha = if (completeFull) 0.4f else 1f
                    onClick {
                        if (completeFull) {
                            T.ss("${if (isEditInfoTask) "" else "今日"}获取次数已达上限，试试参与其他任务吧")
                        } else {
                            callback.onJoin(item)
                        }
                    }
                }
            }
        }
    }

    interface Callback {
        fun onJoin(task: UserTask)
    }
}