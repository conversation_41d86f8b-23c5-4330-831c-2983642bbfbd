package com.kanzhun.marry.activity.film

import android.widget.ImageView
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidViewBinding
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogWindowProvider
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.viewmodel.compose.viewModel
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ui.OImageView
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.activity.R
import com.kanzhun.marry.activity.databinding.FragmentLayoutFilmFlushBinding
import com.kanzhun.marry.activity.film.api.bean.FilmAlertResponse
import com.kanzhun.marry.activity.film.api.bean.RecommendFilm
import com.techwolf.lib.tlog.TLog

private const val TAG = "FilmFlushRoom"

@Composable
fun FilmFlushRoom(modifier: Modifier = Modifier) {
    var shouldReloadRoomInfo by remember { mutableStateOf(false) }

    AndroidViewBinding(
        factory = FragmentLayoutFilmFlushBinding::inflate,
        modifier = modifier.fillMaxSize(),
        onReset = {

        },
        onRelease = {

        },
        update = {
            val filmFlushFragment = fragmentContainerView.getFragment<FilmFlushFragment>()
            TLog.info(TAG, "filmFlushFragment: $filmFlushFragment")

            if (shouldReloadRoomInfo) {
                TLog.info(TAG, "filmFlushFragment.onRetry()")

                filmFlushFragment.onRetry()
                shouldReloadRoomInfo = false
            }
        }
    )

    val guideViewModel: NoviceGuideViewModel = viewModel()
    LaunchedEffect(Unit) { guideViewModel.getNoviceGuideFilmAlert() }

    val alertResponse by guideViewModel.filmAlertLiveData.observeAsState()
    alertResponse?.let {
        var openAlertDialog by remember { mutableStateOf(true) }

        val dismissAction: () -> Unit = {
            openAlertDialog = false
            shouldReloadRoomInfo = true
        }

        if ((it.needAlert == true || isQaDebugUser()) && openAlertDialog) {
            LaunchedEffect(Unit) {
                reportPoint(action = "complimentary-film-chance-popup-expo")
            }

            NoviceGuideDialog(it, onDismiss = {
                // type 记录点击的位置：去试试、关闭
                reportPoint(action = "complimentary-film-chance-popup-click", block = {
                    type = "关闭"
                })

                dismissAction()
            }, onTry = {
                // type 记录点击的位置：去试试、关闭
                reportPoint(action = "complimentary-film-chance-popup-click", block = {
                    type = "去试试"
                })

                dismissAction()
            })
        }
    }
}

@Composable
fun RecommendFilmListContent(
    recommendFilmList: List<RecommendFilm>,
    onRightButtonClick: (film: RecommendFilm, canSeeTa: Boolean) -> Unit = { _, _ -> }
) {
    Column(
        Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(horizontal = 24.dp, vertical = 32.dp)
    ) {
        Text(
            text = "最受欢迎的照片",
            style = TextStyle(
                fontSize = 20.sp,
                fontWeight = FontWeight(900),
                color = colorResource(R.color.common_color_FFE2C0),
                fontFamily = boldFontFamily()
            )
        )

        Spacer(Modifier.height(24.dp))

        Column(verticalArrangement = Arrangement.spacedBy(24.dp)) {
            recommendFilmList.forEach {
                RecommendFilmItem(recommendFilm = it) { canSeeTa ->
                    onRightButtonClick(it, canSeeTa)
                }
            }
        }
    }
}

@Composable
fun RecommendFilmItem(
    modifier: Modifier = Modifier,
    recommendFilm: RecommendFilm,
    onRightButtonClick: (canSeeTa: Boolean) -> Unit = {}
) {
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        val (image, titleContent, flush) = createRefs()

        val cantSeeTa = recommendFilm.cantSeeTa()
        OImageView(
            imageUrl = recommendFilm.img ?: "",
            modifier = Modifier
                .width(87.dp)
                .aspectRatio(87f / 103f)
                .border(width = 2.dp, color = colorResource(R.color.common_color_FFE6BE))
                .constrainAs(image) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                },
            scaleType = ImageView.ScaleType.CENTER_CROP,
            enableBlur = recommendFilm.imgStyle == 2 // 1原样展示 2模糊处理
                && cantSeeTa
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .constrainAs(titleContent) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(image.end, margin = 16.dp)
                    end.linkTo(flush.start, margin = 16.dp)

                    width = Dimension.fillToConstraints
                }) {
            Text(
                text = recommendFilm.nickName ?: "",
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight(600),
                    color = colorResource(R.color.common_color_FFE6BE)
                )
            )

            Spacer(Modifier.height(8.dp))

            val content = recommendFilm.content ?: ""
            if (content.isNotEmpty()) {
                Text(
                    text = content,
                    modifier = Modifier.fillMaxWidth(),
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 3,
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = colorResource(R.color.common_color_B9AD9A)
                    )
                )
            }
        }

        // https://stackoverflow.com/a/67611627/24070075
        val flushArrowId = "flushArrow"
        val flushText = buildAnnotatedString {
            append(if (cantSeeTa) "冲洗" else "看看Ta")
            appendInlineContent(flushArrowId, "[icon]")
        }

        val inlineContent = mapOf(
            Pair(
                flushArrowId,
                InlineTextContent(
                    Placeholder(
                        width = 14.sp,
                        height = 14.sp,
                        placeholderVerticalAlign = PlaceholderVerticalAlign.TextCenter
                    )
                ) {
                    Row(
                        modifier = Modifier.fillMaxSize(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Spacer(Modifier.width(4.dp))

                        Image(
                            painter = painterResource(R.drawable.activity_ff_ic_join_arrow),
                            contentDescription = null,
                            modifier = Modifier
                                .width(8.dp)
                                .aspectRatio(14 / 19f)
                                .align(Alignment.CenterVertically)
                        )
                    }
                }
            )
        )

        Text(
            text = flushText,
            modifier = Modifier
                .border(
                    width = 1.dp,
                    color = colorResource(R.color.common_color_fc9825)
                )
                .padding(horizontal = 18.dp, vertical = 5.dp)
                .noRippleClickable { onRightButtonClick(!cantSeeTa) }
                .constrainAs(flush) {
                    end.linkTo(parent.end)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                },
            inlineContent = inlineContent,
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(600),
                color = colorResource(R.color.common_color_fc9825)
            )
        )
    }
}

@Composable
fun NoviceGuideDialog(
    alertResponse: FilmAlertResponse? = null,
    onDismiss: () -> Unit = {},
    onTry: () -> Unit = {}
) {
    Dialog(onDismissRequest = { onDismiss() }) {
        (LocalView.current.parent as DialogWindowProvider).window.setDimAmount(0.8f)

        Column {
            Column(
                Modifier
                    .fillMaxWidth()
                    .border(
                        width = 2.dp,
                        color = colorResource(R.color.common_color_F7F1E7_80)
                    )
                    .background(Color.Black)
                    .paint(
                        painter = painterResource(R.drawable.activity_ff_ic_dialog_bg),
                        contentScale = ContentScale.FillBounds
                    )
                    .padding(start = 24.dp, end = 24.dp, top = 32.dp, bottom = 28.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = (alertResponse?.title ?: "").ifEmpty { "免费赠送你5次冲洗次数" },
                    fontWeight = FontWeight(900),
                    color = colorResource(R.color.common_color_FFE6BE),
                    style = TextStyle(
                        fontSize = 24.sp,
                        shadow = Shadow(
                            color = colorResource(R.color.common_color_FFE6BE),
                            blurRadius = 24f
                        ),
                        fontFamily = boldFontFamily()
                    ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(Modifier.height(12.dp))

                Text(
                    text = (alertResponse?.subtitle
                        ?: "").ifEmpty { "用照片的方式多认识5个新嘉宾" },
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = colorResource(R.color.common_color_E2DAD4),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(Modifier.height(30.dp))

                Image(
                    painter = painterResource(R.drawable.activity_ff_kodak),
                    contentDescription = null
                )

                Spacer(Modifier.height(45.dp))

                Box(
                    Modifier
                        .fillMaxWidth()
                        .background(color = colorResource(R.color.common_color_F7F1E7))
                        .padding(12.dp)
                        .noRippleClickable { onTry() },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = (alertResponse?.btn ?: "").ifEmpty { "去试试" },
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = colorResource(R.color.common_color_191919),
                    )
                }
            }

            Image(
                painter = painterResource(R.drawable.activity_ff_ic_close_gray),
                contentDescription = null,
                modifier = Modifier
                    .padding(top = 28.dp)
                    .size(24.dp)
                    .align(Alignment.CenterHorizontally)
                    .noRippleClickable { onDismiss() },
            )
        }
    }
}

@Preview(
    showBackground = true,
    backgroundColor = 0x0000000L
)
@Composable
private fun PreviewRecommendFilmListContent() {
    RecommendFilmListContent(recommendFilmList = mutableListOf<RecommendFilm>().apply {
        add(
            RecommendFilm(
                content = "【2022年】《老友记》【2022年】《老友记》【2022年】《老友记》【2022年】《老友记》【2022年】《老友记》【2022年】《老友记》【2022年】《老友记》【2022年】《老友记》",
                nickName = "小明",
                avatar = "https://img.kanzhun.com/2022/04/08/c6e6f7c7-e7b6-4e7c-b7e6-e7b6e7b6e7b6.jpg",
                filmProcessId = "3"
            )
        )

        add(
            RecommendFilm(
                content = "",
                nickName = "小花",
                avatar = "https://img.kanzhun.com/2022/04/08/c6e6f7c7-e7b6-4e7c-b7e6-e7b6e7b6e7b6.jpg",
                filmProcessId = "4",
                securityId = "securityId",
                userId = "userId",
            )
        )

        add(
            RecommendFilm(
                content = "【2022年】《老友记》",
                nickName = "小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王小王",
                avatar = "https://img.kanzhun.com/2022/04/08/c6e6f7c7-e7b6-4e7c-b7e6-e7b6e7b6e7b6.jpg",
                filmProcessId = "5"
            )
        )
    })
}

@Preview
@Composable
private fun PreviewNoviceGuideDialog() {
    NoviceGuideDialog()
}