package com.kanzhun.marry.push

import android.text.TextUtils
import com.hihonor.push.sdk.HonorMessageService
import com.hihonor.push.sdk.HonorPushDataMsg
import com.techwolf.lib.tlog.TLog

class HonorMsgService : HonorMessageService() {
    override fun onNewToken(token: String?) {
        super.onNewToken(token)
        PushSdkManager.getInstance().registerToken(token)
    }

    override fun onMessageReceived(remoteMessage: HonorPushDataMsg?) {
        super.onMessageReceived(remoteMessage)
        val data: String? = remoteMessage?.data
        if (!TextUtils.isEmpty(data)) {
            PushSdkManager.getInstance().onReceivePassThroughMessage(PushSdkConfig.honorType, data)
            TLog.print(HwPushService.TAG, "HonorMsgService Message data payload: %s", data)
        }
    }
}