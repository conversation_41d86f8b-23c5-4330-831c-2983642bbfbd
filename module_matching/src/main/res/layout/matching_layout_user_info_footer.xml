<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/btn_feedback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/matching_user_info_footer_feedback"
            android:textColor="@color/common_color_B7B7B7"
            android:textSize="@dimen/common_text_sp_14"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="90dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <View
            android:layout_width="30dp"
            android:layout_height="1dp"
            android:background="@color/common_color_B7B7B7"
            android:layout_marginEnd="30dp"
            app:layout_constraintTop_toTopOf="@+id/btn_feedback"
            app:layout_constraintBottom_toBottomOf="@+id/btn_feedback"
            app:layout_constraintEnd_toStartOf="@+id/btn_feedback" />

        <View
            android:layout_width="30dp"
            android:layout_height="1dp"
            android:background="@color/common_color_B7B7B7"
            android:layout_marginStart="30dp"
            app:layout_constraintTop_toTopOf="@+id/btn_feedback"
            app:layout_constraintBottom_toBottomOf="@+id/btn_feedback"
            app:layout_constraintStart_toEndOf="@+id/btn_feedback" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>