<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idCard"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="60dp"
        android:layout_marginBottom="38dp"
        android:background="@color/common_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/idIcon"
        app:qmui_radius="24dp">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ivUserAvatar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toTopOf="@+id/space"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/idThemeShadowText"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/idOfficeActivityLayout"
            app:layout_constraintEnd_toEndOf="@+id/ivUserAvatar"
            app:layout_constraintStart_toStartOf="@+id/ivUserAvatar"
            app:layout_constraintTop_toTopOf="@+id/ivUserAvatar">

            <ImageView
                android:id="@+id/idTextShadow1"
                android:layout_width="0dp"
                android:layout_height="35dp"
                android:layout_marginHorizontal="33dp"
                android:layout_marginBottom="60dp"
                android:src="@drawable/theme_christmas_f1_txt"
                android:text="圣诞限时活动"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/idTextShadow2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="参加活动每日可多解锁一个人哦～"
                android:textColor="@color/common_color_FFF8EB"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/idTextShadow1" />

            <TextView
                android:id="@+id/idTextShadow3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="去看看 >"
                android:textColor="@color/common_color_FFF8EB"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/idTextShadow2" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/idThemeShadow"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@mipmap/theme_christmas_frame_0"
            app:layout_constraintBottom_toBottomOf="@+id/ivUserAvatar"
            app:layout_constraintEnd_toEndOf="@+id/ivUserAvatar"
            app:layout_constraintStart_toStartOf="@+id/ivUserAvatar"
            app:layout_constraintTop_toTopOf="@+id/ivUserAvatar">

        </FrameLayout>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/idCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginRight="15dp"
            android:paddingHorizontal="12dp"
            android:paddingVertical="4dp"
            android:textColor="@color/common_white"
            android:textSize="14dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:qmui_backgroundColor="@color/common_color_53C0FF"
            app:qmui_radius="8dp"
            tools:text="25号"
            tools:visibility="visible" />

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/idOfficeActivityLayout"
            android:layout_width="match_parent"
            android:layout_height="72dp"
            android:layout_marginTop="-40dp"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="@+id/clInfo"
            app:qmui_borderColor="@color/color_white"
            app:qmui_borderWidth="3dp"
            app:qmui_hideRadiusSide="bottom"
            app:qmui_radius="16dp"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/idOfficeActivity"
                android:layout_width="match_parent"
                android:layout_height="72dp"
                android:background="@color/color_white"
                android:scaleType="fitXY"
                android:src="@mipmap/f_bg_user_info_card_coevent" />

            <TextView
                android:id="@+id/idOfficeActivityText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="10dp"
                android:drawableLeft="@mipmap/f_ic_coevent_tag"
                android:drawablePadding="4dp"
                android:gravity="center_vertical"
                android:text="Ta即将和你参与同一场活动"
                android:textColor="@color/common_color_292929"
                android:textSize="14dp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="@+id/idOfficeActivity" />
        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clLifePhoto"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toTopOf="@+id/idOfficeActivityLayout"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <com.facebook.drawee.view.SimpleDraweeView
                android:id="@+id/ivPicture1"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:roundedCornerRadius="10dp"
                app:roundingBorderColor="@color/color_white"
                app:roundingBorderWidth="2dp"
                tools:background="@color/common_color_FFFAD8" />

            <com.facebook.drawee.view.SimpleDraweeView
                android:id="@+id/ivPicture2"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginLeft="8dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintLeft_toRightOf="@id/ivPicture1"
                app:layout_constraintTop_toTopOf="parent"
                app:roundedCornerRadius="10dp"
                app:roundingBorderColor="@color/color_white"
                app:roundingBorderWidth="2dp"
                tools:background="@color/common_color_FFFAD8" />

            <com.facebook.drawee.view.SimpleDraweeView
                android:id="@+id/ivPicture3"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginLeft="8dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintLeft_toRightOf="@id/ivPicture2"
                app:layout_constraintTop_toTopOf="parent"
                app:roundedCornerRadius="10dp"
                app:roundingBorderColor="@color/color_white"
                app:roundingBorderWidth="2dp"
                tools:background="@color/common_color_FFFAD8" />

            <com.facebook.drawee.view.SimpleDraweeView
                android:id="@+id/ivPicture4"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:layout_marginLeft="8dp"
                android:scaleType="centerCrop"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintLeft_toRightOf="@id/ivPicture3"
                app:layout_constraintTop_toTopOf="parent"
                app:placeholderImage="@mipmap/matching_ic_remain_story_blur"
                app:roundedCornerRadius="10dp"
                app:roundingBorderColor="@color/color_white"
                app:roundingBorderWidth="2dp"
                tools:background="@color/common_color_FFFAD8"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvRemainNum"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#ffffff"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="@+id/ivPicture4"
                app:layout_constraintLeft_toLeftOf="@+id/ivPicture4"
                app:layout_constraintRight_toRightOf="@+id/ivPicture4"
                app:layout_constraintTop_toTopOf="@+id/ivPicture4"
                tools:text="+2" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <Space
            android:id="@+id/space"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toTopOf="@+id/clInfo" />


        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/clInfo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/common_white"
            android:paddingBottom="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:qmui_borderColor="@color/common_color_F5F5F5"
            app:qmui_borderWidth="2dp"
            app:qmui_radius="24dp">

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tvUserName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="@color/common_color_292929"
                android:textSize="@dimen/common_text_sp_28"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="@string/common_long_placeholder" />

            <TextView
                android:id="@+id/tvAge"
                style="@style/matching_home_card_user_info_text_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="8dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvUserName"
                tools:text="30岁" />

            <View
                android:id="@+id/divider1_1"
                android:layout_width="1dp"
                android:layout_height="8dp"
                android:layout_marginLeft="8dp"
                android:background="@color/common_color_B8B8B8"
                app:layout_constraintBottom_toBottomOf="@id/tvAge"
                app:layout_constraintLeft_toRightOf="@id/tvAge"
                app:layout_constraintTop_toTopOf="@id/tvAge" />

            <TextView
                android:id="@+id/tvHeight"
                style="@style/matching_home_card_user_info_text_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                app:layout_constraintBottom_toBottomOf="@id/tvAge"
                app:layout_constraintLeft_toRightOf="@id/divider1_1"
                app:layout_constraintTop_toTopOf="@id/tvAge"
                tools:text="195cm" />

            <View
                android:id="@+id/divider1_2"
                android:layout_width="1dp"
                android:layout_height="8dp"
                android:layout_marginLeft="8dp"
                android:background="@color/common_color_B8B8B8"
                app:layout_constraintBottom_toBottomOf="@id/tvAge"
                app:layout_constraintLeft_toRightOf="@id/tvHeight"
                app:layout_constraintTop_toTopOf="@id/tvAge" />

            <com.kanzhun.common.kotlin.ui.BlurTextView
                android:id="@+id/tvJobInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="16dp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@id/tvAge"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toRightOf="@id/divider1_2"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvAge" />

            <TextView
                android:id="@+id/tvAddress"
                style="@style/matching_home_card_user_info_text_style"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="16dp"
                android:ellipsize="end"
                android:singleLine="true"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvAge"
                tools:text="@string/common_long_placeholder" />

            <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                android:id="@+id/llIdentityInfo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:orientation="vertical"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvAddress">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/tagView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:layout_marginBottom="16dp"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:itemCount="3"
                    tools:listitem="@layout/common_identity_info_item" />

                <View
                    android:id="@+id/tagClickArea"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

            <com.qmuiteam.qmui.layout.QMUILinearLayout
                android:id="@+id/llSimilar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:background="@color/common_white"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/llIdentityInfo"
                app:qmui_radius="11dp">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/matching_home_similar_point2" />

                <TextView
                    android:id="@+id/tvSimilar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:textColor="@color/common_color_292929"
                    android:textSize="@dimen/common_text_sp_13"
                    tools:text="5个共同点" />
            </com.qmuiteam.qmui.layout.QMUILinearLayout>


            <com.kanzhun.foundation.views.flowlayout.AutoFlowLayout
                android:id="@+id/flTags"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="16dp"
                app:afl_interceptChildClick="true"
                app:afl_maxLines="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/llSimilar"
                app:layout_goneMarginTop="0dp" />


        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

    <com.kanzhun.foundation.views.SendLikeButton
        android:id="@+id/sendLikeButton"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="-22dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/sendLikeButtonTheme"
        android:layout_width="152dp"
        android:layout_height="46dp"
        android:layout_marginBottom="22dp"
        android:background="@mipmap/theme_christmas_like_btn"
        android:gravity="center"
        android:text="需糖果券×3"
        android:textColor="@color/common_color_FFF8EB"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/idIcon"
        android:layout_width="100dp"
        android:layout_height="80dp"
        android:layout_marginTop="@dimen/app_f1_top_text_height"
        android:layout_marginEnd="20dp"
        android:src="@mipmap/theme_christmas_toy_0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>