<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.kanzhun.common.kotlin.ui.statelayout.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:empty_layout="@layout/matching_like_me_empty_layout">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/smartRefreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:srlEnableLoadMoreWhenContentNotFull="false">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="12dp"
                    tools:background="@color/common_color_F0F0F0"
                    tools:listitem="@layout/matching_like_me_list_item_v2" />


            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        </FrameLayout>
    </com.kanzhun.common.kotlin.ui.statelayout.StateLayout>

    <!-- 头像提示条-->
    <include
        android:id="@+id/refresh_hint"
        android:visibility="gone"
        tools:visibility="visible"
        layout="@layout/matching_interact_list_refresh_hint"/>
    <!--显示新手引导的fragment，具体逻辑参阅InteractGuruGuidePerformance-->
    <FrameLayout
        android:id="@+id/fl_guru_guide"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_translate"
        android:visibility="gone" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/idLottieAnimationView"
        android:layout_gravity="end"
        android:layout_width="80dp"
        android:layout_height="80dp"/>
</FrameLayout>