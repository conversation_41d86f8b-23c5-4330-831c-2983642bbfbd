<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.kotlin.ui.statelayout.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/stateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:empty_layout="@layout/matching_like_me_empty_layout">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/idTipBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:background="@color/common_color_FFE9E1"
            android:paddingHorizontal="8dp"
            android:paddingVertical="8dp"
            app:qmui_radius="8dp">

            <com.coorchice.library.SuperTextView
                android:id="@+id/stv_novice_task"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColor="@color/common_color_FF7847"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:textSize="@dimen/common_text_sp_13"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/idWarnBtn"
                tools:text="完成新手任务，他们就能收到你的喜欢啦" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/idWarnBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                android:text="去认证"
                android:textColor="@color/common_white"
                android:textSize="12dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:qmui_backgroundColor="@color/common_color_FF7847"
                app:qmui_radius="11dp" />

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlEnableLoadMoreWhenContentNotFull="false">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="12dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:background="@color/common_color_F0F0F0"
                tools:listitem="@layout/matching_i_like_list_item" />
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </LinearLayout>
</com.kanzhun.common.kotlin.ui.statelayout.StateLayout>