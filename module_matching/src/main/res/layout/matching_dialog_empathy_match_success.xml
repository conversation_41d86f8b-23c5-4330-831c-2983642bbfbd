<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="secCount"
            type="com.kanzhun.foundation.model.OrangeObservableString" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_bg_conor_16_color_white">


        <ImageView
            android:id="@+id/iv"
            android:layout_width="210dp"
            android:layout_height="168dp"
            android:layout_marginTop="32dp"
            android:src="@mipmap/matching_ic_empathy_match_success"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="@string/matching_empathy_match_success"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv" />

        <com.kanzhun.common.views.RoundAlphaButton
            android:id="@+id/tv_sce_count"
            android:layout_width="98dp"
            android:layout_height="98dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:text="@{secCount.stringContent}"
            android:textColor="@color/common_white"
            android:textSize="48dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:qmui_backgroundColor="@color/common_color_7171FF"
            app:qmui_radius="49dp"
            tools:text="0" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>