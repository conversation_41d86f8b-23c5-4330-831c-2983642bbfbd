<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/ll_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/matching_bg_empathy_guide"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:right="@{@string/common_complete}" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="10dp"
            android:fontFamily="sans-serif-medium"
            android:text="@string/matching_lovers_mood_title"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar" />

        <TextView
            android:id="@+id/tv_assist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="8dp"
            android:text="@string/matching_mood_set_assist"
            android:textColor="@color/common_color_000000_50"
            android:textSize="@dimen/common_text_sp_15"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="6dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_assist"
            app:layout_goneMarginTop="22dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.MatchMoodSetCallback" />
    </data>
</layout>