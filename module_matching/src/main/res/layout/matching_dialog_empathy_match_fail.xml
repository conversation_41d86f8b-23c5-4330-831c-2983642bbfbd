<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_conor_16_color_white">


    <ImageView
        android:id="@+id/iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="33dp"
        android:src="@drawable/matching_ic_empathy_match_fail"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_18"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv"
        tools:text="@string/matching_has_not_found_friend" />

    <com.kanzhun.common.views.RoundAlphaButton
        android:id="@+id/tv_negative"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="7.5dp"
        android:layout_marginBottom="24dp"
        android:gravity="center"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:text="@string/common_exist"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@id/gl"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:qmui_borderColor="@color/common_color_EBEBEB"
        app:qmui_borderWidth="1dp"
        app:qmui_radius="25dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <com.kanzhun.common.views.RoundAlphaButton
        android:id="@+id/tv_positive"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="7.5dp"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="24dp"
        android:gravity="center"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:text="@string/matching_search_again"
        android:textColor="@color/common_white"
        android:textSize="@dimen/common_text_sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/gl"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:qmui_backgroundColor="@color/common_color_7171FF"
        app:qmui_radius="25dp" />

</androidx.constraintlayout.widget.ConstraintLayout>