<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_hide"
    android:background="@color/common_color_F5F5F5"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone"
    tools:visibility="visible"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/tv_hide_title"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginLeft="16dp"
        android:fontFamily="sans-serif-medium"
        android:gravity="bottom"
        android:text="@string/common_app_name"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_24"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.airbnb.lottie.LottieAnimationView
        app:lottie_imageAssetsFolder="invisible/images"
        app:lottie_fileName="invisible/invisible.json"
        app:lottie_autoPlay="true"
        app:lottie_loop="false"
        android:id="@+id/iv_icon"
        android:layout_width="173dp"
        android:layout_height="168dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="115dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_hide_title" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="41dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="41dp"
        android:gravity="center"
        android:text="@string/matching_cancellation_set_invisible_tips"
        android:textColor="@color/common_color_7F7F7F"
        android:textSize="@dimen/common_text_sp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_icon" />

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/btn_hide_submit"
        style="@style/common_blue_button_style"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="18dp"
        android:layout_marginRight="18dp"
        android:layout_marginBottom="40dp"
        android:text="@string/matching_cancellation_btn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>