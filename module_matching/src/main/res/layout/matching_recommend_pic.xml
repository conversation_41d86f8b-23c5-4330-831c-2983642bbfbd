<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:paddingBottom="38dp"
    android:orientation="vertical"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/idIcon"
        android:layout_width="100dp"
        android:layout_height="@dimen/app_f1_top_text_height_and_bar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.kanzhun.common.views.image.OImageView
        android:id="@+id/idImageView"
        android:scaleType="centerCrop"
        tools:background="@color/image_color_red"
        app:common_radius="24dp"
        tools:visibility="visible"
        app:common_image_stroke_color="@color/common_color_F3C587"
        app:common_image_stroke_width="2.7dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</LinearLayout>