<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".EmpathyActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.matching.viewmodel.EmpathyViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.EmpathyCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/matching_bg_empathy"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_hello"
            android:layout_width="163dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            app:lottie_fileName="matching_empathy_hello.json"
            app:lottie_loop="false" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:fontFamily="sans-serif-medium"
            android:text="@string/matching_empathy"
            android:textColor="@color/common_color_000000_70"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lav_hello" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/tv_assist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="27dp"
            android:paddingTop="4dp"
            android:paddingRight="27dp"
            android:paddingBottom="4dp"
            android:text="@{viewModel.pageMode == null ? @string/matching_set_mode_to_match : @string/matching_find_empathy}"
            android:textColor="@color/common_color_000000_50"
            app:layout_constraintBottom_toBottomOf="@id/tv_min"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_min"
            app:qmui_backgroundColor="@color/common_color_FFFFFF_20"
            app:qmui_radius="18dp"
            app:visibleInvisible="@{!viewModel.duringShakeSearch}"
            tools:text="@string/matching_find_empathy"
            tools:visibility="visible" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/tv_min"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginTop="12dp"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:text="0"
            android:textColor="@color/common_color_3B3C51"
            android:textSize="@dimen/common_text_sp_20"
            android:textStyle="bold"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_time_divider"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:qmui_backgroundColor="@color/common_color_FFFFFF_30"
            app:qmui_radius="10dp"
            app:visibleInvisible="@{viewModel.duringShakeSearch}" />

        <TextView
            android:id="@+id/tv_time_divider"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:text=":"
            android:textColor="@color/common_color_3B3C51"
            android:textSize="@dimen/common_text_sp_20"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tv_min"
            app:layout_constraintLeft_toRightOf="@id/tv_min"
            app:layout_constraintRight_toLeftOf="@id/tv_sec_one"
            app:layout_constraintTop_toTopOf="@id/tv_min"
            app:visibleInvisible="@{viewModel.duringShakeSearch}" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/tv_sec_one"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:textColor="@color/common_color_3B3C51"
            android:textSize="@dimen/common_text_sp_20"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tv_min"
            app:layout_constraintLeft_toRightOf="@+id/tv_time_divider"
            app:layout_constraintRight_toLeftOf="@+id/tv_sec_tow"
            app:layout_constraintTop_toTopOf="@id/tv_min"
            app:qmui_backgroundColor="@color/common_color_FFFFFF_30"
            app:qmui_radius="10dp"
            app:visibleInvisible="@{viewModel.duringShakeSearch}"
            tools:text="2" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/tv_sec_tow"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginLeft="4dp"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:textColor="@color/common_color_3B3C51"
            android:textSize="@dimen/common_text_sp_20"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/tv_min"
            app:layout_constraintLeft_toRightOf="@+id/tv_sec_one"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_min"
            app:qmui_backgroundColor="@color/common_color_FFFFFF_30"
            app:qmui_radius="10dp"
            app:visibleGone="@{viewModel.duringShakeSearch}"
            tools:text="2" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_min,tv_assist" />

        <com.kanzhun.common.views.MaxChildHeightFrameLayout
            android:id="@+id/lav_content_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:common_max_child_height="346dp"
            app:layout_constraintBottom_toTopOf="@+id/qrb_sub"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/barrier">

            <com.kanzhun.common.views.UniformHWConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|top"
                app:common_uniform_type="uniform_height">

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/lav_content"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_gravity="center_vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:lottie_fileName="matching_before_match.json"
                    app:lottie_loop="true" />

                <com.qmuiteam.qmui.layout.QMUIFrameLayout
                    android:id="@+id/cl_avatar_me"
                    android:layout_width="96dp"
                    android:layout_height="96dp"
                    android:background="@color/common_white"
                    app:layout_constraintBottom_toBottomOf="@id/lav_content"
                    app:layout_constraintLeft_toLeftOf="@id/lav_content"
                    app:layout_constraintRight_toRightOf="@id/lav_content"
                    app:layout_constraintTop_toTopOf="@id/lav_content"
                    app:qmui_radius="48dp">

                    <com.kanzhun.common.views.image.OImageView
                        android:id="@+id/iv_avatar_me"
                        android:layout_width="88dp"
                        android:layout_height="88dp"
                        android:layout_gravity="center"
                        app:common_circle="true"
                        app:common_placeholder="@drawable/common_bg_placeholder_translate" />
                </com.qmuiteam.qmui.layout.QMUIFrameLayout>

                <ImageView
                    android:layout_width="100dp"
                    android:layout_height="61dp"
                    android:layout_marginTop="66dp"
                    android:onClick="@{()->callback.addMode()}"
                    android:src="@mipmap/matching_ic_add_mode"
                    app:layout_constraintLeft_toLeftOf="@id/cl_avatar_me"
                    app:layout_constraintRight_toRightOf="@+id/cl_avatar_me"
                    app:layout_constraintTop_toTopOf="@+id/cl_avatar_me"
                    app:visibleGone="@{viewModel.pageMode == null}"
                    tools:visibility="gone" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/tv_mode_name"
                    android:layout_width="wrap_content"
                    android:layout_height="38dp"
                    android:layout_marginTop="10dp"
                    android:enabled="@{!viewModel.duringShakeSearch}"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center"
                    android:onClick="@{()->callback.addMode()}"
                    android:paddingLeft="13dp"
                    android:paddingRight="13dp"
                    android:text="@{viewModel.pageMode.name}"
                    android:textColor="@color/common_black"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="@id/cl_avatar_me"
                    app:layout_constraintRight_toRightOf="@id/cl_avatar_me"
                    app:layout_constraintTop_toBottomOf="@id/cl_avatar_me"
                    app:qmui_backgroundColor="@color/common_white"
                    app:qmui_radius="12.5dp"
                    app:visibleGone="@{viewModel.pageMode != null}"
                    tools:text="送你花花" />

                <com.kanzhun.common.views.image.OImageView
                    android:layout_width="54dp"
                    android:layout_height="54dp"
                    android:layout_marginBottom="24dp"
                    android:onClick="@{()->callback.addMode()}"
                    app:common_placeholder="@drawable/common_bg_placeholder_translate"
                    app:imageUrl="@{viewModel.pageMode.icon}"
                    app:layout_constraintBottom_toBottomOf="@id/tv_mode_name"
                    app:layout_constraintLeft_toLeftOf="@id/cl_avatar_me"
                    app:layout_constraintRight_toRightOf="@id/cl_avatar_me"
                    app:viewEnable="@{!viewModel.duringShakeSearch}"
                    app:visibleGone="@{viewModel.pageMode != null}" />
            </com.kanzhun.common.views.UniformHWConstraintLayout>

        </com.kanzhun.common.views.MaxChildHeightFrameLayout>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/qrb_sub"
            style="@style/common_blue_button_style"
            android:layout_width="243dp"
            android:layout_height="46dp"
            android:layout_marginBottom="20dp"
            android:enabled="@{viewModel.pageMode != null &amp;&amp; viewModel.remainTimes > 0}"
            android:gravity="center"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text="@{viewModel.pageMode == null ? @string/matching_un_set_mood : (viewModel.remainTimes &lt;= 0 ?  @string/matching_has_no_remain_time : (viewModel.duringShakeSearch ? @string/matching_stop_shake_search : @string/matching_start_shake_search) )}"
            app:greyDisabledStyle="@{viewModel.pageMode != null &amp;&amp; viewModel.remainTimes > 0}"
            app:layout_constraintBottom_toTopOf="@+id/tv_remain_time"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:qmui_radius="25dp" />

        <TextView
            android:id="@+id/tv_remain_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="88dp"
            android:text="@{@string/matching_remain_times(viewModel.remainTimes)}"
            android:textColor="@color/common_color_3B3C51_50"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="@string/matching_remain_times" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>