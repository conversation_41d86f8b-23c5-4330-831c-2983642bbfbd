<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_white">


    <ImageView
        android:id="@+id/iv_bg2"
        android:layout_width="match_parent"
        android:layout_height="375dp"
        android:visibility="gone"
        android:background="@drawable/common_shape_gradient_ffc2ff_ffffff"
        android:scaleType="fitXY" />

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:background="@mipmap/common_bg_theme_3"
        android:scaleType="fitXY" />


    <FrameLayout
        android:id="@+id/ll_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/fl_title"
            android:layout_width="match_parent"
            tools:background="@color/common_color_292929"
            android:layout_height="@dimen/app_f1_top_text_height">

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tvRecommend"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="23dp"
                android:text="今日"
                android:textColor="@color/common_color_292929"
                android:textSize="@dimen/common_text_sp_24"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clNum"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@+id/tvRecommend"
                app:layout_constraintLeft_toRightOf="@+id/tvRecommend"
                app:layout_constraintRight_toLeftOf="@+id/iv_right"
                app:layout_constraintTop_toTopOf="@+id/tvRecommend">

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/rbDot"
                    android:layout_width="4dp"
                    android:layout_height="4dp"
                    android:layout_marginLeft="8dp"
                    android:layout_marginBottom="9dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:qmui_backgroundColor="@color/common_color_292929"
                    app:qmui_radius="2dp" />

                <com.kanzhun.common.views.textview.BoldTextView
                    android:id="@+id/tvNum"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="6dp"
                    android:layout_marginBottom="2dp"
                    android:fontFamily="sans-serif-medium"
                    android:textColor="@color/common_color_292929"
                    android:textSize="@dimen/common_text_sp_14"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@+id/rbDot"
                    tools:text="今日第2位/10" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/iv_right"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="end|center_vertical"
                android:layout_marginEnd="10dp"
                android:padding="10dp"
                android:src="@drawable/matching_home_setting"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.kanzhun.common.kotlin.ui.statelayout.StateLayout
            android:id="@+id/stateLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:error_layout="@layout/matching_home_error_layout">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/smartRefreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:srlEnableAutoLoadMore="false"
                    app:srlEnableLoadMore="false">

                    <com.youth.banner.Banner
                        app:banner_auto_loop="false"
                        app:banner_infinite_loop="false"
                        android:id="@+id/viewPager"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:background="@color/common_color_F5F5F5" />
                </com.scwang.smart.refresh.layout.SmartRefreshLayout>

                <include
                    android:layout_marginTop="@dimen/app_f1_top_text_height_and_bar"
                    tools:visibility="visible"
                    android:id="@+id/userGuideFloatingView"
                    layout="@layout/matching_home_user_guide_floating_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <include
                    android:layout_marginTop="@dimen/app_f1_top_text_height_and_bar"
                    android:id="@+id/updateDialog"
                    layout="@layout/matching_home_today_update_dialog"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                     />
            </FrameLayout>

        </com.kanzhun.common.kotlin.ui.statelayout.StateLayout>

    </FrameLayout>

    <include
        android:id="@+id/blocking"
        tools:visibility="gone"
        layout="@layout/matching_hide_self_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </include>
</FrameLayout>
