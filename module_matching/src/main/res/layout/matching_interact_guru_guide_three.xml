<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_translate"
    tools:background="@color/common_black"
    tools:ignore="MissingDefaultResource">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="80dp"
        android:background="@drawable/matching_bg_color_00000000_to_ffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <include
        android:id="@+id/clBottomAvatars"
        layout="@layout/matching_empty_avatars_layout_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="28dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@id/tvContent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="28dp"
        android:layout_marginBottom="8dp"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_28"
        app:layout_constraintBottom_toTopOf="@id/tvHint1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="13人访问过" />

    <TextView
        android:id="@+id/tvHint1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="28dp"
        android:layout_marginBottom="32dp"
        android:text="@string/matching_interact_guru_guide_hint3"
        android:textColor="@color/common_color_7F7F7F"
        android:textSize="@dimen/common_text_sp_12"
        app:layout_constraintBottom_toTopOf="@id/btComplete"
        app:layout_constraintStart_toStartOf="parent" />

    <com.coorchice.library.SuperTextView
        android:id="@+id/btComplete"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="28dp"
        android:layout_marginTop="36dp"
        android:layout_marginBottom="50dp"
        android:gravity="center"
        android:paddingVertical="12dp"
        android:text="@string/matching_to_complete_education_identity"
        android:textColor="@color/common_white"
        android:textSize="@dimen/common_text_sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:stv_corner="25dp"
        app:stv_solid="@color/common_color_191919" />


</androidx.constraintlayout.widget.ConstraintLayout>