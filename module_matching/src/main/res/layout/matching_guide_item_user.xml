<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="@color/common_white"
    app:cardCornerRadius="12dp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">



        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ivAvatar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="172:209"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@color/common_color_FF7847" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/idGuideline"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        <View
            android:background="@drawable/matching_activity_bg_card"
            app:layout_constraintTop_toTopOf="@+id/idGuideline"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="0dp"/>

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tvUserName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingHorizontal="12dp"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_20"
            app:layout_constraintBottom_toTopOf="@+id/tvAge"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="@string/common_long_placeholder" />


        <TextView
            android:id="@+id/tvAge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="2dp"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toTopOf="@+id/flHighlightTags"
            app:layout_constraintEnd_toStartOf="@id/divider1_1"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="30岁" />

        <View
            android:id="@+id/divider1_1"
            android:layout_width="1dp"
            android:layout_height="8dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:background="@color/common_white"
            app:layout_constraintBottom_toBottomOf="@id/tvAge"
            app:layout_constraintStart_toEndOf="@id/tvAge"
            app:layout_constraintTop_toTopOf="@id/tvAge" />

        <TextView
            android:id="@+id/tvHeight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="@id/tvAge"
            app:layout_constraintStart_toEndOf="@id/divider1_1"
            app:layout_constraintTop_toTopOf="@id/tvAge"
            tools:text="195cm" />

        <com.kanzhun.foundation.views.flowlayout.AutoFlowLayout
            android:id="@+id/flHighlightTags"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginBottom="12dp"
            app:afl_singleLine="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:background="@color/common_color_FFD2D9"
            tools:layout_height="17dp" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/tvTagName"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="12dp"
            android:drawablePadding="2dp"
            android:ellipsize="end"
            android:gravity="center"
            android:paddingHorizontal="8dp"
            android:paddingVertical="3dp"
            android:singleLine="true"
            android:textColor="@color/common_color_292929"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:stv_corner="12dp"
            app:stv_solid="@color/common_color_FFFFFF_90"
            tools:text="@string/common_app_name" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/idCount"
            android:paddingHorizontal="8dp"
            android:paddingVertical="3dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="6dp"
            tools:text="5号"
            tools:visibility="visible"
            android:visibility="gone"
            app:qmui_radius="12dp"
            android:textSize="12dp"
            android:minHeight="24dp"
            android:gravity="center"
            android:textColor="@color/common_white"
            app:qmui_backgroundColor="@color/common_color_53C0FF"
            android:layout_marginRight="6dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>




    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.cardview.widget.CardView>