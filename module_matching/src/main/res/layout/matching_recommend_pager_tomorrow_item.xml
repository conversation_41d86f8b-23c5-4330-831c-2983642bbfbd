<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingBottom="38dp">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/matching_bg_tomorrow_shape_gradient_ffffff_f5f5f5"
        android:padding="2dp"
        app:qmui_radius="20dp">

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/common_color_FFFFFE"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:qmui_radius="20dp">

            <com.lihang.ShadowLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:hl_cornerRadius_leftTop="18dp"
                app:hl_cornerRadius_rightTop="18dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="500dp"
                    android:background="@mipmap/matching_bg_tomorrow_card" />
            </com.lihang.ShadowLayout>

            <ImageView
                android:layout_width="117dp"
                android:layout_height="101dp"
                android:layout_marginTop="51dp"
                android:layout_marginEnd="26dp"
                android:background="@mipmap/matching_tomorrow_card_bg_decoration"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tvCardTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="27dp"
                android:layout_marginTop="54dp"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_34"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="明日 8：00" />

            <com.kanzhun.common.views.textview.BoldTextView
                android:id="@+id/tvSubTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_20"
                app:layout_constraintStart_toStartOf="@id/tvCardTitle"
                app:layout_constraintTop_toBottomOf="@id/tvCardTitle"
                tools:text="准时更新推荐嘉宾" />

            <include
                android:id="@+id/llRecommendUserOne"
                layout="@layout/matching_tomorrow_recommend_user"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="47dp"
                app:layout_constraintTop_toBottomOf="@id/tvSubTitle" />

            <include

                android:id="@+id/llRecommendUserTwo"
                layout="@layout/matching_tomorrow_recommend_user"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toBottomOf="@id/llRecommendUserOne" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableStart="@drawable/matching_icon_tomorrow_recommend"
                android:drawableEnd="@drawable/matching_icon_tomorrow_recommend"
                android:drawablePadding="11dp"
                android:gravity="center_vertical"
                android:text="@string/matching_tomorrow_recommend_hint"
                android:textColor="#994F90FF"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/llRecommendUserTwo" />


        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

</LinearLayout>

