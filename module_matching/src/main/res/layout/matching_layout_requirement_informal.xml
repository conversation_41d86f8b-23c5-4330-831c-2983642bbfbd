<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.matching.viewmodel.MatchRequirementViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.MatchRequirementCallback" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <include
            android:id="@+id/select_age"
            layout="@layout/matching_layout_scroll_select"
            android:layout_width="match_parent"
            android:layout_height="112dp"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp"
            android:background="@drawable/common_bg_conor_12_color_white" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="18dp"
            android:background="@drawable/common_bg_conor_12_color_white">

            <LinearLayout
                android:id="@+id/ll_title"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@mipmap/matching_bg_informal"
                android:orientation="horizontal"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_informal_title"
                    android:layout_width="0dp"
                    android:layout_height="60dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="18dp"
                    android:paddingRight="18dp"
                    android:singleLine="true"
                    android:textColor="@color/common_color_191919"
                    android:textSize="@dimen/common_text_sp_14"
                    tools:text="资料审核中，完成后即可解锁以下功能" />

                <TextView
                    android:id="@+id/tv_do_task"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawableRight="@drawable/common_ic_icon_black_arrow_go"
                    android:gravity="center_vertical"
                    android:onClick="@{()->callback.goDoTask()}"
                    android:paddingRight="12dp"
                    android:text="@string/matching_go_do"
                    android:textColor="@color/common_color_191919"
                    android:textSize="@dimen/common_text_sp_14" />

            </LinearLayout>

            <include
                android:id="@+id/select_stature"
                layout="@layout/matching_layout_scroll_select"
                android:layout_width="match_parent"
                android:layout_height="112dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_title" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:background="@color/common_color_F5F5F5"
                app:layout_constraintBottom_toBottomOf="@+id/select_stature" />

            <TextView
                android:id="@+id/tv_education_title"
                android:layout_width="wrap_content"
                android:layout_height="54dp"
                android:layout_marginTop="8dp"
                android:drawablePadding="6dp"
                android:enabled="false"
                android:gravity="center"
                android:onClick="@{()->callback.onEducationClick()}"
                android:paddingLeft="12dp"
                android:text="@string/common_education"
                android:textColor="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/select_stature" />

            <TextView
                android:id="@+id/tv_education"
                android:layout_width="0dp"
                android:layout_height="54dp"
                android:drawableRight="@drawable/common_ic_gray_right_arrow"
                android:drawablePadding="4dp"
                android:enabled="false"
                android:gravity="center|right"
                android:hint="@string/common_no_limit"
                android:onClick="@{()->callback.onEducationClick()}"
                android:paddingLeft="12dp"
                android:paddingRight="12dp"
                android:singleLine="true"
                android:text="@{viewModel.selectEducation}"
                android:textColor="@color/common_color_CCCCCC"
                android:textColorHint="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintBottom_toBottomOf="@+id/tv_education_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_education_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_education_title"
                app:layout_goneMarginRight="4dp" />

            <TextView
                android:id="@+id/tv_home_title"
                android:layout_width="wrap_content"
                android:layout_height="54dp"
                android:layout_marginTop="8dp"
                android:drawablePadding="6dp"
                android:enabled="false"
                android:gravity="center"
                android:onClick="@{()->callback.onHomeTownClick()}"
                android:paddingLeft="12dp"
                android:text="@string/common_hometown"
                android:textColor="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_education_title" />

            <TextView
                android:id="@+id/tv_home"
                android:layout_width="0dp"
                android:layout_height="54dp"
                android:drawableRight="@drawable/common_ic_gray_right_arrow"
                android:drawablePadding="4dp"
                android:enabled="false"
                android:gravity="center|right"
                android:hint="@string/common_no_limit"
                android:onClick="@{()->callback.onHomeTownClick()}"
                android:paddingLeft="12dp"
                android:paddingRight="12dp"
                android:singleLine="true"
                android:text="@{viewModel.selectProvinceAndCity}"
                android:textColor="@color/common_color_CCCCCC"
                android:textColorHint="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintBottom_toBottomOf="@+id/tv_home_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_home_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_home_title"
                app:layout_goneMarginRight="4dp" />

            <TextView
                android:id="@+id/tv_revenue_title"
                android:layout_width="wrap_content"
                android:layout_height="54dp"
                android:layout_marginTop="8dp"
                android:drawablePadding="6dp"
                android:enabled="false"
                android:gravity="center"
                android:onClick="@{()->callback.onRevenueClick()}"
                android:paddingLeft="12dp"
                android:text="@string/common_revenue"
                android:textColor="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_home_title" />

            <TextView
                android:id="@+id/tv_revenue"
                android:layout_width="0dp"
                android:layout_height="54dp"
                android:drawableRight="@drawable/common_ic_gray_right_arrow"
                android:drawablePadding="4dp"
                android:enabled="false"
                android:gravity="center|right"
                android:hint="@string/common_no_limit"
                android:onClick="@{()->callback.onRevenueClick()}"
                android:paddingLeft="12dp"
                android:paddingRight="12dp"
                android:singleLine="true"
                android:text="@{viewModel.selectRevenue}"
                android:textColor="@color/common_color_CCCCCC"
                android:textColorHint="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintBottom_toBottomOf="@+id/tv_revenue_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_revenue_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_revenue_title"
                app:layout_goneMarginRight="4dp" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>
</layout>