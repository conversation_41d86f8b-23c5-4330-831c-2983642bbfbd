<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/common_color_F0F0F0"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/idBG"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="match_parent"
        android:layout_height="240dp"
        android:background="@mipmap/common_bg_theme_1" />

    <LinearLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/include_notify_open"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical">


        <net.lucode.hackware.magicindicator.MagicIndicator
            android:layout_marginStart="10dp"
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="50dp" />


        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="6dp"
            android:orientation="horizontal" />
    </LinearLayout>

    <!-- 打开通知-->
    <include
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_gravity="bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/include_notify_open"
        layout="@layout/chat_include_notify_open"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
