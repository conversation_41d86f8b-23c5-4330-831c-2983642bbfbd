package com.kanzhun.marry.matching.fragment.interact

import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.views.image.OImageView
import com.kanzhun.marry.matching.databinding.MatchingEmptyAvatarsLayout1Binding
import com.kanzhun.marry.matching.databinding.MatchingEmptyAvatarsLayoutBinding

fun MatchingEmptyAvatarsLayoutBinding.setAvatars(avatars: List<String>?) {
    if (avatars.isNullOrEmpty()) {
        root.gone()
    } else {
        root.visible()
        listOf<OImageView>(ivAvatar3, ivAvatar2, ivAvatar1).forEachIndexed { index, oImageView ->
            if (index < avatars.size) {
                oImageView.visible()
                oImageView.load(avatars[index])
            } else {
                oImageView.gone()
            }
        }
    }
}

fun MatchingEmptyAvatarsLayout1Binding.setAvatars(avatars: List<String>?) {
    if (avatars.isNullOrEmpty()) {
        root.gone()
    } else {
        root.visible()
        listOf<OImageView>(ivAvatar3, ivAvatar2, ivAvatar1).forEachIndexed { index, oImageView ->
            if (index < avatars.size) {
                oImageView.visible()
                oImageView.load(avatars[index])
            } else {
                oImageView.gone()
            }
        }
    }
}