package com.kanzhun.marry.matching.api.model;

import com.kanzhun.foundation.api.model.MatchingPageMood;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/20.
 */
public class ShakeCheckModel implements Serializable {

    private static final long serialVersionUID = -6369511470875238300L;
    public OtherUser otherUser;

    public static final class OtherUser implements Serializable{
        private static final long serialVersionUID = -555079084497197927L;
        public String userId;
        public MatchingPageMood mood;
    }

    public String getIcon() {
        if (otherUser != null && otherUser.mood != null) {
            return otherUser.mood.icon;
        }
        return "";
    }
}
