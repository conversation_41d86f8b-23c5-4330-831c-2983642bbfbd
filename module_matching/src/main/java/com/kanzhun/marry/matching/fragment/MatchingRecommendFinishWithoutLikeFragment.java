package com.kanzhun.marry.matching.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.util.ProtocolHelper;
import com.kanzhun.common.views.CircleNavigatorIndicator;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.api.model.MatchingBlockGuide;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.marry.matching.BR;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.adapter.BlockFinishBannerAdapter;
import com.kanzhun.marry.matching.api.model.MatchBlockGuideModel;
import com.kanzhun.marry.matching.databinding.MatchingFragmentRecommendFinishWithoutLikeBinding;
import com.kanzhun.marry.matching.viewmodel.MatchingRecommendFinishWithoutLikeViewModel;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.OnMultiClickListener;
import com.youth.banner.listener.OnPageChangeListener;

import java.util.List;

/**
 * 匹配滑卡阻断 - 空状态（不带喜欢我）
 * <p>
 * Created by Qu Zhiyong on 2022/5/13
 */
public class MatchingRecommendFinishWithoutLikeFragment extends FoundationVMFragment<MatchingFragmentRecommendFinishWithoutLikeBinding, MatchingRecommendFinishWithoutLikeViewModel> {

    @Override
    protected void initFragment() {
        super.initFragment();
        initView();
    }

    private void initView() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            // 没有更多数据时，展示的文案
            String noDateDesc = arguments.getString(BundleConstants.BUNDLE_NO_DATA_DESC);
            if (!TextUtils.isEmpty(noDateDesc)) {
                getViewModel().getSloganObservable().set(noDateDesc);
            }

            MatchBlockGuideModel guideModel = (MatchBlockGuideModel) arguments.getSerializable(BundleConstants.BUNDLE_BLOCK_GUIDE_MODEL);
            if (guideModel != null && !LList.isEmpty(guideModel.guide)) {
                getDataBinding().clEmptyGuide.setVisibility(View.GONE);
                getDataBinding().clGuide.setVisibility(View.VISIBLE);

                initBanner(guideModel.guide);
            } else {
                getDataBinding().clEmptyGuide.setVisibility(View.VISIBLE);
                getDataBinding().clGuide.setVisibility(View.GONE);
            }
        }
    }

    private int curPosition = 0;// 当前banner所在位置

    private void initBanner(List<MatchingBlockGuide> guideList) {
        if (LList.isEmpty(guideList)) return;
        CircleNavigatorIndicator circleNavigator = new CircleNavigatorIndicator(getContext());
        circleNavigator.setUnSelectedColor(ContextCompat.getColor(getContext(), R.color.matching_color_3A3E47_20));
        circleNavigator.setIndicatorColor(ContextCompat.getColor(getContext(), R.color.matching_color_3A3E47));
        circleNavigator.setRadius(QMUIDisplayHelper.dpToPx(4));
        circleNavigator.setCircleCount(guideList.size());
        circleNavigator.setCircleSpacing(QMUIDisplayHelper.dpToPx(10));
        getDataBinding().mi.setNavigator(circleNavigator);
        getDataBinding().mi.setVisibility(LList.getCount(guideList) > 1 ? View.VISIBLE : View.GONE);

        getDataBinding().btnAction.setText(guideList.get(0) != null ? guideList.get(0).buttonText : "");
        BlockFinishBannerAdapter bannerAdapter = new BlockFinishBannerAdapter(guideList);
        getDataBinding().banner.setAdapter(bannerAdapter)
                .addBannerLifecycleObserver(MatchingRecommendFinishWithoutLikeFragment.this);
        getDataBinding().banner.addOnPageChangeListener(new OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                getDataBinding().mi.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                curPosition = position;
                getDataBinding().mi.onPageSelected(curPosition);
                MatchingBlockGuide item = guideList.get(curPosition);
                getDataBinding().btnAction.setText(item != null ? item.buttonText : "");
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                getDataBinding().mi.onPageScrollStateChanged(state);
            }
        });

        getDataBinding().btnAction.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                MatchingBlockGuide item = bannerAdapter.getData(curPosition);
                if (item == null) return;
                // 跳转协议
                ProtocolHelper.parseProtocol(item.jumpProtocol);
            }
        });
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.matching_fragment_recommend_finish_without_like;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

}