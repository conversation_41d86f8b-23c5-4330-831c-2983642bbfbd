package com.kanzhun.marry.matching.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.util.LDate;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.api.MatchingApi;
import com.kanzhun.marry.matching.api.model.MatchGamesInfoModel;
import com.kanzhun.marry.matching.bean.RecommendPicBean;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.core.Observable;

public class MatchingRecommendPicFragmentViewModel extends FoundationViewModel {
    private ObservableBoolean enableSub = new ObservableBoolean(false);
    private ObservableField<String> selectSUb = new ObservableField<>("");
    private List<RecommendPicBean> pics = new ArrayList<>();
    private ObservableBoolean isFormalUser = new ObservableBoolean(false);
    private ObservableBoolean isLocked = new ObservableBoolean(false);//用户是否被锁定
    private ObservableBoolean hasLoadData = new ObservableBoolean(false);
    private ObservableBoolean shakeLocked = new ObservableBoolean(false);//心情共鸣是否被锁定
    private MutableLiveData<Boolean> loadSuccessLiveData = new MutableLiveData<>();
    private String url;
    private int remainTimes;

    public MatchingRecommendPicFragmentViewModel(@NonNull Application application) {
        super(application);
        pics.add(new RecommendPicBean(R.string.matching_empathy, 0, getResources().getString(R.string.matching_go_empathy), R.mipmap.matching_empathy, true));
        pics.add(new RecommendPicBean(R.string.matching_destiny, 0, getResources().getString(R.string.matching_go_destiny), R.mipmap.matching_destiny, true));
    }

    public ObservableBoolean getEnableSub() {
        return enableSub;
    }

    public void setEnableSub(boolean enableSub) {
        this.enableSub.set(enableSub);
    }

    public List<RecommendPicBean> getPics() {
        return pics;
    }

    public ObservableBoolean getIsFormalUser() {
        return isFormalUser;
    }

    public void setIsFormalUser(boolean isFormalUser) {
        this.isFormalUser.set(isFormalUser);
    }

    public ObservableBoolean getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(boolean isLocked) {
        this.isLocked.set(isLocked);
    }

    public ObservableBoolean getShakeLocked() {
        return shakeLocked;
    }

    public void setShakeLocked(boolean shakeLocked) {
        this.shakeLocked.set(shakeLocked);
    }

    public ObservableField<String> getSelectSUb() {
        return selectSUb;
    }

    public void setSelectSUb(String selectSUb) {
        this.selectSUb.set(selectSUb);
    }

    public MutableLiveData<Boolean> getLoadSuccessLiveData() {
        return loadSuccessLiveData;
    }

    public int getRemainTimes() {
        return remainTimes;
    }

    public ObservableBoolean getHasLoadData() {
        return hasLoadData;
    }

    public String getUrl() {
        return url;
    }

    public void getMatchGamesInfos(boolean canShowError) {
        Observable<BaseResponse<MatchGamesInfoModel>> observable = RetrofitManager.getInstance().createApi(MatchingApi.class).getMatchGamesInfos();
        HttpExecutor.execute(observable, new BaseRequestCallback<MatchGamesInfoModel>() {
            @Override
            public void handleInChildThread(MatchGamesInfoModel data) {
                super.handleInChildThread(data);
                if (data != null) {
                    if (data.shake != null) {
                        RecommendPicBean picBeanOne = pics.get(0);
                        StringBuilder stringBuilder = new StringBuilder();
                        if (data.shake.unlockTime > 0) {//封禁
                            picBeanOne.setEnableSub(false);
                            if (LDate.isToday(data.shake.unlockTime)) {
                                stringBuilder.append("今日").append(LDate.getDate(data.shake.unlockTime, LDate.HmFormat)).append("解除限制");
                            } else {
                                stringBuilder.append("功能被限制至").append(LDate.getDate(data.shake.unlockTime, LDate.yyMdDefaultFormat));
                            }
                        } else {
                            boolean canPlay = data.shake.canEnter == MatchGamesInfoModel.CAN_ENTER;
                            picBeanOne.setEnableSub(canPlay);
                            if (!canPlay && !TextUtils.isEmpty(data.shake.beginTime) && !TextUtils.isEmpty(data.shake.endTime)) {
                                stringBuilder.append("每日").append(data.shake.beginTime).append("~").append(data.shake.endTime);
                            } else {
                                stringBuilder.append(getResources().getString(R.string.matching_go_empathy));
                            }
                        }
                        picBeanOne.setSub(stringBuilder.toString());
                        MatchingRecommendPicFragmentViewModel.this.remainTimes = data.shake.remainTimes;
                    }
                    if (data.starCraft != null) {
                        MatchingRecommendPicFragmentViewModel.this.url = data.starCraft.url;
                        RecommendPicBean picBeanTow = pics.get(1);
                        StringBuilder stringBuilder = new StringBuilder();
                        if (TextUtils.isEmpty(data.starCraft.url)) {
                            picBeanTow.setEnableSub(false);
                            picBeanTow.setSub(getResources().getString(R.string.matching_go_destiny));
                        } else {
                            boolean canPlay = data.starCraft.canEnter == MatchGamesInfoModel.CAN_ENTER;
                            picBeanTow.setEnableSub(canPlay);
                            if (!canPlay && !TextUtils.isEmpty(data.starCraft.beginTime) && !TextUtils.isEmpty(data.starCraft.endTime)) {
                                stringBuilder.append("每日").append(data.starCraft.beginTime).append("~").append(data.starCraft.endTime);
                            } else {
                                stringBuilder.append(getResources().getString(R.string.matching_go_destiny));
                            }
                        }
                        picBeanTow.setSub(stringBuilder.toString());
                    }
                    loadSuccessLiveData.postValue(true);
                    hasLoadData.set(true);
                } else {
                    if (canShowError) {
                        loadSuccessLiveData.postValue(false);
                    }
                }
            }

            @Override
            public void onSuccess(MatchGamesInfoModel data) {

            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (canShowError) {
                    loadSuccessLiveData.postValue(false);
                }
            }
        });
    }
}