package com.kanzhun.marry.matching.fragment;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.decoration.GridDividerItemDecoration;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.marry.matching.BR;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.activity.MatchingReviewHistoryActivity;
import com.kanzhun.marry.matching.api.model.MyLikeBean;
import com.kanzhun.marry.matching.api.model.MyLikeItemBean;
import com.kanzhun.marry.matching.api.model.MyLikeTagBean;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.kanzhun.marry.matching.databinding.FragmentMatchingReviewLikeBinding;
import com.kanzhun.marry.matching.databinding.MatchingItemLayoutReviewLikeBinding;
import com.kanzhun.marry.matching.databinding.MatchingItemReviewLikeTagBinding;
import com.kanzhun.marry.matching.viewmodel.MatchingReviewLikeViewModel;
import com.kanzhun.marry.matching.callback.MatchingReviewLikeCallback;
import com.kanzhun.marry.matching.viewmodel.MatchingReviewListViewModel;
import com.kanzhun.utils.base.LList;

public class MatchingReviewLikeFragment extends FoundationVMShareFragment<FragmentMatchingReviewLikeBinding, MatchingReviewLikeViewModel, MatchingReviewListViewModel> implements MatchingReviewLikeCallback {

    private BaseBinderAdapter adapter;
    
    @Override
    protected void initFragment() {
        super.initFragment();
        LinearLayoutManager layoutManager = new LinearLayoutManager(activity, RecyclerView.VERTICAL, false);
        getDataBinding().recycler.setLayoutManager(layoutManager);
        adapter = new BaseBinderAdapter();
        adapter.addItemBinder(MyLikeTagBean.class, new BaseDataBindingItemBinder<MyLikeTagBean, MatchingItemReviewLikeTagBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.matching_item_review_like_tag;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MatchingItemReviewLikeTagBinding> holder, MatchingItemReviewLikeTagBinding binding, MyLikeTagBean item) {
                binding.setItem(item);
                if (!LList.isEmpty(item.likeList)) {
                    StaggeredGridLayoutManager layoutManager = new StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
                    binding.itemRecycler.setLayoutManager(layoutManager);
                    if (binding.itemRecycler.getItemDecorationCount() == 0) {
                        binding.itemRecycler.addItemDecoration(new GridDividerItemDecoration(QMUIDisplayHelper.dp2px(activity, 9)));
                    }
                    BaseBinderAdapter itemAdapter = getItemAdapter();
                    binding.itemRecycler.setAdapter(itemAdapter);
                    itemAdapter.setList(item.likeList);
                }

            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MatchingItemReviewLikeTagBinding> holder, @NonNull View view, MyLikeTagBean data, int position) {

            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {

            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MatchingItemReviewLikeTagBinding> holder, @NonNull View view, MyLikeTagBean data, int position) {

            }
        });

        getActivityViewModel().getMyLikeMutableLiveData().observe(this, new Observer<MyLikeBean>() {
            @Override
            public void onChanged(MyLikeBean myLikeBean) {
                adapter.setList(myLikeBean.result);
            }
        });
        getDataBinding().recycler.setAdapter(adapter);
    }

    private BaseBinderAdapter getItemAdapter() {
        BaseBinderAdapter itemAdapter = new BaseBinderAdapter();
        itemAdapter.addItemBinder(MyLikeItemBean.class, new BaseDataBindingItemBinder<MyLikeItemBean, MatchingItemLayoutReviewLikeBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.matching_item_layout_review_like;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MatchingItemLayoutReviewLikeBinding> holder, MatchingItemLayoutReviewLikeBinding binding, MyLikeItemBean item) {
                binding.setItem(item);
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MatchingItemLayoutReviewLikeBinding> holder, @NonNull View view, MyLikeItemBean data, int position) {
                ChatPageRouter.jumpToSingleChatActivity(activity, data.userId, data.avatar, data.nickName, false);
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.ov_photo);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MatchingItemLayoutReviewLikeBinding> holder, @NonNull View view, MyLikeItemBean data, int position) {
                if (view.getId() == R.id.ov_photo) {
                    AppUtil.startActivity(activity, MatchingReviewHistoryActivity.createIntent(activity, data.userId, true, getActivityViewModel().getLikeList(), getActivityViewModel().getSkipList(), 12));
                }
            }
        });
        return itemAdapter;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.fragment_matching_review_like;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {

    }

}