package com.kanzhun.marry.matching.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.kanzhun.foundation.api.model.MatchingBlockGuide;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.databinding.MatchingItemBlockFinishBannerBinding;
import com.youth.banner.adapter.BannerAdapter;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 2022/9/13
 */
public class BlockFinishBannerAdapter extends BannerAdapter<MatchingBlockGuide, BlockFinishBannerAdapter.ViewHolder> {

    public BlockFinishBannerAdapter(List<MatchingBlockGuide> result) {
        super(result);
    }

    @Override
    public ViewHolder onCreateHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.matching_item_block_finish_banner, parent, false);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        view.setLayoutParams(params);
        MatchingItemBlockFinishBannerBinding binding = DataBindingUtil.bind(view);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindView(ViewHolder holder, MatchingBlockGuide data, int position, int size) {
        if (data == null) return;
        MatchingItemBlockFinishBannerBinding binding = holder.getBinding();
        binding.ivIcon.load(data.icon);
        binding.tvTitle.setText(data.title);
        binding.tvSubTitle.setText(data.subtitle);
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        private MatchingItemBlockFinishBannerBinding binding;

        public ViewHolder(MatchingItemBlockFinishBannerBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        public MatchingItemBlockFinishBannerBinding getBinding() {
            return binding;
        }
    }
}
