package com.kanzhun.marry.matching.meetingplan

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.absoluteOffset
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.viewmodel.compose.viewModel
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.tooling.DevicePreviews
import com.kanzhun.common.base.compose.ui.HighlightedText
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.api.model.SortingDetailResponse
import com.kanzhun.marry.matching.api.model.SortingDetailResponse.User
import com.kanzhun.marry.matching.meetingplan.viewmodel.MatchingScene
import com.kanzhun.marry.matching.meetingplan.viewmodel.SortingMatchingViewModel
import com.kanzhun.marry.matching.meetingplan.viewmodel.SortingScene
import com.kanzhun.marry.matching.meetingplan.viewmodel.mockSorted
import com.kanzhun.marry.matching.meetingplan.viewmodel.mockSorting

@Composable
fun SortingMatching(
    onChangeTopBarBg: (showTopBarBg: Boolean) -> Unit = {},
    status: Int = 20,
    viewModel: SortingMatchingViewModel = viewModel(
        factory = SortingMatchingViewModel.Factory(
            status
        )
    ),
    inPreviewSelection: Boolean = false,
    selectedGuests: MutableList<User> = mutableStateListOf(),
    hasSubmit: Boolean = false,
    inPreview: Boolean = false,
) {
    val selGuests: MutableList<User> =
        remember { mutableStateListOf<User>().apply { addAll(selectedGuests) } }
    var inSelection by remember { mutableStateOf(inPreviewSelection) }
    val alreadySubmit by remember { mutableStateOf(hasSubmit) }

    val currentScene = viewModel.currentScene
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current

    // Add DisposableEffect to refresh data when returning to this screen
    DisposableEffect(lifecycleOwner) {
        var wasInBackground = false
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME && wasInBackground) {
                // Refresh data when returning from another screen
                if (status == 30) {
                    viewModel.getSortedResult()
                } else {
                    viewModel.getSortingDetail()
                }
                wasInBackground = false
            } else if (event == Lifecycle.Event.ON_PAUSE) {
                wasInBackground = true
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        val scrollState = rememberScrollState()

        var lastThresholdState by remember { mutableStateOf(false) }
        LaunchedEffect(scrollState.value) {
            val newState = scrollState.value > 50
            if (newState != lastThresholdState) {
                onChangeTopBarBg(newState)
                lastThresholdState = newState
            }
        }

        Column(
            modifier = Modifier
                .verticalScroll(scrollState)
                .fillMaxSize()
                .background(color = Color(0xFF0E0610))
        ) {
            Box {
                Image(
                    painter = painterResource(id = R.mipmap.matching_ic_banner_1),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(
                            375f / 240
                        )
                )

                Column(
                    modifier = Modifier.padding(top = 86.dp, start = 20.dp),
                ) {
                    Image(
                        painter = painterResource(id = R.mipmap.matching_ic_match),
                        contentDescription = null,
                        contentScale = ContentScale.Fit,
                        modifier = Modifier
                            .size(width = 124.dp, height = 52.dp)
                    )

                    HighlightedText(
                        text = currentScene?.getDescription() ?: "",
                        totalLines = 4,
                        overflow = TextOverflow.Ellipsis,
                        highlightColor = Color(0xFFFFD3B6),
                        textStyle = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFD0BCB1),
                        ),
                        modifier = Modifier.width(196.dp),
                    )
                }
            }

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        color = Color(0xFF290D2C),
                        shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
                    )
                    .offset(y = (-20).dp)
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.matching_ic_progress_bg),
                    contentDescription = null,
                    contentScale = ContentScale.Fit,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(5f / 4)
                )

                Column {
                    // 进度指示器
                    Progress(
                        currentStep = 1,
                        processList = currentScene?.getProcessList() ?: emptyList(),
                        inPreview = inPreview
                    )

                    if (currentScene is MatchingScene) {
                        Matching(
                            response = currentScene.sortedResponse,
                            inPreview = inPreview
                        )
                    } else if (currentScene is SortingScene) {
                        val response = currentScene.sortingResponse
                        MutualSelectionList(
                            inSelection = inSelection,
                            response = response,
                            onSelect = {
                                if (inSelection) {
                                    val userList = response.userList ?: emptyList()
                                    val profile = userList[it]

                                    if (selGuests.contains(profile)) {
                                        // 需要重新排序，取消选择的选项之后的序号都要-1
                                        for (i in selGuests.indices) {
                                            if (selGuests[i].selectedIndex.value > profile.selectedIndex.value) {
                                                selGuests[i].selectedIndex.value -= 1
                                            }
                                        }
                                        selGuests.remove(profile)
                                    } else {
                                        selGuests.add(profile)
                                    }

                                    profile.selectedIndex.value =
                                        if (profile.selectedIndex.value > 0) 0 else selGuests.size

                                    selGuests.sortWith { l, r ->
                                        if (l.selectedIndex.value > r.selectedIndex.value) {
                                            1
                                        } else if (l.selectedIndex.value < r.selectedIndex.value) {
                                            -1
                                        } else {
                                            0
                                        }
                                    }
                                }
                            },
                            onLongClick = { _, _ ->
                                // 进入选择模式，如果当前是非选择模式
                                if (!inSelection) {
                                    inSelection = true
                                }
                            },
                            inPreview = inPreview
                        )

                        var openDetentionDialog by remember { mutableStateOf(false) }
                        if (openDetentionDialog) {
                            val onDismiss = { openDetentionDialog = false }
                            Dialog(
                                onDismissRequest = onDismiss,
                                properties = DialogProperties(
                                    dismissOnBackPress = false,
                                    dismissOnClickOutside = false
                                ),
                            ) {
                                DetentionDialogContent(
                                    onExit = {
                                        inSelection = false

                                        // 重置已选的排序
                                        selGuests.forEach {
                                            it.selectedIndex.value = 0
                                        }
                                        selGuests.clear()

                                        onDismiss()
                                    },
                                    onContinueSort = onDismiss
                                )
                            }
                        }

                        BackHandler(enabled = inSelection) {
                            // 挽留弹框
                            openDetentionDialog = true
                        }
                    }
                }
            }
        }

        var openSubmitDialog by remember { mutableStateOf(false) }

        val allowSort = currentScene is SortingScene
                && currentScene.sortingResponse.allowSortSubmitTime
            ?.let { it < System.currentTimeMillis() } == true

        if (allowSort && !alreadySubmit) {
            val enabled = (inSelection && selGuests.size
                    ==
                    (currentScene.sortingResponse.userList ?: emptyList()).size) // 全选了，才能提交
                    || !inSelection // 点击进入选择模式
            BeginSort(
                btnText = if (inSelection) "提交互选排序" else "开始排序",
                enabled = enabled,
                selectedGuests = selGuests,
                onClick = {
                    if (selGuests.isEmpty()) {
                        inSelection = !inSelection
                    } else {
                        // 提交互选排序
                        openSubmitDialog = true
                    }
                },
                inPreview = inPreview
            )
        }

        if (openSubmitDialog) {
            val onDismiss = { openSubmitDialog = false }
            Dialog(
                onDismissRequest = onDismiss,
                properties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false
                )
            ) {
                SubmitContent(
                    dialogContent = if (currentScene is SortingScene) currentScene.sortingResponse.dialogContent
                        ?: "" else "",
                    selectedGuests = selGuests,
                    onCancel = onDismiss,
                    onSubmit = {
                        viewModel.submitSorting(
                            userIds =
                                selGuests.filter { it.userId?.isNotEmpty() == true }
                                    .joinToString(",") { it.userId.toString() },
                            onSuccess = {
                                viewModel.getSortedResult()
                                onDismiss()
                            }
                        )
                    }
                )
            }
        }
    }
}

@Composable
private fun DetentionDialogContent(
    onExit: () -> Unit = {},
    onContinueSort: () -> Unit = {},
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(32.dp))
    ) {
        val (content, bg) = createRefs()

        Image(
            painterResource(R.mipmap.matching_ic_detention_dialog_bg),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .constrainAs(bg) {
                    bottom.linkTo(content.bottom)
                    top.linkTo(content.top)
                    start.linkTo(content.start)
                    end.linkTo(content.end)

                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
                .fillMaxWidth()
        )

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp)
                .padding(top = 24.dp, bottom = 28.dp)
                .constrainAs(content) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)

                    width = Dimension.fillToConstraints
                }) {
            Text(
                text = "退出将清空排序选项，确定要退出吗？",
                style = TextStyle(
                    fontSize = 20.sp,
                    fontFamily = boldFontFamily(),
                    fontWeight = FontWeight(700),
                    color = Color(0xFFFFFFFF),
                )
            )

            Spacer(modifier = Modifier.height(32.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "退出",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFD9A877),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .border(
                            width = 1.dp,
                            brush = Brush.horizontalGradient(
                                listOf(Color(0xFFF2D9B5), Color(0xFFD9A877))
                            ),
                            shape = RoundedCornerShape(25.dp)
                        )
                        .padding(vertical = 12.dp)
                        .noRippleClickable { onExit() }
                )

                Text(
                    text = "继续排序",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF191919),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .background(
                            brush = Brush.horizontalGradient(
                                listOf(Color(0xFFF2D9B5), Color(0xFFD9A877))
                            ),
                            shape = RoundedCornerShape(25.dp)
                        )
                        .padding(vertical = 12.dp)
                        .noRippleClickable { onContinueSort() }
                )
            }
        }
    }
}

@Composable
private fun SubmitContent(
    dialogContent: String = "",
    selectedGuests: MutableList<User> = mutableListOf(),
    onCancel: () -> Unit = {},
    onSubmit: () -> Unit = {},
    inPreview: Boolean = false
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(32.dp))
    ) {
        val (content, bg) = createRefs()

        Image(
            painterResource(R.mipmap.matching_ic_submit_dialog_bg),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .constrainAs(bg) {
                    bottom.linkTo(content.bottom)
                    top.linkTo(content.top)
                    start.linkTo(content.start)
                    end.linkTo(content.end)

                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
                .fillMaxWidth()
        )

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp)
                .padding(top = 24.dp, bottom = 28.dp)
                .constrainAs(content) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)

                    width = Dimension.fillToConstraints
                }) {
            Text(
                text = "提交嘉宾排序",
                style = TextStyle(
                    fontSize = 20.sp,
                    fontFamily = boldFontFamily(),
                    fontWeight = FontWeight(700),
                    color = Color(0xFFFFFFFF),
                )
            )

            Spacer(modifier = Modifier.height(12.dp))

            ContentText(desc = dialogContent)

            Spacer(modifier = Modifier.height(24.dp))

            SortedGuestGrid(selectedGuests = selectedGuests, inPreview = inPreview)

            Spacer(modifier = Modifier.height(36.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "我再想想",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFD9A877),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .border(
                            width = 1.dp,
                            brush = Brush.horizontalGradient(
                                listOf(Color(0xFFF2D9B5), Color(0xFFD9A877))
                            ),
                            shape = RoundedCornerShape(25.dp)
                        )
                        .padding(vertical = 12.dp)
                        .noRippleClickable { onCancel() }
                )

                Text(
                    text = "确定提交",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF191919),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .background(
                            brush = Brush.horizontalGradient(
                                listOf(Color(0xFFF2D9B5), Color(0xFFD9A877))
                            ),
                            shape = RoundedCornerShape(25.dp)
                        )
                        .padding(vertical = 12.dp)
                        .noRippleClickable { onSubmit() }
                )
            }
        }
    }
}

@Composable
private fun ContentText(desc: String = "") {
    HighlightedText(
        text = desc,
        highlightColor = Color(0xFFFFD3B6),
        textStyle = TextStyle(
            fontSize = 13.sp,
            lineHeight = 18.sp,
            fontWeight = FontWeight(400),
            color = Color(0xFFD0BCB1),
        )
    )
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun SortedGuestGrid(
    selectedGuests: MutableList<User> = mutableListOf(),
    inPreview: Boolean = false
) {
    FlowRow(
        modifier = Modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        maxItemsInEachRow = 4
    ) {
        repeat(selectedGuests.size) { index ->
            val profile = selectedGuests[index]

            Box {
                val mod = Modifier
                    .size(54.dp)
                    .clip(CircleShape)
                    .border(width = 1.5.dp, color = Color(0xFFD9A877), shape = CircleShape)
                    .background(color = Color(0x66191919))
                if (inPreview) {
                    Image(
                        painter = painterResource(id = R.mipmap.matching_ic_stub_guest),
                        contentDescription = "image description",
                        modifier = mod
                    )
                } else {
                    OImageView2(
                        imageUrl = profile.tinyAvatar,
                        modifier = mod
                    )
                }

                val selectedIndex = profile.selectedIndex
                val modifier = Modifier
                    .size(20.dp)
                    .clip(CircleShape)
                    .align(Alignment.TopEnd)
                if (selectedIndex.value > 0) {
                    SelectedIndex(
                        modifier = modifier,
                        fontSize = 13.sp,
                        selectedIndex = selectedIndex.value
                    )
                } else {
                    Image(
                        painter = painterResource(id = R.mipmap.matching_ic_unsel),
                        contentDescription = "image description",
                        modifier = modifier
                    )
                }
            }
        }
    }
}

@Composable
fun SelectedIndex(
    modifier: Modifier = Modifier,
    fontSize: TextUnit = 18.sp,
    selectedIndex: Int = 1
) {
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(
                brush = Brush.horizontalGradient(
                    listOf(
                        Color(0xFFF2D9B5),
                        Color(0xFFD9A877)
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "$selectedIndex",
            style = TextStyle(
                fontSize = fontSize,
                fontWeight = FontWeight(700),
                color = Color(0xFF732E0B),
            )
        )
    }
}

@Composable
fun MutualSelectionList(
    inSelection: Boolean = false,
    response: SortingDetailResponse,
    onSelect: (Int) -> Unit = {},
    onLongClick: (Int, User) -> Unit = { _, _ -> },
    inPreview: Boolean = false
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .padding(bottom = if (inSelection) (60 + 78 + 16).dp else (78 + 16).dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        response.userList?.forEachIndexed { index, user ->
            ProfileCard(
                inSelection = inSelection,
                index = index,
                user = user,
                onClickIndex = {
                    onSelect(index)
                },
                onSeeReport = {
                    ProtocolHelper.parseProtocol(user.analysisJumpUrl)
                },
                onLongClick = { onLongClick(index, user) },
                inPreview = inPreview
            )
        }
    }
}

@Composable
private fun ProfileCard(
    inSelection: Boolean = false,
    index: Int,
    user: User,
    onClickIndex: () -> Unit = {},
    onSeeReport: () -> Unit = {},
    onLongClick: () -> Unit = {},
    inPreview: Boolean = false
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        modifier = Modifier
    ) {
        val selectedIndex = user.selectedIndex
        val isSelected = selectedIndex.value > 0

        if (inSelection) {
            val modifier = Modifier
                .size(40.dp)
                .noRippleClickable {
                    onClickIndex()
                }
            if (isSelected) {
                SelectedIndex(modifier = modifier, selectedIndex = selectedIndex.value)
            } else {
                Image(
                    painter = painterResource(id = R.mipmap.matching_ic_unsel),
                    contentDescription = "image description",
                    modifier = modifier
                )
            }
        }

        Box(
            modifier = Modifier
                .pointerInput(Unit) {
                    detectTapGestures(
                        onTap = { onSeeReport() },
                        onLongPress = { onLongClick() }
                    )
                }
                .fillMaxWidth()
                .border(
                    width = 1.dp,
                    color = Color(0xFF292020),
                    shape = RoundedCornerShape(size = 16.dp)
                )
                .background(
                    color = if (isSelected) Color(0xFFFFF5E8) else Color(0xFFFFFFFF),
                    shape = RoundedCornerShape(size = 16.dp)
                )
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val modifier = Modifier
                        .size(64.dp)
                        .clip(CircleShape)
                        .border(
                            width = 2.dp,
                            brush = Brush.horizontalGradient(
                                listOf(
                                    Color(0xFFF2D9B5),
                                    Color(0xFFD9A877)
                                )
                            ),
                            shape = CircleShape
                        )
                    if (inPreview) {
                        Image(
                            painter = painterResource(id = R.mipmap.ic_launcher),
                            contentDescription = "Profile Picture",
                            modifier = modifier
                        )
                    } else {
                        OImageView2(imageUrl = user.tinyAvatar, modifier = modifier)
                    }

                    Spacer(modifier = Modifier.width(12.dp))

                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = user.nickName ?: "",
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF292929),
                                ),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.weight(weight = 1f, fill = false)
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            MatchingRate(
                                modifier = Modifier.padding(end = (if (inSelection) 0 else 36).dp),
                                matchScore = user.matchScore ?: ""
                            )
                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        val tags = user.recommendTag ?: emptyList()

                        Row(verticalAlignment = Alignment.CenterVertically) {
                            OneLineTags(
                                modifier = Modifier.weight(weight = 1f),
                                tags = tags
                            )

                            if (tags.isNotEmpty()) {
                                Image(
                                    painter = painterResource(id = R.mipmap.ic_white_arrow_right),
                                    contentDescription = "image description",
                                    colorFilter = ColorFilter.tint(Color(0xFFB8B8B8)),
                                    modifier = Modifier
                                        .size(20.dp)
                                        .padding(3.5.dp)
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(5.dp))

                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                text = user.intro ?: "",
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF7F7F7F),
                                ),
                                maxLines = if (tags.isEmpty()) 2 else 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.weight(weight = 1f)
                            )

                            if (tags.isEmpty()) {
                                Image(
                                    painter = painterResource(id = R.mipmap.ic_white_arrow_right),
                                    contentDescription = "image description",
                                    colorFilter = ColorFilter.tint(Color(0xFFB8B8B8)),
                                    modifier = Modifier
                                        .size(20.dp)
                                        .padding(3.5.dp)
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = buildAnnotatedString {
                        withStyle(
                            style = SpanStyle(
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF7C2800)
                            )
                        ) {
                            append("匹配建议：")
                        }
                        append(user.matchAnalysisOverview ?: "")
                    },
                    fontSize = 14.sp,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            brush = Brush.horizontalGradient(
                                if (isSelected)
                                    listOf(
                                        Color(0xFFFFE7D3),
                                        Color(0x00FFE9D4)
                                    )
                                else listOf(
                                    Color(0xFFFFF0EB),
                                    Color(0xFFFFFBF7)
                                )
                            ),
                            shape = RoundedCornerShape(size = 10.dp)
                        )
                        .padding(horizontal = 16.dp, vertical = 12.dp)
                )
            }

            if (!inSelection) {
                Box(
                    modifier = Modifier
                        .align(Alignment.TopEnd),
                ) {
                    Image(
                        painter = painterResource(id = R.mipmap.matching_ic_order),
                        contentDescription = null,
                        modifier = Modifier.size(width = 52.dp, height = 44.dp)
                    )

                    Text(
                        text = "${index + 1}",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(600),
                            color = Color(0xFF732E0B),
                        ),
                        modifier = Modifier.padding(top = 6.dp, start = 29.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun BoxScope.BeginSort(
    btnText: String,
    enabled: Boolean = false,
    selectedGuests: MutableList<User> = mutableListOf(),
    onClick: () -> Unit = {},
    inPreview: Boolean = false,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color(0xFF290D2C))
            .padding(horizontal = 20.dp)
            .align(Alignment.BottomCenter),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (selectedGuests.isNotEmpty()) {
            SelectedLayout(selectedGuests = selectedGuests, inPreview = inPreview)
        }

        val disabledBtnColor = colorResource(R.color.common_color_CCCCCC)
        Text(
            text = btnText,
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(600),
                color = if (enabled) Color(0xFF292929) else Color.White,
                textAlign = TextAlign.Center,
            ),
            modifier = Modifier
                .fillMaxWidth()
                .noRippleClickable {
                    if (enabled) {
                        onClick()
                    }
                }
                .padding(vertical = 16.dp)
                .conditional(enabled) {
                    background(
                        brush = Brush.horizontalGradient(
                            listOf(
                                Color(0xFFD9A877),
                                Color(0xFFF2D9B5)
                            )
                        ),
                        shape = RoundedCornerShape(25.dp)
                    )
                }
                .conditional(!enabled) {
                    background(
                        color = disabledBtnColor,
                        shape = RoundedCornerShape(25.dp)
                    )
                }
                .padding(vertical = 12.dp)
        )
    }
}

@Composable
private fun SelectedLayout(
    selectedGuests: MutableList<User> = mutableListOf(),
    inPreview: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "当前:",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFFA99999),
                letterSpacing = 0.5.sp,
            )
        )

        // 参加嘉宾头像列表
        Row(
            modifier = Modifier
                .horizontalScroll(rememberScrollState())
                .weight(1f)
        ) {
            Box(modifier = Modifier.width(((selectedGuests.size + 1) * 24).dp)) {
                selectedGuests.forEachIndexed { index, guest ->
                    val modifier = Modifier
                        .size(36.dp)
                        .align(Alignment.CenterStart)
                        .absoluteOffset(x = (index * 24).dp) // 每个头像间隔24dp
                        .clip(CircleShape)
                        .border(
                            width = 1.5.dp,
                            brush = Brush.horizontalGradient(
                                listOf(
                                    Color(0xFFF2D9B5),
                                    Color(0xFFD9A877)
                                )
                            ),
                            shape = CircleShape
                        )
                        .background(color = Color(0x66191919))

                    if (inPreview) {
                        Image(
                            painter = painterResource(id = R.mipmap.matching_ic_stub_guest),
                            contentDescription = null,
                            modifier = modifier
                        )
                    } else {
                        OImageView2(
                            imageUrl = guest.tinyAvatar,
                            modifier = modifier
                        )
                    }
                }
            }
        }
    }
}

@Preview(heightDp = 2000)
@Composable
private fun MutualSelectionScreenPreview() {
    SortingMatching(
        viewModel = viewModel<SortingMatchingViewModel>().apply {
            currentScene = SortingScene(mockSorting).apply {
                sortingResponse.allowSortSubmitTime = System.currentTimeMillis() + 10000
            }
        },
        inPreview = true
    )
}

@Preview(heightDp = 2000)
@Composable
private fun MutualSelectionScreenSelectionPreview() {
    mockSorting.userList?.forEachIndexed { index, user ->
        user.apply {
            selectedIndex.value = index + 1
        }
    }

    SortingMatching(
        viewModel = viewModel<SortingMatchingViewModel>().apply {
            currentScene = SortingScene(mockSorting).apply {
                sortingResponse.allowSortSubmitTime = System.currentTimeMillis() + 10000
            }
        },
        inPreviewSelection = true,
        selectedGuests = SnapshotStateList<User>().apply {
            mockSorting.userList?.let {
                addAll(it)
            }
        },
        inPreview = true
    )
}

@Preview
@Composable
private fun DetentionDialogContentPreview() {
    DetentionDialogContent()
}

@DevicePreviews
@Composable
private fun SubmitContentPreview() {
    SubmitContent(
        selectedGuests = mutableListOf<User>().apply {
            mockSorting.userList?.let {
                addAll(it)
            }
        },
        dialogContent = mockSorting.dialogContent ?: "",
        inPreview = true
    )
}

@Preview(heightDp = 2000)
@Composable
private fun MutualSelectionScreenSubmitPreview() {
    SortingMatching(
        viewModel = viewModel<SortingMatchingViewModel>().apply {
            currentScene = MatchingScene(mockSorted)
        },
        hasSubmit = true,
        inPreview = true
    )
}