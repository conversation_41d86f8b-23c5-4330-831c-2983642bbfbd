package com.kanzhun.marry.matching.api.model

import com.google.gson.annotations.SerializedName
import com.kanzhun.foundation.model.profile.OpenScreen
import java.io.Serializable

/*

    {
        "openScreenInfo": { //开屏测试结果解析 可空
            "mask": true, // 是否打码遮挡
            "status": 0, //是否完成开屏测试 1:是 0:否
            "loginUserStatus": 0, //登录用户是否完成开屏测试 1:是 0:否
            "testUrl": "url", // 测试 h5 地址
            "mbtiKeys": ["犀利", "雄心", "高瞻远瞩"], //关键字 如:"犀利", "雄心", "高瞻远瞩"
            "mbtiName": "猫头鹰", //mbti名称 如:猫头鹰
            "mbtiCode": "INTJ", //mbti对应code 如:INTJ
            "attachment": "安全型", //依恋类型 如:安全型
            "title": "我的恋爱性格", //标题 我的恋爱性格
            "bgHeadImg": "http://www.xx.com", // 背景头图
            "bgColor": "#234355", // 背景颜色
            "icon": "https://icon.png", // 图标地址
            "bar": "https://bar.png", // bar 背景地址
            "linkUrl": "https://xxxxxx", // H5页面跳转链接
            "mbtiScore": [{
                "code": "MBTI_I_E", // 维度 code
                "left": "内倾", // 左侧文案
                "right": "外倾", // 右侧文案
                "score": 3, // 得分
                "maxScore": 5, //最高分
                //"ourMatchContent": "你们会用类似的方式去为人处事和与世界互动，都是内敛的，更喜欢在自己的独立空间里享受生活。" // 匹配文案，非自己返回
            }],
            "matchInfo": { // 非自己看自己时返回
                "match": true,
                "matchContent": "堪称天作之合的搭配，留一分怕你们骄傲", // 匹配度文案
                "matchTips": "#对方mbti和动物类型#和你是#分数对应的文案#", // 提示条文案
                "score": 97, // 匹配分值
                "loginUser": {
                    "tinyAvatar": "xxxx",
                    "mbtiName": "猫头鹰", //mbti名称 如:猫头鹰
                    "mbtiCode": "INTJ", //mbti对应code 如:INTJ
                },
                "viewUser": {
                    "tinyAvatar": "xxxx",
                    "mbtiName": "猫头鹰", //mbti名称 如:猫头鹰
                    "mbtiCode": "INTJ", //mbti对应code 如:INTJ
                },
                "compareContent": [{
                    "left": "内倾", // 左侧文案
                    "right": "外倾", // 右侧文案
                    "ourMatchContent": "你们会用类似的方式去为人处事和与世界互动，都是内敛的，更喜欢在自己的独立空间里享受生活。" // 匹配文案，非自己返回
                }]
            }
        },
        "bigFiveTest": {
            "status": 1, // 1 未完成 2 已完成
            "title": "大五人格测试",
            "content": "基于经典心理学大五人格模型，科学解析你在亲密关系中的特质与潜力",
            "scores": {
                "neuroticism": { // 情绪性/神经质
                    "score": 0, // 当前得分
                    "maxScore": 100 // 满分值
                },
                "conscientiousness": { // 尽责性
                    "score": 0,
                    "maxScore": 100
                },
                "agreeableness": { // 宜人性
                    "score": 0,
                    "maxScore": 100
                },
                "openness": { // 开放性
                    "score": 0,
                    "maxScore": 100
                },
                "extraversion": { // 外向性
                    "score": 0,
                    "maxScore": 100
                }
            },
        },
        "preMarriageQuest": {
            "status": 1, // 1 未完成 2 已完成
            "title": "婚前看准10问",
            "content": "看准核心分歧，透视深层的价值观差异，识别真正同频的那个Ta"
        },
        "testReport": {
            "title": "婚恋供需探测报告",
            "content": "提供多维婚恋竞争力分析，了解你在婚恋关系中的实际供需，深度了解，精准匹配",
            "status": 1, //状态：1-评估中；2-已结束
            "jumpUrl": "http://xx", //跳转链接，如果是评估中，则字段返回空字符串
        }
    }

 */

data class TestModuleResponse(
    @SerializedName("openScreenInfo")
    var openScreenInfo: OpenScreen? = null,
    @SerializedName("bigFiveTest")
    var bigFiveTest: BigFiveTest? = null,
    @SerializedName("preMarriageQuest")
    var preMarriageQuest: TestReport? = null,
    @SerializedName("testReport")
    var testReport: TestReport? = null
) : Serializable {
    data class BigFiveTest(
        @SerializedName("status")
        var status: Int? = null,
        @SerializedName("title")
        var title: String? = null,
        @SerializedName("content")
        var content: String? = null,
        @SerializedName("scores")
        var scores: Scores? = null,
        @SerializedName("jumpUrl")
        var jumpUrl: String? = null
    ) : Serializable {
        data class Scores(
            @SerializedName("neuroticism")
            var neuroticism: Score? = null,
            @SerializedName("conscientiousness")
            var conscientiousness: Score? = null,
            @SerializedName("agreeableness")
            var agreeableness: Score? = null,
            @SerializedName("openness")
            var openness: Score? = null,
            @SerializedName("extraversion")
            var extraversion: Score? = null
        ) {
            data class Score(
                @SerializedName("score")
                var score: Int? = null,
                @SerializedName("maxScore")
                var maxScore: Int? = null
            ) : Serializable {
                fun getProgress(): Float {
                    return (score?.toFloat() ?: 0f) / (maxScore?.toFloat() ?: 100f)
                }
            }
        }
    }
}
