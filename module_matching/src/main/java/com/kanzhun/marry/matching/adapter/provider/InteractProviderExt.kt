package com.kanzhun.marry.matching.adapter.provider

import android.view.View
import android.widget.TextView
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.bean.InteractUserBean

fun setInteractListTag(tv1: TextView, divider: View, tv2: TextView, list: List<String>?) {
    if (list.isNullOrEmpty()) {
        tv1.gone()
        divider.gone()
        tv2.gone()
    } else {
        tv1.textOrGone(list[0])
        divider.visible(list.size > 1)
        if (list.size > 1) {
            tv2.textOrGone(list[1])
        }else{
            tv2.gone()
        }
    }
}

fun InteractUserBean.getStatusString(): String {
    return if (likeEachOther) {
        R.string.matching_like_each_other_1.toResourceString()
    } else {
        when (relationStatus) {
            20, 30, 40 -> {
                "已开启聊天"
            }

            else -> {
                ""
            }

        }
    }
}