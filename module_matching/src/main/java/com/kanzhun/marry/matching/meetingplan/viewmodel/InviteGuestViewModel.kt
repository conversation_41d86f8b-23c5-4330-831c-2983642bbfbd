package com.kanzhun.marry.matching.meetingplan.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.logic.service.SecurityNoticeManager.handelSecurityNotice
import com.kanzhun.foundation.ui.moment.toast
import com.kanzhun.http.createApi
import com.kanzhun.marry.matching.api.KMatchingApi
import com.kanzhun.marry.matching.api.model.InviteListResponse
import com.kanzhun.marry.matching.api.model.InviteListResponse.Description
import com.kanzhun.marry.matching.api.model.InviteListResponse.InviteUser
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

private const val TAG = "InviteGuestViewModel"

val inviteListMock = InviteListResponse(
    planId = "PLAN_20230715_001",
    meetPlanStatus = 30, // 匹配中
    descriptionList = listOf(
        Description(
            title = "为什么没有匹配上其他嘉宾？",
            content = "平台根据双方的\u200b学历、职业、兴趣爱好\u2060等 27 个维度进行匹配，\u200b人气越高\u2060的嘉宾匹配\u200b难度越大\u2060"
        ),
        Description(
            title = "如何提高匹配成功率？",
            content = "完善个人资料（+35%成功率），每日登录获得\u200b曝光加成\u2060，主动邀请心仪嘉宾"
        ),
        Description(
            title = "如何快速/高效/合理地奔现？",
            content = "每日登录获得\u200b曝光加成\u2060，主动邀请\u200b心仪嘉宾\u2060，主动聊天，主动约见！"
        ),
    ),
    userList = listOf(
        InviteUser(
            userId = "U202303001",
            tinyAvatar = "https://randomuser.me/api/portraits/thumb/women/12.jpg",
            nickName = "张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯张晓雯",
            intro = "外企市场总监，喜欢徒步和古典音乐",
            matchScore = 92,
            recommendTag = listOf(
                "32岁",
                "168cm",
                "硕士",
                "年入60w+",
                "35岁",
                "178cm",
                "P8级",
                "有房有车"
            ),
            sortNum = 1,
            inviteStatus = 1, // 已接受
            matchStatus = 10 // 系统匹配
        ),
        InviteUser(
            userId = "U202304015",
            tinyAvatar = "https://randomuser.me/api/portraits/thumb/men/45.jpg",
            nickName = "李浩然",
            intro = "互联网架构师，马拉松爱好者",
            matchScore = 88,
            recommendTag = listOf("35岁", "178cm", "P8级", "有房有车"),
            sortNum = 2,
            inviteStatus = 3, // 待确认
            matchStatus = 20 // 补充邀请
        ),
        InviteUser(
            userId = "U202305022",
            tinyAvatar = "https://randomuser.me/api/portraits/thumb/women/67.jpg",
            nickName = "陈思雨",
            intro = "自由插画师，养了两只布偶猫",
            matchScore = 95,
            recommendTag = listOf("28岁", "162cm", "艺术世家", "年入40w"),
            sortNum = 0,
            inviteStatus = 0, // 已拒绝
            matchStatus = 30 // 解除匹配
        ),
        InviteUser(
            userId = "U202306109",
            tinyAvatar = "https://randomuser.me/api/portraits/thumb/men/89.jpg",
            nickName = "王振宇",
            intro = "三甲医院主治医师，喜欢潜水",
            matchScore = 90,
            recommendTag = listOf("37岁", "182cm", "医学博士", "健身达人"),
            sortNum = 3,
            inviteStatus = 0, // 未邀请
            matchStatus = 0
        ),
        InviteUser(
            userId = "U202307204",
            tinyAvatar = "https://randomuser.me/api/portraits/thumb/women/32.jpg",
            nickName = "林雅婷",
            intro = "大学讲师，国家二级心理咨询师",
            matchScore = 93,
            recommendTag = listOf("30岁", "165cm", "书香门第", "擅长烹饪"),
            sortNum = 4,
            inviteStatus = 2,
            matchStatus = 10
        ),
        InviteUser(
            userId = "U202308017",
            tinyAvatar = "https://randomuser.me/api/portraits/thumb/men/53.jpg",
            nickName = "周子轩",
            intro = "投行精英，红酒收藏爱好者",
            matchScore = 91,
            recommendTag = listOf("33岁", "180cm", "常春藤毕业", "海外经历"),
            sortNum = 5,
            inviteStatus = 1,
            matchStatus = 20
        )
    )
)

class InviteGuestViewModel : BaseViewModel() {
    var inviteListResponse by mutableStateOf<InviteListResponse?>(null)

    init {
        getInviteList()
    }

    private fun getInviteList() {
        viewModelScope.launch {
            try {
                val response =
                    withContext(Dispatchers.IO) { createApi(KMatchingApi::class.java).getInviteList() }
                if (response.isSuccess) {
                    inviteListResponse = response.data
                } else if (response.isSecurityNotice) {
                    handelSecurityNotice(response)
                } else {
                    TLog.error(TAG, "getChatList: ${response.msg}")
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "getChatList: $e")
            }
        }
    }

    fun sendInvite(user: InviteUser, onSendSuccess: () -> Unit = {}) {
        viewModelScope.launch {
            try {
                val response =
                    withContext(Dispatchers.IO) {
                        createApi(KMatchingApi::class.java).submitInvite(
                            planId = inviteListResponse?.planId ?: "",
                            matchUserId = user.userId ?: "",
                            inviteMsg = user.userInput ?: ""
                        )
                    }
                if (response.isSuccess) { // 刷新数据
                    getInviteList()

                    onSendSuccess()
                } else if (response.isSecurityNotice) {
                    handelSecurityNotice(response)
                } else {
                    TLog.error(TAG, "sendInvite: ${response.msg}")

                    toast(response.msg)
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "sendInvite: $e")
            }
        }
    }
}