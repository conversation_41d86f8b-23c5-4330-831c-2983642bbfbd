package com.kanzhun.marry.matching.adpter.home

import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.common.kotlin.ui.recyclerview.CommonListAdapter
import com.kanzhun.foundation.bean.CommonItemSpaceProvider
import com.kanzhun.foundation.utils.AppTheme
import com.kanzhun.foundation.utils.THEME
import com.kanzhun.marry.matching.api.model.IPageBean
import com.kanzhun.marry.matching.api.model.TomorrowRecommendData
import com.kanzhun.marry.matching.databinding.MatchingRecommendPagerTomorrowItem2Binding
import com.kanzhun.marry.matching.item.MatchingTomorrowHeaderBean
import com.kanzhun.marry.matching.item.MatchingTomorrowUserBean
import com.kanzhun.marry.matching.provider.MatchingTomorrowHeaderProvider
import com.kanzhun.marry.matching.provider.MatchingTomorrowUserProvider
import com.kanzhun.marry.matching.type.MatchingTomorrowItemType

class MatchingRecommendTomorrowViewHolder(private val mBinding: MatchingRecommendPagerTomorrowItem2Binding) : IBaseMatchingPageViewHolder(mBinding) {

    val adapter: CommonListAdapter<BaseListItem> = CommonListAdapter()

    override fun onBindView(data: IPageBean, position: Int, size: Int) {
        if (data is TomorrowRecommendData) {
            mBinding.initRecyclerView()
            adapter.setNewInstance(data.convertToDataList())
        }
        when(AppTheme.getTheme()){
            THEME.NORMAL -> {
            }
            THEME.CHRISTMAS -> {

            }
        }

    }


    private fun TomorrowRecommendData.convertToDataList(): MutableList<BaseListItem> {
        val tempList = mutableListOf<BaseListItem>()
        val recommendList = userList ?: listOf()
        tempList.add(MatchingTomorrowHeaderBean(this).also { it.mLocalItemType = MatchingTomorrowItemType.ITEM_HEADER.value })
        recommendList.forEachIndexed { index, user ->
            tempList.add(MatchingTomorrowUserBean(user).also { it.mLocalItemType = MatchingTomorrowItemType.ITEM_USER.value })
        }
        return tempList

    }

    private fun MatchingRecommendPagerTomorrowItem2Binding.initRecyclerView() {
        val context = mBinding.root.context
        mBinding.rvList.layoutManager = GridLayoutManager(context, 2).also {
            it.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return adapter.getItemViewType(position).let { type ->
                        if (type == MatchingTomorrowItemType.ITEM_USER.value) 1 else 2
                    }
                }
            }
        }
        adapter.registerItemProvider(MatchingTomorrowItemType.ITEM_HEADER.value, MatchingTomorrowHeaderProvider(context as FragmentActivity))
        adapter.registerItemProvider(MatchingTomorrowItemType.ITEM_USER.value, MatchingTomorrowUserProvider(context))
        adapter.registerItemProvider(MatchingTomorrowItemType.ITEM_SPACE.value, CommonItemSpaceProvider(context))
        mBinding.rvList.adapter = adapter
    }

}

