package com.kanzhun.marry.matching.adpter.home

import android.widget.TextView
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.foundation.kotlin.ktx.toAgeString
import com.kanzhun.foundation.kotlin.ktx.toHeightString
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.TaskPageRouter
import com.kanzhun.foundation.utils.CertificationIdentifySource
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.api.model.HomeRecommendBlockData
import com.kanzhun.marry.matching.api.model.IPageBean
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.kanzhun.marry.matching.databinding.MatchingRecommendBlockUserItemBinding
import com.kanzhun.marry.matching.databinding.MatchingRecommendPagerGuideBlockItemBinding

/**
 * 新手引导阻断
 */
class MatchingHomeGuideBlockViewHolder(val mBinding: MatchingRecommendPagerGuideBlockItemBinding) : IBaseMatchingPageViewHolder(mBinding) {
    override fun onBindView(data: IPageBean, position: Int, size: Int) {
        if (data is HomeRecommendBlockData) {
            val blockInfo = data.blockInfo ?: return

            when {
                blockInfo.baseInfoBlock -> { //注册用户，引导填写个人信息
                    mBinding.blockContent.run {
                        tvContent.text = "Lv1·丰富个人信息"
                        tvDesc.text = "审核成功后解锁喜欢功能和两位新嘉宾"
                        btExpose.text = "去填写个人信息"
                        btExpose.reportExpose()
                        btExpose.clickWithTrigger {
                            it.reportClick()
                            TaskPageRouter.jumpToTaskNewUserActivity(it.context, blockInfo,PageSource.F1_RECOMMEND_CHILD_FRAGMENT_LOCK_CARS)
                        }
                    }
                }

                blockInfo.faceCertBlock -> { //未完成实名认证
                    mBinding.blockContent.run {
                        iv1.setImageResource(R.drawable.matching_icon_guide_complete)
                        iv2.setImageResource(R.drawable.matching_icon_guide_avatar_complete)
                        tvContent.text = "Lv2·实名及头像认证"
                        tvDesc.text = "审核成功后可无限发送喜欢并解锁两位新嘉宾"
                        btExpose.text = "去完成实名与头像认证"
                        btExpose.reportExpose()
                        btExpose.clickWithTrigger {
                            it.reportClick()
                            MePageRouter.jumpToCertificationActivity(it.context, CertificationIdentifySource.MAIN_TAB,
                                blockInfo,PageSource.F1_RECOMMEND_CHILD_FRAGMENT_LOCK_CARS)
                        }
                    }
                }

                blockInfo.avatarCertBlock -> { //未完成头像认证
                    mBinding.blockContent.run {
                        iv1.setImageResource(R.drawable.matching_icon_guide_complete)
                        iv2.setImageResource(R.drawable.matching_icon_guide_avatar_complete)
                        tvContent.text = "Lv2·头像认证"
                        tvDesc.text = "审核成功后可无限发送喜欢并解锁两位新嘉宾"
                        btExpose.text = "去完成头像认证"
                        btExpose.reportExpose()
                        btExpose.clickWithTrigger {
                            it.reportClick()
                            MePageRouter.jumpToMeAvatarAuthActivity(it.context,PageSource.F1_RECOMMEND_CHILD_FRAGMENT_LOCK_CARS)
                        }
                    }
                }

                blockInfo.eduCertBlock -> { //未完成学历认证
                    mBinding.blockContent.run {
                        iv1.setImageResource(R.drawable.matching_icon_guide_complete)
                        iv2.setImageResource(R.drawable.matching_icon_guide_complete)
                        iv3.setImageResource(R.drawable.matching_icon_guide_edu_complete)
                        tvContent.text = "Lv3·学历认证"
                        tvDesc.text = "审核成功后可解锁平台全部功能及全部推荐嘉宾"
                        btExpose.text = "去完成学历认证"
                        btExpose.reportExpose()
                        btExpose.clickWithTrigger {
                            it.reportClick()
                            MePageRouter.jumpToEducationIdentifyActivity(it.context, CertificationIdentifySource.MAIN_TAB,PageSource.F1_RECOMMEND_CHILD_FRAGMENT_LOCK_CARS)
                        }
                    }
                }

                blockInfo.companyCertBlock -> { //未完成工作认证
                    mBinding.blockContent.run {
                        iv1.setImageResource(R.drawable.matching_icon_guide_complete)
                        iv2.setImageResource(R.drawable.matching_icon_guide_complete)
                        iv3.setImageResource(R.drawable.matching_icon_guide_edu_complete)
                        tvContent.text = "Lv3·工作认证"
                        tvDesc.text = "审核成功后可解锁平台全部功能及全部推荐嘉宾"
                        btExpose.text = "去完成工作认证"
                        btExpose.reportExpose()
                        btExpose.clickWithTrigger {
                            it.reportClick()
                            MePageRouter.jumpToMeCompanyAuthActivity(it.context,PageSource.F1_RECOMMEND_CHILD_FRAGMENT_LOCK_CARS)
                        }
                    }
                }
                else->{
                    mBinding.blockContent.run {
                        btExpose.gone()
                        iv1.setImageResource(R.drawable.matching_icon_guide_complete)
                        iv2.setImageResource(R.drawable.matching_icon_guide_complete)
                        iv3.setImageResource(R.drawable.matching_icon_guide_complete)
                        tvTitle.text = "新手任务已完成"
                        tvContent.text = "资料审核中"
                        tvDesc.text = "审核完成后可解锁平台全部功能"
                    }
                }

            }
        }
    }

    private fun TextView.reportClick(){
    }
    private fun TextView.reportExpose(){
    }


    /**
     * 设置嘉宾信息
     */
    private fun MatchingRecommendBlockUserItemBinding.setUserInfo(data: RecommendUser) {
        root.visible()
        ivAvatar.load(data.baseInfo?.tinyAvatar)
        tvUserName.text = data.baseInfo?.nickName
        tvAge.text = data.baseInfo?.age.toAgeString()
        tvHeight.text = data.baseInfo?.height.toHeightString()
        tvEducation.text = data.baseInfo?.degreeDesc
        tvSalary.textOrGone(data.privateInfo?.annualIncome)
    }
}