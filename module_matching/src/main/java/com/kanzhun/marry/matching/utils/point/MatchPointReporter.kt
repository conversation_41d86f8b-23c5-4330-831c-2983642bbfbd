package com.kanzhun.marry.matching.utils.point

import com.chad.library.adapter.base.entity.SectionEntity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.relationStatusStr
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.matching.adapter.provider.InteractListCardType
import com.kanzhun.marry.matching.bean.InteractUserBean

class MatchPointReporter {

    companion object {
        /**
         * 孩子端新手引导F1锁定卡片点击
         */
        fun reportChildNewGuidanceF1LockedCardClick(userID: String?, title: String?) {
            reportPoint("child-newguidance-F1-lockedcard-click") {
                peer_id = userID
                actionp2 = title
            }
        }

        /**
         * 孩子端新手引导F1锁定卡片曝光
         */
        fun reportChildNewGuidanceF1LockedCardExpose(userId: String?) {
            reportPoint("child-newguidance-F1-lockedcard-expo") {
                peer_id = userId
            }
        }

        /**
         * 孩子端新手引导F1顶部提示条点击
         */
        fun reportChildNewGuidanceF1GuideClick(text: String?, isClose: Boolean = false) {
            reportPoint("child-newguidance-F1-guide-click") {
                actionp2 = text
                type = if (isClose) "关闭" else "去完善"
            }
        }

        /**
         * 孩子端新手引导F1顶部提示条曝光
         */
        fun reportChildNewGuidanceF1GuideExpose(text: String?) {
            reportPoint("child-newguidance-F1-guide-expo") {
                actionp2 = text
            }
        }

        /**
         * F1首页明日推荐预告曝光
         */
        fun reportF1TomorrowCardExpose(showIds: List<String>?) {
            reportPoint("F1-tomorrow-rcd-expo") {
                this.show_ids = showIds
            }
        }

        /**
         *孩子端F1推荐首页卡片曝光
         */
        fun reportF1RecommendUserCardExpose(userId: String?, lid: String?, idx: Int, relationStatus: Int,showText:String?,cacheTs:String?,p5:String?,p6:String?) {
            reportPoint("F1-rcd-user-expo") {
                this.peer_id = userId
                this.lid = lid
                this.actionp2 = showText
                this.idx = idx
                this.status = AccountHelper.getInstance().account?.phase.toString()
                this.friend = relationStatus.relationStatusStr()
                actionp4 = cacheTs
                actionp5 = p5
                actionp6 = p6
            }
        }

        //活动曝光
        fun reportActivityRecommendUserCardExpose(type:String?,userId: String?, lid: String?, idx: Int, relationStatus: Int) {
            reportPoint("activity-signedin-userlist-expo") {
                this.type = type
                this.lid = lid
                this.idx = idx
                this.peer_id = userId
                this.friend = relationStatus.relationStatusStr()
            }
        }

        /**
         *孩子端F1推荐首页卡片曝光
         */
        fun reportSendLike(userId: String?, source: PageSource, message: String?, relationStatus: Int,sourceName:String?,likeReason:String?) {
            reportPoint("send-like") {
                this.peer_id = userId
                this.msg = message
                this.source = sourceName
                this.actionp3 = likeReason
                this.friend = relationStatus.relationStatusStr()
                type = "整体喜欢"
            }
        }

        /**
         * 喜欢我列表曝光
         */
        fun reportLikeMeItemExpose(item: InteractUserBean, list: List<BaseListItem>?, page: Int) {
            val position = list?.filter { it.getLocalItemType() == InteractListCardType.USER_CARD.value }?.indexOf(item)
            reportPoint("F2-notify-likeme-expo") {
                this.actionp2 = if (item.readStatus == 0) "1" else "0"
                this.actionp3 = item.recommendReason
                this.peer_id = item.userId
                this.lid = item.lid
                this.idx = position
                this.page = page
                this.status = AccountHelper.getInstance().account?.phase.toString()
                this.friend = item.relationStatus.relationStatusStr()

                // 新增actionp4 记录卡片上展示的开聊语文案（存完整文案）
                this.actionp4 = item.likeReason
                // 新增actionp5，是否有活动标签，0=没有，1=有
                this.actionp5 = if (item.sameActivity == true) "1" else "0"
            }
        }

        /**
         * 喜欢我列表点击
         */
        fun reportLikeMeItemClick(item: InteractUserBean, actionp2: String, list: List<BaseListItem>?, page: Int) {
            val position = list?.filter { it.getLocalItemType() == InteractListCardType.USER_CARD.value }?.indexOf(item)
            reportPoint("F2-likeme-click") {
//                this.actionp2 = if (item.readStatus == 0) "1" else "0"
                this.actionp2 = actionp2
                this.actionp3 = item.recommendReason
                this.peer_id = item.userId
                this.lid = item.lid
                this.idx = position
                this.status = AccountHelper.getInstance().account?.phase.toString()
                this.page = page
                this.friend = item.relationStatus.relationStatusStr()

                // 新增actionp4 记录卡片上展示的开聊语文案（存完整文案）
                this.actionp4 = item.likeReason
                // 新增actionp5，是否有活动标签，0=没有，1=有
                this.actionp5 = if (item.sameActivity == true) "1" else "0"
            }
        }

        /**
         * 看过我列表曝光
         */
        fun reportViewMeItemExpose(item: InteractUserBean, list: List<BaseListItem>?, page: Int) {
            val position = list?.filter { it.getLocalItemType() == InteractListCardType.USER_CARD.value }?.indexOf(item)
            reportPoint("F2-notify-viewme-expo") {
                this.actionp2 = if (item.readStatus == 0) "1" else "0"
                this.actionp3 = item.recommendReason
                this.peer_id = item.userId
                this.lid = item.lid
                this.idx = position
                this.page = page
                this.status = AccountHelper.getInstance().account?.phase.toString()
                this.friend = item.relationStatus.relationStatusStr()

                // 新增actionp4 记录卡片上展示的开聊语文案（存完整文案）
                this.actionp4 = item.likeReason
                // 新增actionp5，是否有活动标签，0=没有，1=有
                this.actionp5 = if (item.sameActivity == true) "1" else "0"
            }
        }

        /**
         * 我喜欢的列表曝光
         */
        fun reportILikeItemExpose(item: InteractUserBean, list: List<SectionEntity>?, page: Int) {
            val position = list?.filter { !it.isHeader }?.indexOf(item)
            reportPoint("F2-notify-ilike-expo") {
                this.peer_id = item.userId
                this.lid = item.lid
                this.idx = position
                this.page = page
                this.status = AccountHelper.getInstance().account?.phase.toString()
                this.friend = item.relationStatus.relationStatusStr()
            }
        }

        /**
         * 家长推荐列表曝光
         */
        fun reportParentRecommendItemExpose(item: InteractUserBean, position: Int) {
            reportPoint("F2-notify-parent-rcd-expo") {
                this.peer_id = item.userId
                this.lid = item.lid
                this.idx = position
                this.status = AccountHelper.getInstance().account?.phase.toString()
                this.friend = item.relationStatus.relationStatusStr()
            }
        }


    }
}