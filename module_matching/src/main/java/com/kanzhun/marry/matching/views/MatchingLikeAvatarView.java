package com.kanzhun.marry.matching.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.User;
import com.kanzhun.marry.matching.databinding.MatchingLayoutLikeAvatarBinding;
import com.kanzhun.utils.base.LList;

import java.util.List;

/**
 * 空状态引导，环形展示喜欢我的头像
 *
 * Created by <PERSON><PERSON> on 2022/10/11
 */
public class MatchingLikeAvatarView extends ConstraintLayout {

    private MatchingLayoutLikeAvatarBinding binding;

    public MatchingLikeAvatarView(@NonNull Context context) {
        this(context, null);
    }

    public MatchingLikeAvatarView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MatchingLikeAvatarView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        binding = MatchingLayoutLikeAvatarBinding.inflate(LayoutInflater.from(context), this, true);

        User user = ServiceManager.getInstance().getDatabaseService().getUserDao().getUser(AccountHelper.getInstance().getUserId());
        binding.ivSelf.load(user != null ? user.getTinyAvatar() : "");
    }

    public void setAvatarList(List<String> tinyAvatars) {
        if (LList.isEmpty(tinyAvatars)) return;
        int count = LList.getCount(tinyAvatars);
        if (count == 1) {
            binding.ivRightCenter.load(tinyAvatars.get(0));
            binding.ivRightCenter.setVisibility(View.VISIBLE);
        } else if (count == 2) {
            binding.ivRightCenter.load(tinyAvatars.get(0));
            binding.ivRightCenter.setVisibility(View.VISIBLE);

            binding.ivLeftCenter.load(tinyAvatars.get(1));
            binding.ivLeftCenter.setVisibility(View.VISIBLE);
        } else if (count == 3) {
            binding.ivTopCenter.load(tinyAvatars.get(0));
            binding.ivTopCenter.setVisibility(View.VISIBLE);

            binding.ivThree2.load(tinyAvatars.get(1));
            binding.ivThree2.setVisibility(View.VISIBLE);

            binding.ivThree3.load(tinyAvatars.get(2));
            binding.ivThree3.setVisibility(View.VISIBLE);
        } else if (count == 4) {
            binding.ivFour1.load(tinyAvatars.get(0));
            binding.ivFour1.setVisibility(View.VISIBLE);

            binding.ivFour2.load(tinyAvatars.get(1));
            binding.ivFour2.setVisibility(View.VISIBLE);

            binding.ivFour3.load(tinyAvatars.get(2));
            binding.ivFour3.setVisibility(View.VISIBLE);

            binding.ivFour4.load(tinyAvatars.get(3));
            binding.ivFour4.setVisibility(View.VISIBLE);
        } else if (count >= 5) {
            binding.ivTopCenter.load(tinyAvatars.get(0));
            binding.ivTopCenter.setVisibility(View.VISIBLE);

            binding.ivFive2.load(tinyAvatars.get(1));
            binding.ivFive2.setVisibility(View.VISIBLE);

            binding.ivFive3.load(tinyAvatars.get(2));
            binding.ivFive3.setVisibility(View.VISIBLE);

            binding.ivFive4.load(tinyAvatars.get(3));
            binding.ivFive4.setVisibility(View.VISIBLE);

            binding.ivFive5.load(tinyAvatars.get(4));
            binding.ivFive5.setVisibility(View.VISIBLE);
        }
    }
}
