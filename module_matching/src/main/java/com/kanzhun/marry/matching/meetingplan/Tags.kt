package com.kanzhun.marry.matching.meetingplan

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun OneLineTags(modifier: Modifier = Modifier, tags: List<String>? = null, maxLines: Int = 1) {
    FlowRow(
        modifier = modifier,
        maxLines = maxLines,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.Center
    ) {
        tags?.forEach { tag ->
            Box(modifier = Modifier.align(Alignment.CenterVertically)) {
                Tag(tag = tag)
            }
        }
    }
}

@Composable
private fun Tag(tag: String) {
    Text(
        text = tag,
        style = TextStyle(
            fontSize = 14.sp,
            fontWeight = FontWeight(400),
            color = Color(0xFF7F7F7F),
        ),
    )
}

@Preview
@Composable
private fun OneLineTagsPreview() {
    OneLineTags(
        modifier = Modifier.width(width = 384.dp),
        tags = mutableListOf<String>().apply {
            repeat(100) {
                add("标签$it")
            }
        }
    )
}