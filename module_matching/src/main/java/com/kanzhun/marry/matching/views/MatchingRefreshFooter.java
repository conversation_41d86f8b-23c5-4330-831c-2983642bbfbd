package com.kanzhun.marry.matching.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.scwang.smart.drawable.PaintDrawable;
import com.scwang.smart.refresh.layout.api.RefreshFooter;
import com.scwang.smart.refresh.layout.api.RefreshKernel;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.simple.SimpleComponent;
import com.kanzhun.marry.matching.R;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/18
 */
public class MatchingRefreshFooter extends SimpleComponent implements RefreshFooter {

    protected PaintDrawable mProgressDrawable;
    protected TextView mTitleText;
    protected LinearLayout mLlCenter;
    protected ImageView mProgressView;
    protected ImageView mIvAbove;
    protected float showRate = 0.75f;
    private Listener listener;
    /**
     * 是否是匹配回看页
     */
    private boolean isReviewHistory;

    public MatchingRefreshFooter(Context context) {
        this(context, null);
    }

    public MatchingRefreshFooter(Context context, AttributeSet attrs) {
        super(context, attrs, 0);
        View.inflate(context, R.layout.matching_refresh_footer, this);
        final View thisView = this;
        mProgressView = thisView.findViewById(R.id.iv_progress);
        mTitleText = thisView.findViewById(R.id.tv_title);
        mLlCenter = thisView.findViewById(R.id.ll_center);
        mIvAbove = thisView.findViewById(R.id.iv_above);

//        mProgressDrawable = new ProgressDrawable();
//        mProgressDrawable.setColor(0xff666666);
//        mProgressView.setImageDrawable(mProgressDrawable);
//
//        mProgressView.animate().setInterpolator(null);
        mProgressView.setVisibility(GONE);


    }

    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {
        super.onInitialized(kernel, height, maxDragHeight);
    }

    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        switch (newState) {
            case None:
            case PullUpToLoad:
                if (!isReviewHistory) {
                    mTitleText.setText(getResources().getString(R.string.matching_refresh_footer_title));
                } else {
                    mTitleText.setText(getResources().getString(R.string.matching_review_refresh_header_title));
                }

                mLlCenter.setVisibility(VISIBLE);
                mIvAbove.setVisibility(VISIBLE);
                break;
            case Loading:
            case LoadReleased:
                //"正在刷新...";
                mLlCenter.setVisibility(INVISIBLE);
                progressStart();
                break;
            case ReleaseToLoad:
                if (!isReviewHistory) {
                    mTitleText.setText(getResources().getString(R.string.matching_refresh_footer_title_release));
                } else {
                    mTitleText.setText(getResources().getString(R.string.matching_review_refresh_header_title_release));
                }

                break;
            default:
                break;
        }
    }

    private void progressStart() {
        final View progressView = mProgressView;
        if (progressView.getVisibility() != VISIBLE) {
            progressView.setVisibility(VISIBLE);
//            Drawable drawable = mProgressView.getDrawable();
//            if (drawable instanceof Animatable) {
//                ((Animatable) drawable).start();
//            } else {
//                progressView.animate().rotation(36000).setDuration(100000);
//            }
        }
    }

    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {
        if (percent <= 0.75f) {
            mLlCenter.setAlpha(0.0f);
            if (listener != null) {
                listener.onRate(0.0f);
            }
        } else if (percent < 1) {
            float alpha = (percent - showRate) * 4;
            mLlCenter.setAlpha(alpha);
            if (listener != null) {
                listener.onRate(alpha);
            }
        } else {
            mLlCenter.setAlpha(1.0f);
            if (listener != null) {
                listener.onRate(1.0f);
            }
        }
    }

    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        super.onReleased(refreshLayout, height, maxDragHeight);
    }

    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        super.onStartAnimator(refreshLayout, height, maxDragHeight);
    }

    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        final View progressView = mProgressView;
//        Drawable drawable = mProgressView.getDrawable();
//        if (drawable instanceof Animatable) {
//            if (((Animatable) drawable).isRunning()) {
//                ((Animatable) drawable).stop();
//            }
//        } else {
//            progressView.animate().rotation(0).setDuration(0);
//        }

        progressView.setVisibility(GONE);
        mLlCenter.setVisibility(INVISIBLE);
        mProgressView.setVisibility(GONE);
        return 0;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public interface Listener {
        //0-1
        void onRate(float rate);
    }

    public void setReviewHistory(boolean reviewHistory) {
        isReviewHistory = reviewHistory;
    }
}
