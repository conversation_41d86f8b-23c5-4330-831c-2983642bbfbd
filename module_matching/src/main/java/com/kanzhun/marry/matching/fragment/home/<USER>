package com.kanzhun.marry.matching.fragment.home

import android.R.attr.contentDescription
import android.R.attr.maxLines
import android.R.attr.maxWidth
import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.SecondaryTabRow
import androidx.compose.material3.Surface
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.BaselineShift
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.AsyncImage
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.BaseComposeFragment
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.constant.LivedataKeyMatching
import com.kanzhun.common.kotlin.ext.liveEventObserve
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.bean.SerializableMap
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.model.matching.MatchingLikeModel
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.views.SendLikeButton.SendButtonStatus
import com.kanzhun.foundation.views.getSendButtonStatusByRelationShowStatus
import com.kanzhun.foundation.views.likeLick
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.adapter.provider.SeeMeListItemProvider
import com.kanzhun.marry.matching.model.ActivityLiveInfo
import com.kanzhun.marry.matching.model.AiMatchInfo
import com.kanzhun.marry.matching.model.GuestInfo
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.launch
import kotlin.collections.get
import kotlin.math.roundToInt
import kotlin.text.ifEmpty
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.marry.matching.fragment.home.MatchGuestsPagerNewViewModel.MatchGuestsUiState
import com.twl.anti.TwlMD.overflow
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.ui.graphics.graphicsLayer
import com.kanzhun.marry.matching.model.AiMatchInfo.UserInfo
import kotlin.collections.forEachIndexed
import androidx.compose.ui.layout.Placeable
import androidx.compose.ui.layout.SubcomposeLayout

// Tab数据类
data class TabItem(
    val title: String,
    @DrawableRes val icon: Int? = null,
    @DrawableRes val unSelectIcon: Int? = null,
)

class MatchGuestsTestFragment : BaseComposeFragment() {

    // 统一的高斯模糊参数
    private val blurRadius = 25.dp
    private val blurAlpha = 0.80f

    private var activityId = ""

    private val viewModel by lazy { MatchGuestsPagerNewViewModel() }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (arguments?.getSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE) is SerializableMap) {
            val mMap: MutableMap<String, Any> =
                (arguments?.getSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE) as SerializableMap).map as MutableMap<String, Any>
            activityId = mMap["activityId"].toString()
        }

        // Load data from ViewModel
        viewModel.loadData(activityId)

        initObserver()
    }

    private fun initObserver() {
        liveEventObserve<MatchingLikeModel?>(LivedataKeyMatching.MATCH_SEND_LIKE_SUCCESS) {
            TLog.info("SendLike", "MatchGuestsPagerNewFragment")
            it?.pageSource = PageSource.NONE
            MatchingPageRouter.handleSendLikeResult(requireActivity(), it)
            //更新卡片按钮状态
            if (it?.likeEachOther?.otherInfo != null && it?.likeEachOther?.myInfo != null) {
                viewModel.updateSendLikeStatus(it.originToUserId, Constants.RELATION_FRIEND)
            } else {
                viewModel.updateSendLikeStatus(it?.originToUserId, Constants.RELATION_LIKE)
            }
        }
    }

    @Composable
    override fun OnSetContent() {
        TestView()
    }

    @Composable
    fun TestView() {
        NestedScrollWithPagerAndStickyHeaderSample()
    }


    /**
     * 这是一个使用NestedScroll和Pager的样品，同时包含粘性标题（这里是Tabs）。
     * 我们使用的工具栏偏移量更改布局效果，同时模拟了粘性标题的吸顶效果。
     */
    @SuppressLint("UnrememberedMutableState")
    @OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
    @Composable
    fun NestedScrollWithPagerAndStickyHeaderSample() {
        val coroutineScope = rememberCoroutineScope()
        val density: Density = LocalDensity.current
        val context: Context = LocalContext.current

        val guestsUiState = viewModel.uiState.value

        // 根据matchShowStatus决定显示哪些Tab
        val currentTabItems = if (guestsUiState.matchShowStatus == 1) {
            allTabItems // 显示全部标签
        } else {
            listOf(allTabItems[0]) // 只显示"到场嘉宾"标签
        }

        // 使用ViewModel中存储的tabIndex来初始化
        var selectedTabIndex by remember { mutableStateOf(0) }

        // 初始化selectedTabIndex
        LaunchedEffect(Unit) {
            selectedTabIndex = viewModel.getCurrentTabIndex()
        }

        val pagerState =
            rememberPagerState(initialPage = viewModel.getCurrentTabIndex()) { currentTabItems.size }

        val userGender = AccountHelper.getInstance().userInfo?.gender ?: 1

        LaunchedEffect(pagerState) {
            // Collect from the a snapshotFlow reading the currentPage
            snapshotFlow { pagerState.currentPage }.collect { pageIndex ->
                // Do something with each page change, for example:
                // viewModel.sendPageSelectedEvent(page)
                selectedTabIndex = pageIndex
                Log.d("Page change", "Page changed to $pageIndex")
            }
        }

        // 在此处记录当前选中的tab，用于刷新时保持tab不变
        LaunchedEffect(selectedTabIndex) {
            viewModel.setCurrentTabIndex(selectedTabIndex)
        }

        Surface(
            modifier = Modifier
                .fillMaxSize(),
            color = Color(0xFFF5F5F5)
        ) {

            // 用于记录应用栏的高度，以便在嵌套滚动中使用。会在onSizeChanged中获取
            var toolbarHeight by remember { mutableStateOf(0.dp) }
            var tabBarHeight by remember { mutableStateOf(0.dp) }

            //val maxUpPx by mutableStateOf(with(LocalDensity.current) { toolbarHeight.roundToPx().toFloat() - 50.dp.roundToPx().toFloat() - 56.dp.roundToPx().toFloat() }) // 此处会导致负数然后运行报错，这是错误的写法
            // ToolBar 最大向上位移量
            var maxUpPx by remember { mutableStateOf(0f) } // 使用属性初始化，而不是直接在mutableStateOf中初始化
            val barheight = getAppBarHeightAndBarHeight()
            // 当工具栏高度变化时更新maxUpPx，并确保它不是负的
            LaunchedEffect(toolbarHeight) {
                maxUpPx = with(density) {
                    (toolbarHeight.roundToPx().toFloat() - tabBarHeight.roundToPx()
                        .toFloat() - barheight.roundToPx().toFloat()).coerceAtLeast(0f)
                }
            }
            // ToolBar 最小向上位移量
            val minUpPx = 0f
            // 偏移折叠工具栏上移高度
            val toolbarOffsetHeightPx = remember { mutableStateOf(0f) }
            // 现在，让我们创建与嵌套滚动系统的连接并聆听子 LazyColumn 中发生的滚动
            val nestedScrollConnection = remember {
                object : NestedScrollConnection {
                    // 向上滚动逻辑：
                    // 1、工具栏完全可见时，向上滚动先移动工具栏。
                    // 2、工具栏部分不可见时，任意方向滚动都先移动工具栏。
                    // 3、工具栏完全不可见且处于最大偏移时，向上滚动应滚动 LazyColumn。
                    // 
                    // 向下滚动逻辑（修改后）：
                    // 1、工具栏完全可见时，向下滚动应先滚动 LazyColumn。
                    // 2、工具栏有偏移时，向下滚动先展开工具栏。
                    override fun onPreScroll(
                        available: Offset,
                        source: NestedScrollSource,
                    ): Offset {
                        // 向上滚动的处理逻辑保持不变
                        if (available.y < 0) {
                            // 如果toolbar已经有偏移（但未达到最大值），先收缩toolbar
                            if (toolbarOffsetHeightPx.value > -maxUpPx) {
                                val delta = available.y
                                val newOffset = toolbarOffsetHeightPx.value + delta
                                toolbarOffsetHeightPx.value = newOffset.coerceIn(-maxUpPx, minUpPx)
                                // 消费掉所有的y轴上的滚动
                                return Offset(0f, delta)
                            }
                            // 如果toolbar偏移已经是最大值，允许LazyColumn处理滚动事件
                            return Offset.Zero
                        }

                        // 向下滚动的处理逻辑（修改）
                        if (available.y > 0) {
                            // 如果工具栏完全可见，不消费事件，让子组件先处理滚动
                            if (toolbarOffsetHeightPx.value == 0f) {
                                return Offset.Zero
                            }
                            // 如果工具栏有偏移，这里不处理，留给onPostScroll处理
                            return Offset.Zero
                        }

                        // 在其他情况下，不消费滚动事件
                        return Offset.Zero
                    }

                    override fun onPostScroll(
                        consumed: Offset,
                        available: Offset,
                        source: NestedScrollSource,
                    ): Offset {
                        // 向下滚动且有剩余滚动量时，展开工具栏
                        if (available.y > 0 && toolbarOffsetHeightPx.value != 0f) {
                            val delta = available.y
                            val newOffset = toolbarOffsetHeightPx.value + delta
                            toolbarOffsetHeightPx.value = newOffset.coerceIn(-maxUpPx, 0f)
                            // 消费掉剩余的y轴滚动
                            return Offset(0f, delta)
                        }
                        
                        // 其他情况不消费滚动事件
                        return Offset.Zero
                    }
                }
            }

            // 使用toolbarOffsetHeightPx的值来判断是否已经滑动到最大值
            val isScrolledToMax by derivedStateOf {
                toolbarOffsetHeightPx.value <= -maxUpPx
            }

            // 使用toolbarOffsetHeightPx的值来判断是否已经滑动到最小值
            val isScrolledToMin by derivedStateOf {
                toolbarOffsetHeightPx.value >= 0f
            }

            // 计算透明度用于状态栏和TopBar
            val appBarAlpha by remember {
                derivedStateOf {
                    if (maxUpPx <= 0f) 0f else (-toolbarOffsetHeightPx.value / maxUpPx).coerceIn(
                        0f,
                        1f
                    )
                }
            }

            // 系统状态栏控制
            val systemUiController = rememberSystemUiController()

            LaunchedEffect(appBarAlpha) {
                // Set system bars to be immersive and transparent
                systemUiController.setSystemBarsColor(
                    color = Color.Transparent,
                    darkIcons = true,
                    isNavigationBarContrastEnforced = false
                )

                // Just adjust the status bar color based on scroll
                if (appBarAlpha > 0.9f) {
                    // 标题栏白色时，状态栏也使用白色背景和黑色图标
                    systemUiController.setStatusBarColor(
                        color = Color.White,
                        darkIcons = true
                    )
                } else {
                    // 透明状态栏，深色图标
                    systemUiController.setStatusBarColor(
                        color = Color.Transparent,
                        darkIcons = true
                    )
                }
            }

            Box(
                Modifier
                    .fillMaxSize()
                    // 作为父级附加到嵌套滚动系统
                    .nestedScroll(nestedScrollConnection)
            ) {

                FollowNestedScrollToolbar(
                    title = "toolbar offset is ${toolbarOffsetHeightPx.value}",
                    guestsUiState = guestsUiState,
                    scrollableAppBarHeight = toolbarHeight,
                    currentTabItems = currentTabItems,
                    toolbarOffsetHeightPx = toolbarOffsetHeightPx,
                    appBarAlpha = appBarAlpha,
                    isScrolledToMax = isScrolledToMax,
                    activityInfo = viewModel.uiState.value.activityInfo,
                    onNavigate = {
                    },
                    onBoxSizeChanged = { size ->
                        coroutineScope.launch {
                            // 你可以在这里获取到应用栏的高度
                            toolbarHeight = if (toolbarHeight == 0.dp) pxToDp(
                                size.height,
                                context
                            ).dp else toolbarHeight
                        }
                    },
                    onTabSizeChanged = { size ->
                        coroutineScope.launch {
                            // 你可以在这里获取到TabRow的高度
                            tabBarHeight = if (tabBarHeight == 0.dp) pxToDp(
                                size.height,
                                context
                            ).dp else tabBarHeight
                        }
                    },
                    selectedTabIndex = selectedTabIndex,
                    onSelectedTabChange = { tabIndex ->
                        coroutineScope.launch {
                            selectedTabIndex = tabIndex
                            // Call scroll to on pagerState
                            pagerState.scrollToPage(tabIndex) // or pagerState.animateScrollToPage(tabIndex)
                        }
                    }
                )
//                Text(text = if (isScrolledToMax) "达到最大偏移量" else "尚未达到最大偏移量")


                val paddingOffset =
                    toolbarHeight + with(LocalDensity.current) { toolbarOffsetHeightPx.value.toDp() }

                HorizontalPager(
                    modifier = Modifier.fillMaxSize(),
                    state = pagerState,
                    contentPadding = PaddingValues(top = paddingOffset)
                ) { pageIndex ->
                    // page content
                    when (pageIndex) {
                        0 -> {
                            PageOne(userGender)
                        }

                        1 -> {
                            AIMatchContent(userGender)
                        }

                    }

                }

            }
        }
    }

    @Composable
    fun PageOne(userGender: Int = 1) {
        val guests: List<GuestInfo> = viewModel.getCurrentGenderGuests()
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxSize()
        ) {
            item {
                // 性别过滤按钮 - 连接到ViewModel  78dp
                GenderFilterButtons(
                    selectedGender = viewModel.selectedGender.value,
                    onGenderSelected = { viewModel.switchGenderFilter(it) },
                    userGender = userGender
                )
            }

            // 使用固定列数的网格布局
            val numColumns = 2
            val rows = (guests.size + numColumns - 1) / numColumns // 向上取整计算行数
            if (rows > 0) {
                items(rows) { rowIndex ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 12.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        for (colIndex in 0 until numColumns) {
                            val index = rowIndex * numColumns + colIndex
                            if (index < guests.size) {
                                Box(
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(bottom = 8.dp)
                                ) {
                                    UserCard(guest = guests[index], userGender = userGender)
                                    {
                                        MePageRouter.jumpToInfoPreviewActivity(
                                            context = context,
                                            securityId = guests[index].securityId,
                                            userId = guests[index].baseInfo.encUserId,
                                            pageSource = PageSource.ACTIVITY_RECOMMEND
                                        )
                                    }
                                }
                            } else {
                                // 空白占位
                                Spacer(modifier = Modifier.weight(1f))
                            }
                        }
                    }
                }

                // 底部刷新按钮 80dp
                item {
                    RefreshButton(onRefresh = { viewModel.refreshData(activityId) })
                }
            } else {
                item {
                    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                        Column(
                            modifier = Modifier.fillMaxSize(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Image(
                                painter = R.drawable.image_empty.painterResource(),
                                contentDescription = "empty", modifier = Modifier.size(200.dp)
                            )

                            Spacer(modifier = Modifier.height(20.dp))
                            RefreshButton(onRefresh = { viewModel.refreshData(activityId) })
                        }

                    }


                }
            }


        }
    }

    @Composable
    fun UserCard(guest: GuestInfo, userGender: Int = 1, onClick: () -> Unit) {
        val baseInfo = guest.baseInfo

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .noRippleClickable {
                    onClick()
                },
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column {
                // 图片部分
                Box(modifier = Modifier.fillMaxWidth()) {
                    // 用户头像/照片
                    AsyncImage(
                        model = baseInfo.avatar ?: "",
                        contentDescription = "User Photo",
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(172.dp)
                    )

                    if (SeeMeListItemProvider.getF2StatusString(
                            guest.likeEachOther,
                            guest.baseInfo.relationStatus
                        ).isNotEmpty()
                    ) {
                        Box(
                            modifier = Modifier
                                .padding(8.dp)
                                .background(
                                    color = Color(0x99000000),
                                    shape = RoundedCornerShape(12.dp)
                                )
                                .padding(horizontal = 10.dp, vertical = 3.dp)
                                .align(Alignment.TopEnd),
                        ) {
                            Text(
                                text = SeeMeListItemProvider.getF2StatusString(
                                    guest.likeEachOther,
                                    guest.baseInfo.relationStatus
                                ),
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFFFFFFFF),
                                )
                            )
                        }
                    }

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomStart)
                            .background(
                                brush = Brush.verticalGradient(
                                    colors = listOf(
                                        Color(0x00000000),
                                        Color(0x33000000),
                                        Color(0x33000000)
                                    )
                                )
                            )
                            .padding(8.dp)
                    ) {
                        // 号数
                        Box(
                            modifier = Modifier
                                .background(
                                    //如果是女生使用粉丝 男生使用蓝色
                                    color = if (baseInfo.gender == 2) Color(0xFFFF8FFF) else Color(
                                        0xFF53C0FF
                                    ),
                                    shape = RoundedCornerShape(size = 39.dp)
                                )
                                .padding(start = 8.dp, top = 3.dp, end = 8.dp, bottom = 3.dp),
                        ) {
                            Text(
                                text = guest.signInNum,
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight(600),
                                    color = Color(0xFFFFFFFF),
                                )
                            )

                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = baseInfo.nickName ?: "",
                            style = TextStyle(
                                fontSize = 20.sp,
                                lineHeight = 24.sp,
                                fontFamily = boldFontFamily(),
                                color = Color(0xFFFFFFFF),
                            )
                        )
                    }


                }

                // 信息部分
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .align(Alignment.End)
                        .padding(8.dp)

                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize(),
                    ) {
                        // 两行标签，每行最多两个
                        Column(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // 第一行标签（最多2个）
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 只显示前两个标签
                                if (guest.recommendTag.isNotEmpty()) {
                                    Text(
                                        text = guest.recommendTag.getOrElse(0) { "" },
                                        style = TextStyle(
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFF292929)
                                        ),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        modifier = Modifier
                                    )

                                    // 只有当有第二个标签时才显示分隔图片
                                    if (guest.recommendTag.size > 1) {
                                        // 分隔图片
                                        Box(
                                            modifier = Modifier
                                                .padding(horizontal = 6.dp)
                                                .background(
                                                    color = Color(0xFFD4D4D4),
                                                    shape = RoundedCornerShape(0.5.dp)
                                                )
                                                .width(0.5.dp)
                                                .height(8.dp)
                                        )

                                        // 第二个标签
                                        Text(
                                            text = guest.recommendTag[1],
                                            style = TextStyle(
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight(400),
                                                color = Color(0xFF292929)
                                            ),
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis,
                                            modifier = Modifier.weight(1f, fill = true)
                                        )
                                    }
                                }
                            }

                            // 只有当有超过2个标签时才显示第二行
                            if (guest.recommendTag.size > 2) {
                                Spacer(modifier = Modifier.height(4.dp))

                                // 第二行标签（第3个和第4个标签）
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    // 第三个标签
                                    Text(
                                        text = guest.recommendTag.getOrElse(2) { "" },
                                        style = TextStyle(
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFF292929)
                                        ),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        modifier = Modifier
                                    )

                                    // 只有当有第四个标签时才显示分隔图片
                                    if (guest.recommendTag.size > 3) {
                                        // 分隔图片
                                        Box(
                                            modifier = Modifier
                                                .padding(horizontal = 6.dp)
                                                .background(
                                                    color = Color(0xFFD4D4D4),
                                                    shape = RoundedCornerShape(0.5.dp)
                                                )
                                                .width(0.5.dp)
                                                .height(8.dp)
                                        )

                                        // 第四个标签
                                        Text(
                                            text = guest.recommendTag[3],
                                            style = TextStyle(
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight(400),
                                                color = Color(0xFF292929)
                                            ),
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis,
                                            modifier = Modifier.weight(1f, fill = true)
                                        )
                                    }
                                }
                            }
                        }
                    }

                    if (userGender != guest.baseInfo.gender) {
                        // 喜欢按钮
                        Box(
                            modifier = Modifier
                                .size(56.dp)
                                .align(Alignment.BottomEnd)
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colorStops = arrayOf(
                                            0f to Color.Transparent,
                                            0.2f to Color.White,
                                            1f to Color.White
                                        )
                                    )
                                )
                        ) {
                            IconButton(
                                onClick = {
                                    if (getSendButtonStatusByRelationShowStatus(guest.relationShowStatus) == SendButtonStatus.FRIEND) {
                                        ChatPageRouter.jumpToSingleChatActivityForH5(
                                            context,
                                            guest.baseInfo.encUserId ?: ""
                                        )
                                    } else {
                                        likeLick(
                                            guest.relationShowStatus,
                                            guest.baseInfo.encUserId ?: "",
                                            guest.baseInfo.nickName ?: "",
                                            guest.securityId ?: "",
                                            guest.baseInfo.avatar ?: "",
                                            guest.baseInfo.relationStatus
                                        )
                                    }

                                },
                                modifier = Modifier
                                    .size(40.dp)
                                    .align(alignment = Alignment.BottomEnd)
                            ) {
                                Image(
                                    painter = painterResource(
                                        id = when (getSendButtonStatusByRelationShowStatus(guest.relationShowStatus)) {
                                            SendButtonStatus.NORMAL -> R.drawable.img_guest_new_heart_tab_item_like
                                            SendButtonStatus.LIKE -> R.drawable.img_guest_new_heart_tab_item_liked
                                            SendButtonStatus.FRIEND -> R.drawable.img_guest_new_heart_tab_item_normal
                                            SendButtonStatus.GONE -> R.drawable.img_guest_new_heart_tab_item_like
                                        }
                                    ),
                                    contentDescription = "Like",
                                    modifier = Modifier.size(40.dp)
                                )
                            }
                        }
                    }

                }
            }
        }
    }

    val pageOneBottomHeight = 92.dp

    @Composable
    fun RefreshButton(onRefresh: () -> Unit = {}) {
        var isRefreshing by remember { mutableStateOf(false) }
        val rotation by animateFloatAsState(
            targetValue = if (isRefreshing) 180f else 0f,
            animationSpec = tween(durationMillis = 200),
            label = "refresh-rotation"
        )

        // 关键：用 LaunchedEffect 监听 isRefreshing
        LaunchedEffect(isRefreshing) {
            if (isRefreshing) {
                onRefresh()
                kotlinx.coroutines.delay(200)
                isRefreshing = false
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(pageOneBottomHeight)
                .padding(vertical = 20.dp, horizontal = 13.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                modifier = Modifier
                    .noRippleClickable {
                        if (!isRefreshing) {
                            isRefreshing = true
                        }
                    },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.common_icon_refresh_reload),
                    contentDescription = "Refresh",
                    tint = Color.Black,
                    modifier = Modifier
                        .size(16.dp)
                        .graphicsLayer {
                            rotationZ = rotation
                        }
                )

                Spacer(modifier = Modifier.width(4.dp))

                Text(
                    text = "点击刷新",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF292929),
                    )
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Box(
                modifier = Modifier
                    .fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "·更多嘉宾还在来的路上·",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFB2B2B2),
                    )
                )
            }
        }
    }

    // 添加AI匹配内容组件
    @Composable
    fun AIMatchContent(userGender: Int = 1) {
        val uiState = viewModel.uiState.value
        val matchList = uiState.aiMatchList
//        val matchList = MatchGuestsPagerNewViewModel.previewData.aiMatchList

        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxSize()
        ) {
            item {
                Box(
                    modifier = Modifier
                        .padding(top = 24.dp, bottom = 12.dp, start = 16.dp, end = 16.dp)
                ) {
                    Text(
                        text = if(uiState.aiMatchType == 2) "和你非常匹配的嘉宾" else uiState.aiMatchTitle,
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF7F7F7F),
                        ),
                        modifier = Modifier.align(Alignment.BottomStart)
                    )
                }
            }
            if (matchList.isEmpty()) {
                item {
                    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center,
                            modifier = Modifier.fillMaxSize()
                        ) {
                            Spacer(modifier = Modifier.height(100.dp))
                            Image(
                                painter = painterResource(id = R.drawable.img_guest_new_heart_ai_page_empty),
                                contentDescription = "Empty"
                            )

                            Spacer(modifier = Modifier.height(12.dp))

                            Text(
                                text = "暂未查询到匹配结果~",
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    color = Color.Gray
                                )
                            )
                        }
                    }

                }
            } else {
                items(matchList.size) { index ->

                    if (uiState.aiMatchType == 2){
                        //1vn 新样式
                        AIMatchCardN(matchItem = matchList[index], userGender != 1)
                    }else{
                        AIMatchCard(matchItem = matchList[index])
                    }

                    if (index < matchList.size - 1) {
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                }
                item {
                    Spacer(modifier = Modifier.height(34.dp))
                }
            }
        }
    }


    // AI匹配卡片组件
    @SuppressLint("UnusedBoxWithConstraintsScope")
    @Composable
    fun AIMatchCardN(matchItem: AiMatchInfo.MatchItem, useBoy: Boolean = true) {
        val matchUser: UserInfo? = if (useBoy) matchItem.male else matchItem.female

        Box(
            Modifier
                .padding(horizontal = 16.dp)
                .background(color = Color.White, shape = RoundedCornerShape(16.dp))
                .border(width = 1.dp, color = Color.White, shape = RoundedCornerShape(16.dp))
        ) {

            if(matchItem.reason?.isNotEmpty() == true){
                Box(
                    modifier = Modifier
                        .height(64.dp)
                        .fillMaxWidth()
                        .align(Alignment.BottomStart)
                        .background(
                            brush = Brush.verticalGradient(
                                colorStops = arrayOf(
                                    0.0f to Color.White.copy(alpha = 0.2f),
                                    0.2f to Color(0x00E3ECFF).copy(alpha = 0.2f),
                                    1f to if (useBoy)Color(0xff80AFFF).copy(alpha = 0.2f)else Color(0xFFFAB4FF).copy(alpha = 0.2f),
                                )
                            ), shape = RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp)
                        )
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
                    .noRippleClickable {
                        // 点击跳转到个人主页
                        MePageRouter.jumpToInfoPreviewActivity(
                            context = context,
                            securityId = matchUser?.securityId,
                            userId = matchUser?.encUserId,
                            pageSource = PageSource.ACTIVITY_RECOMMEND
                        )
                    },
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                ) {
                    // 头像区域
                    Box(
                        modifier = Modifier
                    ) {
                        // 用户头像
                        AsyncImage(
                            model = matchUser?.avatar?.ifEmpty { matchUser.tinyAvatar },
                            contentDescription = "User Avatar",
                            contentScale = ContentScale.Crop,
                            modifier = Modifier
                                .size(64.dp)
                                .clip(CircleShape)
                                .border(
                                    2.dp,
                                    if (useBoy) Color(0xFF53C0FF) else Color(0xFFFF8FFF),
                                    CircleShape
                                )
                        )

                        Image(
                            painter = painterResource(id = if (useBoy) R.drawable.icon_guest_ai_match_male else R.drawable.icon_guest_ai_match_female),
                            contentDescription = "AI Icon",
                            modifier = Modifier
                                .size(16.dp)
                                .align(Alignment.BottomEnd)
                        )
                    }

                    // 右侧内容
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 12.dp)
                    ) {
                        // 用户名和AI图标
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = matchUser?.nickName ?: "",
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF292929),
                                ),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.weight(1f)
                            )

                            // AI匹配标签
                            Row(
                                modifier =
                                    Modifier
                                        .background(
                                            color = if (useBoy) Color(0xFF53C0FF) else Color(0xFFFD77DE),
                                            shape = RoundedCornerShape(size = 39.dp)
                                        )
                                        .padding(horizontal = 8.dp, vertical = 3.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "${matchUser?.signInNum}号",
                                    style = TextStyle(
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight(600),
                                        color = Color(0xFFFFFFFF),
                                    )
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = "${matchUser?.intro?:""}",
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF7F7F7F),
                            ),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                        )

                        Spacer(modifier = Modifier.height(4.dp))

                        // 用户标签行
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // 标签1 - 匹配度
                            Box(
                                modifier = Modifier
                                    .background(
                                        color = Color(0x263DDBBC),
                                        shape = RoundedCornerShape(6.dp)
                                    )
                                    .padding(horizontal = 8.dp, vertical = 4.dp)
                            ) {
                                Text(
                                    text = "匹配度${matchUser?.score}%",
                                    style = TextStyle(
                                        fontSize = 11.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF13757F),
                                    ),
                                    modifier = Modifier.align(Alignment.Center)
                                )
                            }
                            
                            Spacer(modifier = Modifier.width(4.dp))
                            
                            // 使用AdaptiveTagRow显示其他标签
                            Box(modifier = Modifier.weight(1f)) {
                                AdaptiveTagRow(
                                    tags = matchUser?.recommendTag ?: emptyList(),
                                    useBoy = useBoy,
                                    spacing = 4.dp
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(12.dp))


                    }
                }

                if(matchItem.reason?.isNotEmpty() == true){
                    Spacer(modifier = Modifier.height(16.dp))

                    // AI推荐理由
                    // 图片+文字效果 文字多行显示 切第二行开始 文字要顶着行左对齐 不能从图片右边开始显示
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 12.dp),
                    ) {


                        Image(
                            painter = painterResource(id = R.drawable.img_guest_new_heart_ai_color),
                            contentDescription = "AI Reason",
                            modifier = Modifier
                                .size(18.dp)
                                .padding(top = 2.dp)
                        )

                        Spacer(modifier = Modifier.width(4.dp))
                        // 文本内容，从左边开始
                        Text(
                            text = androidx.compose.ui.text.buildAnnotatedString {
                                withStyle(
                                    style = androidx.compose.ui.text.SpanStyle(
                                        color = if (useBoy)Color(0xFF265EBC)else Color(0xFF882976),
                                        fontWeight = FontWeight(500),
                                        fontSize = 14.sp
                                    )
                                ) {
                                    append("      Deepseek推荐理由: ")
                                }

                                withStyle(
                                    style = androidx.compose.ui.text.SpanStyle(
                                        color = Color(0xFF191919),
                                        fontWeight = FontWeight(400),
                                        fontSize = 14.sp
                                    )
                                ) {
                                    append(matchItem.reason)
                                }
                            },
                            fontSize = 14.sp,
                            lineHeight = 20.sp,
                            modifier = Modifier
                                .fillMaxWidth()
                        )
                    }
                }

            }


        }

    }

    @Preview(showBackground = true)
    @Composable
    fun PreviewAIMatchCardN() {
        AIMatchCardN(
            AiMatchInfo.MatchItem(
                male = AiMatchInfo.UserInfo(
                    securityId = "xxx",
                    avatar = "https://via.placeholder.com/300",
                    tinyAvatar = "https://via.placeholder.com/60",
                    signInNum = 50,
                    recommendTag = listOf<String>("28岁","国企","北京户口","年薪40万+"),
                    intro = ""

                ),
                female = AiMatchInfo.UserInfo(
                    securityId = "xxx",
                    nickName = "小美",
                    avatar = "https://via.placeholder.com/300",
                    tinyAvatar = "https://via.placeholder.com/60",
                    signInNum = 24,
                    intro = "喜欢绿豆",
                    recommendTag = listOf<String>("28岁","国企","北京户口","年薪40万+")
                ),
                reason = "你们都是南方人，你们还是同行，哇！简直是天作之合。",
                relationStatus = 20,
                matchDegree = 98
            )
        )
    }

    @Preview(showBackground = true)
    @Composable
    fun PreviewAIMatchCardNFemale() {
        AIMatchCardN(
            AiMatchInfo.MatchItem(
                male = AiMatchInfo.UserInfo(
                    nickName = "name",
                    securityId = "xxx",
                    avatar = "https://via.placeholder.com/300",
                    tinyAvatar = "https://via.placeholder.com/60",
                    signInNum = 50,
                    recommendTag = listOf<String>("28岁","国企","北京户口","年薪40万+"),
                ),
                female = AiMatchInfo.UserInfo(
                    securityId = "xxx",
                    nickName = "小美",
                    avatar = "https://via.placeholder.com/300",
                    tinyAvatar = "https://via.placeholder.com/60",
                    signInNum = 24,
                    intro = "喜欢绿豆",
                    recommendTag = listOf<String>("28岁","国企","北京户口","年薪40万+"),
                ),
                reason = "",
                relationStatus = 20,
                matchDegree = 98
            ), useBoy = false
        )
    }


    @Preview(showBackground = true)
    @Composable
    fun PreviewAIMatchCard20() {
        AIMatchCard(
            AiMatchInfo.MatchItem(
                male = AiMatchInfo.UserInfo(
                    nickName = "name",
                    securityId = "xxx",
                    avatar = "https://via.placeholder.com/300",
                    tinyAvatar = "https://via.placeholder.com/60",
                    signInNum = 50,
                    intro = "",
                    recommendTag = listOf<String>("28岁","国企","北京户口","年薪40万+"),
                ),
                female = AiMatchInfo.UserInfo(
                    securityId = "xxx",
                    nickName = "小张",
                    avatar = "https://via.placeholder.com/300",
                    tinyAvatar = "https://via.placeholder.com/60",
                    signInNum = 299,
                    recommendTag = listOf<String>("28岁","国企","北京户口","年薪40万+"),
                ),
                reason = "",
                relationStatus = 20
            )
        )
    }

    @Preview(showBackground = true)
    @Composable
    fun PreviewAIMatchCard30() {
        AIMatchCard(
            AiMatchInfo.MatchItem(
                male = AiMatchInfo.UserInfo(
                    securityId = "xxx",
                    avatar = "https://via.placeholder.com/300",
                    tinyAvatar = "https://via.placeholder.com/60",
                    signInNum = 50,
                    intro = "",
                    recommendTag = listOf<String>("28岁","国企","北京户口","年薪40万+"),
                ),
                female = AiMatchInfo.UserInfo(
                    securityId = "xxx",
                    nickName = "小张",
                    avatar = "https://via.placeholder.com/300",
                    tinyAvatar = "https://via.placeholder.com/60",
                    signInNum = 299,
                    recommendTag = listOf<String>("28岁","国企","北京户口","年薪40万+"),
                ),
                reason = "reason",
                relationStatus = 20
            )
        )
    }

    @Composable
    fun AIMatchCard(matchItem: AiMatchInfo.MatchItem) {
        val isSelect by remember { mutableStateOf(matchItem.relationStatus == AiMatchInfo.NORMAL) }
//        val isSelect by remember { mutableStateOf(true) }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
        ) {
            Column(modifier = Modifier.fillMaxWidth()) {
                // 用户匹配部分
                androidx.constraintlayout.compose.ConstraintLayout(
                    modifier = Modifier.fillMaxWidth(),
                ) {
                    val (
                        leftRef, centerRef, rightRef, centerRef2,
                    ) = createRefs()

                    // 左侧女生信息
                    Box(
                        modifier = Modifier
                            .noRippleClickable {
                                MePageRouter.jumpToInfoPreviewActivity(
                                    context = context,
                                    userId = "",
                                    securityId = matchItem.female?.securityId ?: "",
                                    pageSource = PageSource.ACTIVITY_RECOMMEND
                                )
                            }
                            .constrainAs(leftRef) {
                                start.linkTo(parent.start)
                                end.linkTo(centerRef.end, 24.dp)
                                width = Dimension.fillToConstraints
                            }
                    ) {
                        Image(
                            painter = if (isSelect) painterResource(id = R.drawable.img_guest_new_heart_ai_tab_pink_bg_select)
                            else painterResource(id = R.drawable.img_guest_new_heart_ai_tab_pink_bg_unselect),
                            contentDescription = "",
                            modifier = Modifier
                                .aspectRatio(182 / 115.0f),
                            contentScale = ContentScale.FillBounds,
                        )

                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            // 女生头像
                            AsyncImage(
                                model = matchItem.female?.avatar ?: "".ifEmpty {
                                    matchItem.female?.tinyAvatar
                                        ?: "".ifEmpty { "https://via.placeholder.com/60" }
                                },
                                contentDescription = "Female Avatar",
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .size(28.dp)
                                    .clip(CircleShape)
                                    .border(2.dp, Color.White, CircleShape)
                            )

                            Spacer(modifier = Modifier.height(5.dp))

                            Row(verticalAlignment = Alignment.CenterVertically) {
                                // 女生信息
                                Text(
                                    text = "${matchItem.female?.signInNum}",
                                    style = TextStyle(
                                        fontSize = 28.sp,
                                        fontWeight = FontWeight(400),
                                        lineHeight = 34.sp,
                                        color = Color(0xFFA72DAF),
                                    ),
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                )

                                Spacer(modifier = Modifier.width(4.dp))

                                Text(
                                    text = "号女生",
                                    style = TextStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFFA72DAF),
                                    ),
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                )
                            }

                            Text(
                                text = "@${matchItem.female?.nickName?:""}",
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight(600),
                                    color = Color(0xFFAC5DAC),
                                ),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                            )
                        }
                    }

                    // 右侧男生信息
                    Box(
                        modifier = Modifier
                            .noRippleClickable {
                                MePageRouter.jumpToInfoPreviewActivity(
                                    context = context,
                                    userId = "",
                                    securityId = matchItem.male?.securityId,
                                    pageSource = PageSource.ACTIVITY_RECOMMEND
                                )
                            }
                            .constrainAs(rightRef) {
                                end.linkTo(parent.end)
                                start.linkTo(centerRef.start, 24.dp)
                                width = Dimension.fillToConstraints
                            }
                    ) {
                        Image(
                            painter = if (isSelect) painterResource(id = R.drawable.img_guest_new_heart_ai_tab_blue_bg_select)
                            else painterResource(id = R.drawable.img_guest_new_heart_ai_tab_blue_bg_unselect),
                            contentDescription = "",
                            modifier = Modifier
                                .aspectRatio(182 / 115.0f),
                            contentScale = ContentScale.FillBounds,
                        )

                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            horizontalAlignment = Alignment.End,
                        ) {
                            // 男生头像
                            AsyncImage(
                                model = matchItem.male?.avatar ?: "".ifEmpty {
                                    matchItem.male?.tinyAvatar ?: "".ifEmpty { "" }
                                },
                                contentDescription = "Male Avatar",
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .size(28.dp)
                                    .clip(CircleShape)
                                    .border(2.dp, Color.White, CircleShape)
                            )

                            Spacer(modifier = Modifier.height(5.dp))

                            Row(verticalAlignment = Alignment.CenterVertically) {
                                // 男生信息
                                Text(
                                    text = "${matchItem.male?.signInNum}",
                                    style = TextStyle(
                                        fontSize = 28.sp,
                                        fontWeight = FontWeight(400),
                                        lineHeight = 34.sp,
                                        color = Color(0xFF0071B3),
                                    ),
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                )

                                Spacer(modifier = Modifier.width(4.dp))

                                Text(
                                    text = "号男生",
                                    style = TextStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF0071B3),
                                    ),
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis,
                                )
                            }

                            Text(
                                text = "@${matchItem.male?.nickName?:""}",
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight(600),
                                    color = Color(0xFF0071B3),
                                ),
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                            )
                        }
                    }

                    // 中间互选图标
                    Box(
                        modifier = Modifier
                            .size(58.dp)
                            .constrainAs(centerRef) {
                                start.linkTo(parent.start)
                                end.linkTo(parent.end)
                                top.linkTo(parent.top)
                                bottom.linkTo(parent.bottom)
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        // 心形背景
                        Image(
                            painter =
                                if (isSelect) {
                                    painterResource(id = R.drawable.img_guest_new_heart_tab_ai_select)
                                } else {
                                    painterResource(id = R.drawable.img_guest_new_heart_tab_ai_unselect)
                                },
                            contentDescription = "Match Heart",
                            modifier = Modifier.size(58.dp)
                        )
                    }

                    if (isSelect) {
                        // 中间互选图标
                        Box(
                            modifier = Modifier
                                .size(119.dp, 68.dp)
                                .constrainAs(centerRef2) {
                                    start.linkTo(parent.start)
                                    end.linkTo(parent.end)
                                    top.linkTo(parent.top)
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            // 心形背景
                            Image(
                                painter = painterResource(id = R.drawable.image_match_guest_ai_success),
                                contentDescription = "Match Heart",
                                modifier = Modifier.size(119.dp, 68.dp)
                            )
                        }
                    }

                }

                if((matchItem.reason?:"").isNotEmpty()){
                    // 推荐理由部分
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                color = Color.White,
                                shape = RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp)
                            )
                            .padding(end = 8.dp, bottom = 8.dp, start = 8.dp)
                    ) {

                        Image(
                            painter = painterResource(id = R.drawable.img_guest_new_heart_tab_ai_select_left),
                            contentDescription = "Match Heart",
                            modifier = Modifier
                                .size(48.dp, 48.dp)
                                .align(Alignment.TopStart),
                            contentScale = ContentScale.FillBounds,
                        )

                        // 心形背景
                        Image(
                            painter = painterResource(id = R.drawable.img_guest_new_heart_tab_ai_select_text),
                            contentDescription = "Match Heart",
                            modifier = Modifier
                                .size(48.dp, 48.dp)
                                .align(Alignment.BottomEnd),
                            contentScale = ContentScale.FillBounds,
                        )

                        Text(
                            text = matchItem.reason ?: "",
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF292929),
                            ),
                            modifier = Modifier
                                .align(Alignment.CenterStart)
                                .padding(start = 8.dp, top = 8.dp),
                        )
                    }
                }
               
            }
        }
    }


    @Composable
    fun GenderFilterButtons(
        selectedGender: Int,
        onGenderSelected: (Int) -> Unit,
        userGender: Int = 1,//1表示当前用户是男性 2表示当前用户是女性
    ) {
        //如果当前用户是男性，左侧tab是女性 右侧是男性
        //如果当前用户是女性，左侧tab是男性 右侧是女性

        // 根据用户性别确定左右两侧显示的性别 0表示男 1表示女
        val leftGender = if (userGender == 1) 1 else 0  // 男性用户左侧显示女生(1)，女性用户左侧显示男生(0)
        val rightGender = if (userGender == 1) 0 else 1 // 男性用户右侧显示男生(0)，女性用户右侧显示女生(1)

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(78.dp)
                .padding(top = 20.dp, start = 16.dp, end = 16.dp, bottom = 16.dp) // 减少顶部间距为20dp
        ) {
            if (selectedGender == leftGender) {
                // 左侧按钮 - 选中状态
                Box(
                    modifier = Modifier
                        .size(88.dp, 42.dp)
                        .clip(RoundedCornerShape(50))
                        .noRippleClickable { onGenderSelected(leftGender) },
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = if (leftGender == 1) R.drawable.img_guest_new_heart_tab_female else R.drawable.img_guest_new_heart_tab_male),
                        contentDescription = ""
                    )
                }
            } else {
                // 左侧按钮 - 未选中状态
                Box(
                    modifier = Modifier
                        .size(88.dp, 36.dp)
                        .clip(RoundedCornerShape(50))
                        .background(
                            Color.White
                        )
                        .noRippleClickable { onGenderSelected(leftGender) },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = if (leftGender == 1) "女生" else "男生",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF5E5E5E),
                        )
                    )
                }
            }

            Spacer(modifier = Modifier.width(12.dp))

            if (selectedGender == rightGender) {
                // 右侧按钮 - 选中状态
                Box(
                    modifier = Modifier
                        .size(88.dp, 42.dp)
                        .clip(RoundedCornerShape(50))
                        .noRippleClickable { onGenderSelected(rightGender) },
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = if (rightGender == 1) R.drawable.img_guest_new_heart_tab_female else R.drawable.img_guest_new_heart_tab_male),
                        contentDescription = ""
                    )
                }
            } else {
                // 右侧按钮 - 未选中状态
                Box(
                    modifier = Modifier
                        .size(88.dp, 36.dp)
                        .clip(RoundedCornerShape(50))
                        .background(
                            Color.White
                        )
                        .noRippleClickable { onGenderSelected(rightGender) },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = if (rightGender == 1) "女生" else "男生",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF5E5E5E),
                        )
                    )
                }
            }
        }
    }

    @Composable
    private fun FollowNestedScrollToolbar(
        modifier: Modifier = Modifier,
        guestsUiState: MatchGuestsUiState,
        currentTabItems: List<TabItem>,
        title: String,
        selectedTabIndex: Int,
        onSelectedTabChange: (Int) -> Unit,
        scrollableAppBarHeight: Dp, // 可滚动的应用栏高度
        toolbarOffsetHeightPx: MutableState<Float>, //向上偏移量
        appBarAlpha: Float, // 顶部透明度
        isScrolledToMax: Boolean,
        activityInfo: ActivityLiveInfo.ActivityInfo?, // 添加活动信息参数
        onBoxSizeChanged: (IntSize) -> Unit,
        onTabSizeChanged: (IntSize) -> Unit,
        onNavigate: (String) -> Unit,
        nestedScrollConnection: NestedScrollConnection? = null,// 后期扩展多层次的嵌套滚动
    ) {
        // 应用栏最大向上偏移量
        val maxOffsetHeightPx = with(LocalDensity.current) {
            scrollableAppBarHeight.roundToPx().toFloat() - getAppBarHeightAndBarHeight().roundToPx()
                .toFloat()
        }

        Box(
            modifier = modifier
                .zIndex(1f) // 设置z轴高度
                .offset {
                    IntOffset(x = 0, y = toolbarOffsetHeightPx.value.roundToInt()) //设置偏移量
                }
                .fillMaxWidth()
                .onSizeChanged { size ->
                    onBoxSizeChanged(size)
                }
        ) {
            // 顶部高斯模糊背景
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(223.dp) // 固定背景高度为223dp
                    .zIndex(0f)
            ) {
                // 高斯模糊背景
                AsyncImage(
                    model = activityInfo?.showImages?.getOrElse(0) { "" },
                    contentDescription = "Background",
                    contentScale = ContentScale.FillWidth,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(223.dp)
                        .blur(radius = blurRadius)
                        .alpha(blurAlpha)
                )

                // 渐变遮罩层
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0x1AFFFFFF),
                                    Color(0xFFF5F5F5)
                                )
                            )
                        )
                )
            }

            Column {
                // 站位高度（此处是TopAppBar的区域）
                Spacer(modifier = Modifier.height(getAppBarHeightAndBarHeight()))
                // TopAppBar下面的区域内容
                DetailedInfo(guestsUiState.activityInfo)
                // 文件类型Tab（这里是模拟StickyHeader粘性页眉效果 == 模拟吸顶）
                AlbumFileTypeTabRow(
                    receiveLikeCount = guestsUiState.receiveLikeCount,
                    likeModelJumpUrl = guestsUiState.likeModelJumpUrl,
                    tabItems = currentTabItems,
                    selectedTabIndex = selectedTabIndex,
                    onTabSelected = onSelectedTabChange,
                    onTabSizeChanged = onTabSizeChanged,
                    appBarAlpha = appBarAlpha
                )
            }

            CustomTopAppBar(
                appBarAlpha = appBarAlpha,
                activityTitle = activityInfo?.title ?: "",
                modifier = modifier
                    .offset {
                        IntOffset(
                            x = 0,
                            y = -toolbarOffsetHeightPx.value.roundToInt() //保证应用栏是始终不动的
                        )
                    }
                    .fillMaxWidth(),
            )

//            if (isScrolledToMax) {
//                HorizontalDivider(
//                    modifier = modifier
//                        .padding(top = getAppBarHeightAndBarHeight())
//                        .offset {
//                            IntOffset(
//                                x = 0,
//                                y = -toolbarOffsetHeightPx.value.roundToInt() //保证应用栏是始终不动的
//                            )
//                        }
//                        .fillMaxWidth(),
//                )
//            }
        }
    }

    @Composable
    fun TopAppBar(appBarAlpha: Float, activityTitle: String) {
        val isTransparent = appBarAlpha < 0.9f

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(modifier = Modifier.size(24.dp), onClick = { activity?.onBackPressed() }) {
                Image(
                    painter = R.drawable.common_ic_black_back.painterResource(),
                    contentDescription = "",
                )
            }

            Text(
                text = if (isTransparent) "活动现场" else activityTitle,
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.weight(1f, fill = true)
            )


            // 空白占位，与返回按钮对称
            Box(modifier = Modifier.size(24.dp))
        }
    }

    // 标题栏高度
    fun getAppBarHeight(): Dp {
        return 44.dp
    }

    @Composable
    fun getAppBarHeightAndBarHeight(): Dp {
        // 获取状态栏高度
        val localDensity = LocalDensity.current
        val statusBarHeightPx = WindowInsets.statusBars.getTop(localDensity)
        val statusBarHeightDp = with(localDensity) { statusBarHeightPx.toDp() }
        return getAppBarHeight() + statusBarHeightDp
    }


    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun CustomTopAppBar(
        appBarAlpha: Float = 0f,
        activityTitle: String,
        modifier: Modifier = Modifier,
    ) {
        // 顶部区域（状态栏+标题栏）
        Box(
            modifier = modifier
                .height(getAppBarHeightAndBarHeight())
                .fillMaxWidth()
        ) {
            // 顶部标题栏白色背景（随滚动渐变）
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White.copy(alpha = appBarAlpha))
            )

            // 标题栏内容（不包括状态栏部分）
            Box(
                modifier = Modifier
                    .statusBarsPadding() // 这里应用了状态栏的padding，确保内容不会被状态栏遮挡
                    .fillMaxWidth()
            ) {
                TopAppBar(appBarAlpha, activityTitle)
            }
        }
    }

    // 默认的Tab配置移到这里
    private val allTabItems = listOf(
        TabItem("到场嘉宾", null, null),
        TabItem("匹配", R.drawable.img_guest_new_heart_ai_color, R.drawable.img_guest_new_heart_ai)
    )


    // tab高度
    val tabRowHeight = 62.dp

    // 心动互选高度
    val heartSelectHeight = 80.dp

    @Preview
    @Composable
    fun PreviewHeartSelectRow() {
        HeartSelectRow(
            count = 1,
            1.0f
        ) {

        }
    }

    @Composable
    fun HeartSelectRow(count: Int = 0, appBarAlpha: Float, onClick: () -> Unit) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(heartSelectHeight)
                .background(Color.White.copy(alpha = appBarAlpha))
                .padding(start = 16.dp, end = 16.dp)
                .noRippleClickable {
                    onClick()
                },
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.img_guest_new_heart_bg),
                contentDescription = "",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .height(64.dp)
                    .fillMaxWidth(
                    )
                    .border(
                        width = 2.dp,
                        color = Color(0xFFFFFFFF),
                        shape = RoundedCornerShape(16.dp)
                    )
                    .clip(RoundedCornerShape(16.dp))
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // 爱心图标
                Box(
                    modifier = Modifier
                        .size(28.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.img_guest_new_heart_icon),
                        contentDescription = "Heart",
                        modifier = Modifier.size(28.dp)
                    )
                }

                Spacer(modifier = Modifier.width(12.dp))

                Text(
                    text = "心动互选",
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontFamily = boldFontFamily(),
                        color = Color(0xFF292929)
                    ),
                )

                Spacer(modifier = Modifier.weight(1f))

                if (count > 0) {

                    Row {
                        Text(
                            text = "本场已收到${count}个心动",
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF882976),
                            )
                        )

                        Image(
                            painter = painterResource(id = R.drawable.img_guest_new_heart_icon_flower),
                            contentDescription = "Heart",
                            modifier = Modifier.size(14.dp, 20.dp)
                        )
                    }


                    Spacer(modifier = Modifier.width(4.dp))
                }


                Icon(
                    painter = painterResource(id = R.drawable.icon_arrow_right_20),
                    contentDescription = "Arrow Right",
                    tint = Color(0xFF292929),
                    modifier = Modifier.size(20.dp)
                )
            }

        }
    }


    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun AlbumFileTypeTabRow(
        receiveLikeCount: Int = 0,
        likeModelJumpUrl: String? = "",
        tabItems: List<TabItem>,
        selectedTabIndex: Int,
        onTabSelected: (Int) -> Unit,
        onTabSizeChanged: (IntSize) -> Unit,
        appBarAlpha: Float = 0f,
    ) {
        Column(modifier = Modifier.onSizeChanged(onTabSizeChanged)) {
            HeartSelectRow(count = receiveLikeCount, appBarAlpha = appBarAlpha, onClick = {
                ProtocolHelper.parseProtocol(likeModelJumpUrl)
            })
            ConstraintLayout(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(tabRowHeight)
            ) {
                val (tabRowRef, lineRef) = createRefs()

                //             添加一个固定宽度的分割线
                Divider(
                    thickness = 2.dp,
                    color = Color(0xFFEBEBEB),
                    modifier = Modifier
                        .fillMaxWidth()
                        .constrainAs(lineRef) {
                            bottom.linkTo(parent.bottom, 3.dp)
                        }
                )
                ScrollableTabRow(
                    selectedTabIndex = selectedTabIndex,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 8.dp)
                        .height(tabRowHeight)
                        .constrainAs(tabRowRef) {
                            top.linkTo(parent.top)
                            start.linkTo(parent.start)
                        },
                    containerColor = Color.White.copy(alpha = appBarAlpha),
                    contentColor = Color(0xFFF5F5F5),
                    edgePadding = 0.dp,
                    indicator = { tabPositions ->
                        if (selectedTabIndex < tabPositions.size) {
                            // 自定义指示器
                            Box(
                                Modifier
                                    .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                    .height(10.dp)
                                    .padding(horizontal = 8.dp)
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.img_guest_new_heart_tab_line),
                                    contentDescription = "Selected Tab",
                                    contentScale = ContentScale.FillHeight,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(10.dp)
                                )
                            }
                        }
                    },
                    divider = {
                        // 移除这里的divider
                    }
                ) {
                    tabItems.forEachIndexed { index, tabItem ->
                        Tab(
                            selected = selectedTabIndex == index,
                            onClick = { onTabSelected(index) },
                            modifier = Modifier.height(tabRowHeight),
                            content = {
                                Row(
                                    modifier = Modifier.padding(vertical = 8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    if (tabItem.icon != null && tabItem.unSelectIcon != null) {
                                        val icon =
                                            if (selectedTabIndex == index) tabItem.icon else tabItem.unSelectIcon
                                        Image(
                                            painter = painterResource(icon),
                                            contentDescription = tabItem.title,
                                            modifier = Modifier.size(24.dp)
                                        )

                                        Spacer(modifier = Modifier.height(2.dp))
                                    }

                                    // 显示文本
                                    Text(
                                        text = tabItem.title,
                                        style = TextStyle(
                                            fontSize = 18.sp,
                                            fontWeight = if (selectedTabIndex == index) FontWeight.Bold else FontWeight.Normal,
                                            color = if (selectedTabIndex == index) Color(0xFF191919) else Color(
                                                0xFFB2B2B2
                                            ),
                                            textAlign = TextAlign.Center
                                        )
                                    )
                                }
                            }
                        )
                    }
                }


            }

        }
    }

    /**
     * 详情信息
     */
    @Composable
    private fun DetailedInfo(activityInfo: ActivityLiveInfo.ActivityInfo?) {
        val localDensity = LocalDensity.current

        // 计算状态栏高度
        val guestsUiState = viewModel.uiState.value
        // 活动卡片区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(223.dp - getAppBarHeightAndBarHeight()),
            contentAlignment = Alignment.Center
        ) {
            // 活动卡片
            ActivityCard(activityInfo, onClick = {
                ProtocolHelper.parseProtocol(guestsUiState.activityInfo?.jumpUrl)
            })
        }
    }

    @Composable
    fun ActivityCard(activityInfo: ActivityLiveInfo.ActivityInfo?, onClick: () -> Unit) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .noRippleClickable {
                        onClick()
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 活动图片（网络图片）
                AsyncImage(
                    model = activityInfo?.showImages?.getOrElse(0) { "" },
                    contentDescription = "Event",
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .size(78.dp)
                        .clip(RoundedCornerShape(12.dp))
                )

                Column(
                    modifier = Modifier
                        .padding(start = 10.dp)
                        .weight(1f)
                ) {
                    // 活动标题
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = activityInfo?.title ?: "",
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.Black
                            ),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1.0f, fill = true)
                        )
                        Image(
                            painter = R.drawable.icon_arrow_right_20.painterResource(),
                            contentDescription = "",
                            modifier = Modifier.size(20.dp)
                        )
                    }


                    Spacer(modifier = Modifier.height(8.dp))

                    // 活动时间
                    Text(
                        text = activityInfo?.timeStr ?: "",
                        style = TextStyle(
                            fontSize = 13.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF5E5E5E),
                        )
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    // 活动地点
                    Text(
                        text = activityInfo?.address ?: "",
                        style = TextStyle(
                            fontSize = 13.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF5E5E5E),
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }

    /**
     * 将像素转换为dp
     */
    fun pxToDp(px: Int, context: Context): Int {
        val density = context.resources.displayMetrics.density
        return (px / density).toInt()
    }

    @Composable
    fun AdaptiveTagRow(
        tags: List<String>, 
        useBoy: Boolean = true,
        spacing: Dp = 4.dp
    ) {
        if (tags.isEmpty()) return
        
        SubcomposeLayout { constraints ->
            val availableWidth = constraints.maxWidth
            var currentWidth = 0
            val visibleTags = mutableListOf<String>()
            val spacingPx = spacing.roundToPx()
            
            // 逐个测量标签，确定能放下多少个
            for (tag in tags) {
                val tagPlaceable = subcompose("tag_measure_$tag") {
                    Box(
                        modifier = Modifier
                            .background(
                                color = if (useBoy) Color(0x3353C6FF) else Color(0x33FF8FFF),
                                shape = RoundedCornerShape(6.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = tag,
                            style = TextStyle(
                                fontSize = 11.sp,
                                fontWeight = FontWeight(500),
                                color = if (useBoy) Color(0xFF0071B3) else Color(0xFFB400B4),
                            ),
                            maxLines = 1
                        )
                    }
                }[0].measure(constraints)
                
                val tagWidth = tagPlaceable.width
                val spacingNeeded = if (visibleTags.isNotEmpty()) spacingPx else 0
                
                if (currentWidth + spacingNeeded + tagWidth <= availableWidth) {
                    visibleTags.add(tag)
                    currentWidth += spacingNeeded + tagWidth
                } else {
                    break // 如果放不下这个标签，就停止
                }
            }
            
            // 实际布局可见的标签
            val placeables = mutableListOf<Placeable>()
            
            visibleTags.forEach { tag ->
                val tagPlaceable = subcompose("tag_$tag") {
                    Box(
                        modifier = Modifier
                            .background(
                                color = if (useBoy) Color(0x3353C6FF) else Color(0x33FF8FFF),
                                shape = RoundedCornerShape(6.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = tag,
                            style = TextStyle(
                                fontSize = 11.sp,
                                fontWeight = FontWeight(500),
                                color = if (useBoy) Color(0xFF0071B3) else Color(0xFFB400B4),
                            ),
                            maxLines = 1
                        )
                    }
                }[0].measure(constraints)
                placeables.add(tagPlaceable)
            }
            
            val height = placeables.maxOfOrNull { it.height } ?: 0
            
            layout(availableWidth, height) {
                var x = 0
                
                placeables.forEach { placeable ->
                    placeable.place(x, (height - placeable.height) / 2)
                    x += placeable.width + spacingPx
                }
            }
        }
    }

}
