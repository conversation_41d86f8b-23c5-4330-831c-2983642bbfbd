package com.kanzhun.marry.matching.fragment.home

import android.os.Bundle
import android.util.TypedValue
import androidx.recyclerview.widget.GridLayoutManager
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.constant.LivedataKeyMatching
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.liveEventObserve
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.common.kotlin.ui.recyclerview.CommonListAdapter
import com.kanzhun.common.kotlin.ui.recyclerview.setListData
import com.kanzhun.common.kotlin.ui.statusbar.addStatusMargin
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.foundation.bean.SerializableMap
import com.kanzhun.foundation.kotlin.common.performance.PerformManager
import com.kanzhun.foundation.model.matching.MatchingLikeModel
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.foundation.sp.SpManager
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.adapter.provider.InteractListCardType
import com.kanzhun.marry.matching.adpter.home.MatchingGuidePageAdapter
import com.kanzhun.marry.matching.adpter.home.MatchingRecommendPageAdapter
import com.kanzhun.marry.matching.api.model.IPageBean
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.kanzhun.marry.matching.bean.MatchGuestsItemBean
import com.kanzhun.marry.matching.bean.MatchGuestsTitleBean
import com.kanzhun.marry.matching.databinding.MatchingFragmentGuestsListBinding
import com.kanzhun.marry.matching.fragment.home.performance.MatchGuestGridProvider
import com.kanzhun.marry.matching.fragment.home.performance.MatchGuestTitleProvider
import com.kanzhun.marry.matching.fragment.home.performance.MatchingGuestsUpdateDataPerformance
import com.kanzhun.marry.matching.fragment.home.performance.MatchingGuideExposePerformance
import com.kanzhun.marry.matching.fragment.home.performance.MatchingHomeExposePerformance
import com.kanzhun.marry.matching.viewmodel.MatchingGuestsListViewModel
import com.kanzhun.utils.base.LList
import com.techwolf.lib.tlog.TLog
import com.youth.banner.Banner
import com.youth.banner.transformer.AlphaPageTransformer

class MatchGuestsPagerFragment :
    BaseBindingFragment<MatchingFragmentGuestsListBinding, MatchingGuestsListViewModel>() {

    val mPageAdapter: MatchingGuidePageAdapter by lazy {
        MatchingGuidePageAdapter(emptyList())
    }

    //数据曝光埋点
    private val matchingHomeExposePerformance: MatchingGuideExposePerformance by lazy {
        MatchingGuideExposePerformance(this)
    }

    private val mAdapter: CommonListAdapter<BaseListItem> = CommonListAdapter()

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }


    override fun preInit(arguments: Bundle) {
        if(arguments.getSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE) is SerializableMap){
            mViewModel.mMap = (arguments.getSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE) as SerializableMap).map as MutableMap<String, Any>
        }
    }
    fun initModel(model:Int,init:Boolean){
        mBinding.apply {
            val qMUIRoundButton = idAppTitleView.getTvCommonRight()
            if(model == 0){
                qMUIRoundButton.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    R.drawable.match_icon_guests_grid,
                    0,
                    0,
                    0
                )
                qMUIRoundButton.text = "列表模式"
                idText.visible()
                viewPager.visible()
                idRecyclerView.gone()
                if(!init && LList.getCount(mPageAdapter.getAllData()) == 0){
                    mViewModel.recommendList.postValue(mViewModel.recommendList.value)
                }
            }else{
                qMUIRoundButton.text = "划卡模式"
                qMUIRoundButton.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    R.drawable.match_icon_guests_card,
                    0,
                    0,
                    0
                )
                idRecyclerView.visible()
                idText.gone()
                viewPager.gone()
                if(!init && mAdapter.data.size == 0){
                    mViewModel.recommendList.postValue(mViewModel.recommendList.value)
                }
            }

        }


    }

    var model = SpManager.get().user().getInt(Constants.SP_GUESTS_MODEL,1)

    override fun initView() {
        mBinding.idAppTitleView.asBackButton()
        mBinding.idAppTitleView.addStatusMargin()
        val qMUIRoundButton = mBinding.idAppTitleView.getTvCommonRight()
        qMUIRoundButton.setTextSize(TypedValue.COMPLEX_UNIT_DIP,14.0f)
        qMUIRoundButton.visible()
        initModel(model,true)
        qMUIRoundButton.onClick {
            if(model == 0){
                model = 1
            }else{
                model = 0
            }
            SpManager.get().user().edit().putInt(Constants.SP_GUESTS_MODEL,model).apply()
            initModel(model,false)
        }

        initStateLayout()
        //卡片配置
        initViewPager()
        //数据更新相关逻辑
        performManager.addPerformance(MatchingGuestsUpdateDataPerformance(this,mViewModel))

        //曝光上报埋点相关逻辑
        performManager.addPerformance(matchingHomeExposePerformance)

        liveEventObserve<MatchingLikeModel?>(LivedataKeyMatching.MATCH_SEND_LIKE_SUCCESS) {
            TLog.info("SendLike", "MatchingLikeEventPerformance")
            val hasShowToast = it?.hasShowToast ?: true
            it?.pageSource = PageSource.ACTIVITY_RECOMMEND
            MatchingPageRouter.handleSendLikeResult(requireActivity(), it)
            //更新卡片按钮状态
            if (it?.likeEachOther?.otherInfo != null && it?.likeEachOther?.myInfo != null) {
                mPageAdapter.updateSendLikeStatus(it.originToUserId, Constants.RELATION_FRIEND, hasShowToast)
            } else {
                mPageAdapter.updateSendLikeStatus(it?.originToUserId, Constants.RELATION_LIKE, hasShowToast)
            }
        }
    }

    /**
     * 卡片相关配置
     */
    private fun initViewPager() {
        mBinding.viewPager.addPageTransformer(AlphaPageTransformer(0.8F))
        mBinding.viewPager.setBannerGalleryEffect(12, 8, 0.90F)
        val banner = mBinding.viewPager as Banner<IPageBean, MatchingGuidePageAdapter>
        banner.apply {
            setAdapter(mPageAdapter)
        }
        if (context != null){
            mAdapter.registerItemProvider(0, MatchGuestTitleProvider(requireContext()))
            mAdapter.registerItemProvider(1, MatchGuestGridProvider(requireContext()))
        }

        val mGridLayoutManager = GridLayoutManager(context,2).also {
            it.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (mAdapter.getItemViewType(position)) {
                        0 -> 2
                        else -> 1
                    }
                }
            }
        }
        mBinding.idRecyclerView.layoutManager = mGridLayoutManager
        mBinding.idRecyclerView.adapter = mAdapter
    }

    private fun initStateLayout() {
        mBinding.stateLayout.setRetryIds(R.id.btErrorRetry)
        mViewModel.showLoading()
    }

    override fun initData() {
        mViewModel.getRecommendData(mViewModel.getMap())
        mViewModel.recommendList.observe(this) {
            resetLoadingState()
            mBinding.idAppTitleView.setTitle(it?.topContent ?:"")
            mBinding.idText.text = it?.bottomContent

            if(model == 0){
                if (it?.newData == true) {
                    if (mPageAdapter.itemCount > 0 && mBinding.viewPager.currentItem > 0) {
                        mBinding.viewPager.setCurrentItem(0, false)
                    }
                    mPageAdapter.setDatas(it.list)
                    //第0个需要手动通知下
                    matchingHomeExposePerformance.exposeCard(0)
                } else {
                    if (it != null)mPageAdapter.updateList(it.list)
                    matchingHomeExposePerformance.exposeCard(mBinding.viewPager.currentItem)
                }
            }else{
                val qList = mutableListOf<BaseListItem>()
                val titleBean = MatchGuestsTitleBean(it?.activityInfo)
                titleBean.mLocalItemType = 0
                qList.add(titleBean)
                it?.list?.forEach {
                    if(it is RecommendUser){
                        val itemBean = MatchGuestsItemBean(it)
                        itemBean.mLocalItemType = 1
                        qList.add(itemBean)
                    }
                }
                mAdapter.setNewInstance(qList)
            }
        }
    }

    private fun resetLoadingState() {
        if (mBinding.smartRefreshLayout.isRefreshing) {
            mBinding.smartRefreshLayout.finishRefresh()
        }
    }

    override fun onRetry() {
        mViewModel.getRecommendData(mViewModel.getMap())
    }

    override fun onDestroyView() {
        super.onDestroyView()
        performManager.unRegister()
    }

    override fun getStateLayout() = mBinding.stateLayout


}