package com.kanzhun.marry.matching.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class AiMatchInfo(
    @SerializedName("title")
    val title: String? = "",

    @SerializedName("matchType")
    val matchType: Int = 1,// 1 1v1 模式 2 1v多模式
    
    @SerializedName("matchList")
    val matchList: List<MatchItem>? = emptyList()
) : Serializable {

    data class MatchItem(
        @SerializedName("male")
        val male: UserInfo?,
        
        @SerializedName("female")
        val female: UserInfo?,
        
        @SerializedName("reason")
        val reason: String? = "",
        
        @SerializedName("relationStatus")
        val relationStatus: Int = 0,

        @SerializedName("matchDegree")
        val matchDegree: Int = 98
    ) : Serializable

    data class UserInfo(
        @SerializedName("securityId")
        val securityId: String? = "",
        
        @SerializedName("nickName")
        val nickName: String? = "",
        
        @SerializedName("avatar")
        val avatar: String? = "",
        
        @SerializedName("tinyAvatar")
        val tinyAvatar: String? = "",
        
        @SerializedName("signInNum")
        val signInNum: Int? = 0,

        @SerializedName("encUserId")
        val encUserId: String = "",

        @SerializedName("intro")
        val intro: String? = "",

        @SerializedName("recommendTag")
        val recommendTag: List<String>? = emptyList<String>()
    ) : Serializable
    
    // 关系状态常量
    companion object {
        const val LIKE_TA = 11 // 你喜欢TA
        const val LIKE_ME = 12 // TA喜欢你
        const val NORMAL = 20 // 普通好友
        const val DATING = 30 // 约会中
        const val LOVER = 40 // 情侣
        const val HISTORY = 50 // 历史
        const val DELETE = 60 // 删除
    }
} 