package com.kanzhun.marry.matching.activity

import android.content.Context
import android.content.Intent
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.util.ArrayMap
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.constant.LivedataKeyMatching
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.relationStatusStr
import com.kanzhun.common.kotlin.ext.setTextAndSelection
import com.kanzhun.common.kotlin.ext.showSoftInput
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.button.enableButton
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.statusbar.statusBarColor
import com.kanzhun.common.util.ActivityAnimType
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.common.views.edittext.LengthNoticeFilter
import com.kanzhun.foundation.api.model.SendLikeModel
import com.kanzhun.foundation.api.model.SendLikePageSource
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.model.matching.MatchingLikeModel
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.utils.point.toSendLikeButtonSource
import com.kanzhun.foundation.utils.point.toSendLikeButtonSource2
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.api.MatchingApi
import com.kanzhun.marry.matching.databinding.MatchingActivitySendLikeV2Binding
import com.kanzhun.marry.matching.utils.point.MatchPointReporter
import com.kanzhun.utils.T
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import com.qmuiteam.qmui.util.QMUIKeyboardHelper
import java.io.Serializable

class MatchingSendLikeActivity :
    BaseBindingActivity<MatchingActivitySendLikeV2Binding, MatchingSendLikeViewModel>() {

        private var showNoUseAIDialog = false

    override fun preInit(intent: Intent) {
        mViewModel.sendLikeModel =
            intent.getSerializableExtra(BundleConstants.BUNDLE_DATA) as SendLikeModel
    }

    fun getSource(source: PageSource?):String{
        return when (source) {
            PageSource.CHAT_TO_PREVIEW_ACTIVITY ->"喜欢我-聊天-个人信息页"
            PageSource.F1_RECOMMEND_CHILD_FRAGMENT -> "首页推荐卡片"
            PageSource.ACTIVITY_RECOMMEND ->"签到推送嘉宾列表划卡模式"
            PageSource.CHAT->"聊天回复喜欢"
            else ->"个人信息页"
        }
    }

    override fun initView() {
        if (mViewModel.sendLikeModel?.useAi == true){

            showNoUseAIDialog = true
            mViewModel.getLikeAiReason(mViewModel.sendLikeModel!!.userId)

            mViewModel.mAiOpenChatMsgBean.observe(this@MatchingSendLikeActivity){
                if (it.message?.isNotEmpty() == true){
                    mBinding.apply {
                        llBottomFloat2.visible()
                        tvTemplate.gone()
                        idAutoEdit.visible()
                        idTVTryWrite.text = it.message
                        idAutoEdit.onClick {
                            val currentText = etText.text?.toString() ?: ""
                            if (currentText.isNotEmpty()) {
                                etText.setText("${currentText}\n${mViewModel.mAiOpenChatMsgBean.value?.message}")
                            } else {
                                etText.setText(mViewModel.mAiOpenChatMsgBean.value?.message)
                            }
                            etText.setSelection(etText.text?.length ?: 0)
                            showNoUseAIDialog = false
                        }

                        idChange.onClick {
                            mViewModel.getLikeAiReason(mViewModel.sendLikeModel!!.userId)
                        }

                        QMUIKeyboardHelper.setVisibilityEventListener(this@MatchingSendLikeActivity,object :
                            QMUIKeyboardHelper.KeyboardVisibilityEventListener{
                            override fun onVisibilityChanged(
                                isOpen: Boolean,
                                heightDiff: Int,
                            ): Boolean {
                                if (isOpen){
                                    val lp = idScrollView.layoutParams
                                    lp.height = QMUIDisplayHelper.getScreenHeight(this@MatchingSendLikeActivity) - QMUIDisplayHelper.dp2px(this@MatchingSendLikeActivity,88) - heightDiff -QMUIDisplayHelper.dp2px(this@MatchingSendLikeActivity,80) + QMUIDisplayHelper.getStatusBarHeight(this@MatchingSendLikeActivity)
                                    idScrollView.layoutParams = lp
                                }else{
                                    val lp = idScrollView.layoutParams
                                    lp.height = ViewGroup.LayoutParams.WRAP_CONTENT
                                    idScrollView.layoutParams = lp
                                }
                                return false
                            }

                        })
                    }
                }
            }
        }else{
            QMUIKeyboardHelper.showKeyboard(mBinding.etText, true)
            mBinding.etText.requestFocus()
        }
        mBinding.tvTemplate.visible(!mViewModel.sendLikeModel?.chatWordList.isNullOrEmpty())

        if(mViewModel.sendLikeModel?.autoLike == true){
            statusBarColor(color = R.color.common_translate.toResourceColor())
            mBinding.etText.setText("Hi 很高兴认识你")
            sendLike(getSource(mViewModel.sendLikeModel?.pageSource))
        }else{
            statusBarColor(color = R.color.common_color_000000_70.toResourceColor())
            mBinding.idRootLayout.visible()
            initEditText()
            mBinding.ivAvatar.load(mViewModel.sendLikeModel?.avatar)
            mBinding.tvUserName.text = mViewModel.sendLikeModel?.userName
            mBinding.ivClose.setOnClickListener {
                onBackPressed()
                reportClickLike(
                    mViewModel.sendLikeModel?.userId,
                    "关闭",
                    mViewModel.sendLikeModel?.pageSource,
                    mViewModel.sendLikeModel?.relationStatus
                )
            }
            mBinding.btnSendLike.setOnClickListener {
                sendLike(getSource(mViewModel.sendLikeModel?.pageSource))
                reportClickLike(
                    mViewModel.sendLikeModel?.userId,
                    "发送",
                    mViewModel.sendLikeModel?.pageSource,
                    mViewModel.sendLikeModel?.relationStatus
                )
            }

            mBinding.tvTemplate.clickWithTrigger {
                changeTemplate()
                reportClickLike(
                    mViewModel.sendLikeModel?.userId,
                    "帮我写个开场白",
                    mViewModel.sendLikeModel?.pageSource,
                    mViewModel.sendLikeModel?.relationStatus
                )
            }
        }

    }

    private fun initEditText() {
        val inputFilters = arrayOfNulls<InputFilter>(1)
        val filter = LengthNoticeFilter(50, R.string.matching_send_like_text_limit_tip)
        inputFilters[0] = filter
        mBinding.etText.filters = inputFilters
        mBinding.etText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                s.toString().trim().let {
                    mBinding.tvCount.text = it.length.toString()
                    mBinding.btnSendLike.enableButton(it.isNotBlank(), false)
                }
            }

        })
        mBinding.etText.setText("")
        showSoftInput(mBinding.etText, true)
        mBinding.etText.clickWithTrigger {
            reportClickLike(
                mViewModel.sendLikeModel?.userId,
                "输入框",
                mViewModel.sendLikeModel?.pageSource,
                mViewModel.sendLikeModel?.relationStatus
            )
        }

    }

    private fun changeTemplate() {
        mViewModel.sendLikeModel?.chatWordList?.let {
            if (it.isNotEmpty()) {
                mBinding.etText.setTextAndSelection(it.random())
            }
        }
    }

    private fun sendLike(sourceName: String?) {
//        showProgressDialog(R.string.common_sending)
        mViewModel.sendLike(mBinding.etText.text.toString().trim(),sourceName)
    }

    override fun initData() {
        mViewModel.sendLikeResult.observe(this) {
//            dismissProgressDialog()
            if (it.success) {
                onBackPressed()
                val result: MatchingLikeModel = it.model ?: MatchingLikeModel()
                result.originToUserId = this.mViewModel.sendLikeModel!!.userId
                result.showNoUseAIDialog = showNoUseAIDialog

//                if (mViewModel.sendLikeModel?.pageSource == PageSource.OTHER_MOOD_DETAIL) {
//                    LiveEventBus.config(LivedataKeyMatching.MOOD_SEND_LIKE_SUCCESS).autoClear(true)
//                        .lifecycleObserverAlwaysActive(false)
//                    LiveEventBus.get<MatchingLikeModel>(LivedataKeyMatching.MOOD_SEND_LIKE_SUCCESS)
//                        .postDelay(result, 200)
//                    TLog.print("zl_log", "SeeMeListFragment: 喜欢发送成功，返回客态页面，跳转聊天页");
//                } else {
                    LiveEventBus.config(LivedataKeyMatching.MATCH_SEND_LIKE_SUCCESS).autoClear(true)
                        .lifecycleObserverAlwaysActive(false)
                    LiveEventBus.get<MatchingLikeModel>(LivedataKeyMatching.MATCH_SEND_LIKE_SUCCESS)
                        .postDelay(result, 200)
//                }
            }else{
                if (mViewModel.sendLikeModel?.autoLike == true){
                    onBackPressed()
                }
            }
        }
    }

    override fun onRetry() {
    }

    override fun onBackPressed() {
        QMUIKeyboardHelper.hideKeyboard(mBinding.etText)
        AppUtil.finishActivity(this, ActivityAnimType.ALPHA)
    }

    override fun getStateLayout() = null

    companion object {
        fun intent(context: Context, sendLikeModel: SendLikeModel) {
            val intent = Intent(context, MatchingSendLikeActivity::class.java)
            intent.putExtra(BundleConstants.BUNDLE_DATA, sendLikeModel)
            AppUtil.startActivity(context, intent, ActivityAnimType.ALPHA)
        }
    }

    /**
     * 发送喜欢留言弹窗点击
     */
    private fun reportClickLike(userId: String?, type: String?, source: PageSource?, friend: Int?) {
        reportPoint("like-message-popup-click") {
            this.actionp2 = source?.toSendLikeButtonSource()
            this.type = type
            this.peer_id = userId
            this.source = source?.toSendLikeButtonSource2()
            this.friend = friend?.relationStatusStr()
        }

    }
}

data class GetAiOpenChatMsgBean(val message: String? = ""): Serializable

class MatchingSendLikeViewModel : BaseViewModel() {

    var sendLikeModel: SendLikeModel? = null

    var sendLikeResult: MutableLiveData<SendLikeResult> = MutableLiveData()
    var mAiOpenChatMsgBean: MutableLiveData<GetAiOpenChatMsgBean> = MutableLiveData()

    fun getLikeAiReason(friendId: String){
        val baseResponseObservable =
            RetrofitManager.getInstance().createApi(MatchingApi::class.java).getAiOpenChatMsg(friendId)
        HttpExecutor.execute<GetAiOpenChatMsgBean?>(
            baseResponseObservable,
            object : BaseRequestCallback<GetAiOpenChatMsgBean?>() {

                override fun onSuccess(data: GetAiOpenChatMsgBean?) {
                    if (data != null) mAiOpenChatMsgBean.postValue(data)

                }

                override fun dealFail(reason: ErrorReason?) {

                }

            })
    }


    fun sendLike(inputString: String?,sourceName:String? = "") {
        if (inputString.isNullOrBlank()) {
            return
        }
        val map = ArrayMap<String, Any?>()
        map["userId"] = sendLikeModel!!.userId
        map["resourceId"] = sendLikeModel!!.resId ?: ""
        map["resourceType"] = sendLikeModel!!.resourceType
        map["likeReason"] = inputString
        map["subType"] = sendLikeModel!!.subType.toString()
        map["pageSource"] = SendLikePageSource.MATCHING_HOME_PAGE_SOURCE
        map["encMoodId"] = sendLikeModel!!.encMoodId ?: ""
        map["securityId"] = sendLikeModel!!.securityId
        val baseResponseObservable =
            RetrofitManager.getInstance().createApi(MatchingApi::class.java).sendLike(map)
        HttpExecutor.execute<MatchingLikeModel?>(
            baseResponseObservable,
            object : BaseRequestCallback<MatchingLikeModel?>() {

                override fun onSuccess(data: MatchingLikeModel?) {
                    sendLikeResult.postValue(SendLikeResult(true, data))
                    sendLikePoint("",sourceName,inputString)
                }

                override fun dealFail(reason: ErrorReason?) {
                    T.ss(reason?.errReason)
                    sendLikeResult.postValue(SendLikeResult(false))
                    sendLikePoint(reason?.errReason,sourceName,inputString)
                }

            })
    }

    private fun sendLikePoint(msg: String?,sourceName: String?,likeReason: String?) {
        sendLikeModel?.run {
            MatchPointReporter.reportSendLike(userId, pageSource, msg, relationStatus,sourceName = sourceName, likeReason = likeReason)

        }
    }

}

data class SendLikeResult(val success: Boolean, val model: MatchingLikeModel? = null)