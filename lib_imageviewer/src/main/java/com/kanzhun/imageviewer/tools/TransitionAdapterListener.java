package com.kanzhun.imageviewer.tools;

import android.os.Build;
import android.transition.Transition;

import androidx.annotation.RequiresApi;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2020/6/19 4:38 PM
 * @Description:
 */
@RequiresApi(api = Build.VERSION_CODES.KITKAT)
public
class TransitionAdapterListener implements Transition.TransitionListener {
    @Override
    public void onTransitionStart(Transition transition) {

    }

    @Override
    public void onTransitionEnd(Transition transition) {

    }

    @Override
    public void onTransitionCancel(Transition transition) {

    }

    @Override
    public void onTransitionPause(Transition transition) {

    }

    @Override
    public void onTransitionResume(Transition transition) {

    }
}
