package com.kanzhun.imageviewer.impl;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.imageviewer.interfaces.IIndicator;
import com.kanzhun.imageviewer.tools.CommonExt;

/**
 * @Author: MikaelZero
 * @CreateDate: 2020/6/13 5:39 PM
 * @Description:
 */
public class NumIndicator implements IIndicator {

    private TextView numTv;
    private int originBottomMargin = 10;
    private int currentBottomMargin = originBottomMargin;

    @Override
    public void attach(FrameLayout parent) {
        FrameLayout.LayoutParams indexLp = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, QMUIDisplayHelper.dp2px(parent.getContext(), 36));
        indexLp.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;

        numTv = new TextView(parent.getContext());
        numTv.setGravity(Gravity.CENTER_VERTICAL);
        numTv.setLayoutParams(indexLp);
        numTv.setTextColor(Color.WHITE);
        numTv.setTextSize(16);
        parent.addView(numTv);

        Activity activity = CommonExt.scanForActivity(parent.getContext());
        if (activity != null) {
            ViewCompat.setOnApplyWindowInsetsListener(activity.getWindow().getDecorView(), (v, insets) -> {
                originBottomMargin = insets.getInsets(WindowInsetsCompat.Type.navigationBars()).bottom;
                currentBottomMargin = originBottomMargin;

                if (numTv != null) {
                    ViewGroup.MarginLayoutParams currentIndexLp = (ViewGroup.MarginLayoutParams) numTv.getLayoutParams();
                    currentIndexLp.bottomMargin = originBottomMargin;
                    numTv.setLayoutParams(currentIndexLp);
                }
                return insets;
            });
        }
    }

    @Override
    public void onShow(ViewPager2 viewPager) {
        numTv.setVisibility(View.VISIBLE);
        if (viewPager.getAdapter() != null) {
            viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
                @Override
                public void onPageSelected(int position) {
                    String str = (position + 1) + "/" + viewPager.getAdapter().getItemCount();
                    numTv.setText(str);
                }
            });
            String firstStr = (viewPager.getCurrentItem() + 1) + "/" + viewPager.getAdapter().getItemCount();
            numTv.setText(firstStr);
        }
    }


    @Override
    public void move(float moveX, float moveY) {
        if (numTv == null) {
            return;
        }
        FrameLayout.LayoutParams indexLp = (FrameLayout.LayoutParams) numTv.getLayoutParams();
        currentBottomMargin = Math.round(originBottomMargin - moveY / 6f);
        if (currentBottomMargin > originBottomMargin) {
            currentBottomMargin = originBottomMargin;
        }
        indexLp.bottomMargin = currentBottomMargin;
        numTv.setLayoutParams(indexLp);
    }

    @Override
    public void fingerRelease(boolean isToMax, boolean isToMin) {
        if (numTv == null) {
            return;
        }
        int begin = 0;
        int end = 0;
        if (isToMax) {
            begin = currentBottomMargin;
            end = originBottomMargin;
        }
        if (isToMin) {
            numTv.setVisibility(View.GONE);
            return;
        }
        if (begin == end) {
            return;
        }
        final FrameLayout.LayoutParams indexLp = (FrameLayout.LayoutParams) numTv.getLayoutParams();
        ValueAnimator valueAnimator = ValueAnimator.ofInt(begin, end);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                indexLp.bottomMargin = (int) animation.getAnimatedValue();
                numTv.setLayoutParams(indexLp);
            }
        });
        valueAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {

            }
        });
        valueAnimator.setDuration(300).start();
    }
}
