package com.kanzhun.imageviewer.interfaces

import android.view.View
import androidx.fragment.app.FragmentActivity
import com.kanzhun.imageviewer.ImageViewerView

/**
 * @Author:         <PERSON><PERSON><PERSON>Zero
 * @CreateDate:     2020/6/17 1:39 PM
 * @Description:
 */
interface OnListener {
    fun onStartAnim(position: Int)
    fun onClick(view: View, x: Float, y: Float, position: Int)
    fun onLongClick(fragmentActivity: FragmentActivity?, view: View, x: Float, y: Float, position: Int)
    fun onShowFinish(imageViewerView: ImageViewerView, showImmediately: Boolean)
    fun onMojitoViewFinish(pagePosition: Int)
    fun onDrag(view: ImageViewerView, moveX: Float, moveY: Float)
    fun onLongImageMove(ratio: Float)
    fun onViewPageSelected(position: Int)

    //保存视频文件
    fun  onSaveFile(fragmentActivity: FragmentActivity?,path:String)
}