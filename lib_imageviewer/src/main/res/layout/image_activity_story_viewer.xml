<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/indicatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/userCustomLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/ll_story"
        android:layout_width="match_parent"
        android:layout_height="157dp"
        android:layout_gravity="bottom"
        android:orientation="vertical"
        tools:background="@color/common_black_30">

        <net.lucode.hackware.magicindicator.MagicIndicator
            android:id="@+id/indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="12dp" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="28dp"
            android:ellipsize="end"
            android:gravity="center_horizontal"
            android:maxLines="3"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_20"
            tools:text="@string/common_long_placeholder" />


    </LinearLayout>

    <com.qmuiteam.qmui.layout.QMUILinearLayout
        android:id="@+id/ll_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="105dp"
        android:background="@color/common_color_FFD2D9"
        android:orientation="horizontal"
        android:paddingHorizontal="12dp"
        android:paddingVertical="10dp"
        android:visibility="gone"
        app:qmui_radius="10dp"
        tools:visibility="visible">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:src="@drawable/foundation_ic_error_notice" />

        <TextView
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="6dp"
            android:gravity="left"
            android:paddingLeft="8dp"
            android:paddingTop="8dp"
            android:paddingRight="8dp"
            android:paddingBottom="8dp"
            android:textColor="@color/common_color_292929"
            android:textSize="@dimen/common_text_sp_13"
            tools:text="该用户资料违规，正在修改。该用户资料违规，正在修改。" />
    </com.qmuiteam.qmui.layout.QMUILinearLayout>


</FrameLayout>