package com.kanzhun.marry.module_new_task.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.MotionEvent
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.base.compose.ext.onSetViewTreeContent
import com.kanzhun.common.dialog.model.SelectBottomBean
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.util.ScrollUtil
import com.kanzhun.common.util.SpaceUtil
import com.kanzhun.common.views.edittext.LengthNoticeFilter
import com.kanzhun.foundation.ai.AgcDialog
import com.kanzhun.foundation.ai.AgcGrantLayout
import com.kanzhun.foundation.ai.GrantAiProtocolDialog
import com.kanzhun.foundation.api.SCENE_HOBBY
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.foundation.model.profile.AboutMeSuggestModel
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.http.response.BaseResponse
import com.kanzhun.marry.me.info.util.getMeEditInfoSourceStrByStatus
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentIntroBinding
import com.kanzhun.marry.module_new_task.fragment.abtest.IntroSuggestDialog
import com.kanzhun.marry.module_new_task.net.ApiTask
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction
import com.kanzhun.utils.T
import com.qmuiteam.qmui.util.QMUIKeyboardHelper
import com.qmuiteam.qmui.util.QMUIKeyboardHelper.KeyboardVisibilityEventListener
import io.reactivex.rxjava3.core.Observable

class NewTaskIntroFragment : NewTaskBaseFragment<TaskFragmentIntroBinding>() {
    private val list: MutableList<SelectBottomBean> = ArrayList()
    private var nowSelect = 0
    private var backCall: OnBackPressedCallback? = null
    private var hasShowGenerateIntroDialog = false

    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.idTitleView.asBackButton()
        mBinding.idTitleView.setRightText("保存") {
            reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK) {
                step = mBinding.idTitle.getTitle()
                source = activityViewModel.getPageSourceStr()
            }

            val length = mBinding.editText.text.toString().length

            if (length < 20) {
                T.ss("请至少写20字吧，不要太敷衍哦~")
                return@setRightText
            }

            // 若字数≥50字：提交保存，不变

            // 若字数20~49字：触发点选个性标签弹窗，生成文本后带入到自我介绍编辑框中，若编辑框内已有文本则间隔一行拼接在后面
            if (length < 50 && !hasShowGenerateIntroDialog) { // 本次未弹框，看是否需要弹框
                val shouldShowGenerateIntroDialog =
                    activityViewModel.guideData2?.generateIntroGray == 1

                if (shouldShowGenerateIntroDialog || isQaDebugUser()) {
                    IntroSuggestDialog.shouldShow(
                        activity = activity as FragmentActivity,
                        profileTagList =
                            activityViewModel.guideData2?.profileTagList ?: emptyList(),
                        onTagToContent = { generatedContent -> // 生成文本回调
                            // 是否需要弹 ai 辅助弹框
                            val shouldShowGrantAiProtocolDialog =
                                activityViewModel.guideData2?.aiGenerateProfileGray == 1
                            if (shouldShowGrantAiProtocolDialog || isQaDebugUser()) {
                                shouldTriggerGrant(onClose = {
                                    // 用户没有使用AI生成内容，使用原标签拼接
                                    useTagToGeneratedContent(generatedContent)
                                })
                            } else {
                                useTagToGeneratedContent(generatedContent)
                            }
                        }
                    )
                    hasShowGenerateIntroDialog = true // 本次已弹框
                    return@setRightText
                }
            }

            activityViewModel.introContentUpdate(mBinding.editText.text.toString())
        }

        showFragmentContent()
        activityViewModel.introContentLiveData.observe(this) {
            if (it.isNullOrEmpty()) {
                mBinding.idWarn.gone()
            } else {
                mBinding.idWarn.text = it
                mBinding.idWarn.visible()
            }
        }

        reportPoint("myinfo-introduction-page-expo") {
            source = "首善"
            status = getMeEditInfoSourceStrByStatus(
                mBinding.editText.text.toString(),
                activityViewModel.initIntroCertStatus
            )
        }

        mBinding.idTitle.setTaskProgress(childFragmentManager, this::class.java.simpleName)

        mBinding.composeView.apply {
            visibility = View.GONE

            onSetViewTreeContent {
                AgcGrantLayout(onGrant = { shouldTriggerGrant() })
            }
        }
    }

    private fun useTagToGeneratedContent(generatedContent: String) {
        // 点选标签生成文本后带入编辑框，若编辑框内已有文本则间隔一行拼接在后面
        val userInputContent = mBinding.editText.text.toString()
        val finalContent =
            if (generatedContent.isNotEmpty()) {
                if (userInputContent.isNotEmpty()) {
                    userInputContent + "\n\n" + generatedContent
                } else {
                    generatedContent
                }
            } else {
                userInputContent
            }
        mBinding.editText.setText(finalContent)
        mBinding.editText.setSelection(userInputContent.length)
    }

    private fun shouldTriggerGrant(onClose: () -> Unit = {}) {
        GrantAiProtocolDialog.shouldShow(
            activity = activity as FragmentActivity,
            onShowGrantDialog = {
                reportPoint("aigenerate-authorize-popup-expo") {
                    actionp2 = "自我介绍" // 记录触发的类型：自我介绍、我的理想型
                }
            },
            onGrant = { isUserTrigger ->
                showAgcDialog(onClose = onClose)

                if (isUserTrigger) {
                    reportPoint("aigenerate-authorize-popup-click") {
                        actionp2 = "自我介绍" // 记录触发的类型：自我介绍、我的理想型
                        type = "同意并继续" // 记录点击的位置：同意并继续、关闭
                    }
                }
            },
            onClose = {
                onClose()

                // 没有授权，显示引导授权入口
                mBinding.composeView.visibility = View.VISIBLE

                reportPoint("aigenerate-authorize-popup-click") {
                    actionp2 = "自我介绍" // 记录触发的类型：自我介绍、我的理想型
                    type = "关闭" // 记录点击的位置：同意并继续、关闭
                }
            }
        )
    }

    private fun showAgcDialog(onClose: () -> Unit = {}) {
        AgcDialog.shouldShow(
            activity = activity as FragmentActivity,
            scene = SCENE_HOBBY,
            onReplace = {
                mBinding.editText.setText(it)
                mBinding.editText.setSelection(mBinding.editText.text.length)
            },
            onAppend = {
                val text = "${mBinding.editText.text}\n\n$it"
                mBinding.editText.setText(text)
                mBinding.editText.setSelection(mBinding.editText.text.length)
            },
            onClose = onClose
        )

        // 隐藏入口
        mBinding.composeView.visibility = View.GONE
    }

    @Suppress("DEPRECATION")
    @SuppressLint("RestrictedApi", "ClickableViewAccessibility")
    private fun showFragmentContent() {
        mBinding.editText.setText(activityViewModel.initInIntro)
        mBinding.idCount.text = getLengthStr(activityViewModel.initInIntro).toString()
        activityViewModel.allPage.observe(this) {
            mBinding.idTitle.setAllStep("/" + activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep(
                "${
                    activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(
                        1
                    )
                }"
            )
        }
        mBinding.editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                activityViewModel.introContentLiveData.postValue("")
                mBinding.idCount.text = getLengthStr(s.toString()).toString()
                mBinding.idWarn.text = ""
                mBinding.idWarn.gone()
                mBinding.idTitleView.setRightEnable(true)
            }

        })
        mBinding.editText.setOnTouchListener(object : View.OnTouchListener {
            override fun onTouch(v: View, motionEvent: MotionEvent): Boolean {
                if (v.id == R.id.edit_text && ScrollUtil.canVerticalScroll(mBinding.editText)) {
                    //通知父控件不要干扰
                    v.parent.requestDisallowInterceptTouchEvent(true)
                }
                if (motionEvent.action == MotionEvent.ACTION_UP) {
                    v.parent.requestDisallowInterceptTouchEvent(false)
                }
                return false
            }
        })

        val inputFilters = arrayOfNulls<InputFilter>(1)
        val filter = LengthNoticeFilter(1000)
        inputFilters[0] = filter
        mBinding.editText.setFilters(inputFilters)
        QMUIKeyboardHelper.setVisibilityEventListener(
            activity,
            KeyboardVisibilityEventListener { isOpen, heightDiff ->
                if (activity != null && host != null && activity?.isDestroyed == false) {
                    mBinding.idTitle.setGroupVisibilityOrGone(
                        if (isOpen) View.GONE else View.VISIBLE,
                        if (isOpen) "介绍一下自己" else ""
                    )
                    mBinding.idTitleView.setTitle(if (isOpen) "介绍一下自己" else "")
                }
                false
            })

        backCall = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (TextUtils.isEmpty(mBinding.editText.text)) {
                    backCall?.remove()
                    activity?.onBackPressed()
                } else {
                    if (activityViewModel.allPage.value == 1) {
                        activity?.showTwoButtonDialog(
                            content = "内容未保存，确认要退出吗",
                            cancelable = false, canceledOnTouchOutside = false,
                            positiveText = "退出", positiveButtonClick = {
                                backCall?.remove()
                                activity?.onBackPressed()
                            },
                            negativeText = "继续编辑", negativeButtonClick = {})
                    } else {
                        activity?.showTwoButtonDialog(
                            content = "本页内容未保存，要回到上一步吗",
                            cancelable = false, canceledOnTouchOutside = false,
                            positiveText = "上一步", positiveButtonClick = {
                                backCall?.remove()
                                activity?.onBackPressed()
                            },
                            negativeText = "继续编辑", negativeButtonClick = {})
                    }
                }

            }
        }
        requireActivity().onBackPressedDispatcher.addCallback(this, backCall!!)

        if (activityViewModel.baseInfoLiveData.value?.baseInfo?.introCertStatus == 2) {
            mBinding.idWarn.text =
                activityViewModel.baseInfoLiveData.value?.baseInfo?.introCertInfo ?: ""
            mBinding.idWarn.visible()
            mBinding.idTitleView.setRightEnable(false)
        } else {
            mBinding.idWarn.text =
                activityViewModel.baseInfoLiveData.value?.baseInfo?.introCertInfo ?: ""
            mBinding.idWarn.gone()
        }


        val responseObservable: Observable<BaseResponse<AboutMeSuggestModel>> =
            RetrofitManager.getInstance().createApi<ApiTask>(
                ApiTask::class.java
            ).getAboutMeSuggest()
        HttpExecutor.execute<AboutMeSuggestModel>(
            responseObservable,
            object : BaseRequestCallback<AboutMeSuggestModel>() {
                override fun onSuccess(data: AboutMeSuggestModel) {
                    nowSelect = 0
                    if (data.introSuggest.isNotEmpty() && data.introSuggest[0].isNotBlank()) {
                        for (str in data.introSuggest) {
                            val selectBottomBean = SelectBottomBean(str)
                            list.add(selectBottomBean)
                        }
                        mBinding.tvSure.onClick {
                            nowSelect++
                            if (nowSelect >= list.size) {
                                nowSelect = 0
                            }

                            setBottomText(list[nowSelect].getContent())
                            reportPoint("myinfo-introduction-case-change") {
                                source = "首善"
                                status = getMeEditInfoSourceStrByStatus(
                                    mBinding.editText.text.toString(),
                                    activityViewModel.initIntroCertStatus
                                )
                            }
                        }
                        setBottomText(list[nowSelect].getContent())
                        mBinding.llBottomFloat.visible()
                    } else {
                        mBinding.llBottomFloat.gone()
                    }
                }

                override fun dealFail(reason: ErrorReason) {
                    mBinding.llBottomFloat.gone()
                }
            })
    }

    private fun setBottomText(str: String) {
        SpaceUtil.setParagraphSpacing(context, mBinding.idTVTryWrite, str, 42, 20)
    }

    private fun getLengthStr(s: String): String {
        return getLength(s).toString()
    }

    private fun getLength(s: String): Int {
        return StringUtil.trimEAndN(s).length
    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO) {
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    override fun onStop() {
        super.onStop()
        QMUIKeyboardHelper.hideKeyboard(mBinding.editText)
    }

    override fun onDestroyView() {
        super.onDestroyView()

    }

    override fun initData() {

    }

    override fun onRetry() {

    }

    override fun getStateLayout() = mBinding.idStateLayout
}