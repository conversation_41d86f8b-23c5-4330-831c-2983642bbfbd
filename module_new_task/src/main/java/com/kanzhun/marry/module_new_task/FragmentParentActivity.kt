package com.kanzhun.marry.module_new_task

import android.content.Intent
import android.os.Bundle
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.base.EmptyViewModel
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.kotlin.ktx.getPageSourceBundle
import com.kanzhun.foundation.kotlin.ktx.parsePageSourceFromBundle
import com.kanzhun.foundation.router.TaskPageRouter
import com.kanzhun.marry.module_new_task.databinding.ActivityFragmentParentBinding
import com.kanzhun.marry.module_new_task.fragment.NewTaskLoverIntroTagFragment
import com.sankuai.waimai.router.annotation.RouterUri

@RouterUri(path = [TaskPageRouter.FRAGMENT_PARENT_ACTIVITY])
class FragmentParentActivity :
    BaseBindingActivity<ActivityFragmentParentBinding, EmptyViewModel>() {
        var pageSource = PageSource.NONE
    override fun preInit(intent: Intent) {
        pageSource = parsePageSourceFromBundle(intent)
    }

    override fun initView() {
        supportFragmentManager.beginTransaction().add(
            R.id.idContent,
            NewTaskLoverIntroTagFragment().apply {
                arguments = getPageSourceBundle(pageSource)
                arguments?.putAll(intent.extras ?: Bundle())
            },
            NewTaskLoverIntroTagFragment::class.simpleName,
        ).commitAllowingStateLoss()
    }

    override fun initData() {
    }


    override fun onRetry() {
    }

    override fun getStateLayout() = null
}