package com.kanzhun.marry.module_new_task.fragment.abtest

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ext.onSetViewTreeContent
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.kotlin.ktx.contentShowRange
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.dialog.showIncomeRangeBottomDialog
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentAssetBinding
import com.kanzhun.marry.module_new_task.fragment.NewTaskBaseFragment
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction
import com.kanzhun.utils.SettingBuilder
import com.kanzhun.utils.base.LList
import com.kanzhun.utils.configuration.UserSettingConfig

class NewTaskAssetFragment : NewTaskBaseFragment<TaskFragmentAssetBinding>() {
    private var carSelect: Int = 0 // 车 1 无 2 有
    private var houseSelect: Int = 0 // 房 1 无 2 有

    private var realRevenue: Int = 0 // 1~9

    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.btnNext.onClick {
            val houseCarNotChanged = (activityViewModel.initCarHold == carSelect
                    && houseSelect == activityViewModel.initHouseHold
                    && carSelect != 0
                    && houseSelect != 0)

            val revenueChanged = realRevenue != activityViewModel.initRevenue

            if (houseCarNotChanged && !revenueChanged) {
                // 房车和薪资都没有修改，直接下一步
                gotoNext()
                reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK) {
                    step = mBinding.idTitle.getTitle()
                    source = activityViewModel.getPageSourceStr()
                }
            } else {
                if (!houseCarNotChanged && revenueChanged) { // 房车和薪资都有修改，依次保存
                    activityViewModel.updateHouseCar(carSelect, houseSelect) {
                        activityViewModel.updateRevenue(realRevenue) {
                            gotoNext()
                            reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK) {
                                step = mBinding.idTitle.getTitle()
                                source = activityViewModel.getPageSourceStr()
                            }
                        }
                    }
                } else if (revenueChanged) { // 薪资有修改，保存薪资
                    activityViewModel.updateRevenue(realRevenue) {
                        gotoNext()
                        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK) {
                            step = mBinding.idTitle.getTitle()
                            source = activityViewModel.getPageSourceStr()
                        }
                    }
                } else { // 房车有修改，保存房车
                    activityViewModel.updateHouseCar(carSelect, houseSelect) {
                        gotoNext()
                        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK) {
                            step = mBinding.idTitle.getTitle()
                            source = activityViewModel.getPageSourceStr()
                        }
                    }
                }
            }
        }

        val revenueArray = resources.getStringArray(R.array.common_array_annual_income)
        mBinding.idComposeView.onSetViewTreeContent {
            var currentHourCarPosition by remember {
                mutableIntStateOf(
                    // carSelect houseSelect 转换为 currentHourCarPosition

                    // 有房：0
                    // 有房有车：1
                    // 有车：2
                    // 无房无车：3

                    // houseSelect: 0/1/2
                    // carSelect: 0/1/2

                    if (houseSelect == 2 && carSelect == 2) {
                        1 // 有房有车
                    } else if (houseSelect == 2) {
                        0 // 有房
                    } else if (carSelect == 2) {
                        2 // 有车
                    } else if (houseSelect > 0 && carSelect > 0) {
                        3 // 无房无车
                    } else {
                        -1 // 未选
                    }
                )
            }

            var currentRevenuePosition by remember {
                mutableIntStateOf(
                    if (activityViewModel.initRevenue > 0 && activityViewModel.initRevenue - 1 < revenueArray.size) {
                        activityViewModel.initRevenue - 1
                    } else {
                        -1
                    }
                )
            }

            AssetContent(
                houseCars = listOf("有房", "有房有车", "有车", "无房无车"),
                currentHourCarPosition = currentHourCarPosition,
                onHouseCarSelectChange = { selectHourCarPosition ->
                    currentHourCarPosition = selectHourCarPosition

                    // 缓存选中的房车情况
                    when (selectHourCarPosition) {
                        0 -> {
                            carSelect = 1
                            houseSelect = 2
                        }

                        1 -> {
                            carSelect = 2
                            houseSelect = 2
                        }

                        2 -> {
                            carSelect = 2
                            houseSelect = 1
                        }

                        3 -> {
                            carSelect = 1
                            houseSelect = 1
                        }
                    }

                    updateButtonStatus()
                },

                revenueArray = revenueArray,
                currentRevenuePosition = currentRevenuePosition,
                onRevenueSelectChange = { selectRevenuePosition ->
                    currentRevenuePosition = selectRevenuePosition
                    this.realRevenue = selectRevenuePosition + 1

                    updateButtonStatus()
                }
            )
        }

        showFragmentContent()

        mBinding.idTitle.setTaskProgress(childFragmentManager,this::class.java.simpleName)
    }

    private fun updateButtonStatus() {
        mBinding.btnNext.isEnabled = (carSelect != 0 && houseSelect != 0) && (realRevenue != 0)
    }

    @SuppressLint("RestrictedApi")
    private fun showFragmentContent() {
        activityViewModel.allPage.observe(this) {
            mBinding.idTitle.setAllStep("/" + activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep(
                "${
                    activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(
                        1
                    )
                }"
            )
        }

        carSelect = activityViewModel.initCarHold
        houseSelect = activityViewModel.initHouseHold
        realRevenue = activityViewModel.initRevenue

        updateButtonStatus()

        var i = if (SettingBuilder.getInstance()
                .getUserSettingValue(UserSettingConfig.PERSONALITY_HIDE_REVENUE).isEmpty()
        ) 0 else SettingBuilder.getInstance()
            .getUserSettingValue(UserSettingConfig.PERSONALITY_HIDE_REVENUE).toInt()
        mBinding.idSubText.text = i.contentShowRange()

        mBinding.idSubText.onClick {
            showIncomeRangeBottomDialog(
                requireContext(), i, false,
                { _, pos, _ -> i = pos }) {
                requestSetUpdate(
                    UserSettingConfig.PERSONALITY_HIDE_REVENUE,
                    i
                )
            }
        }
    }

    private fun requestSetUpdate(@Suppress("SameParameterValue") key: Int, value: Int) {
        val observable = RetrofitManager.getInstance().createApi(
            FoundationApi::class.java
        ).settingUpdate(key, value)
        HttpExecutor.requestSimple(observable, object : SimpleRequestCallback() {
            override fun onSuccess() {
                SettingBuilder.Builder()
                    .addUserSetting(key, UserSettingConfig(key, value.toString())).build()
                val i = if (SettingBuilder.getInstance()
                        .getUserSettingValue(UserSettingConfig.PERSONALITY_HIDE_REVENUE)
                        .isEmpty()
                ) 0 else SettingBuilder.getInstance()
                    .getUserSettingValue(UserSettingConfig.PERSONALITY_HIDE_REVENUE).toInt()
                mBinding.idSubText.text = i.contentShowRange()
            }

            override fun dealFail(reason: ErrorReason) {}
        })
    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO) {
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    override fun initData() {

    }

    override fun onRetry() {

    }

    override fun getStateLayout() = mBinding.idStateLayout
}

@Composable
private fun AssetContent(
    modifier: Modifier = Modifier,

    houseCars: List<String> = listOf("有房", "有房有车", "有车", "无房无车"),
    currentHourCarPosition: Int,
    onHouseCarSelectChange: (currentHourCarPosition: Int) -> Unit = {},

    revenueArray: Array<String> = arrayOf(
        "5万以下",
        "5-10万",
        "10-20万",
        "20-30万",
        "30-40万",
        "40-50万",
        "50-70万",
        "70-100万",
        "100万以上"
    ),
    currentRevenuePosition: Int,
    onRevenueSelectChange: (currentRevenuePosition: Int) -> Unit = {}
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
            .padding(bottom = 40.dp)
            .verticalScroll(state = rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(32.dp)
    ) {
        HouseCar(
            houseCars = houseCars,
            currentHourCarPosition = currentHourCarPosition,
            onHouseCarSelectChange = onHouseCarSelectChange
        )

        Revenue(
            revenueArray = revenueArray,
            currentRevenuePosition = currentRevenuePosition,
            onRevenueSelectChange = onRevenueSelectChange
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun HouseCar(
    modifier: Modifier = Modifier,
    houseCars: List<String>,
    currentHourCarPosition: Int,
    onHouseCarSelectChange: (currentHourCarPosition: Int) -> Unit
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "房车情况",
            style = TextStyle(
                fontSize = 18.sp,
                lineHeight = 24.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF191919),
            )
        )

        Spacer(Modifier.height(20.dp))

        val rows = 2
        val columns = 2
        FlowRow(
            modifier = Modifier,
            horizontalArrangement = Arrangement.spacedBy(10.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp),
            maxItemsInEachRow = rows
        ) {
            val itemModifier = Modifier.weight(1f)
            repeat(rows * columns) {
                val text = houseCars[it]
                Item(
                    modifier = itemModifier,
                    text = text,
                    isSelected = text == LList.getElement<String>(
                        houseCars,
                        currentHourCarPosition
                    ),
                    onClick = {
                        onHouseCarSelectChange(it)
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun Revenue(
    modifier: Modifier = Modifier,
    revenueArray: Array<String>,
    currentRevenuePosition: Int,
    onRevenueSelectChange: (currentRevenuePosition: Int) -> Unit
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "薪资情况",
            style = TextStyle(
                fontSize = 18.sp,
                lineHeight = 24.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF191919),
            )
        )

        Spacer(Modifier.height(20.dp))

        val rows = 3
        val columns = 3
        FlowRow(
            modifier = Modifier,
            horizontalArrangement = Arrangement.spacedBy(10.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp),
            maxItemsInEachRow = rows
        ) {
            val itemModifier = Modifier.weight(1f)
            repeat(rows * columns) {
                val text = revenueArray[it]
                Item(
                    modifier = itemModifier,
                    text = text,
                    isSelected = text == LList.getElement<String>(
                        revenueArray,
                        currentRevenuePosition
                    ),
                    onClick = {
                        onRevenueSelectChange(it)
                    }
                )
            }
        }
    }
}

@Composable
private fun Item(
    modifier: Modifier = Modifier,
    text: String,
    isSelected: Boolean = false,
    onClick: () -> Unit = {}
) {
    Text(
        text = text,
        style = TextStyle(
            fontSize = 16.sp,
            fontWeight = FontWeight(500),
            color = Color(0xFF292929),
            textAlign = TextAlign.Center,
        ),
        modifier = modifier
            .background(
                color = if (isSelected) Color(0xFFF0F0F0) else Color(0xFFF5F5F5),
                shape = RoundedCornerShape(size = 32.dp)
            )
            .conditional(isSelected) {
                border(
                    width = 1.dp,
                    color = Color(0xFF292929),
                    shape = RoundedCornerShape(size = 32.dp)
                )
            }
            .padding(16.dp)
            .noRippleClickable {
                onClick()
            }
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewAssetContent() {
    AssetContent(
        currentHourCarPosition = 0,
        currentRevenuePosition = 0
    )
}