package com.kanzhun.marry.module_new_task.fragment

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import com.google.android.flexbox.FlexboxLayoutManager
import com.kanzhun.common.app.AppThreadFactory
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.decoration.VerticalDividerItemDecoration
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.liveEventObserve
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.foundation.adapter.MultiTypeBindingAdapter
import com.kanzhun.foundation.adapter.buildMultiTypeAdapterByType
import com.kanzhun.foundation.adapter.replaceData
import com.kanzhun.foundation.model.CustomIndustryBean
import com.kanzhun.foundation.model.profile.IndustryBean
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentJobBinding
import com.kanzhun.marry.module_new_task.databinding.TaskItemAuthSeachJobBinding
import com.kanzhun.marry.module_new_task.databinding.TaskItemAuthSeachJobHelpBinding
import com.kanzhun.marry.module_new_task.databinding.TaskItemIndustrySelectBinding
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction
import com.kanzhun.utils.T
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.subjects.PublishSubject
import java.util.Locale
import java.util.regex.Pattern

class NewTaskJobFragment:NewTaskBaseFragment<TaskFragmentJobBinding>() {
    var searchContent: String? = null
    var temWriteName = ""
    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.btnNext.onClick {
            goNext()
        }
        showFragmentContent()
        activityViewModel.occupationLiveData.observe(this
        ) { industries ->
            if(industries == null){
                return@observe
            }
            searchContent = mBinding.editText.getText().toString().trim()
            adapter2?.replaceData(industries)
            if(industries.isEmpty()){
                mBinding.rvOccupation.gone()
                mBinding.rvRecommend.visible()
                mBinding.tvRecommendTitle.visible()
                mBinding.tvErrorDesc.visible()
            }else{
                mBinding.tvRecommendTitle.gone()
                mBinding.rvOccupation.visible()
                mBinding.rvRecommend.gone()
                mBinding.tvErrorDesc.gone()
            }
            activityViewModel.occupationLiveData.postValue(null)
        }

        liveEventObserve("jobNameEdit"){it:String->
            if (TextUtils.isEmpty(it))return@liveEventObserve
            AppThreadFactory.getMainHandler().postDelayed({
                temWriteName = it
                mBinding.editText.setText(it)
                mBinding.editText.setSelection(mBinding.editText.length())
                mBinding.editText.enable(true)

            },200)

        }

        mBinding.idTitle.setTaskProgress(childFragmentManager,this::class.java.simpleName)

    }

    private fun goNext() {
        activityViewModel.setCareer(mBinding.editText.text.toString())
        gotoNext()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK){
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    var recommendAdapter: MultiTypeBindingAdapter<IndustryBean, ViewBinding>? = null
    var adapter2: MultiTypeBindingAdapter<IndustryBean, ViewBinding>? = null
    var search:Boolean = false

    private fun checkEnable(s: String){
        if(mBinding.editText.text.toString().isEmpty()){
            mBinding.btnNext.enable(false)
        }else{
            if (s.equals(temWriteName)){
                mBinding.btnNext.enable(true)
            }else{
                mBinding.btnNext.enable(false)
            }
        }
    }
    @SuppressLint("RestrictedApi")
    private fun showFragmentContent() {
        search = false
        mBinding.editText.setText(activityViewModel.initCareerStr)
        checkEnable(mBinding.editText.text.toString())
        activityViewModel.allPage.observe(this){
            mBinding.idTitle.setAllStep("/"+activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep("${activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(1)}")
        }
        mBinding.rvRecommend.layoutManager = FlexboxLayoutManager(context)
        mBinding.viewLine.setBackgroundColor(R.color.common_color_191919.toResourceColor())
        recommendAdapter = buildMultiTypeAdapterByType {
            layout(TaskItemIndustrySelectBinding::inflate) { _, bean: IndustryBean ->
                binding.idText.text = bean.name
                binding.idText.tag = bean.code
                binding.idText.setBackgroundColor(R.color.common_color_F5F5F5.toResourceColor())
                binding.idText.setTextColor(R.color.common_color_545454.toResourceColor())
                binding.root.onClick {
                    search = false
                    mBinding.editText.setText(bean.name)
                    mBinding.editText.setSelection(bean.name.length)
                    activityViewModel.selectInitCareerCode(bean.code)
                    search = true
                    temWriteName = bean.name?:""
                    mBinding.btnNext.enable(true)
//                    goNext()

                    reportPoint("position-advice-click"){
                        actionp2 = activityViewModel.baseInfoLiveData.value?.baseInfo?.industry
                        actionp3 = temWriteName
                        source = "新手阶段"
                    }
                }
            }
        }
        mBinding.rvRecommend.adapter = recommendAdapter

        if(activityViewModel.industry != null){
            if(TextUtils.isEmpty(activityViewModel.initIndustryCode)){
                recommendAdapter!!.replaceData(activityViewModel.industry!!.get(0).career)
            }else{
                var code = activityViewModel.initIndustryCode
                for (item in activityViewModel.industry!!) {
                    if (TextUtils.equals(activityViewModel.initIndustryCode, item.code)) { // 找到所在行业
                        recommendAdapter!!.replaceData(item.career)
                    }
                }
            }
        }

        mBinding.editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {


            }

            override fun afterTextChanged(s: Editable?) {
                if(s.toString().length > 8){
                    mBinding.editText.setText(s?.substring(0,8))
                    mBinding.editText.setSelection(mBinding.editText.length())
                    T.ss("请输入8个字以内的职业名")
                }else{
                    if (!TextUtils.isEmpty(s.toString())) {
                        val CUSTOM_REGEX = "^[\\u4E00-\\u9FA5A-Za-z0-9_/-]+$"
                        val pattern = Pattern.compile(CUSTOM_REGEX, Pattern.CASE_INSENSITIVE)
                        val matcher = pattern.matcher(s.toString())
                        if (!matcher.matches()) {
                            mBinding.tvErrorDesc.text = "职业名称只支持汉字，英文，数字和部分符号"
                            return
                        }
                    }
                    if(search){
                        activityViewModel.searchOccupation(s.toString().trim { it <= ' ' })
                    }
                }
                checkEnable(mBinding.editText.text.toString())
                mBinding.tvErrorDesc.text = ""
            }

        })
        mBinding.tvErrorDesc.text = activityViewModel.baseInfoLiveData.value?.baseInfo?.careerCertInfo ?: ""
        if(mBinding.tvErrorDesc.text.toString().isNotEmpty()){
            mBinding.btnNext.enable(false)
        }

        mBinding.rvOccupation.layoutManager = LinearLayoutManager(context)
        mBinding.rvOccupation.gone()
        val itemDecoration = VerticalDividerItemDecoration(QMUIDisplayHelper.dpToPx(1))
        itemDecoration.dividerColor = ContextCompat.getColor(context!!,R.color.common_color_EBEBEB)
        mBinding.rvOccupation.addItemDecoration(itemDecoration)
        adapter2 = buildMultiTypeAdapterByType {
            layout(TaskItemAuthSeachJobBinding::inflate) { position: Int, bean: IndustryBean ->
                setAllContentText(binding.tvContent.context,binding.tvContent,bean.name,mBinding.editText.text.toString())
                binding.root.onClick {
                    search = false
                    mBinding.editText.setText(bean.name)
                    mBinding.editText.setSelection(mBinding.editText.text.length)
                    temWriteName = bean.name?:""
                    mBinding.tvRecommendTitle.visible()
                    mBinding.rvOccupation.gone()
                    mBinding.rvRecommend.visible()
                    mBinding.tvErrorDesc.visible()
                    activityViewModel.selectInitCareerCode(bean.code)
                    search = true
                    mBinding.btnNext.enable(true)
                    reportPoint("position-suggestion-click"){
                        actionp2 = activityViewModel.baseInfoLiveData.value?.baseInfo?.industry
                        actionp3 = temWriteName
                        source = "新手阶段"
                        idx = position
                    }
                }
            }
            layout(TaskItemAuthSeachJobHelpBinding::inflate) { _, bean: CustomIndustryBean ->
                binding.root.onClick {
                    MePageRouter.jumpToJobNameCustomActivity(requireContext(),mBinding.editText.text.toString(),
                        PageSource.NEW_USER_TASK_ACTIVITY,"")
                }
            }
        }
        mBinding.rvOccupation.adapter = adapter2
        search = true
    }

    private fun setAllContentText(
        context: Context,
        textView: TextView,
        text: String,
        searchContent: String,
    ) {
        var searchContent = searchContent
        var lowText = ""
        if (!TextUtils.isEmpty(text)) {
            lowText = text.lowercase(Locale.getDefault())
        }
        if (!TextUtils.isEmpty(searchContent)) {
            searchContent = searchContent.lowercase(Locale.getDefault())
        }
        if (!TextUtils.isEmpty(searchContent) && lowText.contains(searchContent)) {
            val s = SpannableString(lowText)
            val p = Pattern.compile(searchContent)
            val m = p.matcher(s)
            while (m.find()) {
                val start = m.start()
                val end = m.end()
                s.setSpan(
                    ForegroundColorSpan(
                        ContextCompat.getColor(
                            context,
                            R.color.common_color_7171FF
                        )
                    ), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            textView.text = s
        } else {
            textView.text = text
        }
    }

    private fun searchObservable(content: String) {
        if (TextUtils.equals(content, activityViewModel.searchContent)) {
            return
        }
        activityViewModel.searchContent = content
        if (!TextUtils.isEmpty(content)) {
            searchSubject?.onNext(content)
        } else {
            activityViewModel.searchCompanyLiveData.setValue(null)
        }
    }

    private var searchSubject: PublishSubject<String>? = null
    private var searchDisposable: Disposable? = null


    override fun onDestroy() {
        super.onDestroy()
        if (searchDisposable != null) {
            searchDisposable!!.dispose()
        }
    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO){
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
    }

    override fun initData() {

    }

    override fun onRetry() {

    }

    override fun getStateLayout() = mBinding.idStateLayout

}