package com.kanzhun.marry.module_new_task.net

import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.api.model.ProfileInfoModel.Story
import com.kanzhun.foundation.model.profile.ExtInfoBean
import com.kanzhun.foundation.model.profile.GuideItemBean
import com.kanzhun.marry.module_new_task.fragment.abtest.interest.Bean

class GuideItemsResponse2 {
    var interestTagGray: Int = 0 // 兴趣爱好标签灰度 1:实验中A 2:实验组B
    var generateIntroGray: Int = 0 // 生成自我介绍灰度 0:未命中 1:名字灰度
    var aiGenerateProfileGray: Int = 0 // ai生成的灰度 0:未命中 1:命中灰度

    var interestTagList: List<Bean> = ArrayList()
    var profileTagList: List<Bean> = ArrayList()

    var result: MutableList<GuideItemBean?>? = null // 为空则已填写完成
    var extInfo: ExtInfoBean? = null

    var baseInfo: ProfileInfoModel.BaseInfo? = null

    var storyList: MutableList<Story?>? = null // 用户故事
}
