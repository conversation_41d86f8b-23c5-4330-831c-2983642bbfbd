package com.kanzhun.marry.module_new_task.fragment.abtest.interest

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.navigation.findNavController
import com.kanzhun.common.base.compose.ext.onSetViewTreeContent
import com.kanzhun.common.kotlin.constract.LivedataKeyLogin
import com.kanzhun.common.kotlin.ext.color
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentInterestKeywordBinding
import com.kanzhun.marry.module_new_task.fragment.NewTaskBaseFragment
import com.kanzhun.utils.T

class NewTaskInterestFragment : NewTaskBaseFragment<TaskFragmentInterestKeywordBinding>() {
    override fun preInit(arguments: Bundle) {
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        liveEventBusObserve(LivedataKeyLogin.LOGIN_KEY_WOLD_ADD_END) { it: Bean ->
            addItem(it, true)
        }
    }

    @SuppressLint("RestrictedApi")
    override fun initView() {
        mBinding.btnNext.onClick {
            if (activityViewModel.keyMap.size > 2) {
                activityViewModel.editLabel {
                    gotoNext()
                }
            } else {
                T.ss("请至少选择3个兴趣标签哦")
            }
        }

        val beans = activityViewModel.beanList ?: emptyList()

        val selectedBeans = mutableListOf<Bean>()

        activityViewModel.keyMap.map { entry ->
            val key = entry.key // 选中的
            beans.forEach { keyword ->
                keyword.subTag?.forEach { subKeyword ->
                    if (key == subKeyword.id) {
                        selectedBeans.add(subKeyword)
                        subKeyword.isSelected.value = true
                    }
                }
            }
        }

        val selectedBeanList = SnapshotStateList<Bean>().apply { addAll(selectedBeans) }
        mBinding.composeView.onSetViewTreeContent {
            KeywordContent(
                onBack = {
                    @Suppress("DEPRECATION")
                    activity?.onBackPressed()
                },
                onSearch = {
                    mBinding.composeView.findNavController().navigate(R.id.searchFragment)
                },
                currentStep =
                    activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(1) ?: 0,
                stepCount = activityViewModel.allPage.value ?: 0,
                selectedBeans = selectedBeanList,
                beans = SnapshotStateList<Bean>().apply { addAll(beans) },
                onSelectChange = {
                    if (it.isSelected.value == false) {
                        val addResult = addItem(bean = it.toBean(), addToEnd = true)
                        if (addResult) {
                            selectedBeanList.add(it.apply { isSelected.value = true })
                        }
                    } else {
                        removeItem(bean = it.toBean())
                        selectedBeanList.remove(it.apply { isSelected.value = false })
                    }
                },
                onRemoveSelectedBean = {
                    if (it.isSelected.value == true) {
                        removeItem(it.toBean())
                        selectedBeanList.remove(it.apply { isSelected.value = false })
                    }
                }
            )
        }
    }

    private fun showFragmentContent() {
        if (activityViewModel.beanList.isNullOrEmpty()) {
            mBinding.idStateLayout.showError()
            return
        }
        mBinding.idStateLayout.showContent()
        updateBtn()
    }

    private fun addItem(
        bean: Bean,
        @Suppress("SameParameterValue") addToEnd: Boolean = false
    ): Boolean {
        if (activityViewModel.keyMap.size > 29) {
            T.ss("最多可以选择30个标签")
            return false
        }

        if (activityViewModel.keyMap.containsKey(bean.id)) {
            if (addToEnd) {
                activityViewModel.keyMap.remove(bean.id)
                activityViewModel.keyMap[bean.id] = bean
            }
            return false
        }

        activityViewModel.keyMap[bean.id] = bean
        updateBtn()

        return true
    }

    private fun removeItem(bean: Bean) {
        if (activityViewModel.keyMap.containsKey(bean.id)) {
            activityViewModel.keyMap.remove(bean.id)
            updateBtn()
        }
    }

    private fun Bean.toBean(): Bean {
        return Bean().apply {
            id = <EMAIL>
            title = <EMAIL>
            icon = <EMAIL>
            subTag = <EMAIL>?.map {
                Bean().apply {
                    id = it.id
                    title = it.content
                    icon = it.icon
                }
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateBtn() {
        val size = activityViewModel.keyMap.size
        if (size > 2) {
            activity?.color(R.color.common_black)
                ?.let {
                    mBinding.btnNext.setBackgroundColor(it)
                }
        } else {
            activity?.color(R.color.common_color_CCCCCC)
                ?.let {
                    mBinding.btnNext.setBackgroundColor(it)
                }
        }
        mBinding.btnNext.text = "下一步（$size/30）"
    }

    override fun initData() {
        getProfile()
    }

    override fun onRetry() {
        getProfile()
    }

    private fun getProfile() {
        activityViewModel.getProfile({
            showFragmentContent()
        }, {
            showFragmentContent()
        })
    }

    override fun getStateLayout() = mBinding.idStateLayout
}