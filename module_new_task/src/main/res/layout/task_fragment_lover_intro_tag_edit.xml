<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.kanzhun.common.views.AppTitleView
        android:id="@+id/icTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginRight="@dimen/app_layout_page_right_padding"
        android:textColor="@color/common_color_191919"
        tools:text="我的家乡"
        android:textSize="@dimen/common_text_sp_28"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="20dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icTitle"
        />

    <TextView
        android:id="@+id/tvTitle2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/app_layout_page_sub_title_margin"
        android:layout_marginRight="@dimen/app_layout_page_right_padding"
        android:textColor="@color/common_color_7F7F7F"
        tools:text="我的家乡"
        android:textSize="16dp"
        app:layout_constraintLeft_toLeftOf="@+id/tvTitle"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        />

    <com.thecodeyard.playground.jbox2d.BubbleContainerView
        android:id="@+id/idHorizontalScrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginVertical="40dp"
        app:layout_constraintBottom_toTopOf="@+id/btn_next"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle2">

    </com.thecodeyard.playground.jbox2d.BubbleContainerView>


    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/btn_next"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="28dp"
        android:gravity="center"
        android:textColor="@color/common_white"
        android:textSize="18sp"
        android:textStyle="bold"
        android:paddingTop="10dp"
        android:paddingBottom="11dp"
        android:layout_marginLeft="@dimen/app_layout_page_left_padding"
        android:layout_marginRight="@dimen/app_layout_page_right_padding"
        app:qmui_radius="25dp"
        android:text="下一步"
        app:qmui_backgroundColor="@color/common_color_CCCCCC"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
