<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.kotlin.ui.statelayout.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idStateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/idTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_icon="@drawable/me_ic_house_car_page"
            app:title_text="@string/me_asset_question_title" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/idComposeView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_link"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTitle"
            tools:composableName="com.kanzhun.marry.module_new_task.fragment.abtest.NewTaskAssetFragmentKt.PreviewAssetContent" />

        <TextView
            android:id="@+id/tv_link"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginBottom="20dp"
            android:drawableStart="@drawable/me_ic_revenue"
            android:drawablePadding="6dp"
            android:text="年薪"
            android:textColor="@color/common_color_292929"
            android:textSize="16dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@+id/btn_next"
            app:layout_constraintLeft_toLeftOf="parent"
            tools:ignore="HardcodedText,SpUsage,UseCompatTextViewDrawableXml" />

        <TextView
            android:id="@+id/idSubText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/common_ic_gray_right_arrow"
            android:textColor="@color/common_color_B2B2B2"
            android:textSize="16dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_link"
            app:layout_constraintEnd_toEndOf="@+id/btn_next"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_link"
            tools:ignore="SpUsage,UseCompatTextViewDrawableXml"
            tools:text="仅自己可见" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            style="@style/button_large_next_page_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="28dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:enabled="false" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.kanzhun.common.kotlin.ui.statelayout.StateLayout>


