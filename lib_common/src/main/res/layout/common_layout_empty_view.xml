<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.airbnb.lottie.LottieAnimationView
                app:lottie_imageAssetsFolder="nocontent/images"
                app:lottie_fileName="nocontent/nocontent.json"
                app:lottie_autoPlay="true"
                app:lottie_loop="false"
                android:id="@+id/iv_pic"
                android:layout_width="174dp"
                android:layout_height="174dp"
                android:layout_marginTop="161dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/common_nothing_left"
                android:textColor="@color/common_color_7F7F7F"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="@id/iv_pic"
                app:layout_constraintRight_toRightOf="@id/iv_pic"
                app:layout_constraintTop_toBottomOf="@id/iv_pic" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>