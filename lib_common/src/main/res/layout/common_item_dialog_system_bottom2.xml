<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground">

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:paddingVertical="16dp"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_16"
        android:textStyle="bold"
        tools:ignore="SpUsage"
        tools:text="删除语音" />

</FrameLayout>