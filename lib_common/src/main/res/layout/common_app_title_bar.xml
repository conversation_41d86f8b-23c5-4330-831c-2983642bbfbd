<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clTitleBar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/common_title_height">

    <ImageView
        android:id="@+id/iv_common_back"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingStart="20dp"
        android:paddingEnd="5dp"
        android:src="@drawable/common_ic_black_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 为保证title居中，右侧隐藏一对和左侧一样的布局 -->
    <ImageView
        android:id="@+id/iv_right_placeholder"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingLeft="20dp"
        android:paddingRight="5dp"
        android:src="@drawable/common_ic_black_back"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_common_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/barrier"
        app:layout_constraintStart_toEndOf="@+id/barrier_left"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:id="@+id/tv_common_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:textStyle="bold"
            tools:visibility="visible"
            app:layout_constrainedWidth="true"
            tools:text="标题标题标题标题标题标题" />

        <ImageView
            android:id="@+id/idTitleIcon"
            tools:visibility="visible"
            app:layout_constraintLeft_toRightOf="@+id/tv_common_title"
            app:layout_constraintTop_toTopOf="@+id/tv_common_title"
            app:layout_constraintBottom_toBottomOf="@+id/tv_common_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="9dp"
            tools:src="@mipmap/me_icon_help" />

        <LinearLayout
            android:id="@+id/idTwoTab"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/idTab1"
                android:layout_weight="1"
                android:text="资料编辑"
                android:textStyle="bold"
                android:textSize="16sp"
                android:gravity="end"
                android:textColor="@color/common_color_191919"
                android:layout_width="wrap_content"
                android:layout_marginEnd="16dp"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/idTab2"
                android:layout_weight="1"
                android:textStyle="bold"
                android:layout_marginStart="16dp"
                android:text="主页预览"
                android:textSize="16sp"
                android:textColor="@color/common_color_B8B8B8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/tv_common_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="5dp"
        android:gravity="center"
        tools:text="预览"
        android:layout_marginRight="12dp"
        tools:visibility="visible"
        android:textColor="@color/common_selector_text_color_191919_enable_color_cccccc"
        android:textSize="17dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_common_right"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:src="@drawable/common_ic_icon_chat_setting"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_left"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="end"
        app:constraint_referenced_ids="iv_common_back"
        app:layout_constrainedWidth="true" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="iv_right_placeholder"
        app:layout_constrainedWidth="true" />

    <View
        android:visibility="invisible"
        android:id="@+id/v_title_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_divider_height"
        android:background="#cfcfcf"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
