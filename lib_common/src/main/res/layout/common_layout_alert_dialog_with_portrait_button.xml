<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:padding="24dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_conor_16_color_white">


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_24"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="登录失败，请重新扫码登录" />

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="270dp"
        android:scrollbars="none"
        android:layout_marginTop="12dp"
        android:textColor="@color/common_color_4C4C4C"
        android:textSize="@dimen/common_text_sp_14"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="content" />

    <com.kanzhun.common.views.RoundAlphaButton
        android:id="@+id/tv_positive"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/common_blue_button_style"
        android:layout_marginTop="24dp"
        app:layout_constraintLeft_toRightOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_content"
        tools:text="确定" />

    <com.kanzhun.common.views.RoundAlphaButton
        android:id="@+id/tv_negative"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:layout_marginTop="12dp"
        android:gravity="center"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_positive"
        app:qmui_borderColor="@color/common_color_EBEBEB"
        app:qmui_borderWidth="1dp"
        app:qmui_radius="25dp"
        tools:text="取消" />

</androidx.constraintlayout.widget.ConstraintLayout>