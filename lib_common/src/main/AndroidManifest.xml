<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE" />
    <application>
        <service
            android:name="com.kanzhun.common.recorder.RecordService"
            android:enabled="true"
            android:exported="false" />
    </application>
</manifest>