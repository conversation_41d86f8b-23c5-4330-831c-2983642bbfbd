package com.kanzhun.foundation.screenShot.o

import android.os.Build
import android.os.Bundle
import androidx.annotation.CallSuper
import androidx.appcompat.app.AppCompatActivity
import com.kanzhun.common.router.service.IPermissionHelperService
import com.kanzhun.common.router.service.PERMISSION_HELPER_SERVICE
import com.kanzhun.common.router.service.PermissionCallback
import com.kanzhun.foundation.screenShot.o.ScreenshotDetectionListener.Companion.SCREENSHOT_FROM_SYSTEM
import com.sankuai.waimai.router.Router
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import java.lang.ref.WeakReference

private const val TAG = "ScreenshotDetection"

open class ScreenshotDetectionActivity : AppCompatActivity(), ScreenshotDetectionListener {
    private var screenshotDetectionDelegate: ScreenshotDetectionDelegate? = null

    // https://developer.android.com/about/versions/14/features/screenshot-detection
    private var screenCaptureCallbackRef: WeakReference<Any?>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (shouldEnableScreenshotDetection()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                screenCaptureCallbackRef = WeakReference<Any?>(ScreenCaptureCallback {
                    // Add logic to take action in your app.
                    onScreenCaptured("", SCREENSHOT_FROM_SYSTEM)
                })
            } else {
                // shouldCheckReadPermission() // 默认不再申请权限，有权限生效，无权限忽视
                screenshotDetectionDelegate = ScreenshotDetectionDelegate(this, this)
            }
        }
    }

    @Suppress("unused")
    private fun shouldCheckReadPermission() {
        Router.getService(IPermissionHelperService::class.java, PERMISSION_HELPER_SERVICE)
            ?.shouldCheckReadPermission(this, object : PermissionCallback {
                override fun onResult(yes: Boolean) {
                    if (!yes) {
                        onShowReadPermissionDeniedMessage()
                    }
                }
            })
    }

    @CallSuper
    fun onShowReadPermissionDeniedMessage() {
    }

    @OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
    override fun onStart() {
        super.onStart()
        if (shouldEnableScreenshotDetection()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                // Pass in the callback created in the previous step
                // and the intended callback executor (e.g. Activity's mainExecutor).
                val screenCaptureCallback =
                    screenCaptureCallbackRef?.get() as? ScreenCaptureCallback
                screenCaptureCallback?.let {
                    registerScreenCaptureCallback(mainExecutor, it)
                }
            } else {
                screenshotDetectionDelegate?.startScreenshotDetection()
            }
        }
    }

    override fun onStop() {
        super.onStop()
        if (shouldEnableScreenshotDetection()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                val screenCaptureCallback =
                    screenCaptureCallbackRef?.get() as? ScreenCaptureCallback
                screenCaptureCallback?.let {
                    unregisterScreenCaptureCallback(it)
                }
            } else {
                screenshotDetectionDelegate?.stopScreenshotDetection()
            }
        }
    }

    open fun shouldEnableScreenshotDetection(): Boolean = false

    @CallSuper
    override fun onScreenCaptured(path: String, via: Int) {
        // Do something when screen was captured
        TLog.info(
            TAG,
            "onScreenCaptured: $path, via: ${if (via == SCREENSHOT_FROM_SYSTEM) "system" else "app"}"
        )
    }
}