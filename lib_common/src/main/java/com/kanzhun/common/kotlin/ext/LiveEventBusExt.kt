package com.kanzhun.common.kotlin.ext

import com.kanzhun.common.kotlin.constant.HomeTabType
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.constant.ParentHomeTabType
import com.kanzhun.common.util.liveeventbus.LiveEventBus


/**
 * 发送String类型的LiveEvent
 * @param alwaysActive
 *  set if then observer can always receive message
 * true: observer can always receive message
 * false: observer can only receive message when resumed
 */
fun sendStringLiveEvent(
    key: String,
    value: String,
    alwaysActive: Boolean = true,
    autoClear: Boolean = true
) {
    LiveEventBus.config(key).apply {
        lifecycleObserverAlwaysActive(alwaysActive)
        autoClear(autoClear)
    }
    LiveEventBus.get<String>(key).post(value)
}

/**
 * 发送String类型的LiveEvent
 * @param alwaysActive
 *  set if then observer can always receive message
 * true: observer can always receive message
 * false: observer can only receive message when resumed
 */
fun sendBooleanLiveEvent(
    key: String,
    value: Boolean,
    alwaysActive: Boolean = true,
    autoClear: Boolean = true
) {
    LiveEventBus.config(key).apply {
        lifecycleObserverAlwaysActive(alwaysActive)
        autoClear(autoClear)
    }
    LiveEventBus.get<Boolean>(key).post(value)
}

/**
 * 发送Int类型的LiveEvent
 * @param alwaysActive
 *  set if then observer can always receive message
 * true: observer can always receive message
 * false: observer can only receive message when resumed
 */
fun sendIntLiveEvent(
    key: String,
    value: Int,
    alwaysActive: Boolean = true,
    autoClear: Boolean = true
) {
    LiveEventBus.config(key).apply {
        lifecycleObserverAlwaysActive(alwaysActive)
        autoClear(autoClear)
    }
    LiveEventBus.get<Int>(key).post(value)
}

/**
 * 发送Object类型的LiveEvent
 * @param alwaysActive
 *  set if then observer can always receive message
 * true: observer can always receive message
 * false: observer can only receive message when resumed
 */
fun <T> sendObjectLiveEvent(
    key: String,
    value: T,
    alwaysActive: Boolean = true,
    autoClear: Boolean = true
) {
    LiveEventBus.config(key).apply {
        lifecycleObserverAlwaysActive(alwaysActive)
        autoClear(autoClear)
    }
    LiveEventBus.get<T>(key).post(value)
}


/**
 * 到探索页
 */
fun goMainExposeTab() {
    sendMainTabEvent(HomeTabType.EXPOSE)
}

/**
 * 去首页互动Tab(非父母模式)
 */
fun goMainInteractLikeMeTab() {
    sendMainTabEvent(HomeTabType.LIKE_ME)
}

/**
 * 去首页互动Tab(非父母模式)
 */
fun goMainInteractILikeTab() {
    sendMainTabEvent(HomeTabType.I_LIKE)
}

/**
 * 去首页互动Tab(非父母模式)
 */
fun goMainInteractSeeMeTab() {
    sendMainTabEvent(HomeTabType.SEE_ME)
}

/**
 * 去首页互动-父母推荐Tab(非父母模式)
 */
fun goMainInteractParentTab() {
    sendMainTabEvent(HomeTabType.PARENT_RECOMMEND)
}

/**
 * 去首页消息Tab(非父母模式)
 */
fun goMainMessageTab() {
    sendMainTabEvent(HomeTabType.MESSAGE)
}

/**
 * 去首页我的Tab(非父母模式)
 */
fun goMainMeTab() {
    sendMainTabEvent(HomeTabType.ME)
}


private fun sendMainTabEvent(type: HomeTabType) {
    LiveEventBus.get<HomeTabType>(LivedataKeyCommon.EVENT_KEY_MAIN_TAB).post(type)
}


/**
 * 父母模式到相亲页
 */
fun goParentHomeTab() {
    sendParentTabEvent(ParentHomeTabType.BLIND_DATE)
}

/**
 * 去转发记录Tab(父母模式)
 */
fun goParentShareRecordTab() {
    sendParentTabEvent(ParentHomeTabType.SHARE_RECORD)
}

/**
 * 去首页消息Tab(非父母模式)
 */
fun goParentMeTab() {
    sendParentTabEvent(ParentHomeTabType.ME)
}


private fun sendParentTabEvent(type: ParentHomeTabType) {
    LiveEventBus.get<ParentHomeTabType>(LivedataKeyCommon.EVENT_KEY_PARENT_MAIN_TAB).post(type)
}

/**
 * 发送父母应用内分享对象给孩子的行为
 */
fun sendParentShareActionEvent(userId: String?) {
    if (!userId.isNullOrBlank()) {
        sendStringLiveEvent(
            LivedataKeyCommon.EVENT_KEY_PARENT_SHARE_ACTION,
            userId,
            alwaysActive = true,
            autoClear = true
        )
    }
}

fun sendBindStatusChangeEvent(userId: String?) {
    if (!userId.isNullOrBlank()) {
        sendStringLiveEvent(
            LivedataKeyCommon.EVENT_KEY_PARENT_BIND_STATE_CHANGE,
            userId,
            alwaysActive = true,
            autoClear = true
        )
    }
    LiveEventBus.get<String>(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_STATE_CHANGE).post(userId)
}

fun setInviteSuccessEvent(userId: String?) {
    if (!userId.isNullOrBlank()) {
        sendStringLiveEvent(
            LivedataKeyCommon.EVENT_KEY_INVITE_SUCCESS,
            userId,
            alwaysActive = true,
            autoClear = true
        )
    }
}

fun setTopicCardRecommendEvent(extend: String?) {
    if (!extend.isNullOrBlank()) {
        sendStringLiveEvent(
            LivedataKeyCommon.EVENT_KEY_TOPIC_CARD_RECOMMEND,
            extend,
            alwaysActive = true,
            autoClear = true
        )
    }
}

fun setMeetingPlanRecommendEvent(extend: String?) {
    if (!extend.isNullOrBlank()) {
        sendStringLiveEvent(
            LivedataKeyCommon.EVENT_KEY_CHAT_MEETING_PLAN_TIP,
            extend,
            alwaysActive = true,
            autoClear = true
        )
    }
}

fun setChatSendStatusEvent(extend: String?) {
    if (!extend.isNullOrBlank()) {
        sendStringLiveEvent(
            LivedataKeyCommon.EVENT_SYNC_CHAT_SEND_STATUS,
            extend,
            alwaysActive = true,
            autoClear = true
        )
    }
}

fun sendDynamicRecommendEvent(extend: String?) {
    if (!extend.isNullOrBlank()) {
        sendStringLiveEvent(
            LivedataKeyCommon.EVENT_KEY_DYNAMIC_RECOMMEND,
            extend,
            alwaysActive = true,
            autoClear = true
        )
    }
}

fun sendRestrictChatEvent() {
    sendBooleanLiveEvent(
        key = LivedataKeyCommon.EVENT_KEY_RESTRICT_CHAT,
        value = true,
        alwaysActive = true,
        autoClear = true
    )
}

fun sendLocalMessageChangedEvent() {
    sendBooleanLiveEvent(
        key = LivedataKeyCommon.EVENT_KEY_LOCAL_MESSAGE_CHANGED,
        value = true
    )
}

fun sendLocalMessageNotifySingleChatTopFresh() {
    sendBooleanLiveEvent(
        key = LivedataKeyCommon.EVENT_KEY_SINGLE_CHAT_TOP_FRESH,
        value = true,
        alwaysActive = true,
        autoClear = true
    )
}
