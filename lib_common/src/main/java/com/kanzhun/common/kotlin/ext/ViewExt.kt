package com.kanzhun.common.kotlin.ext

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.view.View
import android.widget.FrameLayout
import com.kanzhun.common.blurkit.blurry.Blurry
import com.kanzhun.common.kotlin.ui.BlurryedLayout
import com.kanzhun.utils.L

fun View.gone() {
    visibility = View.GONE
}

fun View.visible() {
    visibility = View.VISIBLE
}

fun View.isVisible(): Boolean {
    return visibility == View.VISIBLE
}

/**
 * @param visible true:visible,false:gone,不会占用位置
 */
fun View.visible(visible: Boolean) {
    visibility = if (visible) View.VISIBLE else View.GONE
}

/**
 * @param visible true:visible,false:invisible,会占用位置
 */
fun View.invisible(visible: Boolean) {
    visibility = if (visible) View.VISIBLE else View.INVISIBLE
}

fun View.invisible() {
    visibility = View.INVISIBLE
}

fun View.enable() {
    isEnabled = true
}

fun View.unable() {
    isEnabled = false
}

fun View.enable(enabled: Boolean) {
    isEnabled = enabled
}

fun View.asBackButton(callback: () -> Unit = {}) {
    setOnClickListener {
        val context = context
        if (context is Activity) {
            callback()
            context.onBackPressed()
        }
    }
}

fun View.getViewTopLocationOnScreen(): Int {
    val contentViewLocationInScreen = IntArray(2)
    this.getLocationOnScreen(contentViewLocationInScreen)
    return contentViewLocationInScreen[1]
}

fun View.blurry(blurryedLayout: BlurryedLayout,btnTxt:String = "去添加",contentText:String,enable:Boolean = true,hitViews:Boolean = false,click:()->Unit = {}) {
    this.invisible()
    blurryedLayout.isCancel = false
    this.post {
//        if(blurryedLayout.isBlurry){
//            blurryedLayout.toBlurry(btnTxt,contentText,enable,hitViews,click)
//            return@post
//        }
        if(blurryedLayout.isCancel){
            return@post
        }
        blurryedLayout.toBlurry(btnTxt,contentText,enable,hitViews,click)
        Blurry.with(context)
            .radius(25)
            .async()
            .color(Color.argb(122, 255, 255, 255))
            .capture(this)
            .getAsync {
                blurryedLayout.getImageView().setImageDrawable(BitmapDrawable(resources, it))
            }
    }
}

fun View.blurryHeight(blurryedLayout: BlurryedLayout,btnTxt:String = "去添加",contentText:String,enable:Boolean = true,hitViews:Boolean = false,click:()->Unit = {}) {
    this.invisible()
    blurryedLayout.isCancel = false
    this.post {
//        if(blurryedLayout.isBlurry){
//            blurryedLayout.toBlurry(btnTxt,contentText,enable,hitViews,click)
//            return@post
//        }
        if(blurryedLayout.isCancel){
            return@post
        }
        blurryedLayout.toBlurry(btnTxt,contentText,enable,hitViews,click)
        Blurry.with(context)
            .radius(25)
            .async()
            .color(Color.argb(40, 255, 255, 255))
            .sampling(2)
            .capture(this)
            .getAsync {
                blurryedLayout.getImageView().setImageDrawable(BitmapDrawable(resources, it))
            }
    }
}

fun View.unBlurry(blurryedLayout: BlurryedLayout) {
    this.visible()
    blurryedLayout.cancelBlurry()
}

/**
 * 进行简单的模糊，一般用于文本
 */
fun View.simpleBlurry(isBlur:Boolean,frameLayout: FrameLayout){
    if(isBlur){
        this.post {
            Blurry.with(this.context)
                .radius(25)
                .async()
                .color(Color.argb(255, 0, 0, 0))
                .onto(frameLayout)
            this.invisible()
        }
    }else{
        removeBlurry(frameLayout)
    }
}

fun View.removeBlurry(frameLayout: FrameLayout){
    this.visible()
    Blurry.delete(frameLayout)
}

