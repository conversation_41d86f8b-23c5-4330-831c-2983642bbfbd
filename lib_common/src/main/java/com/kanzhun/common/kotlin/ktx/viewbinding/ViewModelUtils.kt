package com.kanzhun.common.kotlin.ktx.viewbinding

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.ParameterizedType

class ViewModelUtils {

    companion object {
        @JvmStatic
        fun <M : ViewModel> createWithGeneric(genericOwner: Any, ower: ViewModelStoreOwner): M =
            withGenericViewModelClass<M>(genericOwner) { clazz ->
                ViewModelProvider(ower)[clazz]
            }

//        private fun <M : ViewModel> withGenericViewModelClass(genericOwner: Any, block: (Class<M>) -> M): M {
//            val genericSuperclass = genericOwner.javaClass.genericSuperclass
//            val parameterizedType: ParameterizedType = genericSuperclass as ParameterizedType
//            val bindingClass = parameterizedType.actualTypeArguments[1] as Class<M>
//            return block.invoke(bindingClass)
//        }

        private fun <VB : ViewModel> withGenericViewModelClass(genericOwner: Any, block: (Class<VB>) -> VB): VB {
            var genericSuperclass = genericOwner.javaClass.genericSuperclass
            var superclass = genericOwner.javaClass.superclass
            while (superclass != null) {
                if (genericSuperclass is ParameterizedType) {
                    if(genericSuperclass.actualTypeArguments != null && genericSuperclass.actualTypeArguments.size >1){
                        try {
                            return block.invoke(genericSuperclass.actualTypeArguments[1] as Class<VB>)
                        } catch (e: NoSuchMethodException) {
                        } catch (e: ClassCastException) {
                        } catch (e: InvocationTargetException) {
                            var tagException: Throwable? = e
                            while (tagException is InvocationTargetException) {
                                tagException = e.cause
                            }
                            throw tagException ?: IllegalArgumentException("ViewModel generic was found, but creation failed.")
                        }
                    }
                }
                genericSuperclass = superclass.genericSuperclass
                superclass = superclass.superclass
            }
            throw IllegalArgumentException("There is no generic of ViewBinding.")
        }


    }

}