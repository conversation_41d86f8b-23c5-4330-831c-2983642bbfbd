package com.kanzhun.common.kotlin.ext

import java.text.SimpleDateFormat
import java.util.Locale

fun Long.to99PlusString(): String {
    return if (this > 99) {
        "99+"
    } else {
        toString()
    }
}

fun Long.toSimpleDateFormatText(pattern: String = "yyyy-MM-dd HH:mm:ss"): String {

    try {
        return SimpleDateFormat(pattern, Locale.getDefault()).format(this)
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return ""

}


