package com.kanzhun.common.views.slanted;


import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.text.TextPaint;

/**
 * <AUTHOR>
 * @date 2022/3/8.
 */
public class SlantedTextPathFactory {

    public static Path create(SlantedTextPathMode mode, int w, int h, float slantedLength) {
        switch (mode) {
            case MODE_RIGHT:
                return getModeRightPath(w, h, slantedLength);
            case MODE_LEFT_BOTTOM:
                return getModeLeftBottomPath(w, h, slantedLength);
            case MODE_RIGHT_BOTTOM:
                return getModeRightBottomPath(w, h, slantedLength);
            case MODE_LEFT_TRIANGLE:
                return getModeLeftTrianglePath(w, h);
            case MODE_RIGHT_TRIANGLE:
                return getModeRightTrianglePath(w, h);
            case MODE_LEFT_BOTTOM_TRIANGLE:
                return getModeLeftBottomTrianglePath(w, h);
            case MODE_RIGHT_BOTTOM_TRIANGLE:
                return getModeRightBottomTrianglePath(w, h);
            default:
                return getModeLeftPath(w, h, slantedLength);
        }
    }

    private static Path getModeLeftPath(int w, int h, float slantedLength) {
        Path path = new Path();
        path.moveTo(w, 0);
        path.lineTo(0, h);
        path.lineTo(0, h - slantedLength);
        path.lineTo(w - slantedLength, 0);
        return path;
    }

    private static Path getModeRightPath(int w, int h, float slantedLength) {
        Path path = new Path();
        path.lineTo(w, h);
        path.lineTo(w, h - slantedLength);
        path.lineTo(slantedLength, 0);
        return path;
    }

    private static Path getModeLeftBottomPath(int w, int h, float slantedLength) {
        Path path = new Path();
        path.lineTo(w, h);
        path.lineTo(w - slantedLength, h);
        path.lineTo(0, slantedLength);
        return path;
    }

    private static Path getModeRightBottomPath(int w, int h, float slantedLength) {
        Path path = new Path();
        path.moveTo(0, h);
        path.lineTo(slantedLength, h);
        path.lineTo(w, slantedLength);
        path.lineTo(w, 0);
        return path;
    }

    private static Path getModeLeftTrianglePath(int w, int h) {
        Path path = new Path();
        path.lineTo(0, h);
        path.lineTo(w, 0);
        return path;
    }

    private static Path getModeRightTrianglePath(int w, int h) {
        Path path = new Path();
        path.lineTo(w, 0);
        path.lineTo(w, h);
        return path;
    }

    private static Path getModeLeftBottomTrianglePath(int w, int h) {
        Path path = new Path();
        path.lineTo(w, h);
        path.lineTo(0, h);
        return path;
    }

    private static Path getModeRightBottomTrianglePath(int w, int h) {
        Path path = new Path();
        path.moveTo(0, h);
        path.lineTo(w, h);
        path.lineTo(w, 0);
        return path;
    }

    public static float[] calculateXY(SlantedTextPathMode mode, int w, int h, float mSlantedLength, int rotateAngle, TextPaint mTextPaint, String mSlantedText) {
        float[] xy = new float[5];
        Rect rect = null;
        RectF rectF = null;
        int offset = (int) (mSlantedLength / 2);
        switch (mode) {
            case MODE_LEFT_TRIANGLE:
            case MODE_LEFT:
                rect = new Rect(0, 0, w, h);
                rectF = new RectF(rect);
                rectF.right = mTextPaint.measureText(mSlantedText, 0, mSlantedText.length());
                rectF.bottom = mTextPaint.descent() - mTextPaint.ascent();
                rectF.left += (rect.width() - rectF.right) / 2.0f;
                rectF.top += (rect.height() - rectF.bottom) / 2.0f;
                xy[0] = rectF.left;
                xy[1] = rectF.top - mTextPaint.ascent();
                xy[2] = w / 2;
                xy[3] = h / 2;
                xy[4] = -rotateAngle;
                break;
            case MODE_RIGHT_TRIANGLE:
            case MODE_RIGHT:
                rect = new Rect(offset, 0, w + offset, h);
                rectF = new RectF(rect);
                rectF.right = mTextPaint.measureText(mSlantedText, 0, mSlantedText.length());
                rectF.bottom = mTextPaint.descent() - mTextPaint.ascent();
                rectF.left += (rect.width() - rectF.right) / 2.0f;
                rectF.top += (rect.height() - rectF.bottom) / 2.0f;
                xy[0] = rectF.left;
                xy[1] = rectF.top - mTextPaint.ascent();
                xy[2] = w / 2 + offset;
                xy[3] = h / 2;
                xy[4] = rotateAngle;
                break;
            case MODE_LEFT_BOTTOM_TRIANGLE:
            case MODE_LEFT_BOTTOM:
                rect = new Rect(0, offset, w, h + offset);
                rectF = new RectF(rect);
                rectF.right = mTextPaint.measureText(mSlantedText, 0, mSlantedText.length());
                rectF.bottom = mTextPaint.descent() - mTextPaint.ascent();
                rectF.left += (rect.width() - rectF.right) / 2.0f;
                rectF.top += (rect.height() - rectF.bottom) / 2.0f;

                xy[0] = rectF.left;
                xy[1] = rectF.top - mTextPaint.ascent();
                xy[2] = w / 2;
                xy[3] = h / 2 + offset;
                xy[4] = rotateAngle;
                break;
            case MODE_RIGHT_BOTTOM_TRIANGLE:
            case MODE_RIGHT_BOTTOM:
                rect = new Rect(offset, offset, w + offset, h + offset);
                rectF = new RectF(rect);
                rectF.right = mTextPaint.measureText(mSlantedText, 0, mSlantedText.length());
                rectF.bottom = mTextPaint.descent() - mTextPaint.ascent();
                rectF.left += (rect.width() - rectF.right) / 2.0f;
                rectF.top += (rect.height() - rectF.bottom) / 2.0f;
                xy[0] = rectF.left;
                xy[1] = rectF.top - mTextPaint.ascent();
                xy[2] = w / 2 + offset;
                xy[3] = h / 2 + offset;
                xy[4] = -rotateAngle;
                break;
        }
        return xy;
    }
}
