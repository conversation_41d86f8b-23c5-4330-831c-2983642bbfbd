package com.kanzhun.common.views.textview;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.AnimationSet;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout;
import android.widget.TextSwitcher;
import android.widget.TextView;

import androidx.annotation.StringRes;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.R;
import com.kanzhun.common.animator.interpolator.InterpolatorUtil;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/3/31
 */
public class TextSwitchView extends FrameLayout {

    private TextView tv1;
    private TextView tv2;
    private TextSwitcher textSwitcher;

    public TextSwitchView(Context context) {
        this(context, null);
    }

    public TextSwitchView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initTextView(context);
    }

    private void initTextView(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.common_view_text_switch, this);
        tv1 = view.findViewById(R.id.tv_1);
        tv2 = view.findViewById(R.id.tv_2);
        textSwitcher = view.findViewById(R.id.text_switch);
        int width = QMUIDisplayHelper.dp2px(context, 40);

        AnimationSet animationSet1 = new AnimationSet(false);
        animationSet1.setInterpolator(InterpolatorUtil.createDefaultBezierInterpolator());
        TranslateAnimation inTranslateAnimation = new TranslateAnimation(width, 0, 0, 0);
        AlphaAnimation inAlphaAnimation = new AlphaAnimation(0, 1);
        animationSet1.addAnimation(inTranslateAnimation);
        animationSet1.addAnimation(inAlphaAnimation);
        animationSet1.setDuration(500);

        AnimationSet animationSet2 = new AnimationSet(false);
        animationSet2.setInterpolator(InterpolatorUtil.createDefaultBezierInterpolator());
        TranslateAnimation outTranslateAnimation = new TranslateAnimation(0, -width, 0, 0);
        AlphaAnimation outAlphaAnimation = new AlphaAnimation(1, 0);
        animationSet2.addAnimation(outTranslateAnimation);
        animationSet2.addAnimation(outAlphaAnimation);
        animationSet2.setDuration(500);

        textSwitcher.setInAnimation(animationSet1);
        textSwitcher.setOutAnimation(animationSet2);
    }

    public void setTextDefault(@StringRes int resId) {
        CharSequence text = getResources().getText(resId);
        setTextDefault(text);
    }

    public void setTextDefault(CharSequence text) {
        textSwitcher.setCurrentText(text);
    }

    public void setTextAnim(@StringRes int resId) {
        CharSequence text = getResources().getText(resId);
        textSwitcher.setText(text);
    }

    public void setTextAnim(CharSequence text) {
        textSwitcher.setText(text);
    }

    public void setTextColor(int color) {
        TextView currentView = (TextView) textSwitcher.getCurrentView();
        currentView.setTextColor(color);
    }

    public void setNextTextColor(int color) {
        TextView currentView = (TextView) textSwitcher.getNextView();
        currentView.setTextColor(color);
    }

}
