package com.kanzhun.common.model;


import java.io.Serializable;

public class ImageInfo implements Serializable {
    private static final long serialVersionUID = -6214516774598519616L;
    public int width;
    public int height;
    public String url;

    public String getPostfix() {
        return postfix;
    }

    public String postfix;

    public ImageInfo() {

    }

    public ImageInfo(int width, int height, String url) {
        this.width = width;
        this.height = height;
        this.url = url;
    }

    public ImageInfo(int width, int height, String url, String postfix) {
        this.width = width;
        this.height = height;
        this.url = url;
        this.postfix = postfix;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public String getUrl() {
        return url;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void setPostfix(String postfix) {
        this.postfix = postfix;
    }
}
