package com.kanzhun.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProvider;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.kanzhun.common.R;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.base.BaseViewModel;
import com.kanzhun.common.kotlin.ui.dialog.loading.LoadingDialog;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;

/**
 * <AUTHOR>
 * @date 2020/4/10.
 */
public abstract class BottomSheetBehaviorBaseFragment<D extends ViewDataBinding, M extends BaseViewModel> extends BottomSheetDialogFragment {
    private BottomSheetBehavior behavior;
    private BottomSheetHideCallback mHideCallback;
    private D mDataBinding;
    private M mFragmentViewModel;
    protected Activity activity;
    private Handler progressHandler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            progressDialog.show(msg.obj.toString());
            return false;
        }
    });

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity == null) {
            this.activity = activity;
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (activity == null) {
            activity = (Activity) context;
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        getDialog().getWindow().findViewById(R.id.design_bottom_sheet).setBackgroundColor(getResources().getColor(android.R.color.transparent));
        getDialog().setCanceledOnTouchOutside(true);
        getDialog().setCancelable(true);
        Dialog dialog = getDialog();
        FrameLayout bottomSheet = dialog.findViewById(R.id.design_bottom_sheet);
        ViewGroup.LayoutParams layoutParams = bottomSheet.getLayoutParams();
        layoutParams.height = getBottomSheetLayoutParamsHeight();
        behavior = BottomSheetBehavior.from(bottomSheet);
        behavior.setSkipCollapsed(getSkipCollapsed());//下滑会跳过STATE_COLLAPSED状态
        setBehavior();
    }

    protected void setBehavior() {

    }

    protected boolean getSkipCollapsed() {
        return true;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mDataBinding = DataBindingUtil.inflate(inflater, getContentLayoutId(), container, false);
        View view = mDataBinding.getRoot();
        initViewHolder();
        initFragment();
        return view;
    }

    private void initViewHolder() {
        ViewModelProvider.AndroidViewModelFactory factory = ViewModelProvider.AndroidViewModelFactory.getInstance(BaseApplication.getApplication());
        /**
         * owner为fragment本身
         */
        ViewModelProvider providerOwner = new ViewModelProvider(this);
        Class<M> entityClassOwner = (Class<M>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        mFragmentViewModel = providerOwner.get(entityClassOwner);
        if (getBindingVariable() > 0) {
            mDataBinding.setVariable(getBindingVariable(), mFragmentViewModel);
        }
        if (getCallbackVariable() >= 0 && getCallback() != null) {
            mDataBinding.setVariable(getCallbackVariable(), getCallback());
        }
        mDataBinding.executePendingBindings();
    }

    public abstract int getContentLayoutId();

    protected abstract void initFragment();

    public abstract int getCallbackVariable();

    public abstract Object getCallback();

    public abstract int getBindingVariable();

    public D getDataBinding() {
        return mDataBinding;
    }

    public M getViewModel() {
        return mFragmentViewModel;
    }

    public int getBottomSheetLayoutParamsHeight() {
        return ViewGroup.LayoutParams.WRAP_CONTENT;
    }

    public void setHideCallback(BottomSheetHideCallback hideCallback) {
        mHideCallback = hideCallback;
    }

    public BottomSheetHideCallback getHideCallback() {
        return mHideCallback;
    }

    @Override
    public int getTheme() {
        return R.style.common_bottom_sheet_Style;
    }

    @Override
    public void onStart() {
        super.onStart();
        setBehaviorDefaultState();
    }

    protected void setBehaviorDefaultState() {
        if (behavior != null) {
            // 初始为展开状态
            behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        }
    }

    public BottomSheetBehavior getBehavior() {
        return behavior;
    }

    public interface BottomSheetHideCallback {
        void onHide();
    }

    public float getYVelocity() {
        if (behavior != null) {
            VelocityTracker velocityTracker;
            try {
                Field field = behavior.getClass().getDeclaredField("velocityTracker");
                field.setAccessible(true);
                velocityTracker = (VelocityTracker) field.get(behavior);
                if (velocityTracker != null) {
                    return velocityTracker.getYVelocity();
                }

                Method[] methods = behavior.getClass().getDeclaredMethods();
                for (Method method : methods) {
                    method.setAccessible(true);
                    if (TextUtils.equals("getYVelocity", method.getName())) {
                        float velocityTrackerY = (float) method.invoke(behavior);
                        return velocityTrackerY;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    @Override
    public void onDestroy() {
        dismissProgressDialog();
        super.onDestroy();
        if (progressHandler != null) {
            progressHandler.removeCallbacksAndMessages(null);
            progressHandler = null;
        }
    }

    /**
     * 加载框
     */
    private LoadingDialog progressDialog;

    public void showProgressDialog(int resId) {
        String text = getResources().getString(resId);
        showProgressDialog(text);
    }

    public void showProgressDialog(String text) {
        showProgressDialog(text, false);
    }

    public void showProgressDialog(int resId, boolean flag) {
        String text = getResources().getString(resId);
        showProgressDialog(text, flag);
    }

    public void showProgressDialog(String text, boolean flag) {
        try {
            if (activity == null || activity.isFinishing()) return;
            if (progressDialog == null) {
                progressDialog = new LoadingDialog(activity);
            }
            progressDialog.setCancelable(flag);
            progressDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    dismissProgressDialog();
                }
            });
            progressHandler.removeMessages(1);
            Message msg = Message.obtain();
            msg.what = 1;
            msg.obj = text; //携带当前值
            progressHandler.sendMessageDelayed(msg, 300);
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public void dismissProgressDialog() {
        try {
            progressHandler.removeMessages(1);
            if (progressDialog != null) {
                progressDialog.dismiss();
            }
            progressDialog = null;
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }
}
