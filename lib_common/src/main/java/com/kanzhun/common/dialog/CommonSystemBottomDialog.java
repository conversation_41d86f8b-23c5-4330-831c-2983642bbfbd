package com.kanzhun.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.R;
import com.kanzhun.common.decoration.VerticalDividerItemDecoration;
import com.kanzhun.common.views.RoundAlphaButton;
import com.kanzhun.utils.base.LList;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>g<PERSON>g
 * Date: 2022/9/27
 * 底部公共系统弹窗 按照最新规范修改
 */
public class CommonSystemBottomDialog extends CommonBaseDialog<CommonSystemBottomDialog.Builder> {
    private RoundAlphaButton cancelBtn;
    private RecyclerView recyclerView;

    protected CommonSystemBottomDialog(Builder builder, Context context) {
        super(builder, context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.common_dialog_system_bottom);
        setWindowParams();
        initView();
    }

    private void initView() {
        cancelBtn = findViewById(R.id.btn_cancel);
        recyclerView = findViewById(R.id.recycler);

        if (!TextUtils.isEmpty(builder.cancelContent)) {
            cancelBtn.setText(builder.cancelContent);
        }
        if (builder.cancelColorRed) {
            cancelBtn.setTextColor(getContext().getColor(R.color.common_color_FD6666));
        }

        if (!LList.isEmpty(builder.data)) {
            BaseQuickAdapter<String, BaseViewHolder> adapter = new BaseQuickAdapter<>(R.layout.common_item_dialog_system_bottom, builder.data) {
                @Override
                protected void convert(@NonNull BaseViewHolder baseViewHolder, String item) {
                    baseViewHolder.setText(R.id.tv_content, item);
                }
            };
            recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
            VerticalDividerItemDecoration itemDecoration = new VerticalDividerItemDecoration(QMUIDisplayHelper.dpToPx(1));
            itemDecoration.setDividerColor(getContext().getColor(R.color.common_color_E5E5E5));
            recyclerView.addItemDecoration(itemDecoration);
            recyclerView.setAdapter(adapter);
            adapter.setList(builder.data);

            if (builder.onBottomItemClickListener != null) {
                adapter.setOnItemClickListener(new com.chad.library.adapter.base.listener.OnItemClickListener() {
                    @Override
                    public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                        builder.onBottomItemClickListener.onBottomItemClick(CommonSystemBottomDialog.this, view, position);
                    }
                });
            }
        }

        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (builder.autoCancelDismiss) {
                    dismiss();
                }
                if (builder.onBottomItemClickListener != null) {
                    builder.onBottomItemClickListener.onCancelClick(CommonSystemBottomDialog.this);
                }
            }
        });
    }

    public static class Builder extends CommonBaseDialog.Builder<CommonSystemBottomDialog.Builder, CommonSystemBottomDialog> {
        private String cancelContent;
        private List<String> data;
        private boolean cancelColorRed;
        private boolean autoCancelDismiss = true;
        private OnBottomItemClickListener onBottomItemClickListener;

        public Builder(Context context) {
            super(context);
        }

        public Builder setCancelContent(String cancelContent) {
            this.cancelContent = cancelContent;
            return this;
        }

        public Builder setData(List<String> data) {
            this.data = data;
            return this;
        }

        public Builder setData(String... items) {
            if (items.length > 0) {
                this.data = new ArrayList<>(items.length);
                Collections.addAll(this.data, items);
            }
            return this;
        }

        public Builder setCancelColorRed(boolean cancelColorRed) {
            this.cancelColorRed = cancelColorRed;
            return this;
        }

        public Builder setAutoCancelDismiss(boolean autoCancelDismiss) {
            this.autoCancelDismiss = autoCancelDismiss;
            return this;
        }

        public Builder setOnBottomItemClickListener(OnBottomItemClickListener onBottomItemClickListener) {
            this.onBottomItemClickListener = onBottomItemClickListener;
            return this;
        }

        @Override
        public CommonSystemBottomDialog createDialog() {
            setCancelable(true)
                    .setCanceledOnTouchOutside(true)
                    .setPadding(0, 0)
                    .setGravity(Gravity.BOTTOM)
                    .setAnimationStyle(R.style.common_window_bottom_to_top_anim);
            return new CommonSystemBottomDialog(this, context);
        }
    }

    public interface OnBottomItemClickListener {
        void onBottomItemClick(Dialog dialog, View view, int pos);

        void onCancelClick(Dialog dialog);
    }
}
