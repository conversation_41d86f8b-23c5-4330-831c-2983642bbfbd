package com.kanzhun.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.View;
import android.widget.TextView;

import com.kanzhun.common.views.wheel.pick.LinkagePickerOption;
import com.kanzhun.common.views.wheel.pick.LinkageView;
import com.kanzhun.common.views.wheel.pick.listener.OnOptionsSelectChangeListener;

/**
 * <AUTHOR>
 * @date 2022/3/23.
 */
public class LinkageDialog extends CommonBaseDialog<LinkageDialog.Builder> {
    private int selectPosition1 = 0;
    private int selectPosition2 = 0;
    private int selectPosition3 = 0;
    private OnItemSureClickListener onItemSureClickListener;

    protected LinkageDialog(Builder builder, Context context) {
        super(builder, context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(builder.layoutId);
        setWindowParams();
        setViewClick();
        SparseArray<String> displayTextList = builder.displayTextList;
        for (int index = 0; index < displayTextList.size(); index++) {
            int id = builder.displayTextList.keyAt(index);
            View view = findViewById(id);
            if (view != null && view instanceof TextView) {
                String text = displayTextList.get(id);
                if (TextUtils.isEmpty(text)) {
                    view.setVisibility(View.GONE);
                } else {
                    ((TextView) view).setText(text);
                }
            }
        }
        if (builder.linkageId > 0) {
            LinkageView linkageView = findViewById(builder.linkageId);
            LinkagePickerOption linkagePickerOption = builder.getPickerOptions();
            if (linkagePickerOption != null) {
                linkagePickerOption.optionsSelectChangeListener = new OnOptionsSelectChangeListener() {
                    @Override
                    public void onOptionsSelectChanged(int options1, int options2, int options3) {
                        selectPosition1 = options1;
                        selectPosition2 = options2;
                        selectPosition3 = options3;
                    }
                };
                selectPosition1 = linkagePickerOption.options1;
                selectPosition2 = linkagePickerOption.options2;
                selectPosition3 = linkagePickerOption.options3;
                linkageView.setPickerOption(builder.getPickerOptions());
            }
        }
    }

    @Override
    protected void setViewClick() {
        super.setViewClick();
        if (builder.sureId > 0) {
            View view = findViewById(builder.sureId);
            if (view != null) {
                view.setOnClickListener(this);
            }
        }
    }

    public void setOnItemSureClickListener(OnItemSureClickListener onItemSureClickListener) {
        this.onItemSureClickListener = onItemSureClickListener;
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == builder.sureId && onItemSureClickListener != null) {
            onItemSureClickListener.onSureItemClick(this, view, selectPosition1, selectPosition2, selectPosition3);
            return;
        }
        if (listener == null) {
            return;
        }
        listener.onItemClick(this, view);
    }

    public static class Builder extends CommonBaseDialog.Builder<Builder, LinkageDialog> {
        private int linkageId;
        private LinkagePickerOption mPickerOptions;
        protected int layoutId;
        protected SparseArray<String> displayTextList = new SparseArray<String>();
        protected int sureId;
        private OnItemSureClickListener onItemSureClickListener;
        private int selectPosition;

        public Builder(Context context) {
            super(context);
        }

        public LinkageDialog.Builder setLinkageId(int linkageId) {
            this.linkageId = linkageId;
            return this;
        }

        public LinkagePickerOption getPickerOptions() {
            return mPickerOptions;
        }

        public LinkageDialog.Builder setPickerOptions(LinkagePickerOption mPickerOptions) {
            this.mPickerOptions = mPickerOptions;
            return this;
        }

        public LinkageDialog.Builder setLayoutId(int layoutId) {
            this.layoutId = layoutId;
            return this;
        }

        public LinkageDialog.Builder setDisplayTextById(int id, String text) {
            this.displayTextList.put(id, text);
            return this;
        }

        public LinkageDialog.Builder setSureId(int sureId) {
            this.sureId = sureId;
            return this;
        }

        public LinkageDialog.Builder setSelectPosition(int selectPosition) {
            this.selectPosition = selectPosition;
            return this;
        }

        public LinkageDialog.Builder setOnItemSureClickListener(OnItemSureClickListener onItemSureClickListener) {
            this.onItemSureClickListener = onItemSureClickListener;
            return this;
        }

        @Override
        public LinkageDialog createDialog() {
            LinkageDialog dialog = new LinkageDialog(this, context);
            if (onItemSureClickListener != null) {
                dialog.setOnItemSureClickListener(onItemSureClickListener);
            }
            return dialog;
        }
    }

    public interface OnItemSureClickListener {
        void onSureItemClick(Dialog dialog, View view, int options1, int options2, int options3);
    }
}
