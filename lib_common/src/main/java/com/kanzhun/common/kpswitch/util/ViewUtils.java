package com.kanzhun.common.kpswitch.util;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Point;
import android.os.Build;
import android.text.InputFilter;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;

import androidx.core.widget.TextViewCompat;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class ViewUtils {

    private static final String ELLIPSIS_NORMAL = "\u2026"; // HORIZONTAL ELLIPSIS (…)


    public static Layout getLayout(TextPaint tp, CharSequence cs, int lineWidth) {
        StaticLayout layout = new StaticLayout(cs, tp, lineWidth, Layout.Alignment.ALIGN_NORMAL,
                1.0f, 0.0f, true);
        return layout;
    }

    /**
     * 计算每一行的开始字符位置和结束字符位置
     *
     * @return List.size()为总行数．point.x 为当前行的开始字符位置， point.y 为当前行的结束字符位置
     */
    public static List<Point> getLineStartAndEnd(TextPaint tp, CharSequence cs, int lineWidth) {
        // StaticLayout是android中处理文字换行的一个工具类，StaticLayout已经实现了文本绘制换行处理
        StaticLayout layout = new StaticLayout(cs, tp, lineWidth, Layout.Alignment.ALIGN_NORMAL,
                1.0f, 0.0f, true);
        int count = layout.getLineCount();
        List<Point> list = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            list.add(new Point(layout.getLineStart(i), layout.getLineEnd(i)));
        }
        return list;
    }

    public static void setEllipsizeMiddle(TextView textView, String content) {
        int count = 8;
        textView.post(new Runnable() {
            @Override
            public void run() {
                try {
                    int maxLine = TextViewCompat.getMaxLines(textView);
                    int availableWidth = textView.getWidth() - textView.getPaddingLeft() - textView.getPaddingRight();
                    if (availableWidth <= 0) {
                        return;
                    }
                    if (maxLine < 2) {
                        textView.setText(content);
                    } else {
                        List<Point> linesStart = getLineStartAndEnd(textView.getPaint(), content, availableWidth);
                        if (linesStart.size() <= maxLine) { // 行数没有超过限制，不做处理
                            textView.setText(content);
                            return;
                        }
                        textView.setGravity(Gravity.LEFT);
                        int middleLineStart = maxLine - 1;
                        Point point = linesStart.get(middleLineStart);
                        int startEllipsize = point.y - ELLIPSIS_NORMAL.length() - count;// 减count是为了在后缀名前面留几个文字
                        final String substringStart = content.substring(0, startEllipsize); //省略号之前的文字

                        int middleLineEnd = linesStart.size() - 1;
                        Point pointEnd = linesStart.get(middleLineEnd);
                        String substringEnd = content.substring(pointEnd.x);
                        // 裁剪省略号后面的文字，直到整体在行数范围内可显示
                        int interval = 4;
                        while (getLayout(textView.getPaint(), substringStart + ELLIPSIS_NORMAL + substringEnd, availableWidth).getLineCount() > maxLine) {
                            if (substringEnd.length() > count + interval) {
                                substringEnd = substringEnd.substring(interval);//会少一些文字，但是会大大减少循环次数
                            } else {
                                substringEnd = substringEnd.substring(1);
                            }
                        }
                        textView.setText(substringStart + ELLIPSIS_NORMAL + substringEnd);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });
    }

    /**
     * 设置指定view的实际大小
     *
     * @param view   指定控件
     * @param width  view的实际宽
     * @param height view的实际高
     */
    public static void layoutView(View view, int width, int height) {
        // 指定整个View的大小 参数是左上角 和右下角的坐标
        view.layout(0, 0, width, height);
        int measuredWidth = View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY);
        int measuredHeight = View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.AT_MOST);
        // 当然，measure完后，并不会实际改变View的尺寸，需要调用View.layout方法去进行布局。
        view.measure(measuredWidth, measuredHeight);
        view.layout(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
    }

    /**
     * 通过canvas将view转化为bitmap
     *
     * @param view 指定view
     * @return bitmap
     */
    public static Bitmap getBitmap(View view) {
        if (view == null) {
            return null;
        }
        Bitmap bmp = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bmp);
        view.draw(canvas);
        return bmp;
    }

    public static boolean refreshHeight(final View view, final int aimHeight) {
        if (view.isInEditMode()) {
            return false;
        }

        if (view.getHeight() == aimHeight) {
            return false;
        }

        if (Math.abs(view.getHeight() - aimHeight)
                == StatusBarHeightUtil.getStatusBarHeight(view.getContext())) {
            return false;
        }

        final int validPanelHeight = KeyboardUtil.getValidPanelHeight(view.getContext());
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    validPanelHeight);
            view.setLayoutParams(layoutParams);
        } else {
            layoutParams.height = validPanelHeight;
            view.requestLayout();
        }

        return true;
    }

    public static boolean isFullScreen(final Activity activity) {
        return (activity.getWindow().getAttributes().flags
                & WindowManager.LayoutParams.FLAG_FULLSCREEN) != 0;
    }

    @TargetApi(Build.VERSION_CODES.KITKAT)
    public static boolean isTranslucentStatus(final Activity activity) {
        //noinspection SimplifiableIfStatement
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            return (activity.getWindow().getAttributes().flags
                    & WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS) != 0;
        }
        return false;
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN)
    public static boolean isFitsSystemWindows(final Activity activity) {
        //noinspection SimplifiableIfStatement
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            return ((ViewGroup) activity.findViewById(android.R.id.content)).getChildAt(0).
                    getFitsSystemWindows();
        }

        return false;
    }

    public static Activity getActivityForContext(Context context) {
        if (context instanceof Activity) {
            return (Activity) context;
        }
        if (context instanceof ContextWrapper) {
            ContextWrapper wrapper = (ContextWrapper) context;
            return getActivityForContext(wrapper.getBaseContext());
        } else {
            return null;
        }
    }


    public static int getEditMaxLength(EditText editText) {
        int length = -1;
        try {
            InputFilter[] inputFilters = editText.getFilters();
            for (InputFilter filter : inputFilters) {
                Class<?> c = filter.getClass();
                if (c.getName().equals("android.text.InputFilter$LengthFilter")) {
                    Field[] f = c.getDeclaredFields();
                    for (Field field : f) {
                        if (field.getName().equals("mMax")) {
                            field.setAccessible(true);
                            length = (Integer) field.get(filter);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return length;
    }
}
