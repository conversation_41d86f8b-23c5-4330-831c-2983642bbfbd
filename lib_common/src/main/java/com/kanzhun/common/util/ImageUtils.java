package com.kanzhun.common.util;

import android.content.ContentResolver;
import android.database.Cursor;
import android.graphics.BitmapFactory;
import android.graphics.Point;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.kanzhun.utils.platform.Utils;
import com.zhihu.matisse.internal.utils.PhotoMetadataUtils;

import java.io.File;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/6/24.
 */
public class ImageUtils {

    private static final String TAG = "ImageUtils";

    /**
     * 设置视频缩略图的宽高
     *
     * @param width
     * @param height
     * @return
     */
    public static ImageSize initVideoImageSize(int maxWidth, float width, float height) {
        //原图比例
        float scale = width / height; //宽高比例
        if (scale > 1) { //宽度长
            width = maxWidth;
            height = width / scale;
        } else {
            height = maxWidth;
            width = height * scale;
        }
        ImageSize imageSize = new ImageSize();
        imageSize.width = (int) (width);
        imageSize.height = (int) (height);
        return imageSize;
    }

    /**
     * 设置视频缩略图的宽高
     *
     * @param width
     * @param height
     * @return
     */
    public static ImageSize initChatImageSize(float maxWidth, float minWidth, float width, float height) {
        //原图比例
        if (width > maxWidth || height > maxWidth) {
            float scale = width / height; //宽高比例
            if (scale > 1) { //宽度长
                height = height / (width / maxWidth);
                width = maxWidth;
                if (height < minWidth) {
                    height = minWidth;
                }
            } else {
                width = width / (height / maxWidth);
                height = maxWidth;
                if (width < minWidth) {
                    width = minWidth;
                }
            }
        }

        ImageSize imageSize = new ImageSize();
        imageSize.width =  width;
        imageSize.height = height;
        return imageSize;
    }

    public static ImageSize chatSourceSize(Uri uri) {
        ImageSize size = new ImageSize();
        ContentResolver contentResolver = Utils.getApp().getContentResolver();
        Cursor cursor = contentResolver.query(uri, new String[]{MediaStore.MediaColumns.WIDTH, MediaStore.MediaColumns.HEIGHT}, null, null, null);
        if (cursor != null) {
            if (cursor.moveToFirst()) {
                int columnIndex = cursor.getColumnIndex(MediaStore.Images.ImageColumns.WIDTH);
                if (columnIndex > -1) {
                    size.setWidth(cursor.getInt(columnIndex));
                }
                int columnIndex1 = cursor.getColumnIndex(MediaStore.Images.ImageColumns.HEIGHT);
                if (columnIndex1 > -1) {
                    size.setHeight(cursor.getInt(columnIndex1));
                }
            }
            cursor.close();
            if (size.width > 0 && size.height > 0) {
                return size;
            }
        }
        Point point = PhotoMetadataUtils.getBitmapBound(contentResolver, uri);
        size.setWidth(point.x);
        size.setHeight(point.y);
        return size;
    }

    public static ImageSize chatSourceSize(File filePath) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(filePath.toString(), options);
        int oriWidth = options.outWidth;
        int orgHeight = options.outHeight;
        ImageSize size = new ImageSize();
        size.setWidth(oriWidth);
        size.setHeight(orgHeight);
        return size;
    }

    public static boolean isGif(String url) {
        return TextUtils.equals("gif", url.substring(url.lastIndexOf(".") + 1).toLowerCase());
    }

    public static String getBitmapType(File filePath) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(filePath.toString(), options);
        options.inJustDecodeBounds = false;
        return options.outMimeType;
    }

    /**
     * Image宽高保存Bean类。
     */
    public static class ImageSize {

        private float width;

        private float height;

        public void setWidth(float width) {
            this.width = width;
        }

        public void setHeight(float height) {
            this.height = height;
        }

        public float getWidth() {
            return width;
        }

        public float getHeight() {
            return height;
        }
    }
}
