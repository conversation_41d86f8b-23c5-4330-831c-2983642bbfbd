package com.kanzhun.common.util;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.view.View;

public class BitmapUtils {

    public static Bitmap getCacheBitmapFromView(View v) {
        if (v.getLayoutParams().width == 0 || v.getLayoutParams().height == 0) {
            return null;
        }
        try {
            Bitmap b = Bitmap.createBitmap(v.getMeasuredWidth(), v.getMeasuredHeight(), Bitmap.Config.ARGB_4444);
            Canvas c = new Canvas(b);
            v.layout(v.getLeft(), v.getTop(), v.getRight(), v.getBottom());
            v.draw(c);
            return b;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将drawable转换成指定大小的bitmap
     */
    public static Bitmap zoomDrawable(Drawable drawable, int w, int h) {
        int width = drawable.getIntrinsicWidth();
        int height = drawable.getIntrinsicHeight();

        Bitmap oldBmp = drawableToBitmap(drawable);// drawable转换成bitmap

        Matrix matrix = new Matrix();// 创建操作图片用的Matrix对象
        float scaleWidth = ((float) w / width);// 计算缩放比例
        float scaleHeight = ((float) h / height);
        matrix.postScale(scaleWidth, scaleHeight);// 设置缩放比例

        return Bitmap.createBitmap(oldBmp, 0, 0, width, height, matrix, true);
    }

    /**
     * drawable 转换成bitmap
     */
    public static Bitmap drawableToBitmap(Drawable drawable) {
        // 取drawable的长宽
        int width = drawable.getIntrinsicWidth();
        int height = drawable.getIntrinsicHeight();

        // 取drawable的颜色格式
        Bitmap.Config config = drawable.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565;

        Bitmap bitmap = Bitmap.createBitmap(width, height, config);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, width, height);
        drawable.draw(canvas);

        return bitmap;
    }

    /**
     * 将bitmap转换成指定大小的bitmap
     */
    public static Bitmap zoomDrawable(Bitmap oldBmp, int w, int h) {
        int width = oldBmp.getWidth();
        int height = oldBmp.getHeight();

        Matrix matrix = new Matrix();// 创建操作图片用的Matrix对象
        float scaleWidth = ((float) w / width);// 计算缩放比例
        float scaleHeight = ((float) h / height);
        matrix.postScale(scaleWidth, scaleHeight);// 设置缩放比例

        return Bitmap.createBitmap(oldBmp, 0, 0, width, height, matrix, true);
    }

}
