<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.kanzhun.marry.take.widget.CameraContainer
        android:id="@+id/camera_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/iv_flash"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="40dp"
        android:scaleType="fitCenter"
        android:src="@mipmap/take_flash_close" />

    <ImageView
        android:id="@+id/iv_switch"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_centerVertical="true"
        android:layout_gravity="bottom|right"
        android:layout_marginRight="40dp"
        android:layout_marginBottom="60dp"
        android:background="@mipmap/take_camera_ic_switch" />

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="160dp"
        android:text="@string/take_editor_record_tips"
        android:textColor="@color/color_white" />


    <com.kanzhun.marry.take.widget.CameraButton
        android:id="@+id/iv_take_photo"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="40dp"
        android:translationZ="30dp"
        app:take_progress_wheel_color="@color/common_color_5D68E8"
        app:take_progress_wheel_width="10dp"
        app:take_round_radius="30dp" />

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="bottom"
        android:layout_marginLeft="40dp"
        android:layout_marginBottom="60dp"
        android:src="@mipmap/take_camera_close" />
</FrameLayout>