package com.kanzhun.marry.take.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Point;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

import com.kanzhun.marry.take.R;

/**
 * <AUTHOR>
 * @date 2022/7/6.
 */
public class FocusImageView extends AppCompatImageView {
    private int mFocusImg;
    private int mFocusSucceedImg;
    private int mFocusFailedImg;
    private ValueAnimator valueAnimator;
    private static final int HIDE_CODE = 1;
    private static final int START_FOCUS_DELAY = 2500;
    private static final int FOCUS_CALLBACK_DELAY = 1000;
    int focusWith;
    float scale;
    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg != null && msg.what == HIDE_CODE) {
                setVisibility(GONE);
            }
        }
    };

    public FocusImageView(Context context) {
        this(context, null);
    }

    public FocusImageView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FocusImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setVisibility(View.GONE);
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.take_focus_image_view);
        mFocusImg = a.getResourceId(R.styleable.take_focus_image_view_take_focusing_id, R.drawable.take_focus_focusing);
        mFocusSucceedImg = a.getResourceId(R.styleable.take_focus_image_view_take_success_id, R.drawable.take_focus_focused);
        mFocusFailedImg = a.getResourceId(R.styleable.take_focus_image_view_take_fail_id, R.drawable.take_focus_focus_failed);
        a.recycle();
        focusWith = getResources().getDimensionPixelOffset(R.dimen.take_focus_with);
        valueAnimator = ValueAnimator.ofFloat(1F, 0.6F);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                scale = (float) animation.getAnimatedValue();
                invalidate();
            }
        });
    }

    @Override
    protected void onDraw(Canvas canvas) {
        int scalePoint = focusWith / 2;
        canvas.scale(scale, scale, scalePoint, scalePoint);
        super.onDraw(canvas);
    }

    /**
     * 显示聚焦图案
     *
     * @param point
     */
    public void startFocus(Point point) {
        //根据触摸的坐标设置聚焦图案的位置
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) getLayoutParams();
        params.topMargin = point.y - focusWith / 2;
        params.leftMargin = point.x - focusWith / 2;
        setLayoutParams(params);
        //设置控件可见，并开始动画
        setVisibility(View.VISIBLE);
        if (valueAnimator.isRunning()) {
            valueAnimator.end();
        }
        valueAnimator.start();
        mHandler.removeMessages(HIDE_CODE);
        //3秒后隐藏View。在此处设置是由于可能聚焦事件可能不触发。
        mHandler.sendEmptyMessageDelayed(HIDE_CODE, START_FOCUS_DELAY);
    }

    /**
     * 聚焦成功回调
     */
    public void onFocusSuccess() {
        //移除在startFocus中设置的callback，1秒后隐藏该控件
        mHandler.removeMessages(HIDE_CODE);
        mHandler.sendEmptyMessageDelayed(HIDE_CODE, FOCUS_CALLBACK_DELAY);

    }

    /**
     * 聚焦失败回调
     */
    public void onFocusFailed() {
        //移除在startFocus中设置的callback，1秒后隐藏该控件
        mHandler.removeMessages(HIDE_CODE);
        mHandler.sendEmptyMessageDelayed(HIDE_CODE, FOCUS_CALLBACK_DELAY);
    }

    /**
     * 设置开始聚焦时的图片
     *
     * @param focus
     */
    public void setFocusImg(int focus) {
        this.mFocusImg = focus;
    }

    /**
     * 设置聚焦成功显示的图片
     *
     * @param focusSucceed
     */
    public void setFocusSucceedImg(int focusSucceed) {
        this.mFocusSucceedImg = focusSucceed;
    }

}

