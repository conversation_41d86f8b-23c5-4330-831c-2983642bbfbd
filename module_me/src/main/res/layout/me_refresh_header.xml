<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:paddingTop="10dp"
    tools:paddingBottom="10dp"
    tools:parentTag="android.widget.RelativeLayout">

    <com.kanzhun.common.views.image.OImageView
        android:id="@+id/iv_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        app:common_src="@drawable/common_refresh_loading"
        tools:src="@android:drawable/stat_notify_sync"
        tools:tint="#666666" />

    <LinearLayout
        android:id="@+id/ll_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_above"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/me_ic_icon_match_user_above" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:maxLines="1"
            android:text="@string/matching_refresh_header_title"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_14" />
    </LinearLayout>

</merge>