<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    app:qmui_backgroundColor="@color/common_white"
    app:qmui_radiusBottomLeft="0dp"
    app:qmui_radiusBottomRight="0dp"
    app:qmui_radiusTopLeft="20dp"
    app:qmui_radiusTopRight="20dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/common_ic_identity_fail"
        android:drawablePadding="6dp"
        android:gravity="center_vertical"
        android:text="@string/me_avatar_identify_fail_title"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_18" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ivAvatarAuthPicture"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_marginEnd="4dp"
            android:scaleType="centerCrop"
            app:common_radius="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@mipmap/me_a_b_work" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_reason"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textColor="@color/common_color_5E5E5E"
        android:textSize="@dimen/common_text_sp_14"
        tools:text="@string/common_long_placeholder" />

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/btn_next"
        style="@style/common_black_button_style"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="46dp"
        android:text="@string/me_restart_identify" />

</com.qmuiteam.qmui.widget.roundwidget.QMUIRoundLinearLayout>