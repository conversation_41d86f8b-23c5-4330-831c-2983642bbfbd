<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kanzhun.marry.me.views.MeUCropView
            android:id="@+id/fl_crop"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="h,1:1"
            android:layout_marginStart="55dp"
            android:layout_marginEnd="55dp"
            app:layout_constraintBottom_toTopOf="@+id/rl_bottom"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_crop_circle_avatar_desc"
            android:textColor="@color/common_white"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fl_crop"
            android:layout_marginTop="20dp"
            android:textSize="@dimen/common_text_sp_14"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="44dp"
            android:layout_marginTop="25dp"
            android:onClick="@{(view)->callback.clickLeft(view)}"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:src="@drawable/common_ic_white_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <RelativeLayout
            android:id="@+id/rl_bottom"
            android:layout_width="match_parent"
            android:layout_height="77dp"
            android:background="@color/common_color_0B0B0B_56"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="26dp"
                android:onClick="@{(view)->callback.clickLeft(view)}"
                android:padding="10dp"
                android:text="@string/me_above"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_14"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:padding="10dp"
                android:layout_marginTop="26dp"
                android:layout_marginEnd="24dp"
                android:onClick="@{()->callback.clickComplete()}"
                android:text="@string/common_complete"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_14"
                android:textStyle="bold" />

        </RelativeLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeAvatarUploadCropCallback" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.MeAvatarUploadCropViewModel" />
    </data>


</layout>