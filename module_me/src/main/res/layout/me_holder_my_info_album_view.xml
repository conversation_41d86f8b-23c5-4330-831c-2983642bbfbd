<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    android:id="@+id/idAlbumViewOld"
    tools:background="@color/common_color_F0F0F0">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idChildRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:background="@color/common_white"
        android:paddingTop="@dimen/me_card_top_margin"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/me_card_bottom_margin"
        app:qmui_radius="12dp">

        <ImageView
            android:id="@+id/idIcon"
            android:layout_width="28dp"
            android:layout_height="29dp"
            android:src="@drawable/me_icon_pic"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/idTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:layout_goneMarginTop="0dp"
            android:text="@string/me_my_pictures"
            android:textColor="@color/common_color_191919"
            android:textSize="24dp"
            app:layout_constraintLeft_toLeftOf="@+id/idIcon"
            app:layout_constraintTop_toBottomOf="@+id/idIcon" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/idCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:text="0/6"
            android:textColor="@color/common_color_858585"
            android:textSize="18dp"
            app:layout_constraintLeft_toRightOf="@+id/idTitle"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintTop_toTopOf="@+id/idTitle" />

        <ImageView
            android:visibility="gone"
            android:id="@+id/idIconRight"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/me_my_info_ic_right"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idTitle" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/idRVStory"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            android:orientation="vertical"
            app:spanCount="3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTitle" />
        
        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/idLottieAnimationView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="80dp"
            android:layout_height="80dp"/>

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>