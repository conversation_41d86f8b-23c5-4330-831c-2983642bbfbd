<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:qmui_radius="20dp">

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/compose_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@color/common_color_000000_10"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_reselect"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="28dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/common_bg_corner_16_color_66000000"
            android:drawableStart="@drawable/me_ic_story_reselect"
            android:drawablePadding="4dp"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="@string/me_reselect"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_12"
            android:visibility="gone"
            tools:ignore="SpUsage,UseCompatTextViewDrawableXml"
            tools:visibility="visible" />

    </com.qmuiteam.qmui.layout.QMUIFrameLayout>

</layout>