<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="item"
            type="com.kanzhun.marry.me.api.bean.MeCancellationReason" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:descendantFocusability="blocksDescendants"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:background="@drawable/common_bg_corner_12_color_ffffff"
        android:paddingLeft="12dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <CheckBox
            style="@style/check_box_style"
            android:drawablePadding="8dp"
            android:clickable="false"
            android:focusable="false"
            android:checked="@={item.isCheck}"
            android:enabled="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:text="@{item.name}"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="123213213123" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
