<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.views.UniformHWConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="2.5dp">

    <com.kanzhun.common.views.image.OImageView
        android:id="@+id/ov_story"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:common_cache_source="true"
        app:common_radius="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/idPlay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:src="@mipmap/me_ic_story_upload_video_play"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:src="@drawable/me_story_delete"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
         />

    <ImageView
        android:id="@+id/idTxtIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left|bottom"
        android:layout_marginLeft="6dp"
        android:layout_marginBottom="6dp"
        android:src="@drawable/me_ic_story_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
         />

    <ImageView
        android:id="@+id/idIdentify"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="6dp"
        android:scaleType="center"
        android:src="@drawable/me_ic_fail_small"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
         />

    <View
        android:id="@+id/idBG"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/me_bg_corner_12_color_4c000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <com.kanzhun.common.views.CircleProgressView
        android:id="@+id/pb_progress"
        android:layout_width="29dp"
        android:layout_height="29dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <com.kanzhun.common.views.CircleProgressView
        android:id="@+id/down_progress"
        android:layout_width="29dp"
        android:layout_height="29dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />


    <TextView
        android:id="@+id/tv_retry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/me_ic_retry"
        android:drawablePadding="2dp"
        android:text="@string/me_retry"
        android:textColor="@color/common_white"
        android:textSize="@dimen/common_text_sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/tv_retry_down"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableTop="@drawable/me_ic_retry_down"
        android:drawablePadding="2dp"
        android:text="@string/me_retry_down"
        android:textColor="@color/common_color_7F7F7F"
        android:textSize="@dimen/common_text_sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <ImageView
        android:id="@+id/idEndAnim"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/me_ic_upload_success"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

</com.kanzhun.common.views.UniformHWConstraintLayout>
