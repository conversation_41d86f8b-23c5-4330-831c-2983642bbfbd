<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUILinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:paddingBottom="20dp">


    <com.qmuiteam.qmui.layout.QMUILinearLayout
        app:qmui_radius="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:background="@color/common_color_F5F5F5"
        android:orientation="vertical"
        android:paddingBottom="7dp">

        <com.kanzhun.common.views.textview.BoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="20dp"
            android:text="第一步"
            android:textColor="@color/common_color_191919"
            android:textSize="18dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="20dp"
            android:text="@string/me_web_xuexin_sub_1"
            android:textColor="@color/common_color_4C4C4C"
            android:textSize="14dp" />


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="16dp"
            android:src="@mipmap/me_img_bg_xue_xin_step_web_1" />

    </com.qmuiteam.qmui.layout.QMUILinearLayout>

    <com.qmuiteam.qmui.layout.QMUILinearLayout
        app:qmui_radius="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:background="@color/common_color_F5F5F5"
        android:orientation="vertical"
        android:paddingBottom="7dp">


        <com.kanzhun.common.views.textview.BoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="20dp"
            android:text="第二步"
            android:textColor="@color/common_color_191919"
            android:textSize="18dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="20dp"
            android:text="@string/me_web_xuexin_sub_2"
            android:textColor="@color/common_color_4C4C4C"
            android:textSize="14dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="20dp"
            android:src="@mipmap/me_img_bg_xue_xin_step_web_2" />

    </com.qmuiteam.qmui.layout.QMUILinearLayout>

    <com.qmuiteam.qmui.layout.QMUILinearLayout
        app:qmui_radius="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:background="@color/common_color_F5F5F5"
        android:orientation="vertical">


        <com.kanzhun.common.views.textview.BoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="20dp"
            android:text="第三步"
            android:textColor="@color/common_color_191919"
            android:textSize="18dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="20dp"
            android:text="@string/me_web_xuexin_sub_3"
            android:textColor="@color/common_color_4C4C4C"
            android:textSize="14dp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="20dp"
            android:src="@mipmap/me_img_bg_xue_xin_step_web_3" />

    </com.qmuiteam.qmui.layout.QMUILinearLayout>


</com.qmuiteam.qmui.layout.QMUILinearLayout>

