<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    tools:background="@color/common_color_F0F0F0">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idChildRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:background="@color/common_white"
        android:paddingTop="@dimen/me_card_top_margin"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/me_card_bottom_margin"
        app:qmui_radius="12dp">

        <ImageView
            android:id="@+id/idIcon"
            android:layout_width="28dp"
            android:layout_height="29dp"
            android:src="@drawable/me_ic_voice"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/idTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:layout_goneMarginTop="0dp"
            android:text="我的声音"
            tools:text="发动机阿是咖啡店李经理卡机开发机啊拉法基受打击放大卡拉登记卡防静电垃圾啊"
            android:textColor="@color/common_color_191919"
            android:textSize="24dp"
            app:layout_constraintLeft_toLeftOf="@+id/idIcon"
            app:layout_constraintTop_toBottomOf="@+id/idIcon" />


        <TextView
            android:id="@+id/idSecondTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="用声音讲述你的故事"
            android:textColor="@color/common_color_858585"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="@+id/idTitle"
            app:layout_constraintTop_toBottomOf="@+id/idTitle" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:visibility="gone"
            android:id="@+id/idSecondTitleB"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="用声音讲述你的故事"
            tools:visibility="visible"
            tools:text="用声音讲述你的故事用声音讲述你的故事用声音讲述你的故事用声音讲述你的故事"
            android:drawableRight="@drawable/me_ic_fail_small"
            android:drawablePadding="15dp"
            app:layout_constraintRight_toLeftOf="@+id/ivError"
            android:textColor="@color/common_color_191919"
            android:textSize="16dp"
            app:layout_constraintLeft_toLeftOf="@+id/idTitle"
            app:layout_constraintTop_toBottomOf="@+id/idTitle" />

        <ImageView
            android:id="@+id/idIconRight"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/me_my_info_ic_right"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idTitle" />

        <com.kanzhun.common.views.AudioRecorderPlayView
            android:id="@+id/idVoice"
            android:layout_width="0dp"
            android:layout_height="52dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginTop="14dp"
            app:layout_constraintLeft_toLeftOf="@+id/idSecondTitle"
            app:layout_constraintRight_toLeftOf="@+id/idTvDuration"
            app:layout_constraintTop_toBottomOf="@+id/idSecondTitleB"
            android:layout_marginRight="10dp"
             />

        <TextView
            android:id="@+id/idTvDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/idVoice"
            app:layout_constraintTop_toTopOf="@id/idVoice"
            tools:text="25s" />


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>