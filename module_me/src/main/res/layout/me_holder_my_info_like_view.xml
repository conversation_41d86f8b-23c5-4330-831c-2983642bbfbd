<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    tools:background="@color/common_color_F0F0F0">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idChildRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:background="@color/common_white"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/me_card_top_margin"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/me_card_bottom_margin"
        app:qmui_radius="12dp">

        <ImageView
            android:id="@+id/idIcon"
            android:layout_width="28dp"
            android:layout_height="29dp"
            android:src="@drawable/common_ic_icon_title_like"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/idTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="我的兴趣爱好"
            android:textColor="@color/common_color_191919"
            android:textSize="24dp"
            app:layout_constraintLeft_toLeftOf="@+id/idIcon"
            app:layout_constraintTop_toBottomOf="@+id/idIcon"
            app:layout_goneMarginTop="0dp" />


        <TextView
            android:id="@+id/idTitle2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="17dp"
            android:text="描述与众不同的你"
            android:textColor="@color/common_color_191919"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="@+id/idIcon"
            app:layout_constraintTop_toBottomOf="@+id/idTitle"
            app:layout_goneMarginTop="0dp" />

        <com.kanzhun.common.views.span.ZPUISpanTextView
            android:id="@+id/idContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginRight="16dp"
            android:hint="说说你的爱好，让对方更了解你"
            android:lineHeight="24dp"
            android:textColor="@color/common_color_5E5E5E"
            android:textColorHint="@color/common_color_858585"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="@+id/idTitle"
            app:layout_constraintRight_toLeftOf="@+id/idIconRight"
            app:layout_constraintTop_toBottomOf="@+id/idTitle2"
            app:zpui_stv_expand_color="@color/common_color_003580"
            app:zpui_stv_expand_text="@string/common_all"
            app:zpui_stv_max_line="4"
            app:zpui_stv_support_custom_expand="false"
            app:zpui_stv_support_expand="true"
            app:zpui_stv_support_real_expand_or_collapse="false" />

        <ImageView
            android:id="@+id/idIconRight0"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/me_my_info_ic_right"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle2"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idTitle2" />

        <ImageView
            android:id="@+id/idIconRight"
            android:layout_width="20dp"
            android:layout_height="21dp"
            android:src="@drawable/me_my_info_ic_right"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idContent" />

        <ImageView
            android:id="@+id/idIconRightWarn"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/me_ic_fail_small"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/idIconRight"
            app:layout_constraintRight_toLeftOf="@+id/idIconRight"
            app:layout_constraintTop_toTopOf="@+id/idIconRight"
            tools:visibility="visible" />

        <com.kanzhun.marry.me.info.views.MeImageChooserView
            android:id="@+id/rvHobbyImg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idContent"
            app:spanCount="3" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/idLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="@+id/idIcon"
            app:layout_constraintTop_toBottomOf="@+id/rvHobbyImg">

            <TextView
                android:id="@+id/idTitle3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="17dp"
                android:text="打上你的兴趣标签"
                android:textColor="@color/common_color_191919"
                android:textSize="16dp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginTop="0dp" />

            <com.kanzhun.common.views.span.ZPUISpanTextView
                android:id="@+id/idContent3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginRight="16dp"
                android:hint="通过标签，更快找到同频的人"
                android:lineHeight="24dp"
                android:textColor="@color/common_color_5E5E5E"
                android:textColorHint="@color/common_color_858585"
                android:textSize="14dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/idTitle3"
                app:zpui_stv_expand_color="@color/common_color_003580"
                app:zpui_stv_expand_text="@string/common_all"
                app:zpui_stv_max_line="4"
                app:zpui_stv_support_custom_expand="false"
                app:zpui_stv_support_expand="true"
                app:zpui_stv_support_real_expand_or_collapse="false" />

            <ImageView
                android:id="@+id/idIconRight2"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/me_my_info_ic_right"
                app:layout_constraintBottom_toBottomOf="@+id/idTitle3"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/idTitle3" />

            <com.kanzhun.foundation.views.flowlayout.AutoFlowLayout
                android:id="@+id/idRVStory"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginRight="16dp"
                app:afl_maxLines="3"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/idIconRight2"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/idIconRight3"
                android:layout_width="20dp"
                android:layout_height="21dp"
                android:layout_marginTop="14dp"
                android:src="@drawable/me_my_info_ic_right"
                android:visibility="gone"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/idRVStory" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>