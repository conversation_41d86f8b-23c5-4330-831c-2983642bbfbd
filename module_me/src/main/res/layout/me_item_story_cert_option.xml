<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.text.TextUtils" />

        <import type="com.kanzhun.foundation.model.profile.ProfileMetaModel" />

        <variable
            name="canShowStoryDelete"
            type="androidx.databinding.ObservableBoolean" />

        <variable
            name="bean"
            type="com.kanzhun.foundation.bean.BaseStoryShowItem" />
    </data>

    <merge
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/iv_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:src="@drawable/me_story_delete"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleGone="@{canShowStoryDelete}" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left|bottom"
            android:layout_marginLeft="6dp"
            android:layout_marginBottom="6dp"
            android:src="@drawable/me_ic_story_text"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:visibleGone="@{!TextUtils.isEmpty(bean.storyText)}" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="6dp"
            android:scaleType="center"
            android:src="@drawable/me_ic_fail_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:visibleGone="@{bean.certStatus==ProfileMetaModel.STATUS_REJECTED}" />

    </merge>
</layout>