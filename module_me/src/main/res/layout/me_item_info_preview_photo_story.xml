<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:qmui_radius="12dp"
        android:background="@color/common_white">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/iv_photo"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            app:layout_constraintDimensionRatio="h,3:4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:common_error="@color/common_color_CCCCCC" />

        <FrameLayout
            android:id="@+id/fl_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="@+id/iv_photo"
            app:layout_constraintBottom_toBottomOf="@+id/iv_photo"
            app:layout_constraintStart_toStartOf="@+id/iv_photo"
            app:layout_constraintEnd_toEndOf="@+id/iv_photo"
            android:visibility="gone">
            <include
                layout="@layout/common_layout_image_load_failed" />
        </FrameLayout>

        <com.qmuiteam.qmui.layout.QMUIFrameLayout
            android:id="@+id/iv_like"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:background="@drawable/common_bg_selector_white_gray"
            app:layout_constraintEnd_toEndOf="@+id/iv_photo"
            app:layout_constraintBottom_toBottomOf="@+id/iv_photo"
            android:layout_marginBottom="12dp"
            android:layout_marginEnd="12dp"
            app:qmui_shadowAlpha="0.22"
            app:qmui_shadowElevation="22dp"
            app:qmui_radius="23dp">

            <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/common_ic_icon_match_like"
                android:layout_gravity="center" />
        </com.qmuiteam.qmui.layout.QMUIFrameLayout>

        <com.kanzhun.common.views.OTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_photo"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            android:layout_marginStart="22dp"
            android:layout_marginEnd="22dp"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_24"
            android:gravity="center"
            android:text="@{item.text}"
            app:visibleGone="@{item.text}"
            tools:text="朋友乔迁送的花花朋友乔迁送的花花朋友乔迁送的花花"/>


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

    <data>
        <variable
            name="item"
            type="com.kanzhun.foundation.api.bean.PhotoStoryBean" />
    </data>
</layout>