<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/idCardRoot"
    tools:background="@color/common_color_F5F5F5"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idTopLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:layout_marginHorizontal="12dp"
        android:background="@color/common_white"
        android:paddingHorizontal="12dp"
        android:paddingTop="12dp"
        app:qmui_backgroundColor="@color/common_white"
        app:qmui_radius="12dp">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/idImageView"
            android:layout_width="78dp"
            android:layout_height="78dp"
            app:common_radius="12dp"
            tools:background="@color/image_color_red" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/idActivityRelationStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="5dp"
            android:paddingHorizontal="4dp"
            android:paddingVertical="2dp"
            android:text="已签到"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="11dp"
            android:visibility="invisible"
            app:layout_constraintLeft_toRightOf="@+id/idImageView"
            app:layout_constraintTop_toTopOf="@+id/idImageView"
            app:qmui_borderColor="@color/common_color_B2B2B2"
            app:qmui_borderWidth="1dp"
            app:qmui_radius="4dp" />

        <TextView
            android:id="@+id/idActivityRelationStatus2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="5dp"
            android:paddingHorizontal="4dp"
            android:paddingVertical="2dp"
            android:text="已签到"
            android:background="@drawable/shape_bg_me_activity_state_bg"
            android:textColor="@color/common_color_292929"
            android:textSize="11dp"
            app:layout_constraintLeft_toRightOf="@+id/idImageView"
            app:layout_constraintTop_toTopOf="@+id/idImageView"
            />

        <TextView
            android:id="@+id/idTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="4dp"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/common_color_292929"
            android:textSize="16dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@+id/idActivityRelationStatus"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idImageView"
            tools:text="看准首届心动大会大会会看准首届心动大会大会会看准首届心动大会大会会" />


        <TextView
            android:id="@+id/idTextTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/common_color_B2B2B2"
            android:textSize="13dp"
            app:layout_constraintLeft_toLeftOf="@+id/idActivityRelationStatus"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTitle"
            tools:text="看准首届心动大会大会会看准首届心动大会大会会看准首届心动大会大会会" />

        <TextView
            android:id="@+id/idTextLocation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/common_color_B2B2B2"
            android:textSize="13dp"
            app:layout_constraintLeft_toLeftOf="@+id/idActivityRelationStatus"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTextTime"
            tools:text="看准首届心动大会大会会看准首届心动大会大会会看准首届心动大会大会会" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_marginTop="12dp"
            android:id="@+id/idBottomLayout"
            app:layout_constraintLeft_toLeftOf="@+id/idImageView"
            app:layout_constraintTop_toBottomOf="@+id/idTextLocation"
            android:layout_height="56dp">

            <View
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:id="@+id/idLine"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/common_color_F5F5F5"
                />

            <com.kanzhun.foundation.views.AvatarsListView
                android:id="@+id/idAvatars"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/idBtnRight"
                android:layout_marginRight="13dp"
                android:layout_width="0dp"
                android:layout_height="0dp"

                />

            <TextView
                android:id="@+id/idBtnLeft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/me_icon_activity_btn_left"
                android:drawablePadding="4dp"
                android:maxLines="1"
                android:text="火热报名中"
                android:textColor="@color/common_color_292929"
                android:textSize="14dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/idLine" />

            <TextView
                android:id="@+id/idBtnRight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableRight="@drawable/common_ic_gray_right_arrow"
                android:maxLines="1"
                android:text="查看报名活动嘉宾"
                android:textColor="@color/common_color_B8B8B8"
                android:textSize="13dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/idLine" />

        </androidx.constraintlayout.widget.ConstraintLayout>



        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/idBottomBtn"
            android:visibility="gone"
            android:layout_width="match_parent"
            app:layout_constraintTop_toBottomOf="@+id/idBottomLayout"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_height="wrap_content">

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/idBtn"
                android:text="进入活动现场"
                android:textColor="@color/color_white"
                android:gravity="center"
                android:textSize="16dp"
                android:background="@color/common_color_191919"
                app:qmui_backgroundColor="@color/common_color_191919"
                app:qmui_radius="36dp"
                android:layout_width="match_parent"
                android:layout_height="46dp"/>

            <View
                app:layout_constraintTop_toBottomOf="@+id/idBtn"
                android:layout_width="wrap_content"
                android:layout_height="24dp"/>

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <ImageView
            android:visibility="gone"
            android:id="@+id/idBtnPic"
            android:src="@mipmap/image_f4_me_fragment_activity_item_btn_pic"
            android:layout_width="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="-4dp"
            app:layout_constraintBottom_toTopOf="@+id/idBottomBtn"
            android:layout_marginBottom="-16dp"
            android:layout_height="wrap_content"/>


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

    <androidx.compose.ui.platform.ComposeView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:composableName="com.kanzhun.marry.me.info.activity.MeTextAnswerKt.PreviewAnswerManageButton" />

</LinearLayout>