<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_white">


    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/iv_error"
        android:layout_width="174dp"
        android:layout_height="174dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toTopOf="@id/tv_desc"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_fileName="net_error/lost.json"
        app:lottie_imageAssetsFolder="net_error/images"
        app:lottie_loop="false" />


    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/common_loading_error"
        android:textColor="@color/common_color_7F7F7F"
        android:textSize="@dimen/common_text_sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.coorchice.library.SuperTextView
        android:id="@+id/btn_retry"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="24dp"
        android:text="@string/common_click_retry"
        android:textColor="@color/common_white"
        android:textSize="@dimen/common_text_sp_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_desc"
        app:stv_corner="100dp"
        app:stv_solid="@color/common_color_292929" />

</androidx.constraintlayout.widget.ConstraintLayout>