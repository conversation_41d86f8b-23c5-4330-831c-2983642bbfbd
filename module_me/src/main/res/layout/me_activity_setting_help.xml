<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".setting.SettingHelpActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.setting.viewmodel.SettingHelpViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.setting.callback.SettingHelpCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_color_F5F5F5"
        android:orientation="vertical">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:layout_constraintTop_toTopOf="parent"
            app:title="@{@string/me_setting_help}" />


        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="12dp"
            android:text="@string/me_feed_cotent"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/me_must_input"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="@+id/tv_content"
            app:layout_constraintLeft_toRightOf="@+id/tv_content" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_edit"
            android:layout_width="match_parent"
            android:layout_height="168dp"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="12dp"
            android:layout_marginRight="20dp"
            android:background="@drawable/common_bg_conor_12_color_white"
            android:orientation="vertical"
            android:paddingTop="12dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_content">

            <EditText
                android:id="@+id/et_text"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="43dp"
                android:layout_marginBottom="2dp"
                android:layout_weight="1"
                android:background="@null"
                android:gravity="left|top"
                android:hint="@string/me_input_feed"
                android:text="@={viewModel.editContent}"
                android:textColor="@color/common_color_191919"
                android:textColorHint="@color/common_color_B2B2B2"
                android:textSize="@dimen/common_text_sp_14"
                app:layout_constraintBottom_toTopOf="@+id/tv_total_count"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:gravity="right"
                android:text="@{String.valueOf(viewModel.editContent.length())}"
                android:textColor="@{viewModel.editContent.length()>0 ? @color/common_color_191919 : @color/common_color_B2B2B2}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/tv_total_count"
                tools:text="0" />

            <TextView
                android:id="@+id/tv_total_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:layout_marginRight="12dp"
                android:layout_marginBottom="12dp"
                android:gravity="right"
                android:text="/500"
                android:textColor="@color/common_color_B2B2B2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_assist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="32dp"
            android:text="@string/me_upload_pic"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_edit" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_pic"
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_marginTop="12dp"
            android:clipToPadding="false"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_assist" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/common_blue_button_style"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="16dp"
            android:enabled="@{viewModel.editContent.length()>=2}"
            app:greyDisabledStyle="@{viewModel.editContent.length()>=2}"
            android:gravity="center"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text="@string/common_submit"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>