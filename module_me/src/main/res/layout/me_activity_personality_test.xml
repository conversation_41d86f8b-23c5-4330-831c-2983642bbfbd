<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.personality.viewmodel.PersonalityTestViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.personality.callback.PersonalityTestCallback" />

    </data>

    <com.kanzhun.marry.me.personality.view.GuideAnimationContainer
        android:id="@+id/gac"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white"
        android:clipChildren="true"
        app:me_hide_child_id="@id/vp2_container">

        <LinearLayout
            android:id="@+id/cl_personality_guide"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/common_white"
            android:clickable="true"
            android:orientation="vertical">

            <com.kanzhun.marry.me.views.OVideoView
                android:id="@+id/vv_tow"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:alpha="0" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="355dp">

                <com.kanzhun.common.views.OTextView
                    android:id="@+id/tv_explore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="29dp"
                    android:alpha="0"
                    android:text="@string/me_explore"
                    android:textColor="@color/common_color_191919"
                    android:textSize="49sp"
                    app:layout_constraintBottom_toTopOf="@+id/tv_meet_other_half"
                    app:layout_constraintLeft_toLeftOf="parent"
                    tools:alpha="1" />

                <com.kanzhun.common.views.OTextView
                    android:id="@+id/tv_love_personality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginBottom="5dp"
                    android:alpha="0"
                    android:text="@string/me_love_personality"
                    android:textColor="@color/common_color_191919"
                    android:textSize="27sp"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_explore"
                    app:layout_constraintLeft_toRightOf="@+id/tv_explore"
                    tools:alpha="1" />

                <com.kanzhun.common.views.OTextView
                    android:id="@+id/tv_meet_other_half"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="220dp"
                    android:alpha="0"
                    android:text="@string/meet_other_half"
                    android:textColor="@color/common_color_191919"
                    android:textSize="33sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_explore"
                    tools:alpha="1" />

                <TextView
                    android:id="@+id/tv_personality_guide_notice_one"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="29dp"
                    android:layout_marginRight="29dp"
                    android:alpha="0"
                    android:lineSpacingMultiplier="1.1"
                    android:layout_marginTop="12dp"
                    android:textSize="@dimen/common_text_sp_14"
                    android:textColor="@color/common_color_7F7F7F"
                    android:text="@string/me_personality_end_guide_notice_one"
                    app:layout_constraintTop_toBottomOf="@id/tv_meet_other_half"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    tools:alpha="1" />


                <com.kanzhun.marry.me.personality.view.PressAnimationButton
                    android:id="@+id/btn_start"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="28dp"
                    android:layout_marginRight="28dp"
                    android:layout_marginBottom="8dp"
                    android:alpha="0"
                    android:background="@drawable/common_bg_corner_25_color_000000"
                    android:gravity="center"
                    android:onClick="@{(view)->callback.clickSubmit(view)}"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:text="@string/me_start_explore"
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_16"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@+id/tv_test_notice"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    tools:alpha="1" />

                <TextView
                    android:id="@+id/tv_test_notice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="23dp"
                    android:alpha="0"
                    android:textSize="@dimen/common_text_sp_14"
                    android:textColor="@color/common_color_7F7F7F"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    tools:alpha="1"
                    tools:text="@string/me_test_notice" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <com.kanzhun.marry.me.personality.view.ViewPagerScrollLimitFrameLayout
            android:id="@+id/vp2_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false">

            <com.kanzhun.common.views.pager.VerticalViewPager
                android:id="@+id/view_pager_2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:orientation="vertical"
                app:visibleGone="@{viewModel.questionNum > 0}" />

            <com.kanzhun.common.views.RoundAlphaButton
                android:id="@+id/btn_finish"
                style="@style/common_blue_button_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginLeft="28dp"
                android:layout_marginRight="28dp"
                android:layout_marginBottom="97dp"
                android:onClick="@{(view)->callback.clickSubmit(view)}"
                android:text="@string/me_finish_explore"
                app:showVisibleAlphaAnimation="@{viewModel.finishTest &amp;&amp; viewModel.questionNum > 0}"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_index"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center"
                android:layout_marginBottom="60dp"
                android:textColor="@color/common_color_4C4C4C"
                android:textSize="@dimen/common_text_sp_12"
                app:visibleGone="@{viewModel.questionNum > 0}"
                tools:text="1/14"
                tools:visibility="visible" />

            <com.kanzhun.foundation.views.OLoadingEmptyView
                android:id="@+id/empty_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@color/common_translate"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:visibleGone="@{viewModel.questionNum &lt;= 0}" />
        </com.kanzhun.marry.me.personality.view.ViewPagerScrollLimitFrameLayout>

        <com.kanzhun.marry.me.views.OVideoView
            android:id="@+id/vv_one"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:visibility="gone" />


        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_vv_one_assist"
            android:layout_width="262dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="28dp"
            android:layout_marginTop="124dp"
            android:alpha="0"
            android:text="@string/me_vv_one_assist_one"
            android:textColor="@color/common_color_191919"
            android:textSize="26dp"
            tools:alpha="1" />

        <LinearLayout
            android:id="@+id/ll_vv_one_assist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="28dp"
            android:layout_marginTop="222dp"
            android:alpha="0"
            android:orientation="vertical"
            tools:alpha="1">

            <com.kanzhun.common.views.OTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/me_vv_one_assist_tow"
                android:textColor="@color/common_color_191919"
                android:textSize="26dp" />

            <com.kanzhun.common.views.OTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/me_vv_one_assist_three"
                android:textColor="@color/common_color_191919"
                android:textSize="26dp" />
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:id="@+id/iv_back"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:onClick="@{(view)->callback.clickLeft(view)}"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:src="@drawable/common_ic_black_back" />

            <TextView
                android:padding="8dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="20dp"
                android:id="@+id/tv_skip"
                android:onClick="@{(view)->callback.onClickSkip()}"
                android:textSize="18sp"
                android:textColor="@color/common_color_191919"
                android:layout_gravity="end"
                android:text="@string/me_skip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </com.kanzhun.marry.me.personality.view.GuideAnimationContainer>

</layout>