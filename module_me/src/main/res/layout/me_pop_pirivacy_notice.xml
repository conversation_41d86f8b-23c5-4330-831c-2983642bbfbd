<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="90dp">

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="265dp"
            android:layout_height="68dp"
            android:layout_marginTop="-1dp"
            android:background="@color/common_color_000000_60"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_arrow_up"
            app:qmui_radius="12dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingLeft="93dp"
                android:paddingRight="14dp"
                android:text="@string/me_privacy_show_status"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <ImageView
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:layout_marginLeft="3dp"
            android:src="@mipmap/me_privacy_notice" />

        <ImageView
            android:id="@+id/iv_arrow"
            android:layout_width="22dp"
            android:layout_height="11dp"
            android:layout_gravity="left"
            android:src="@mipmap/me_pop_arrow_up" />

    </FrameLayout>
</layout>