<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/cl_birthday"
    style="@style/Common_BaseInfoItem"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintTop_toBottomOf="@+id/tv_title">

    <TextView
        android:id="@+id/tv_birthday_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableLeft="@drawable/me_ic_birthday"
        android:drawablePadding="6dp"
        android:gravity="center_vertical"
        android:text="@string/me_birthday"
        android:textColor="@color/common_color_191919"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tv_birthday"
        app:layout_constraintBottom_toBottomOf="@+id/tv_birthday_title"
        app:layout_constraintLeft_toRightOf="@+id/tv_birthday_title"
        app:layout_constraintRight_toLeftOf="@+id/iv_arrow_birthday"
        app:layout_constraintTop_toTopOf="@+id/tv_birthday_title"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="5dp"
        android:layout_marginRight="4dp"
        android:gravity="right|center_vertical"
        android:hint="@string/me_go_select"
        android:singleLine="true"
        android:textColor="@color/me_selector_text_color_707070_enable_color_b7b7b7"
        android:textColorHint="@color/common_color_707070"
        android:textSize="@dimen/common_text_sp_14" />

    <ImageView
        android:id="@+id/iv_arrow_birthday"
        app:layout_constraintBottom_toBottomOf="@+id/tv_birthday_title"
        app:layout_constraintLeft_toRightOf="@+id/tv_birthday"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_birthday_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/common_ic_gray_right_arrow" />
</androidx.constraintlayout.widget.ConstraintLayout>