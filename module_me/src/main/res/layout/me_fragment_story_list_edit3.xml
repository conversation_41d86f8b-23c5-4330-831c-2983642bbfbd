<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.text.TextUtils" />

        <import type="com.kanzhun.foundation.api.model.ProfileInfoModel" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.StoryListEditViewModel3" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.StoryListEditCallback" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <FrameLayout
            android:id="@+id/story_list_edit_title_bar"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/idLeftIcon"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:paddingStart="18dp"
                android:paddingEnd="18dp"
                android:src="@drawable/common_ic_black_back"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:layout_gravity="end"
                android:enabled="@{viewModel.textRejectType != ProfileInfoModel.CONTENT_TYPE_REJECTED &amp;&amp; viewModel.photoRejectType != ProfileInfoModel.CONTENT_TYPE_REJECTED}"
                android:gravity="center_vertical"
                android:onClick="@{view->callback.clickRight(view)}"
                android:paddingStart="18dp"
                android:paddingEnd="18dp"
                android:text="@string/common_save_p"
                android:textColor="@color/common_selector_text_color_191919_enable_color_cccccc"
                android:textSize="@dimen/common_text_sp_18"
                tools:ignore="SpUsage" />

        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_media_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/story_list_edit_title_bar">

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/compose_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:background="@color/common_color_000000_10"
                tools:layout_height="256dp"
                tools:visibility="visible" />

            <include
                android:id="@+id/layout_report_tips"
                layout="@layout/foundation_view_report_tips"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:background="@drawable/common_bg_conor_10_color_black_70"
                android:paddingLeft="11dp"
                android:paddingRight="11dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tips="@{viewModel.contentRejectedText}"
                app:visibleGone="@{viewModel.textRejectType == ProfileInfoModel.CONTENT_TYPE_REJECTED || viewModel.photoRejectType == ProfileInfoModel.CONTENT_TYPE_REJECTED}"
                tools:visibility="visible" />

            <View
                android:id="@+id/v_bg_selector"
                android:layout_width="200dp"
                android:layout_height="150dp"
                android:background="@drawable/me_answer_img_selector_bg"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/story_content"
                app:layout_constraintEnd_toEndOf="@+id/story_content"
                app:layout_constraintStart_toStartOf="@+id/story_content"
                app:layout_constraintTop_toTopOf="@+id/story_content"
                tools:visibility="visible" />

            <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                android:id="@+id/ql_player_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@+id/story_content"
                app:layout_constraintLeft_toLeftOf="@+id/story_content"
                app:layout_constraintRight_toRightOf="@+id/story_content"
                app:layout_constraintTop_toTopOf="@+id/story_content"
                app:qmui_radius="16dp">

                <org.alita.webrtc.SurfaceViewRenderer
                    android:id="@+id/sv_player"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="0dp" />

            </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/story_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginTop="8dp"
                android:background="@color/common_translate"
                android:orientation="horizontal"
                android:transitionName="edit"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_report_tips"
                tools:layout_height="142dp"
                tools:layout_width="192dp" />

            <ImageView
                android:id="@+id/iv_editor"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_margin="8dp"
                android:src="@mipmap/me_ic_edit"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/story_content"
                app:layout_constraintTop_toTopOf="@+id/story_content"
                tools:ignore="ContentDescription"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_see_others"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_media_layout">

            <EditText
                android:id="@+id/et_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="18dp"
                android:background="@null"
                android:hint="说说这张照片有什么故事？"
                android:minHeight="50dp"
                android:paddingVertical="18dp"
                android:text="@={viewModel.currentEditContent}"
                android:textColor="@color/common_color_191919"
                android:textColorHint="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_18"
                tools:ignore="Autofill,HardcodedText,SpUsage,TextFields"
                tools:text="她一直生活在这个小镇，却总觉得自己的灵魂在寻觅着什么。那座钟楼，对她来说，是一个神秘的存在，仿佛隐藏着解开她内心困惑的钥匙。
关于这座钟楼的传说，传说在特定的时刻，站在钟楼的顶端，可以听到来自远方的声音，那是梦想的呼唤。艾莉丝被这个传说深深吸引，她决定亲自登上钟楼，去寻找那可能改变她命运的声音。
楼梯的石板有些陈旧，每一级都承载着岁月的重量。艾莉丝的脚步坚定而缓慢，她的长发在阳光下闪烁着如丝般的光泽。她的心中既充满了期待，又有一丝紧张。她不知道在钟楼的顶端会遇到什么，但她知道，这是她必须要走的路。" />

        </ScrollView>

        <TextView
            android:id="@+id/tv_see_others"
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_marginStart="18dp"
            android:layout_marginBottom="10dp"
            android:drawableStart="@drawable/me_look"
            android:drawablePadding="4dp"
            android:gravity="center_vertical"
            android:onClick="@{()->callback.clickShowGuideDialog()}"
            android:text="@string/me_look_how_write"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            tools:ignore="SpUsage,UseCompatTextViewDrawableXml" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>