<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idScrollToThisView"
    style="@style/style_edit_me_info_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="0dp"
    android:paddingEnd="0dp"
    tools:visibility="visible">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idChildRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/idIcon"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginStart="20dp"
            android:src="@drawable/common_ic_icon_title_like"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kanzhun.marry.me.views.GradientBorderConstraintLayout
            android:id="@+id/idGradientBorderConstraintLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="20dp"
            android:paddingTop="8dp"
            android:layout_marginTop="8dp"
            android:paddingBottom="8dp"
            app:layout_constraintTop_toBottomOf="@+id/idIcon">

            <TextView
                android:id="@+id/idTitle"
                style="@style/text_c7f_s15"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我的兴趣爱好"
                android:textStyle="bold" />

            <com.kanzhun.common.views.span.ZPUISpanTextView
                android:id="@+id/idContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginRight="16dp"
                android:hint="描述与众不同的你"
                android:lineHeight="24dp"
                android:textColor="@color/common_color_292929"
                android:textColorHint="@color/common_color_D4D4D4"
                android:textSize="18dp"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@+id/idTitle"
                app:zpui_stv_expand_color="@color/common_color_003580"
                app:zpui_stv_expand_text="@string/common_all"
                app:zpui_stv_max_line="4"
                app:zpui_stv_support_custom_expand="false"
                app:zpui_stv_support_expand="true"
                app:zpui_stv_support_real_expand_or_collapse="false" />

            <ImageView
                android:id="@+id/idIconRight0"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/me_my_info_ic_right_new"
                app:layout_constraintBottom_toBottomOf="@+id/idTitle"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/idTitle" />

            <TextView
                android:id="@+id/idHitText"
                style="@style/text_c29_s14"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableEnd="@drawable/icon_persion_edit_red_point"
                android:drawablePadding="4dp"
                android:text="填写"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/idIconRight0"
                app:layout_constraintRight_toLeftOf="@+id/idIconRight0"
                app:layout_constraintTop_toTopOf="@+id/idIconRight0" />

            <ImageView
                android:id="@+id/idIconRightWarn"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/me_ic_fail_small"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/idIconRight0"
                app:layout_constraintRight_toLeftOf="@+id/idIconRight0"
                app:layout_constraintTop_toTopOf="@+id/idIconRight0"
                tools:visibility="gone" />

        </com.kanzhun.marry.me.views.GradientBorderConstraintLayout>


        <com.kanzhun.marry.me.info.views.MeImageChooserView
            android:id="@+id/rvHobbyImg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="13dp"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idGradientBorderConstraintLayout"
            app:spanCount="3" />


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/idLabel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="13dp"
        android:paddingTop="8dp"
        android:paddingHorizontal="20dp"
        app:layout_constraintLeft_toLeftOf="@+id/idIcon"
        app:layout_constraintTop_toBottomOf="@+id/idChildRoot">

        <TextView
            android:id="@+id/idTitle3"
            style="@style/text_c7f_s15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="我的兴趣标签"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginTop="0dp" />

        <com.kanzhun.common.views.span.ZPUISpanTextView
            android:id="@+id/idContent3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginRight="16dp"
            android:hint="通过标签，更快找到同频的人"
            android:lineHeight="24dp"
            android:textColor="@color/common_color_D4D4D4"
            android:textColorHint="@color/common_color_D4D4D4"
            android:textSize="18dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTitle3"
            app:zpui_stv_expand_color="@color/common_color_003580"
            app:zpui_stv_expand_text="@string/common_all"
            app:zpui_stv_max_line="4"
            app:zpui_stv_support_custom_expand="false"
            app:zpui_stv_support_expand="true"
            app:zpui_stv_support_real_expand_or_collapse="false" />

        <ImageView
            android:id="@+id/idIconRight2"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/me_my_info_ic_right_new"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle3"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idTitle3" />

        <TextView
            android:id="@+id/idHitText2"
            style="@style/text_c29_s14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/icon_persion_edit_red_point"
            android:drawablePadding="4dp"
            android:text="填写"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/idIconRight2"
            app:layout_constraintRight_toLeftOf="@+id/idIconRight2"
            app:layout_constraintTop_toTopOf="@+id/idIconRight2" />

        <com.kanzhun.foundation.views.flowlayout.AutoFlowLayout
            android:id="@+id/idRVStory"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginRight="16dp"
            app:afl_maxLines="3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/idIconRight2"
            app:layout_constraintTop_toBottomOf="@+id/idTitle3" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>