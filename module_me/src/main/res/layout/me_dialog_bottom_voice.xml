<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    >

    <View
        android:background="@color/common_translate"
        android:layout_width="1px"
        android:layout_height="53dp"/>


    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/common_white"
        android:paddingHorizontal="20dp"
        android:paddingTop="20dp"
        app:qmui_hideRadiusSide="bottom"
        app:qmui_radius="32dp">

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/idTitle"
            android:text="选择一个话题"
            android:textSize="24dp"
            android:textColor="@color/common_color_191919"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:textColor="@color/common_color_B8B8B8"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@+id/idTitle"
            android:id="@+id/idSubTitle"
            android:text="用声音讲述你的故事"
            android:paddingBottom="16dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        
        <ImageView
            android:layout_marginTop="16dp"
            android:layout_marginRight="16dp"
            android:src="@drawable/me_ic_icon_voice_answer_desc"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="32dp"
            android:layout_height="36dp"/>

        <androidx.recyclerview.widget.RecyclerView
            app:layout_constraintTop_toBottomOf="@+id/idSubTitle"
            android:id="@+id/recycler"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            tools:itemCount="2"
            tools:listitem="@layout/me_dialog_bottom_voice" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
</LinearLayout>