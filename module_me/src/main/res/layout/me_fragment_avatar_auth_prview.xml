<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/common_black_70"
    android:layout_height="match_parent"
    >




    <com.github.chrisbanes.photoview.PhotoView
        android:id="@+id/imageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.coorchice.library.SuperTextView
        android:id="@+id/btnChange"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:paddingHorizontal="20dp"
        android:paddingVertical="8dp"
        android:text="更换头像"
        android:textColor="@color/common_white"
        android:textSize="@dimen/common_text_sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:stv_corner="25dp"
        app:stv_stroke_width="1dp"
        app:stv_stroke_color="@color/common_white"/>

    <ImageView
        android:id="@+id/iv_common_back"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:paddingStart="20dp"
        android:paddingEnd="5dp"
        android:src="@drawable/common_ic_white_close"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>