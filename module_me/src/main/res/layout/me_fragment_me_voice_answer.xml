<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".info.MeVoiceAnswerFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/fragmentMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/ic_title"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title="@{@string/me_question_answer_voice_title}"/>

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ic_title"
            android:layout_marginTop="16dp"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:background="@color/common_white"
            app:qmui_radius="12dp">

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="37dp"
                android:drawableLeft="@drawable/me_ic_icon_voice_answer_desc"
                android:drawablePadding="8dp"
                android:textColor="@color/common_color_B2B2B2"
                android:textSize="@dimen/common_text_sp_14"
                android:gravity="center_vertical"
                android:text="@string/me_question_answer_voice_desc"/>

            <com.kanzhun.common.views.OTextView
                android:id="@+id/tv_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_desc"
                app:layout_constraintBottom_toTopOf="@+id/ll_re_sle"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp"
                android:layout_marginTop="38dp"
                android:textSize="@dimen/common_text_sp_18"
                android:textColor="@color/common_color_191919"
                android:gravity="center"
                app:layout_goneMarginBottom="40dp"
                android:text="@{activityViewModel.currQuestionObservable.name}"
                tools:text="今年发生的最让你兴奋的事情是 什么？或者说最激动的事情？"/>


                <LinearLayout
                    android:id="@+id/ll_re_sle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_name"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginTop="39dp"
                    android:layout_marginBottom="40dp"
                    android:orientation="horizontal"
                    app:visibleGone="@{TextUtils.isEmpty(activityViewModel.answerId)}"
                    android:onClick="@{v->callback.clickReSelect()}">

                    <ImageView
                        android:id="@+id/iv_re_sel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/me_ic_icon_voice_answer_re_sel"/>

                    <TextView
                        android:id="@+id/tv_re_sel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/common_color_7F7F7F"
                        android:textSize="@dimen/common_text_sp_16"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="5dp"
                        android:text="@string/me_question_answer_voice_re_sel"/>
                </LinearLayout>
        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <com.kanzhun.marry.me.views.MeVoiceView
            android:id="@+id/voice_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>
        <import type="android.text.TextUtils"/>
        <import type="android.view.View"/>

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.me.info.viewmodel.MeQuestionAndAnswerViewModel" />
        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.MeVoiceAnswerViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeVoiceAnswerCallback" />
    </data>

</layout>