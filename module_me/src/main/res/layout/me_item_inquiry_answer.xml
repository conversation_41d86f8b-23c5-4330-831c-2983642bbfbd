<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.kanzhun.marry.me.info.bean.MyInquiryAnswer" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallback" />

    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp">

        <View
            android:id="@+id/v_answer_top"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@drawable/common_bg_corner_12_top_2_color_white"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_answer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_white"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/v_answer_top">

            <ImageView
                android:id="@+id/iv_text_answer"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:layout_marginLeft="12dp"
                android:src="@drawable/me_ic_text_answer"
                app:layout_constraintBottom_toBottomOf="@+id/tv_answer_title"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_answer_title"
                app:visibleGone="@{bean.answerNum &lt;= 0}" />

            <com.kanzhun.common.views.OTextView
                android:id="@+id/tv_answer_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:gravity="center"
                android:text="@string/me_text_answer"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_18"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@+id/iv_text_answer"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginLeft="12dp"
                app:layout_goneMarginTop="20dp" />

            <com.kanzhun.common.views.OTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:gravity="center"
                android:textColor="@color/common_color_B2B2B2"
                android:textSize="@dimen/common_text_sp_14"
                app:layout_constraintBottom_toBottomOf="@+id/tv_answer_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_answer_title"
                app:selectCount="@{bean.answerNum}"
                app:totalCount="@{3}"
                tools:text="0/3" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_answer_assist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_white"
            android:paddingLeft="39dp"
            android:paddingTop="7dp"
            android:text="@string/me_text_answer_asssit"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_answer"
            app:visibleGone="@{bean.answerNum &lt;= 0}" />

        <View
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@drawable/common_bg_corner_12_bottom_2_color_white"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_answer_assist"
            app:visibleGone="@{bean.answerNum &lt;= 0}" />

        <ImageView
            android:id="@+id/iv_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:onClick="@{()->callback.clickAddAnswer()}"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:scaleType="fitStart"
            android:src="@drawable/me_info_add"
            app:layout_constraintBottom_toBottomOf="@id/tv_answer_assist"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/cl_answer"
            app:visibleGone="@{bean.answerNum &lt; 3}" />


        <com.kanzhun.marry.me.info.views.NestRecyclerView
            android:id="@+id/rv_text_answer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/cl_answer"
            app:visibleGone="@{bean.answerNum > 0}" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>