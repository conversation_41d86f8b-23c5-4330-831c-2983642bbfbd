<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.setting.viewmodel.MeSettingViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.setting.callback.MeSettingCallback" />

    </data>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <include
            android:id="@+id/ic_title_bar"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:title="@{@string/me_setting}" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_login_out"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="40dp"
            android:background="@color/common_color_F0F0F0"
            android:gravity="center"
            android:onClick="@{v->callback.clickLoginOut()}"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:text="@string/me_login_out"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_18"
            app:qmui_backgroundColor="@color/common_color_F0F0F0"
            app:qmui_radius="24dp"
            tools:ignore="SpUsage" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/btn_login_out"
            android:layout_below="@+id/ic_title_bar"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickAccount(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_account"
                    app:common_setting_text="@string/me_setting_account" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickGuardian(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_gardian"
                    app:common_setting_text="亲友见面守护" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:id="@+id/ll_"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickExportInfoSetting(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_export"
                    app:common_setting_text="个人信息浏览与导出" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:id="@+id/ll_private_setting"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickPrivateSetting(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_safety_guide"
                    app:common_setting_text="隐私设置" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:id="@+id/ll_safety_guide"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickSafetyGuide(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_safety_guide"
                    app:common_setting_text="@string/me_setting_safety_guide" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:id="@+id/ll_notify"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickMessageNotify(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_message_notify"
                    app:common_setting_text="@string/me_setting_message_notify" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:id="@+id/sl_cache"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickCache(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_cache"
                    app:common_setting_text="@string/me_setting_cache"
                    tools:common_setting_content="46.6MB" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:id="@+id/idUnChildBind"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickUnbind(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_unbind"
                    app:common_setting_text="解绑孩子账号" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickAbout(v)}"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_about"
                    app:common_setting_text="@string/me_setting_about" />

                <com.kanzhun.foundation.views.CommonItemSettingLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickChangeMode(v)}"
                    android:visibility="gone"
                    app:common_setting_left_icon="@drawable/me_ic_icon_setting_change_model"
                    app:common_setting_text="切换身份" />

            </LinearLayout>

        </ScrollView>

    </RelativeLayout>

</layout>