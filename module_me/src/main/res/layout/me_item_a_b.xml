<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.kanzhun.foundation.model.profile.ProfileMetaModel" />

        <variable
            name="bean"
            type="com.kanzhun.foundation.api.model.ABFace" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="213dp">

        <com.kanzhun.foundation.views.MeABOImageView
            android:id="@+id/iv_a"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:imageUrl="@{bean.photoA}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.53" />


        <com.kanzhun.foundation.views.MeABOImageView
            android:id="@+id/iv_b"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:imageUrl="@{bean.photoB}"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.53" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:src="@mipmap/me_ic_info_edit_a"
            app:layout_constraintBottom_toTopOf="@+id/tv_a_title"
            app:layout_constraintLeft_toLeftOf="parent" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_a_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:maxWidth="136dp"
            android:maxLines="2"
            android:text="@{bean.titleA}"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintWidth_percent="0.416"
            tools:text="生活中的我" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:src="@mipmap/me_ic_info_edit_b"
            app:layout_constraintBottom_toTopOf="@+id/tv_b_title"
            app:layout_constraintRight_toRightOf="parent" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_b_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:gravity="right"
            android:maxLines="2"
            android:text="@{bean.titleB}"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintWidth_percent="0.416"
            tools:text="工作中的我" />

        <com.kanzhun.common.views.OTextView
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginLeft="10dp"
            android:drawableRight="@drawable/me_ic_fail_small"
            android:drawablePadding="8dp"
            android:gravity="center_vertical"
            android:text="@string/me_cert_fail"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintLeft_toLeftOf="@id/iv_a"
            app:layout_constraintTop_toTopOf="@+id/iv_a"
            app:visibleGone="@{bean.certStatus==ProfileMetaModel.STATUS_REJECTED}" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>