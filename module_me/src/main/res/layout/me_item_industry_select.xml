<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.text.TextUtils" />
        <variable
            name="bean"
            type="com.kanzhun.foundation.model.profile.IndustryBean" />

        <variable
            name="selectedIndustryCode"
            type="String" />
    </data>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginRight="12dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:background="@{TextUtils.equals(bean.code, selectedIndustryCode) ? @drawable/me_bg_corner_20_color_e8eeff : @drawable/me_bg_corner_20_color_f5f5f5}"
        android:gravity="center"
        android:text="@{bean.name}"
        android:textColor="@{TextUtils.equals(bean.code, selectedIndustryCode) ? @color/common_color_7171FF : @color/common_color_545454}"
        android:textSize="@dimen/common_text_sp_14"
        tools:text="互联网" />
</layout>