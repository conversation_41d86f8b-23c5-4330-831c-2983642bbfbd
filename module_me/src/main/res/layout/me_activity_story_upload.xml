<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".info.StoryUploadActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.StoryUploadViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.StoryUploadCallback" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_story_upload"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:fitsSystemWindows="true">

            <include
                android:id="@+id/title_bar"
                layout="@layout/common_title_bar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/common_title_height"
                app:callback="@{callback}"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:right='@{viewModel.skipObservable?@string/me_skip:""}' />

            <ImageView
                android:id="@+id/iv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="24dp"
                android:src="@drawable/common_ic_icon_avatar_page"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_bar" />

            <com.kanzhun.common.views.OTextView
                android:id="@+id/tv_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="18dp"
                android:textColor="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_20"
                app:count="@{viewModel.countObservable}"
                app:index="@{viewModel.indexObservable - 1}"
                app:layout_constraintBottom_toBottomOf="@id/iv_title"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/iv_title"
                app:visibleGone="@{viewModel.indexObservable > 0}" />

            <com.kanzhun.common.views.OTextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginRight="18dp"
                android:paddingLeft="18dp"
                android:text="@string/me_share_life"
                android:textSize="@dimen/common_text_sp_28"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_title" />

            <TextView
                android:id="@+id/tv_assist"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:paddingLeft="18dp"
                android:paddingRight="18dp"
                android:text="@{viewModel.hasStoryData ? @string/me_make_photo_clear : @string/me_show_yourself}"
                android:textColor="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_14"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title"
                tools:text="@string/me_show_yourself" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_story"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:paddingLeft="14dp"
                android:paddingRight="14dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_assist" />

            <TextView
                android:id="@+id/tv_drag_assist"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:paddingLeft="18dp"
                android:paddingRight="18dp"
                android:text="@string/me_drag_assist"
                android:textColor="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_12"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rv_story"
                app:visibleGone="@{viewModel.hasStoryData}" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/btn_next"
                style="@style/common_blue_button_style"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="20dp"
                android:enabled="@{viewModel.enableNext}"
                android:onClick="@{(view)->callback.clickSubmit(view)}"
                android:text='@{viewModel.indexObservable>0&amp;&amp;viewModel.indexObservable&lt;viewModel.countObservable ? @string/me_next : @string/me_save}'
                app:greyDisabledStyle="@{viewModel.enableNext}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>

</layout>