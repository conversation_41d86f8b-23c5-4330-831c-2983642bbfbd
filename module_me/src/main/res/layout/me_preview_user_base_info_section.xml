<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:background="@color/common_color_F2F7FF">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kanzhun.foundation.views.PreviewLikeView
            android:id="@+id/idLike"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="30dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <com.qmuiteam.qmui.layout.QMUILinearLayout
            android:id="@+id/llMood"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:layout_marginBottom="30dp"
            android:background="@color/common_color_F5F5F5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="12dp"
            android:paddingVertical="8dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:qmui_radius="10dp"
            tools:visibility="visible">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/ivMood"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:scaleType="centerCrop"
                tools:background="@color/common_color_FF7847" />

            <TextView
                android:id="@+id/tvMoodName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:textColor="@color/common_color_292929"
                android:textSize="@dimen/common_text_sp_16"
                tools:text="Emo中" />

            <ImageView
                android:id="@+id/ivArrow"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@mipmap/common_ic_black_arrow" />

        </com.qmuiteam.qmui.layout.QMUILinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:layout_marginTop="-18dp"
        android:background="@color/common_white"
        app:qmui_radius="12dp">

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tvUserName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="昵称未填写"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_32"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/idLottieAnimationView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="80dp"
            android:layout_height="80dp"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/idRecommendLayout"
            android:layout_width="match_parent"
            android:background="@drawable/me_preview_ai_recommend_text_bg"
            android:layout_marginHorizontal="12dp"
            app:layout_constraintTop_toBottomOf="@+id/tvUserName"
            android:paddingVertical="16dp"
            android:layout_marginTop="16dp"
            android:layout_height="wrap_content">

            <ImageView
                app:layout_constraintTop_toTopOf="parent"
                android:id="@+id/idRecommendIcon"
                android:layout_marginStart="16dp"
                android:src="@mipmap/image_icon_ai_recommend"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_width="24dp"
                android:layout_height="24dp"/>

            <TextView
                android:id="@+id/idRecommendTitle"
                app:layout_constraintTop_toTopOf="@+id/idRecommendIcon"
                app:layout_constraintBottom_toBottomOf="@+id/idRecommendIcon"
                app:layout_constraintStart_toEndOf="@+id/idRecommendIcon"
                android:layout_marginStart="3dp"
                android:text="根据理想型推荐"
                android:textSize="16dp"
                android:textColor="@color/common_color_292929"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>


            <TextView
                android:id="@+id/idRecommendPoint"
                app:layout_constraintTop_toTopOf="@+id/idRecommendIcon"
                app:layout_constraintBottom_toBottomOf="@+id/idRecommendIcon"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="17dp"
                android:text="匹配度100%"
                android:textSize="16dp"
                android:textColor="@color/common_color_882976"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/idRecommendContent"
                app:layout_constraintTop_toBottomOf="@+id/idRecommendIcon"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginHorizontal="17dp"
                android:layout_marginTop="10dp"
                android:text="他有北京户口，年入50万+且无婚史，完全符合你对京户高收入和未婚的要求，高收入和未婚的要"
                android:textSize="13dp"
                android:textColor="@color/common_color_292929"
                android:layout_width="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rlvIdentityTags"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/idRecommendLayout"
            tools:itemCount="2"
            tools:listitem="@layout/common_identity_info_item" />

        <View
            android:id="@+id/clickArea"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/rlvIdentityTags"
            app:layout_constraintEnd_toEndOf="@id/rlvIdentityTags"
            app:layout_constraintStart_toStartOf="@id/rlvIdentityTags"
            app:layout_constraintTop_toTopOf="@id/rlvIdentityTags" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcvInformation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="6dp"
            app:layout_constraintBottom_toTopOf="@id/clPrivateInfo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rlvIdentityTags"
            app:layout_goneMarginBottom="17dp"
            tools:itemCount="2"
            tools:listitem="@layout/me_preview_user_base_info_item" />

        <com.lihang.ShadowLayout
            android:id="@+id/clPrivateInfo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="24dp"
            android:visibility="gone"
            app:hl_angle="0"
            app:hl_cornerRadius="13dp"
            app:hl_endColor="@color/common_color_F0F0F0"
            app:hl_startColor="@color/common_color_F0F0F0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rcvInformation"
            tools:visibility="visible">


            <!--隐私信息-->
            <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="1dp"
                android:background="@color/common_color_FAFAFA"
                app:qmui_radius="12dp">

                <TextView
                    android:id="@+id/tvPrivateInfoHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:text="@string/me_preview_private_info_hint"
                    android:textColor="@color/common_color_7F7F7F"
                    android:textSize="@dimen/common_text_sp_14"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.kanzhun.foundation.views.flowlayout.FlowLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="16dp"
                    app:fl_horizontal_spacing="19dp"
                    app:fl_vertical_spacing="16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvPrivateInfoHint">

                    <RelativeLayout
                        android:id="@+id/idLayoutCompany"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/idIcon"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_centerVertical="true"
                            android:src="@drawable/me_ic_icon_work_company"
                            />

                        <TextView
                            android:id="@+id/tv_company"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="4dp"
                            android:ellipsize="end"
                            android:singleLine="true"
                            android:layout_toRightOf="@+id/idIcon"
                            android:textColor="@color/common_color_191919"
                            android:textSize="15dp"
                            android:layout_centerVertical="true"
                            android:visibility="visible"
                            tools:text="placeholder" />

                        <ImageView
                            android:id="@+id/idCompanyShadow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="4dp"
                            android:background="@mipmap/me_privacy_blur_cover"
                            android:visibility="invisible"
                            android:layout_toRightOf="@+id/idIcon"
                             />

                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/idLayoutSalary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/idIcon2"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/me_ic_revenue"
                            android:layout_centerVertical="true" />

                        <TextView
                            android:id="@+id/tv_salary"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="4dp"
                            android:ellipsize="end"
                            android:singleLine="true"
                            android:layout_centerVertical="true"
                            android:textColor="@color/common_color_191919"
                            android:textSize="@dimen/common_text_sp_15"
                            android:layout_toRightOf="@+id/idIcon2"
                            tools:text="placeholdelaceholder"
                            tools:visibility="gone" />

                        <ImageView
                            android:id="@+id/idSalaryShadow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="4dp"
                            android:layout_toRightOf="@+id/idIcon2"
                            android:background="@mipmap/me_privacy_blur_cover"
                            android:visibility="invisible"
                            tools:visibility="visible" />


                    </RelativeLayout>

                </com.kanzhun.foundation.views.flowlayout.FlowLayout>

            </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
        </com.lihang.ShadowLayout>


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

</LinearLayout>
