<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:blurkit="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Padding for both views can be different -->

    <com.kanzhun.common.blurkit.BlurLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        blurkit:blk_fps="0"
        blurkit:blk_blurRadius="2"
        ></com.kanzhun.common.blurkit.BlurLayout>

    <com.kanzhun.marry.me.views.MeGestureCropImageView
        android:id="@+id/image_view_crop"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.yalantis.ucrop.view.MeAvatarOverlayView
        android:id="@+id/view_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</merge>