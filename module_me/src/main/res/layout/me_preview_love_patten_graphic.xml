<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="287dp"
    android:layout_height="287dp"
    android:layout_marginTop="24dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/tvTitle"
    >

    <View
        android:id="@+id/vAxle"
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="@color/common_color_191919"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/hAxle"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/common_color_191919"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <View
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/bg1"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:background="@drawable/me_love_patten_bg_man1"
        app:layout_constraintBottom_toTopOf="@id/hAxle"
        app:layout_constraintStart_toStartOf="@id/vAxle" />

    <View
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/bg2"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:background="@drawable/me_love_patten_bg_man2"
        app:layout_constraintBottom_toTopOf="@id/hAxle"
        app:layout_constraintEnd_toStartOf="@id/vAxle" />

    <View
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/bg3"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:background="@drawable/me_love_patten_bg_man3"
        app:layout_constraintEnd_toStartOf="@id/vAxle"
        app:layout_constraintTop_toBottomOf="@id/hAxle" />

    <View
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/bg4"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:background="@drawable/me_love_patten_bg_man4"
        app:layout_constraintStart_toStartOf="@id/vAxle"
        app:layout_constraintTop_toBottomOf="@id/hAxle" />


    <ImageView
        android:id="@+id/placeHolder1"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_margin="31dp"
        android:background="@drawable/me_love_patten_graphic_placeholder_1"
        app:layout_constraintBottom_toTopOf="@id/hAxle"
        app:layout_constraintStart_toStartOf="@id/vAxle" />

    <ImageView
        android:id="@+id/placeHolder2"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_margin="31dp"
        android:background="@drawable/me_love_patten_graphic_placeholder_2"
        app:layout_constraintBottom_toTopOf="@id/hAxle"
        app:layout_constraintEnd_toStartOf="@id/vAxle" />

    <ImageView
        android:id="@+id/placeHolder3"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_margin="31dp"
        android:background="@drawable/me_love_patten_graphic_placeholder_2"
        app:layout_constraintEnd_toStartOf="@id/vAxle"
        app:layout_constraintTop_toBottomOf="@id/hAxle" />

    <ImageView
        android:id="@+id/placeHolder4"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_margin="31dp"
        android:background="@drawable/me_love_patten_graphic_placeholder_4"
        app:layout_constraintStart_toStartOf="@id/vAxle"
        app:layout_constraintTop_toBottomOf="@id/hAxle" />

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvYPositive"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="5dp"
        android:text="心态积极"
        android:textColor="@color/common_white"
        app:layout_constraintEnd_toEndOf="@id/vAxle"
        app:layout_constraintStart_toStartOf="@id/vAxle"
        app:layout_constraintTop_toTopOf="parent"
       android:background="@drawable/me_black_tag_bg" />

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvYNegative"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="5dp"
        android:text="心态佛系"
        android:textColor="@color/common_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/vAxle"
        app:layout_constraintStart_toStartOf="@id/vAxle"
        android:background="@drawable/me_black_tag_bg" />

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvXPositive"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="5dp"
        android:text="行为主动"
        android:textColor="@color/common_white"
        app:layout_constraintBottom_toBottomOf="@id/hAxle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/hAxle"
        android:background="@drawable/me_black_tag_bg" />

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvXNegative"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="5dp"
        android:text="行为被动"
        android:textColor="@color/common_white"
        app:layout_constraintBottom_toBottomOf="@id/hAxle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/hAxle"
        android:background="@drawable/me_black_tag_bg" />


    <ImageView
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/ivAvatar1"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_margin="21dp"
        android:background="@mipmap/me_icon_graphic_man"
        app:layout_constraintBottom_toTopOf="@id/hAxle"
        app:layout_constraintStart_toStartOf="@id/vAxle" />

    <ImageView
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/ivAvatar2"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_margin="21dp"
        android:background="@mipmap/me_icon_graphic_woman"
        app:layout_constraintBottom_toTopOf="@id/hAxle"
        app:layout_constraintEnd_toStartOf="@id/vAxle" />

    <ImageView
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/ivAvatar3"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_margin="21dp"
        android:background="@mipmap/me_icon_graphic_together"
        app:layout_constraintEnd_toStartOf="@id/vAxle"
        app:layout_constraintTop_toBottomOf="@id/hAxle" />

    <ImageView
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/ivAvatar4"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_margin="21dp"
        android:background="@mipmap/me_icon_graphic_together"
        app:layout_constraintStart_toStartOf="@id/vAxle"
        app:layout_constraintTop_toBottomOf="@id/hAxle" />


</androidx.constraintlayout.widget.ConstraintLayout>