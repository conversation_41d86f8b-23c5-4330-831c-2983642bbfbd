<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/me_user_report_navigation"
    app:startDestination="@+id/companyAuthFragment">

    <fragment
        android:id="@+id/companyAuthFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthFragment"
        android:label="fragment_company_auth"
        tools:layout="@layout/me_fragment_company_auth">
        <action
            android:id="@+id/action_companyAuthFragment_to_inputFragment"
            app:destination="@id/inputFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />

        <action
            android:id="@+id/action_companyAuthFragment_to_chooseFragment"
            app:destination="@id/chooseFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />

    </fragment>

    <fragment
        android:id="@+id/inputFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthInputFragment"
        android:label="fragment_input"
        tools:layout="@layout/me_fragment_company_auth_input" />

    <fragment
        android:id="@+id/chooseFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthChooseFragment"
        android:label="fragment_choose"
        tools:layout="@layout/me_fragment_company_auth_choose">

        <action
            android:id="@+id/action_chooseFragment_to_socialFragment"
            app:destination="@id/socialFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />

        <action
            android:id="@+id/action_chooseFragment_to_workCardFragment"
            app:destination="@id/workCardFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />

        <action
            android:id="@+id/action_chooseFragment_to_emailFragment"
            app:destination="@id/emailFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
        <action
            android:id="@+id/me_action_choosefragment_to_me_companyauthwechatdingfragment"
            app:destination="@id/me_companyauthwechatdingfragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
        <action
            android:id="@+id/action_chooseFragment_to_me_taxauthfragment"
            app:destination="@id/me_taxauthfragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
    </fragment>

    <fragment
        android:id="@+id/socialFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthSocialFragment"
        android:label="fragment_social"
        tools:layout="@layout/me_fragment_company_auth_social">
        <action
            android:id="@+id/action_socialFragment_to_successFragment"
            app:destination="@id/successFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
    </fragment>

    <fragment
        android:id="@+id/emailFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthEmailFragment"
        android:label="emailFragment"
        tools:layout="@layout/me_fragment_company_auth_email">
        <action
            android:id="@+id/action_emailFragment_to_successEmailFragment"
            app:destination="@id/successEmailFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />


        <action
            android:id="@+id/action_emailFragment_to_emailFadeBackFragment"
            app:destination="@id/emailFadeBackFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
    </fragment>

    <fragment
        android:id="@+id/emailFadeBackFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthEmailFadeBackFragment"
        android:label="emailFragment"
        tools:layout="@layout/me_fragment_company_auth_email_fade_back">

        <action
            android:id="@+id/action_emailFadeBackFragment_to_successFragment"
            app:destination="@id/successFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
    </fragment>

    <fragment
        android:id="@+id/workCardFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthWorkCardFragment"
        android:label="fragment_work_card"
        tools:layout="@layout/me_fragment_company_auth_work_card">
        <action
            android:id="@+id/action_workCardFragment_to_successFragment"
            app:destination="@id/successFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
    </fragment>

    <fragment
        android:id="@+id/successFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthSuccessFragment"
        android:label="fragment_success"
        tools:layout="@layout/me_fragment_base_auth_submit_success" />

    <fragment
        android:id="@+id/successEmailFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthEmailSuccessFragment"
        android:label="fragment_email_success"
        tools:layout="@layout/me_fragment_base_auth_email_submit_success">


        <action
            android:id="@+id/action_successEmailFragment_to_emailFragment"
            app:destination="@id/emailFragment"
            app:enterAnim="@anim/nav_pop_enter"
            app:exitAnim="@anim/nav_pop_exit"
            app:popEnterAnim="@anim/nav_enter"
            app:popExitAnim="@anim/nav_exit"
            app:popUpTo="@id/successEmailFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/certIngFragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyCertIngFragment"
        android:label="fragment_cert_ing"
        tools:layout="@layout/me_fragment_company_cert_ing" />

    <fragment
        android:id="@+id/me_companyauthwechatdingfragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthWechatDingFragment"
        android:label="CompanyAuthWechatDingFragment">
        <action
            android:id="@+id/me_action_me_companyauthwechatdingfragment_to_successfragment"
            app:destination="@id/successFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />
    </fragment>

    <fragment
        android:id="@+id/me_taxauthfragment"
        android:name="com.kanzhun.marry.me.identify.fragment.CompanyAuthTaxFragment"
        android:label="TaxAuthFragment">

        <action
            android:id="@+id/action_me_taxauthfragment_to_successFragment"
            app:destination="@id/successFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />

    </fragment>

</navigation>