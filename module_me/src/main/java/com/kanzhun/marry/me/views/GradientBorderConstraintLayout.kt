package com.kanzhun.marry.me.views

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.kanzhun.common.kotlin.ext.dp
import com.qmuiteam.qmui.layout.QMUIConstraintLayout

class GradientBorderConstraintLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : QMUIConstraintLayout(context, attrs, defStyleAttr) {

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 12f
    }

    private var path = Path()
    private val rectF = RectF()
    private val colors = intArrayOf(Color.parseColor("#3AA5FB"), Color.parseColor("#FE92ED"))
    private val positions = floatArrayOf(0f, 1f)

    var showBorder: Boolean = false
        set(value) {
            field = value
            invalidate()
        }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        rectF.set(0f, 0f, w.toFloat(), h.toFloat())
        path = Path()
        path.addRoundRect(rectF, 12.dp, 12.dp, Path.Direction.CW)
        paint.shader = LinearGradient(0f, 0f, w.toFloat(), 0f, colors, positions, Shader.TileMode.CLAMP)
    }

    override fun dispatchDraw(canvas: Canvas) {
        if (showBorder) {
            canvas.clipPath(path)
        }
        super.dispatchDraw(canvas)
        if (showBorder) {
            canvas.drawPath(path,paint)
        }

    }
}