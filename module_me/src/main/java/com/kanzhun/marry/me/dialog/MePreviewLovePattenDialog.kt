package com.kanzhun.marry.me.dialog

import android.os.Build
import android.text.style.DynamicDrawableSpan
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import androidx.core.text.buildSpannedString
import androidx.fragment.app.FragmentActivity
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.model.profile.RomanticMode
import com.kanzhun.foundation.model.profile.ext.isMyself
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MePreviewLovePattenDialogBinding
import com.kanzhun.utils.string.image

/**
 *
 * 个人预览页查看恋爱模式倾向弹框
 */
class MePreviewLovePattenDialog(val activity: FragmentActivity) {

    val isMan = AccountHelper.getInstance().userInfo?.gender == 1


    fun show(romanticMode: RomanticMode,userId:String?) {
        if(userId.isNullOrBlank()){
            return
        }
        CommonViewBindingDialog(activity,
            mCancelable = false,
            mCanceledOnTouchOutside = false,
            mGravity = Gravity.CENTER,
            mPaddingLeft = 32,
            mPaddingRight = 32,
            onInflateCallback = { inflater, dialog ->
                val binding = MePreviewLovePattenDialogBinding.inflate(inflater)
                binding.apply {
                    //获取他们所在的象限
                    val quadrant = romanticMode.getQuadrant()
                    val loginUserQuadrant = romanticMode.getLoginUserQuadrant()
                    if(userId.isMyself()){
                        val component = getComponentByQuadrant(loginUserQuadrant, binding)
                        component.setData(if(isMan) GenderType.MAN else GenderType.WOMAN, loginUserQuadrant)
                    }else if (quadrant == loginUserQuadrant) { //位于相同的象限
                        val component = getComponentByQuadrant(quadrant, binding)
                        component.setData(GenderType.TOGETHER, quadrant)
                    } else { //位于不同的象限
                        val component = getComponentByQuadrant(quadrant, binding)
                        component.setData(if(!isMan) GenderType.MAN else GenderType.WOMAN, quadrant)
                        val loginComponent = getComponentByQuadrant(loginUserQuadrant, binding)
                        loginComponent.setData(if(isMan) GenderType.MAN else GenderType.WOMAN, loginUserQuadrant)
                    }
                    iconYou.setGenderStyle(isMan)
                    iconTA.setGenderStyle(!isMan)
                    recyclerView.adapter = PattenCompareAdapter().also { adapter ->
                        adapter.setNewInstance(romanticMode.matchContent?.toMutableList() ?: mutableListOf())
                    }
                    ivClose.setOnClickListener {
                        dialog.dismiss()
                    }

                }
                binding
            })
            .show()
    }

    private fun getComponentByQuadrant(quadrant: Quadrant, binding: MePreviewLovePattenDialogBinding): Component {
        binding.clGraphic.run {
            return when (quadrant) {
                Quadrant.FIRST -> Component(ivAvatar1, placeHolder1, bg1)
                Quadrant.SECOND -> Component(ivAvatar2, placeHolder2, bg2)
                Quadrant.THIRD -> Component(ivAvatar3, placeHolder3, bg3)
                Quadrant.FOURTH -> Component(ivAvatar4, placeHolder4, bg4)
            }

        }
    }

    private fun Component.setData(type: GenderType, quadrant: Quadrant) {
        ivAvatar.setBackgroundResource(getAvatarByGenderType(type))
        ivAvatar.visible()
        ivPlaceHolder.gone()
        viewBG.setBackgroundResource(getBgByGenderTypeAndQuadrant(type, quadrant))
        viewBG.visible()
    }

    /**
     * 查看用户的象限
     */
    private fun RomanticMode.getQuadrant(): Quadrant {
        return when {
            positive && proactive -> Quadrant.FIRST
            positive && !proactive -> Quadrant.SECOND
            !positive && !proactive -> Quadrant.THIRD
            else -> Quadrant.FOURTH
        }
    }

    /**
     * 查看登录用户的象限
     */
    private fun RomanticMode.getLoginUserQuadrant(): Quadrant {
        return when {
            loginUserPositive && loginUserProactive -> Quadrant.FIRST
            loginUserPositive && !loginUserProactive -> Quadrant.SECOND
            !loginUserPositive && !loginUserProactive -> Quadrant.THIRD
            else -> Quadrant.FOURTH
        }
    }


    private fun ImageView.setGenderStyle(isMan: Boolean) {
        setImageResource(if (isMan) R.mipmap.me_icon_graphic_blue_heart else R.mipmap.me_icon_graphic_pink_heart)
    }

    private fun getAvatarByGenderType(genderType: GenderType): Int {
        return when (genderType) {
            GenderType.MAN -> R.mipmap.me_icon_graphic_man
            GenderType.WOMAN -> R.mipmap.me_icon_graphic_woman
            else -> R.mipmap.me_icon_graphic_together
        }
    }

    private fun getBgByGenderTypeAndQuadrant(genderType: GenderType, quadrant: Quadrant): Int {
        return when (genderType) {
            GenderType.MAN -> {
                when (quadrant) {
                    Quadrant.FIRST -> R.drawable.me_love_patten_bg_man1
                    Quadrant.SECOND -> R.drawable.me_love_patten_bg_man2
                    Quadrant.THIRD -> R.drawable.me_love_patten_bg_man3
                    Quadrant.FOURTH -> R.drawable.me_love_patten_bg_man4
                }
            }

            else -> {
                when (quadrant) {
                    Quadrant.FIRST -> R.drawable.me_love_patten_bg_woman1
                    Quadrant.SECOND -> R.drawable.me_love_patten_bg_woman2
                    Quadrant.THIRD -> R.drawable.me_love_patten_bg_woman3
                    Quadrant.FOURTH -> R.drawable.me_love_patten_bg_woman4
                }
            }
        }
    }

    data class Component(val ivAvatar: ImageView, val ivPlaceHolder: ImageView, val viewBG: View)

    enum class Quadrant {
        FIRST,
        SECOND,
        THIRD,
        FOURTH
    }

    enum class GenderType {
        MAN, WOMAN, TOGETHER
    }

    class PattenCompareAdapter() : BaseQuickAdapter<String, BaseViewHolder>(R.layout.me_preview_love_patten_dialog_item) {
        private val isMan = AccountHelper.getInstance().userInfo?.gender == 1
        override fun convert(holder: BaseViewHolder, item: String) {
            item.run {
                holder.setText(R.id.tvContent, buildSpannedString {
                    image(if(isMan)R.mipmap.me_love_patten_point_man else R.mipmap.me_love_patten_point_woman,holder.itemView.context, if(Build.VERSION.SDK_INT < 29) DynamicDrawableSpan.ALIGN_BASELINE else DynamicDrawableSpan.ALIGN_CENTER)
                    append(" ")
                    append(item)
                })
                holder.setGone(R.id.bottom, holder.bindingAdapterPosition != data.size - 1)
            }
        }



    }
}