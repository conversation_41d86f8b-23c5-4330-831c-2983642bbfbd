package com.kanzhun.marry.me.personality.fragment;

import android.view.View;
import android.view.animation.AlphaAnimation;

import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentPersonalityTestGuideStartBinding;
import com.kanzhun.marry.me.personality.callback.PersonalityTestGuideStartFragmentCallback;
import com.kanzhun.marry.me.personality.viewmodel.PersonalityTestGuideStartFragmentViewModel;

/**
 * <AUTHOR>
 * @date 2022/3/25.
 */
public class PersonalityTestGuideStartFragment extends FoundationVMFragment<MeFragmentPersonalityTestGuideStartBinding, PersonalityTestGuideStartFragmentViewModel> implements PersonalityTestGuideStartFragmentCallback {
    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_personality_test_guide_start;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        getDataBinding().clPersonalityGuideStart.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        AlphaAnimation alpha1 = new AlphaAnimation(0, 1);
        alpha1.setDuration(200);
        alpha1.setStartOffset(200);
        getDataBinding().tvPersonalityGuideOne.setAnimation(alpha1);
        AlphaAnimation alpha2 = new AlphaAnimation(0, 1);
        alpha2.setDuration(200);
        alpha2.setStartOffset(400);
        getDataBinding().tvPersonalityGuideTow.setAnimation(alpha2);
        AlphaAnimation alpha3 = new AlphaAnimation(0, 1);
        alpha3.setDuration(200);
        alpha3.setStartOffset(600);
        getDataBinding().tvPersonalityGuideThree.setAnimation(alpha3);

        alpha1.start();
        alpha2.start();
        alpha3.start();
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(activity);
    }
}
