package com.kanzhun.marry.me.setting.viewmodel;

import android.app.Application;

import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.configuration.UserSettingConfig;

import io.reactivex.rxjava3.core.Observable;

public class MeNotifySettingViewModel extends FoundationViewModel {
    private static final String TAG = "MeNotifySettingViewModel";

    public MeNotifySettingViewModel(Application application) {
        super(application);
    }

    public void requestSetUpdate(int key, int value) {
        setShowProgressBar();
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(FoundationApi.class).settingUpdate(key, value);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                new SettingBuilder.Builder().addUserSetting(key, new UserSettingConfig(key, String.valueOf(value))).build();
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public void requestSetUpdate(int key, int value,CallBack callBack) {
        setShowProgressBar();
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(FoundationApi.class).settingUpdate(key, value);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                new SettingBuilder.Builder().addUserSetting(key, new UserSettingConfig(key, String.valueOf(value))).build();
                if(callBack != null){
                    callBack.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

   public interface CallBack{
        void onSuccess();
    }


}