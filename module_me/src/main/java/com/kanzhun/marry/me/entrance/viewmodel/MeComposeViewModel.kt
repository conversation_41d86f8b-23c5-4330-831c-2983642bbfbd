package com.kanzhun.marry.me.entrance.viewmodel

import android.text.SpannableString
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.model.OfficeActivityBean
import com.kanzhun.foundation.model.profile.UserTabModel
import com.kanzhun.foundation.model.profile.UserTabModel.ActivityBean
import com.kanzhun.foundation.model.profile.UserTabModel.ButtonBean
import com.kanzhun.foundation.model.profile.UserTabModel.MenuBean
import com.kanzhun.foundation.model.profile.UserTabModel.TodoInfo
import com.kanzhun.foundation.ui.moment.toast
import com.kanzhun.http.createApi
import com.kanzhun.marry.me.api.KMeApi
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.jvm.java

private const val TAG = "MeComposeViewModel"

class MeComposeViewModel(private val inPreview: Boolean = false) : BaseViewModel() {
    var userTabModel by mutableStateOf<UserTabModel?>(null)

    var officeActivityBean by mutableStateOf<OfficeActivityBean?>(null)

    init {
        if (inPreview) {
            userTabModel = UserTabModel(
            ).apply {
                menuList = listOf(
                    MenuBean().apply {
                        menuTitle = "我的活动"
                        menuIcon = ""
                        tipContent = "tipContent"
                        jumpUrl = ""
                    },
                    MenuBean().apply {
                        menuTitle = "我的活动"
                        menuIcon = ""
                        tipContent = ""
                        jumpUrl = ""
                    }
                )
            }
        } else {
            refreshData()
        }
    }

    fun refreshData() {
        getUserTabInfo()
        getMyActivity()
    }

    fun getUserTabInfo() {
        viewModelScope.launch {
            try {
                val response =
                    withContext(Dispatchers.IO) { createApi(KMeApi::class.java).getUserTabInfo() }

                if (response.isSuccess) {
                    userTabModel = response.data

                } else {
                    TLog.error(TAG, "getChatList: ${response.msg}")
                    toast(response.msg)
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "getChatList: $e")
                e.message?.let { toast(it) }
            }
        }
    }

    private fun getMyActivity() {
        viewModelScope.launch {
            try {
                val response =
                    withContext(Dispatchers.IO) { createApi(KMeApi::class.java).getMyActivity() }
                if (response.isSuccess) {
                    officeActivityBean = response.data
                } else {
                    TLog.error(TAG, "getInviteDialog: ${response.msg}")
                    toast(response.msg)
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "getInviteDialog: $e")
                e.message?.let { toast(it) }
            } finally {

            }
        }
    }

    class Factory(private val inPreview: Boolean = false) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return MeComposeViewModel(inPreview = inPreview) as T
        }
    }
}

val mockUserTabModel: UserTabModel = UserTabModel(
).apply {
    menuList = listOf(
        MenuBean().apply {
            menuTitle = "我的活动"
            menuIcon = ""
            tipContent = "tipContent"
            jumpUrl = ""
        },
        MenuBean().apply {
            menuTitle = "我的活动"
            menuIcon = ""
            tipContent = ""
            jumpUrl = ""
        }
    )
    todoInfo = TodoInfo().apply {
        icon = ""
        title = "title"
        content = "content"
        button = ButtonBean().apply {
            text = "查看"
            url = ""
        }
        status = 0
    }
    showNoviceTask = 1
}

val mockOfficeActivityBean: OfficeActivityBean = OfficeActivityBean(
).apply {
    activityList = listOf(
        ActivityBean().apply {
            activityId = "1"
            showStatus = "已报名"
            showImages = listOf("https://via.placeholder.com/150")
            jumpUrl = ""
            avatarList = listOf("https://via.placeholder.com/150")
            activityRelationStatus = 1
            content = "content"
            title = "title"
        }
    )
}