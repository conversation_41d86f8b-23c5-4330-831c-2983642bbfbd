package com.kanzhun.marry.me.setting.viewmodel;

import android.app.Application;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.UserSafeModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.api.KMeApi;
import com.kanzhun.marry.me.api.bean.DeviceListResponse;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.configuration.UserSettingConfig;

import io.reactivex.rxjava3.core.Observable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/2
 */
public class AccountAndSecurityViewModel extends FoundationViewModel {

    public MutableLiveData<UserSafeModel> safeInfo = new MutableLiveData<>();
    public MutableLiveData<DeviceListResponse> deviceListResponse = new MutableLiveData<>();

    public AccountAndSecurityViewModel(Application application) {
        super(application);
    }

    public void requestSetUpdate(int key, int value,boolean freshF1) {
        setShowProgressBar();
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(FoundationApi.class).settingUpdate(key, value);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                new SettingBuilder.Builder().addUserSetting(key, new UserSettingConfig(key, String.valueOf(value))).build();
                if(freshF1){
                    ServiceManager.getInstance().getSettingService().getMatchVisibleLiveData().setValue(value == 1 ? true : false);
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    /**
     * 请求用户安全信息
     */
    public void requestUserSafeInfo() {
        Observable<BaseResponse<UserSafeModel>> observable = RetrofitManager.getInstance().createApi(FoundationApi.class).getUserSafeInfo();
        HttpExecutor.execute(observable, new BaseRequestCallback<UserSafeModel>() {

            @Override
            public void onSuccess(UserSafeModel data) {
                safeInfo.postValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }


        });
    }

    public void getUserDeviceList() {
        Observable<BaseResponse<DeviceListResponse>> observable = RetrofitManager.getInstance().createApi(KMeApi.class).getUserDeviceList();
        HttpExecutor.execute(observable, new BaseRequestCallback<>() {

            @Override
            public void onSuccess(DeviceListResponse data) {
                deviceListResponse.postValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }


        });
    }




    public void modifyUserWechat(String code) {
        Observable<BaseResponse<Object>> observable = RetrofitManager.getInstance().createApi(KMeApi.class).modifyUserWechat(code);
        HttpExecutor.execute(observable, new BaseRequestCallback<Object>(true) {

            @Override
            public void onSuccess(Object data) {
                requestUserSafeInfo();
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }


        });
    }
}
