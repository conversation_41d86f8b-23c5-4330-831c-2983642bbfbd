package com.kanzhun.marry.me.info.activity

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Picture
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Environment
import android.util.Base64
import android.util.Base64.DEFAULT
import android.widget.ImageView
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.scaleIn
import androidx.compose.animation.slideIn
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.drawscope.draw
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.font.FontWeight.Companion.W400
import androidx.compose.ui.text.font.FontWeight.Companion.W500
import androidx.compose.ui.text.font.FontWeight.Companion.W900
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.createBitmapFromPicture
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ui.O2Button
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.common.kotlin.ext.colorResource
import com.kanzhun.foundation.api.model.ProfileInfoModel.AnswerImage
import com.kanzhun.foundation.api.model.ProfileInfoModel.BaseInfo
import com.kanzhun.foundation.api.model.ProfileInfoModel.QuestionAnswer
import com.kanzhun.foundation.model.profile.QuestionTemplateModel.AnswerOptionBean
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.bean.QrInfo
import com.kanzhun.marry.me.api.bean.QuestionAnswerShareResponse
import com.kanzhun.utils.T
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.coroutines.resume

private const val TAG = "MeTextAnswer"

//region AnswerManageButton
@Composable
fun AnswerManageButton(
    onDeleteClick: () -> Unit = {},
    hasDeleteButton: Boolean = false,
    saveButtonEnabled: Boolean = false,
    onSaveClick: () -> Unit = {},
    // 保存按钮禁用，点击事件
    onDisabledSaveClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(18.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        if (hasDeleteButton) {
            O2Button(
                modifier = Modifier.weight(1f),
                text = "删除",
                enabled = true,
                reversed = true,
                onClick = onDeleteClick
            )
        }

        Box(
            modifier = Modifier
                .weight(2f),
        ) {
            O2Button(
                text = "保存",
                enabled = saveButtonEnabled,
                onClick = onSaveClick
            )

            if (!saveButtonEnabled) {
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .noRippleClickable {
                            onDisabledSaveClick()
                        }
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewAnswerManageButton() {
    AnswerManageButton(hasDeleteButton = true, saveButtonEnabled = false)
}
//endregion

//region AnswerShareContent
@Composable
fun AnswerShareContent(
    qaShareResp: QuestionAnswerShareResponse,
    onDismiss: () -> Unit = {},
    onShareWechat: (Bitmap) -> Unit = {},
    onSaveImage: (Bitmap) -> Unit = {},
    inPreview: Boolean = false
) {
    var shareContainerVisible by remember { mutableStateOf(inPreview) }
    LaunchedEffect(Unit) {
        shareContainerVisible = true
    }
    AnimatedVisibility(
        visible = shareContainerVisible,
        enter = fadeIn(animationSpec = tween(durationMillis = 200)),
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(color = colorResource(R.color.common_black_60))
                .noRippleClickable { /* do nothing */ }
        ) {
            var columnHeightDp by remember { mutableStateOf(if (inPreview) 64.dp else 0.dp) }

            var shareContentVisible by remember { mutableStateOf(inPreview) }
            LaunchedEffect(Unit) {
                delay(450)
                shareContentVisible = true
            }

            val picture = remember { Picture() }
            if (columnHeightDp > 0.dp) {
                AnimatedVisibility(
                    visible = shareContentVisible,
                    enter = scaleIn(animationSpec = tween(durationMillis = 400))
                        + fadeIn(animationSpec = tween(durationMillis = 400)),
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .verticalScroll(rememberScrollState())
                            .padding(horizontal = 32.dp),
                        verticalArrangement = Arrangement.Center,
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(with(LocalDensity.current) {
                                    PaddingValues(
                                        top = WindowInsets.statusBars.getTop(this).toDp(),
                                        bottom = WindowInsets.navigationBars.getBottom(this)
                                            .toDp() + columnHeightDp + 20.dp
                                    )
                                })
                                .clip(RoundedCornerShape(32.dp))
                                .background(color = Color.White)
                                .drawWithCache {
                                    val width = this.size.width.toInt()
                                    val height = this.size.height.toInt()

                                    onDrawWithContent {
                                        val pictureCanvas =
                                            androidx.compose.ui.graphics.Canvas(
                                                picture.beginRecording(
                                                    width,
                                                    height
                                                )
                                            )

                                        draw(this, this.layoutDirection, pictureCanvas, this.size) {
                                            <EMAIL>()
                                        }

                                        picture.endRecording()

                                        drawIntoCanvas { canvas ->
                                            canvas.nativeCanvas.drawPicture(
                                                picture
                                            )
                                        }
                                    }
                                }
                        ) {
                            qaShareResp.userInfo?.let {
                                UserInfo(
                                    userInfo = it,
                                    inPreview = inPreview
                                )
                            }

                            qaShareResp.answerInfo?.let {
                                QuestionAnswer(
                                    answerInfo = it,
                                    inPreview = inPreview
                                )
                            }

                            val qrData = qaShareResp.qrInfo?.qrData
                            if (!qrData.isNullOrEmpty()) {
                                QrCode(qrCode = qrData, inPreview = inPreview)
                            } else {
                                Spacer(modifier = Modifier.height(32.dp))
                            }
                        }
                    }
                }
            }

            var bottomSheetVisible by remember { mutableStateOf(inPreview) }
            LaunchedEffect(Unit) { bottomSheetVisible = true }
            val localDensity = LocalDensity.current
            AnimatedVisibility(
                visible = bottomSheetVisible,
                modifier = Modifier
                    .align(Alignment.BottomCenter),
                enter = slideIn(
                    animationSpec = tween(durationMillis = 400),
                    initialOffset = { fullSize -> IntOffset(x = 0, y = fullSize.height) }),
            ) {
                BottomSheet(
                    modifier = Modifier
                        .onGloballyPositioned { coordinates ->
                            columnHeightDp = with(localDensity) { coordinates.size.height.toDp() }
                        },
                    onDismiss = onDismiss,
                    onShareWechat = {
                        val bitmap = try {
                            createBitmapFromPicture(picture = picture)
                        } catch (e: Throwable) {
                            TLog.error(TAG, "create image bitmap failed: $e")
                            null
                        }

                        bitmap?.let {
                            onShareWechat(bitmap)
                        } ?: run {
                            T.ss("分享失败，请重试")
                        }
                    },
                    onSaveImage = {
                        val bitmap = try {
                            createBitmapFromPicture(picture = picture)
                        } catch (e: Throwable) {
                            TLog.error(TAG, "create image bitmap failed: $e")
                            null
                        }

                        bitmap?.let {
                            onSaveImage(bitmap)
                        } ?: run {
                            T.ss("保存失败，请重试")
                        }
                    }
                )
            }
        }
    }
}

@Composable
private fun BottomSheet(
    modifier: Modifier,
    onShareWechat: () -> Unit = {},
    onSaveImage: () -> Unit = {},
    onDismiss: () -> Unit = {},
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .clip(RoundedCornerShape(topStart = 32.dp, topEnd = 32.dp))
            .background(color = com.kanzhun.foundation.R.color.color_white.colorResource())
            .padding(horizontal = 16.dp)
            .padding(with(LocalDensity.current) {
                PaddingValues(
                    top = 16.dp,
                    bottom = WindowInsets.navigationBars.getBottom(this).toDp() + 32.dp
                )
            })
    ) {
        Column {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "分享我的问答",
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Center,
                    fontWeight = W500,
                    fontSize = 18.sp,
                    color = colorResource(R.color.common_color_191919)
                )

                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        painter = painterResource(R.drawable.common_ic_white_close),
                        contentDescription = "关闭",
                    )
                }
            }

            Spacer(modifier = Modifier.height(20.dp))

            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .weight(1f)
                        .noRippleClickable {
                            onShareWechat()
                        }
                        .padding(vertical = 8.dp)
                ) {
                    Image(
                        painter = painterResource(R.mipmap.me_ic_wechat),
                        contentDescription = null,
                        modifier = Modifier.shareIconStyle()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "分享到微信好友",
                        color = colorResource(R.color.common_color_292929),
                        fontSize = 14.sp,
                        fontWeight = W400
                    )
                }

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .weight(1f)
                        .noRippleClickable {
                            onSaveImage()
                        }
                        .padding(vertical = 8.dp)
                ) {
                    Image(
                        painter = painterResource(R.mipmap.me_ic_download),
                        contentDescription = null,
                        modifier = Modifier.shareIconStyle()
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "保存图片到本地",
                        color = colorResource(R.color.common_color_292929),
                        fontSize = 14.sp,
                        fontWeight = W400
                    )
                }
            }
        }
    }
}

@Composable
private fun Modifier.shareIconStyle(): Modifier {
    return this then Modifier
        .size(64.dp)
        .background(
            color = colorResource(R.color.common_color_F5F5F5),
            shape = CircleShape
        )
        .padding(16.dp)
}

@Composable
private fun UserInfo(userInfo: BaseInfo, inPreview: Boolean = false) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 32.dp)
            .padding(top = 27.dp)
    ) {
        val (avatar, gender, name, desc) = createRefs()

        OImageView2(
            imageUrl = userInfo.tinyAvatar,
            modifier = Modifier
                .size(56.dp)
                .conditional(inPreview) {
                    background(color = Color.Gray, shape = CircleShape)
                }
                .clip(CircleShape)
                .constrainAs(avatar) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                })

        Image(
            painter = painterResource(if (userInfo.gender == 1) R.drawable.common_icon_male_reverse else R.drawable.common_icon_female_reverse),
            contentDescription = null,
            modifier = Modifier
                .size(16.dp)
                .border(width = 1.dp, color = Color.White, shape = CircleShape)
                .constrainAs(gender) {
                    end.linkTo(avatar.end)
                    bottom.linkTo(avatar.bottom)
                }
        )


        Text(
            text = userInfo.nickName,
            style = TextStyle(
                fontSize = 18.sp, fontWeight = FontWeight(600), color = colorResource(
                    R.color.common_color_292929
                )
            ),
            modifier = Modifier
                .padding(top = 4.dp)
                .constrainAs(name) {
                    start.linkTo(avatar.end, margin = 10.dp)
                    top.linkTo(avatar.top)
                })

        val context = LocalContext.current
        Text(
            text = listOf(
                "${userInfo.age}岁",
                getEducationBackGround(context = context, index = userInfo.degree),
                userInfo.career
            ).filter { !it.isNullOrEmpty() }.joinToString("·"),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = TextStyle(
                fontSize = 14.sp, fontWeight = FontWeight(400), color = colorResource(
                    R.color.common_color_B2B2B2
                )
            ),
            modifier = Modifier
                .padding(top = 2.dp)
                .constrainAs(desc) {
                    start.linkTo(name.start)
                    top.linkTo(name.bottom)
                }
        )
    }
}

private fun getEducationBackGround(context: Context, index: Int): String {
    if (index <= 0) {
        return ""
    }

    val stringArray =
        context.resources.getStringArray(R.array.common_array_educational_background)

    val codesArray =
        context.resources.getStringArray(R.array.common_array_educational_background_code)

    if (codesArray.isEmpty()) {
        return ""
    }

    for (i in codesArray.indices) {
        if (index.toString() == codesArray[i]) {
            return stringArray[i]
        }
    }

    return ""
}

@Composable
private fun QuestionAnswer(
    answerInfo: QuestionAnswer,
    modifier: Modifier = Modifier,
    inPreview: Boolean = false
) {
    Column(modifier = modifier) {
        ConstraintLayout(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 12.dp)
                .padding(top = 24.dp)
                .background(
                    color = colorResource(R.color.common_color_F5F5F5),
                    shape = RoundedCornerShape(12.dp)
                )
        ) {
            val (icon, title, bg) = createRefs()

            Image(
                painter = painterResource(R.drawable.me_icon_preview_qa),
                contentDescription = null,
                modifier = Modifier
                    .constrainAs(icon) {
                        start.linkTo(parent.start, margin = 16.dp)
                        top.linkTo(anchor = title.top, margin = 20.dp)
                    })

            Text(
                text = answerInfo.question ?: "",
                style = TextStyle(
                    color = colorResource(R.color.common_color_191919),
                    fontSize = 16.sp,
                    fontWeight = FontWeight(700),
                    fontFamily = boldFontFamily()
                ),
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .constrainAs(title) {
                        start.linkTo(icon.end, margin = 4.dp)
                        top.linkTo(parent.top)
                        end.linkTo(parent.end, margin = 16.dp)

                        width = Dimension.fillToConstraints
                    }
            )

            Image(
                painter = painterResource(R.mipmap.me_qa_section_bg),
                contentDescription = null,
                modifier = Modifier
                    .height(66.dp)
                    .width(84.dp)
                    .constrainAs(bg) {
                        top.linkTo(parent.top)
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom)
                    })
        }

        Text(
            text = listOf(
                answerInfo.options?.filter { !it.name.isNullOrEmpty() }
                    ?.joinToString("、") { it.name },
                answerInfo.answer
            ).filter { !it.isNullOrEmpty() }.joinToString("\n\n"),
            style = TextStyle(
                color = colorResource(R.color.common_color_191919),
                fontSize = 13.sp,
                fontWeight = FontWeight(400),
            ),
            modifier = Modifier
                .padding(horizontal = 24.dp)
                .padding(top = 16.dp)
        )

        val imgs = answerInfo.imgs
        if (!imgs.isNullOrEmpty()) {
            AnswerImages(answerImages = imgs, inPreview = inPreview)
        }
    }
}

@Composable
private fun AnswerImages(
    answerImages: List<AnswerImage>,
    modifier: Modifier = Modifier,
    inPreview: Boolean = false
) {
    Column(
        modifier = modifier
            .padding(top = 24.dp)
            .fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        answerImages.forEach {
            OImageView2(
                imageUrl = it.photo, modifier = Modifier
                    .fillMaxWidth()
                    .conditional(it.width > 0 && it.height > 0) {
                        aspectRatio(it.width * 1f / it.height)
                    }
                    .conditional(inPreview) {
                        background(color = Color.Gray)
                    },
                scaleType = ImageView.ScaleType.CENTER_CROP
            )
        }
    }
}

@Composable
private fun QrCode(
    qrCode: String,
    modifier: Modifier = Modifier,
    inPreview: Boolean = false
) {
    ConstraintLayout(
        modifier = modifier
            .padding(horizontal = 24.dp, vertical = 32.dp)
            .fillMaxWidth()
    ) {
        val (logo, name, desc, qr) = createRefs()

        Image(
            painter = painterResource(id = R.mipmap.ic_launcher),
            contentDescription = null,
            modifier = Modifier
                .size(36.dp)
                .clip(
                    RoundedCornerShape(12.dp)
                )
                .constrainAs(logo) {
                    start.linkTo(parent.start)
                    top.linkTo(qr.top)
                }
        )

        Text(
            text = "看准",
            style = TextStyle(
                color = colorResource(R.color.common_color_191919),
                fontSize = 22.sp,
                fontWeight = W900,
                fontFamily = boldFontFamily()
            ),
            modifier = Modifier.constrainAs(name) {
                top.linkTo(logo.top)
                bottom.linkTo(logo.bottom)
                start.linkTo(logo.end, margin = 8.dp)
            }
        )

        Text(
            text = "扫描二维码查看，我的社交主页",
            style = TextStyle(
                color = colorResource(R.color.common_color_B2B2B2),
                fontSize = 12.sp,
                fontWeight = W400,
            ),
            modifier = Modifier.constrainAs(desc) {
                top.linkTo(logo.bottom, margin = 8.dp)
                start.linkTo(logo.start)
                end.linkTo(qr.start)

                width = Dimension.fillToConstraints
            }
        )

        var bitmap by remember { mutableStateOf<Bitmap?>(null) }
        LaunchedEffect(qrCode) {
            launch {
                try {
                    val decoded = Base64.decode(qrCode, DEFAULT)
                    bitmap = BitmapFactory.decodeByteArray(decoded, 0, decoded.size)
                } catch (e: Throwable) {
                    TLog.error(TAG, "decode qrCode error: $e")
                }
            }
        }
        bitmap?.let {
            Image(
                bitmap = it.asImageBitmap(),
                contentDescription = null,
                modifier = Modifier
                    .size(66.dp)
                    .conditional(inPreview) {
                        background(color = Color.Gray)
                    }
                    .constrainAs(qr) {
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom)
                        top.linkTo(parent.top)
                    })
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewAnswerShareContent() {
    AnswerShareContent(qaShareResp = QuestionAnswerShareResponse().apply {
        userInfo = BaseInfo().apply {
            nickName = "小明"
            gender = 2
            age = 18
            degree = 50
            career = "产品经理"
        }
        answerInfo = QuestionAnswer().apply {
            question = "你最喜欢哪类电影？"
            answer =
                "可以介绍一下自己的性格、爱好、工作、恋爱期望等可以介绍一下自己的性格、爱好、工作、恋爱期望等可以介绍一下自己的性格、爱好、工作、恋爱期望等"

            imgs = listOf(
                AnswerImage().apply {
                    width = 800
                    height = 300
                },
                AnswerImage().apply {
                    width = 800
                    height = 600
                },
                AnswerImage().apply {
                    width = 800
                    height = 900
                }
            ).take(3)

            options = listOf(
                AnswerOptionBean()
                    .apply {
                        name = "选项 A"
                    },
                AnswerOptionBean()
                    .apply {
                        name = "选项 B"
                    },
            ).take(2)
        }
        // https://www.themarryapp.com/
        qrInfo = QrInfo(
            id = "123456789",
            expireTime = 1630000000000,
            qrData = "aHR0cHM6Ly93d3cudGhlbWFycnlhcHAuY29tLw=="
        )
    }, inPreview = true)
}
//endregion

//region 页面生成位图
suspend fun Bitmap.saveToDisk(context: Context): Uri {
    val file = File(
        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
        "screenshot-${System.currentTimeMillis()}.png"
    )

    file.writeBitmap(this, Bitmap.CompressFormat.PNG, 100)

    return scanFilePath(context, file.path) ?: throw Exception("File could not be saved")
}

private suspend fun scanFilePath(context: Context, filePath: String): Uri? {
    return suspendCancellableCoroutine { continuation ->
        MediaScannerConnection.scanFile(
            context,
            arrayOf(filePath),
            arrayOf("image/png")
        ) { _, scannedUri ->
            if (scannedUri == null) {
                continuation.cancel(Exception("File $filePath could not be scanned"))
            } else {
                continuation.resume(scannedUri)
            }
        }
    }
}

private fun File.writeBitmap(bitmap: Bitmap, format: Bitmap.CompressFormat, quality: Int) {
    outputStream().use { out ->
        bitmap.compress(format, quality, out)
        out.flush()
    }
}

suspend fun toast(msg: String) {
    withContext(Dispatchers.Main) {
        T.ss(msg)
    }
}
//endregion