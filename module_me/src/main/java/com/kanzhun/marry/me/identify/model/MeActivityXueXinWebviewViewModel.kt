package com.kanzhun.marry.me.identify.model

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.base.FoundationViewModel
import com.kanzhun.foundation.bean.UserSchoolGetBean
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason

class MeActivityXueXinWebviewViewModel(application: Application?) :
    FoundationViewModel(application) {

        var userSchoolGetBeanLiveData : MutableLiveData<UserSchoolGetBean> = MutableLiveData()

        fun getData(){
            val responseObservable = RetrofitManager.getInstance().createApi(
                FoundationApi::class.java
            ).querySchoolGet()
            HttpExecutor.execute<UserSchoolGetBean>(
                responseObservable,
                object : BaseRequestCallback<UserSchoolGetBean>() {
                    override fun onSuccess(data: UserSchoolGetBean) {
                        userSchoolGetBeanLiveData.postValue(data)
                    }

                    override fun dealFail(reason: ErrorReason) {}
                })
        }


    fun getEduLevelName(): String {
        if (userSchoolGetBeanLiveData.value == null) return ""
        return StringUtil.getEducationBackGround(userSchoolGetBeanLiveData.value!!.degree)
    }
}
