package com.kanzhun.marry.me.identify.activity;

import static com.kanzhun.common.kotlin.ext.ActivityExtKt.liveEventBusObserve;
import static com.kanzhun.foundation.kotlin.ktx.BundleExtKt.parsePageSourceFromBundle;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.kotlin.constract.LivedataKeyMe;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.ProtocolHelper;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.bean.UserSchoolGetBean;
import com.kanzhun.foundation.newtasktop.NewTaskTopFragment;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityEducationIdentifyChooseBinding;
import com.kanzhun.marry.me.databinding.MeLayoutItemEducaiotnIdentifyBinding;
import com.kanzhun.marry.me.identify.EducationIdentifyActivityHandler;
import com.kanzhun.marry.me.identify.callback.EducationIdentifyChooseCallback;
import com.kanzhun.marry.me.identify.model.EducationIdentifyBean;
import com.kanzhun.marry.me.identify.viewmodel.EducationIdentifyChooseViewModel;
import com.kanzhun.marry.me.point.MePointAction;
import com.kanzhun.marry.me.util.EduSubTitleHandler;
import com.kanzhun.utils.base.LList;
import com.sankuai.waimai.router.annotation.RouterUri;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * <AUTHOR>
 * @date 2022/3/18.
 * 选择认证方式
 */
@RouterUri(path = MePageRouter.ME_EDUCATION_IDENTIFY_CHOOSE_ACTIVITY)
public class EducationIdentifyChooseActivity extends FoundationVMActivity<MeActivityEducationIdentifyChooseBinding, EducationIdentifyChooseViewModel> implements EducationIdentifyChooseCallback {
    BaseBinderAdapter mainLandAdapter;
    BaseBinderAdapter otherAdapter;

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_education_identify_choose;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected boolean shouldUseNewGrayStatusBar() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mainLandAdapter = new BaseBinderAdapter();
        mainLandAdapter.addItemBinder(EducationIdentifyBean.class, new BaseDataBindingItemBinder<EducationIdentifyBean, MeLayoutItemEducaiotnIdentifyBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_layout_item_educaiotn_identify;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeLayoutItemEducaiotnIdentifyBinding> holder, MeLayoutItemEducaiotnIdentifyBinding binding, EducationIdentifyBean item) {
                binding.setCallback(EducationIdentifyChooseActivity.this);
                binding.setBean(item);
            }
        });
        otherAdapter = new BaseBinderAdapter();
        otherAdapter.addItemBinder(EducationIdentifyBean.class, new BaseDataBindingItemBinder<EducationIdentifyBean, MeLayoutItemEducaiotnIdentifyBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_layout_item_educaiotn_identify;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeLayoutItemEducaiotnIdentifyBinding> holder, MeLayoutItemEducaiotnIdentifyBinding binding, EducationIdentifyBean item) {
                binding.setCallback(EducationIdentifyChooseActivity.this);
                binding.setBean(item);
            }
        });
        getViewModel().certTypeList();
        getDataBinding().rvChineseMainlandEducation.setLayoutManager(new LinearLayoutManager(getBaseContext()));
        getDataBinding().rvChineseMainlandEducation.setAdapter(mainLandAdapter);

        getDataBinding().rvOtherEducation.setLayoutManager(new LinearLayoutManager(getBaseContext()));
        getDataBinding().rvOtherEducation.setAdapter(otherAdapter);

        getViewModel().getLoadSuccess().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    if (LList.getCount(getViewModel().getOther()) > 0 && LList.getCount(getViewModel().getMainLand()) > 0) {
                        mainLandAdapter.setNewInstance(getViewModel().getMainLand());
                        otherAdapter.setNewInstance(getViewModel().getOther());
                        getDataBinding().tvChineseMainlandEducation.setVisibility(View.VISIBLE);
                        getDataBinding().flMainlandEducation.setVisibility(View.VISIBLE);
                        getDataBinding().tvEducationIdentifyChooseTitle.setText("选择1个认证方式");

                        getDataBinding().tvOtherEducation.setVisibility(View.VISIBLE);
                        getDataBinding().flOtherEducation.setVisibility(View.VISIBLE);
                    } else if (LList.getCount(getViewModel().getMainLand()) > 0) {
                        mainLandAdapter.setNewInstance(getViewModel().getMainLand());

                        getDataBinding().tvChineseMainlandEducation.setVisibility(View.GONE);

                        getDataBinding().tvChineseMainlandEducation.setVisibility(View.GONE);
                        getDataBinding().flMainlandEducation.setVisibility(View.VISIBLE);
                        getDataBinding().tvEducationIdentifyChooseTitle.setText("中国大陆学历");

                        getDataBinding().tvOtherEducation.setVisibility(View.GONE);
                        getDataBinding().flOtherEducation.setVisibility(View.GONE);
                    } else {
                        otherAdapter.setNewInstance(getViewModel().getOther());

                        getDataBinding().tvEducationIdentifyChooseTitle.setText("中国港澳台/海外学历");

                        getDataBinding().tvChineseMainlandEducation.setVisibility(View.GONE);
                        getDataBinding().flMainlandEducation.setVisibility(View.GONE);

                        getDataBinding().tvOtherEducation.setVisibility(View.GONE);
                        getDataBinding().flOtherEducation.setVisibility(View.VISIBLE);
                    }

                }
            }
        });


        getViewModel().getUserSchoolGetBeanLiveData().observe(this, new Observer<UserSchoolGetBean>() {
            @Override
            public void onChanged(UserSchoolGetBean userSchoolGetBean) {
                setSpanText(userSchoolGetBean);
            }
        });
        if (getIntent().getSerializableExtra(BundleConstants.BUNDLE_DATA) instanceof UserSchoolGetBean) {
            UserSchoolGetBean mUserSchoolGetBean = (UserSchoolGetBean) getIntent().getSerializableExtra(BundleConstants.BUNDLE_DATA);
            if (mUserSchoolGetBean != null) {
                if (mUserSchoolGetBean.getEduCertStatus() == 1) {
                    EducationIdentifyActivityHandler.Companion.showCerting(getDataBinding().idIncludeCerting, mUserSchoolGetBean, new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            clickLeft(getDataBinding().idIncludeCerting.idBtn);
                            return null;
                        }
                    });
                    getDataBinding().rvChineseMainlandEducation.setVisibility(View.GONE);
                    return;
                }
                if (mUserSchoolGetBean.getEduCertStatus() == 2) {
                    EducationIdentifyActivityHandler.Companion.showAuthFailBottomDialog(EducationIdentifyChooseActivity.this, mUserSchoolGetBean, getPageSource());
                }
                getViewModel().getUserSchoolGetBeanLiveData().postValue(mUserSchoolGetBean);
            }
        } else {
            getViewModel().getData();
        }

        liveEventBusObserve(this, LivedataKeyMe.AUTH_EDU_COMMIT_SUCCESS, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                finish();
            }
        });

        getDataBinding().idFragmentTop.setVisibility(View.VISIBLE);
        getSupportFragmentManager().beginTransaction()
                .replace(getDataBinding().idFragmentTop.getId(), new NewTaskTopFragment(), CertificationActivity.class.getSimpleName())
                .commitAllowingStateLoss();
    }

    private PageSource getPageSource() {
        return parsePageSourceFromBundle(getIntent());
    }

    private void setSpanText(UserSchoolGetBean userSchoolGetBean) {
        EduSubTitleHandler.Companion.handlerUserSchoolGetBean(EducationIdentifyChooseActivity.this, getDataBinding().tvDesc, userSchoolGetBean, "", getPageSource());

        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_METHOD_EXPO, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setActionp2(StringUtil.getEducationBackGround(userSchoolGetBean.getDegree()));
                return null;
            }
        });
    }

    @Override
    protected void onNewIntent(@NonNull Intent intent) {
        super.onNewIntent(intent);
        initNetData();
    }

    private void initNetData() {
        getViewModel().getData();
        getViewModel().certTypeList();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            initNetData();
        }
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void onEducationIdentifyItemClick(EducationIdentifyBean bean) {
        // 追加pageSource到协议上
        PageSource pageSource = getPageSource();
        ProtocolHelper.parseProtocol(bean.getCert().getJumpUrl() + "&pageSource=" + pageSource.ordinal());
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_METHOD_CLICK, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                if (getViewModel().getUserSchoolGetBeanLiveData().getValue() != null) {
                    pointBean.setActionp2(StringUtil.getEducationBackGround(getViewModel().getUserSchoolGetBeanLiveData().getValue().getDegree()));
                }
                pointBean.setType(bean.getCert().getTitle());
                return null;
            }
        });
    }
}
