package com.kanzhun.marry.me.info.fragment;

import static android.app.Activity.RESULT_OK;

import android.app.Dialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.transition.Fade;
import android.view.View;

import androidx.activity.OnBackPressedCallback;
import androidx.core.app.ActivityOptionsCompat;
import androidx.core.util.Pair;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.FragmentActivity;

import com.common.AvoidOnResult;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.util.BitmapUtils;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.foundation.photoselect.PhotoSelectManager;
import com.kanzhun.foundation.utils.PathUtil;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentABImpressionEditBinding;
import com.kanzhun.marry.me.info.callback.ABImpressionEditCallback;
import com.kanzhun.marry.me.info.viewmodel.ABImpressionEditViewModel;
import com.kanzhun.marry.me.info.viewmodel.ABImpressionViewModel;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.file.FileUtils;
import com.kanzhun.utils.rxbus.RxBus;
import com.yalantis.ucrop.UCrop;
import com.yalantis.ucrop.UCropFragment;
import com.yalantis.ucrop.UCropFragmentCallback;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.internal.utils.PathUtils;

import java.io.File;
import java.io.Serializable;
import java.util.List;

public class ABImpressionEditFragment extends FoundationVMShareFragment<MeFragmentABImpressionEditBinding, ABImpressionEditViewModel, ABImpressionViewModel> implements ABImpressionEditCallback, UCropFragmentCallback {
    public static final String TAG = "ABImpressionEditFragment";
    public static final String AB_EDIT_URI = "ab_edit_uri";
    public static final String CONTENT_EDIT_TEXT = "content_edit_text";

    private String mSide;
    private boolean fileChanged = false;//重新编辑的状态下，是否重新选择过图片
    private String initTxt;
    private boolean textIsChanged;

    public static ABImpressionEditFragment getInstance(Bundle bundle) {
        ABImpressionEditFragment fragment = new ABImpressionEditFragment();
        bundle.putInt(UCrop.Options.EXTRA_CROP_FRAME_COLOR, BaseApplication.getApplication().getResources().getColor(R.color.common_translate));
        fragment.setArguments(bundle);
        return fragment;
    }

    private void initParams(Bundle bundle) {
        if (bundle != null) {
            mSide = bundle.getString(BundleConstants.BUNDLE_A_B_IMPRESSION_SIDE);
            getViewModel().setSide(mSide);
        }
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        getDataBinding().abEdit.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        initParams(getArguments());
//        if (TextUtils.isEmpty(getActivityViewModel().getId().get())) {//新增ab面用原有的图片裁剪逻辑
        getDataBinding().cropView.setCallback(this);
//        }
        getDataBinding().cropView.initData(getArguments());
        startPostponedEnterTransition();
        if (TextUtils.equals(mSide, ABImpressionViewModel.A_SIDE)) {
            getViewModel().setText(getActivityViewModel().getTextA().get());
        } else {
            getViewModel().setText(getActivityViewModel().getTextB().get());
        }
        initTxt = (getViewModel().getText().get() == null ? "" : getViewModel().getText().get().toString());
        RxBus.getInstance().subscribe(this, TAG, new RxBus.Callback<String>() {

            @Override
            public void onEvent(String s) {
                getDataBinding().etContent.setText(s);
            }
        });
        requireActivity().getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                clickLeft(null);
            }
        });
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_a_b_impression_edit;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        boolean picChanged = fileChanged || getDataBinding().cropView.isCropRectChanged();
        boolean txtChanged = !initTxt.equals(StringUtil.trimEAndN(getDataBinding().etContent.getText().toString()));

        if (picChanged || txtChanged) {
            CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(activity)
                    .setTitle(getString(R.string.me_intro_text_dialog_title))
                    .setPositiveText(getResources().getString(R.string.me_intro_text_dialog_p))
                    .setNegativeText(getResources().getString(R.string.me_intro_text_dialog_n))
                    .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                        @Override
                        public void onPositiveClick(Dialog dialog, View view) {

                        }

                        @Override
                        public void onNegativeClick(Dialog dialog, View view) {
                            getParentFragmentManager().popBackStack();
                        }
                    });
            builder.create().show();
        } else {
            getParentFragmentManager().popBackStack();
        }
    }


    @Override
    public void clickRight(View view) {
        if (StringUtil.trimEAndN(getViewModel().getText().get()).length() < 2) {
            T.ss(R.string.me_edit_min_2_length);
            clickDescribe();
        } else {
            if (getActivityViewModel().hasRejected()) {//是否还有未消除的被驳回状态
                boolean picChanged = fileChanged || getDataBinding().cropView.isCropRectChanged();
                if (!picChanged) {
                    if (TextUtils.equals(mSide, ABImpressionViewModel.A_SIDE)) {
                        getActivityViewModel().setReEditTextA(getViewModel().getText().get());
                    } else {
                        getActivityViewModel().setReEditTextB(getViewModel().getText().get());
                    }
                    ((FragmentActivity) activity).getSupportFragmentManager().popBackStack();
                } else {
                    ExecutorFactory.execLocalTask(new Runnable() {
                        @Override
                        public void run() {
                            Uri uri = getDataBinding().cropView.getCropImage();
                            if (uri != null) {
                                if (TextUtils.equals(mSide, ABImpressionViewModel.A_SIDE)) {
                                    getActivityViewModel().serReEditPicA(uri);
                                    getActivityViewModel().setReEditTextA(getViewModel().getText().get());
                                } else {
                                    getActivityViewModel().setReEditPicB(uri);
                                    getActivityViewModel().setReEditTextB(getViewModel().getText().get());
                                }
                                ExecutorFactory.execMainTask(new Runnable() {
                                    @Override
                                    public void run() {
                                        ((FragmentActivity) activity).getSupportFragmentManager().popBackStack();
                                    }
                                });
                            } else {
                                T.ss("裁剪失败，请选择其他图片");
                            }
                        }
                    });
                }
            } else {
                cropAndSaveImage();
            }
        }
    }

    @Override
    public void clickDescribe() {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                Bitmap bitmap = BitmapUtils.getCacheBitmapFromView(getDataBinding().cropView.getGestureCropImageView());
                if (bitmap != null) {
                    getActivityViewModel().setPreviewBitmap(bitmap);
//                    File file = new File(getActivityViewModel().getPictureOutputFile(TAG).getPath());
                    File file = new File(PathUtil.getPictureOutputFile(activity, null, TAG).getPath());
                    if (file.exists()) {
                        file.delete();
                    }
//                    file = FileUtils.saveBitmapToFile(bitmap, getActivityViewModel().getPictureOutputFile(System.currentTimeMillis() + "").getPath());
                    file = FileUtils.saveBitmapToFile(bitmap, PathUtil.getPictureOutputFile(activity, null, System.currentTimeMillis() + "").getPath());
                    File finalFile = file;
                    ExecutorFactory.execMainTask(new Runnable() {
                        @Override
                        public void run() {
                            android.transition.Fade fade = new Fade(); //渐隐
                            fade.setDuration(300);
                            setReenterTransition(fade);
                            setExitTransition(fade);
                            setAllowEnterTransitionOverlap(false);
                            setAllowReturnTransitionOverlap(false);
                            Intent intent = new Intent(getContext(), ABImpressionContentEditActivity.class);
                            intent.putExtra(AB_EDIT_URI, Uri.fromFile(finalFile));
                            intent.putExtra(CONTENT_EDIT_TEXT, getViewModel().getText().get());
                            Pair<View, String>[] pairs = new Pair[]{Pair.create(getDataBinding().cropView, ViewCompat.getTransitionName(getDataBinding().cropView)), Pair.create(getDataBinding().etContent, ViewCompat.getTransitionName(getDataBinding().etContent))};
                            ActivityOptionsCompat transitionActivityOptions = ActivityOptionsCompat.makeSceneTransitionAnimation(activity, pairs);
                            startActivity(intent, transitionActivityOptions.toBundle());
                        }
                    });
                }
            }
        });
    }

    @Override
    public void clickReselect() {
        jumpGallery();
    }

    private void jumpGallery() {
        PhotoSelectManager.jumpForGalleryOnlyImageToCropResult((FragmentActivity) activity, 1, new AvoidOnResult.Callback() {
            @Override
            public void onActivityResult(int requestCode, int resultCode, Intent data) {
                if (resultCode == RESULT_OK && data != null) {
                    List<Uri> result = Matisse.obtainResult(data);
                    if (LList.getCount(result) == 1) {
                        Uri source = result.get(0);
                        if (source != null) {
//                            switch (mSide) {
//                                case ABImpressionViewModel.A_SIDE:
//                                    getActivityViewModel().setPathA(source.toString());
////                                    getActivityViewModel().setTextA(getViewModel().getText().get());
//                                    break;
//                                case ABImpressionViewModel.B_SIDE:
//                                    getActivityViewModel().setPathB(source.toString());
////                                    getActivityViewModel().setTextB(getViewModel().getText().get());
//                                    break;
//                            }
                            fileChanged = true;
//                            getDataBinding().cropView.reSetUri(source);
//                            Uri destination = Uri.fromFile(PathUtils.getPictureOutputFile(activity));
                            Uri destination = Uri.fromFile(PathUtil.getPictureOutputFile(activity, source, ""));
                            Bundle mCropOptionsBundle = new Bundle();
                            mCropOptionsBundle.putParcelable(UCrop.EXTRA_INPUT_URI, source);
                            mCropOptionsBundle.putParcelable(UCrop.EXTRA_OUTPUT_URI, destination);
                            mCropOptionsBundle.putInt(UCrop.Options.EXTRA_CROP_FRAME_COLOR, BaseApplication.getApplication().getResources().getColor(R.color.common_translate));
                            getDataBinding().cropView.initData(mCropOptionsBundle);
                        }
//                        Bundle bundle = getArguments();
//                        bundle.putParcelable(UCrop.EXTRA_INPUT_URI, source);
                    }
                }
            }
        });
    }

    protected void cropAndSaveImage() {
        getDataBinding().cropView.onCropListener();
    }

    @Override
    public void loadingProgress(boolean showLoader) {
    }

    @Override
    public void onCropFinish(UCropFragment.UCropResult result) {
        Intent mResultData = result.mResultData;
        if (result.mResultCode == RESULT_OK) {
//            activity.setResult(RESULT_OK,mResultData);
            Uri uri = mResultData.getParcelableExtra(UCrop.EXTRA_OUTPUT_URI);
            if (TextUtils.equals(mSide, ABImpressionViewModel.A_SIDE)) {
                getActivityViewModel().setPathA(uri.toString());
                getActivityViewModel().setInitialUriA(uri);
                getActivityViewModel().setUrlA("");
                getActivityViewModel().setTextA(getViewModel().getText().get());
            } else {
                getActivityViewModel().setPathB(uri.toString());
                getActivityViewModel().setInitialUriB(uri);
                getActivityViewModel().setUrlB("");
                getActivityViewModel().setTextB(getViewModel().getText().get());
            }
            try {
                ((FragmentActivity) activity).getSupportFragmentManager().popBackStack();
            }catch (Exception e){
            }
        } else if (result.mResultCode == UCrop.RESULT_ERROR) {
            //region 上报裁剪异常原因
            if (mResultData != null) {
                Serializable error = mResultData.getSerializableExtra(UCrop.EXTRA_ERROR);
                if (error instanceof Throwable) {
                }
            }
            T.ss("裁剪失败，请选择其他图片");
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        RxBus.getInstance().unregister(this);
    }
}