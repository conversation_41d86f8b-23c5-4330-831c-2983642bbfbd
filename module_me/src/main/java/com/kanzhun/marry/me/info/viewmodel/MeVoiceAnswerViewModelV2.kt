package com.kanzhun.marry.me.info.viewmodel

import android.net.Uri
import android.text.TextUtils
import android.util.ArrayMap
import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.bean.VoiceExtInfoBean
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.api.model.VoiceUploadModel
import com.kanzhun.foundation.model.profile.QuestionAnswerSuccessModel
import com.kanzhun.foundation.model.profile.QuestionTemplateModel
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.UploadFileUtil
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.download.callback.DownLoadListener
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.http.upload.UploadRequestCallback
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.utils.GsonUtils
import com.kanzhun.utils.T
import com.kanzhun.utils.file.FileUtils
import io.reactivex.rxjava3.disposables.Disposable
import java.io.File

class MeVoiceAnswerViewModelV2:BaseViewModel() {

    var answerType: Int = MePageRouter.TYPE_ANSWER_VOICE

    var isRelSelect: Boolean = false //是否是重新录音

    //问题答案
    var mQuestionAnswer: ProfileInfoModel.QuestionAnswer? = null

    //问题
    var mQuestionInfoBean: QuestionTemplateModel.QuestionInfoBean? = null

    //语音bean
    private var voiceInfoBean: VoiceExtInfoBean? = null

    private var voiceUrl: String? = null

    val successLiveData: MutableLiveData<QuestionAnswerSuccessModel?> = MutableLiveData()

    val updateSuccessLiveData: MutableLiveData<Boolean?> = MutableLiveData()

    val voiceSaveLiveData = MutableLiveData<Boolean>()

    val voiceDownloadLiveData = MutableLiveData<File?>()

    var hasVoiceFile:Boolean = false

    fun questionId(): Long = mQuestionInfoBean?.id ?: 0L

    fun question(): String = mQuestionInfoBean?.name ?: ""


    fun answerId(): String = mQuestionAnswer?.id ?: ""
    /**
     * 是否是编辑问题答案
     */
    fun isEditMode(): Boolean = answerId().isNotBlank()

    fun setVoiceAnswer(answer: ProfileInfoModel.QuestionAnswer?){
        this.mQuestionAnswer = answer
        this.voiceUrl = answer?.answer
        try {
            voiceInfoBean = GsonUtils.getGson().fromJson<VoiceExtInfoBean>(answer?.extendInfo, VoiceExtInfoBean::class.java)
        }catch (e:Exception){
            e.printStackTrace()
        }

    }


    fun voiceSave(file: File?, duration: Int, wave: IntArray?) {
        val infoBean = VoiceExtInfoBean()
        infoBean.duration = duration
        infoBean.wave = wave
        showLoadingDialog(true)
        UploadFileUtil.uploadVoice(UploadFileUtil.PROFILE, Uri.fromFile(file), infoBean, object : UploadRequestCallback<VoiceUploadModel?>() {
            override fun handleInChildThread(data: VoiceUploadModel?) {
                super.handleInChildThread(data)
            }

           override fun onSuccess(data: VoiceUploadModel?) {
                if (TextUtils.isEmpty(answerId())) {
                    answerSave(data?.token){
                        reNameVoiceFile(data,file)
                    }
                } else {
                    answerUpdate(data?.token){
                        reNameVoiceFile(data,file)
                    }
                }
            }

            override fun dealFail(reason: ErrorReason) {
                showLoadingDialog(false)
            }
        })
    }

    private fun reNameVoiceFile(data: VoiceUploadModel?, file: File?) {
        ExecutorFactory.execLocalTask { FileUtils.reNameFile(data?.url, file?.absolutePath) }
    }

    fun setHaveVoiceFile(haveSuccess: Boolean) {
        hasVoiceFile = haveSuccess
    }

    fun voiceAnswerSave() {
        voiceSaveLiveData.postValue(true)
    }


    fun getVoiceInfoBean(): VoiceExtInfoBean? {
        return voiceInfoBean
    }

    fun voiceDownload() {
        if (TextUtils.isEmpty(voiceUrl)) {
            return
        }
        showLoadingDialog(true)
        HttpExecutor.downLoadFile(voiceUrl, object : DownLoadListener<File?>() {
            override fun onSuccess(file: File?) {
                showLoadingDialog(false)
                voiceDownloadLiveData.value = file
            }

            override fun onFail(e: Throwable) {
                showLoadingDialog(false)
                T.ss(e.message)
            }

            override fun onProgress(percent: Int) {}
        })
    }

    fun answerSave(answer: String?,callback:()->Unit) {
        val map = ArrayMap<String, Any>()
        map["answerType"] = answerType
        map["question"] = question()
        map["answer"] = answer
        val questionId: Long = questionId()
        if (questionId > 0) {
            map["questionId"] = questionId
        }
        val optionString = mQuestionInfoBean?.optionString
        if (!optionString.isNullOrBlank()) {
            map["answerOptions"] = optionString
        }
        val responseObservable = RetrofitManager.getInstance().createApi(MeApi::class.java).requestAddQuestionAnswer(map)
        HttpExecutor.execute<QuestionAnswerSuccessModel>(responseObservable, object : BaseRequestCallback<QuestionAnswerSuccessModel?>(true) {
            override fun onStart(disposable: Disposable) {
                super.onStart(disposable)
                showLoadingDialog(true)
            }

            override fun onSuccess(data: QuestionAnswerSuccessModel?) {
                callback()
                successLiveData.value = data
            }

            override fun dealFail(reason: ErrorReason) {

            }
            override fun onComplete() {
                showLoadingDialog(false)
            }
        })
    }


    fun answerUpdate(answer: String?,callback:()->Unit) {
        val params: MutableMap<String, Any> = HashMap()
        params["id"] = answerId()
        params["answer"] = answer?:""
        val optionString = mQuestionInfoBean?.optionString
        if (!optionString.isNullOrBlank()) {
            params["answerOptions"] = optionString
        }
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_UPDATE_QUESTION_ANSWER, params, object : SimpleRequestCallback(true) {
            override fun onStart(disposable: Disposable) {
                super.onStart(disposable)
                showLoadingDialog(true)
            }

            override fun onSuccess() {
                callback()
                updateSuccessLiveData.setValue(true)
            }

            override fun dealFail(reason: ErrorReason) {}
            override fun onComplete() {
                super.onComplete()
                showLoadingDialog(false)
            }
        })
    }



    fun answerDel() {
        val params: MutableMap<String, Any> = HashMap()
        params["id"] = answerId()
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_DEL_QUESTION_ANSWER, params, object : SimpleRequestCallback(true) {
            override fun onStart(disposable: Disposable) {
                super.onStart(disposable)
                showLoadingDialog(true)
            }

            override fun onSuccess() {
                updateSuccessLiveData.value = true
            }

            override fun dealFail(reason: ErrorReason) {}
            override fun onComplete() {
                super.onComplete()
                showLoadingDialog(false)
            }
        })
    }


}