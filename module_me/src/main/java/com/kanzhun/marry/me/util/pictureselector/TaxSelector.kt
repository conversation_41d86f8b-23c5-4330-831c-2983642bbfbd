package com.kanzhun.marry.me.util.pictureselector

import android.app.Activity
import android.net.Uri
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.kanzhun.foundation.photoselect.PhotoSelectManager
import com.kanzhun.marry.me.databinding.MeLayoutTaxSelectRuleBinding
import com.kanzhun.utils.base.LList
import com.zhihu.matisse.Matisse

class TaxSelector(
    val context: FragmentActivity,
    clickCallback: (type: PictureSelectorItemType) -> Unit = {},
    val callback: (cameraFileUri: Uri, isGallery: Boolean) -> Unit,
) : BasePictureSelector(context, clickCallback, callback) {

    override fun getHeaderView(): View {
        val binding = MeLayoutTaxSelectRuleBinding.inflate(context.layoutInflater)
        return binding.root
    }

    override fun isCameraEnabled(): Boolean = false

    override fun onGalleryClick() {
        PhotoSelectManager.jumpForGalleryOnlyVideoResult(
            /* context = */ activity,
            /* videoCount = */ 1
        ) { _, resultCode, data ->
            if (resultCode == Activity.RESULT_OK && data != null) {
                val result = Matisse.obtainResult(data)
                if (LList.getCount<Uri>(result) == 1) {
                    val source = result[0]
                    if (source != null) {
                        callback.invoke(source, true)
                    }
                }
            }
        }
    }

}