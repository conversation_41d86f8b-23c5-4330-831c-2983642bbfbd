package com.kanzhun.marry.me.identify.viewmodel;

import android.app.Application;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.kanzhun.foundation.base.FoundationViewModel;

/**
 * <AUTHOR>
 * @date 2022/3/18.
 */
public class ResultViewModel extends FoundationViewModel {
    ObservableBoolean showResult = new ObservableBoolean(true);
    ObservableField<String> title = new ObservableField<String>();
    ObservableField<String> assist = new ObservableField<String>();
    ObservableField<String> submit = new ObservableField<String>();

    public ResultViewModel(Application application) {
        super(application);
    }

    public ObservableBoolean getShowResult() {
        return showResult;
    }

    public ObservableField<String> getTitle() {
        return title;
    }

    public ObservableField<String> getAssist() {
        return assist;
    }

    public ObservableField<String> getSubmit() {
        return submit;
    }

    public void setShowResult(boolean showResult) {
        this.showResult.set(showResult);
    }

    public void setTitle(String title) {
        this.title.set(title);
    }

    public void setAssist(String assist) {
        this.assist.set(assist);
    }

    public void setSubmit(String submit) {
        this.submit.set(submit);
    }

}
