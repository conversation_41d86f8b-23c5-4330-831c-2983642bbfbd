package com.kanzhun.marry.me.identify.fragment.car

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.MotionEvent
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.text.buildSpannedString
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.button.enableBlackButton
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.foundation.api.base.H5URL
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.foundation.kotlin.common.performance.PerformManager
import com.kanzhun.foundation.kotlin.ktx.hasEmoji
import com.kanzhun.foundation.kotlin.ktx.toCompleteH5Link
import com.kanzhun.foundation.kotlin.ktx.toCompleteLink
import com.kanzhun.foundation.photoselect.upload.SinglePictureUploadHelper
import com.kanzhun.foundation.photoselect.upload.UploadResult
import com.kanzhun.foundation.photoselect.upload.UploadSceneType
import com.kanzhun.foundation.photoselect.upload.UploadStatus
import com.kanzhun.foundation.utils.point.DurationPointPerformance
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.imageviewer.ext.imageViewer
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.databinding.MeFragmentCarAuthEditBinding
import com.kanzhun.marry.me.identify.activity.CAR_AUTH_STATUS_SUBMIT
import com.kanzhun.marry.me.identify.viewmodel.CarAuthViewModel
import com.kanzhun.marry.me.point.MePointReporter
import com.kanzhun.marry.me.util.AuthFailDialogType
import com.kanzhun.marry.me.util.AuthFailDialogUtil
import com.kanzhun.marry.me.util.IBackPressFragmentDelegate
import com.kanzhun.marry.me.util.pictureselector.PictureSelectorManager
import com.kanzhun.utils.T
import com.kanzhun.utils.string.appendClickable
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton

/**
 * <AUTHOR>
 * 车产认证编辑页
 */

class CarAuthEditFragment :
    BaseBindingFragment<MeFragmentCarAuthEditBinding, CarAuthEditViewModel>(),
    IBackPressFragmentDelegate {
    private var pageSource: PageSource? = PageSource.NONE

    fun setPageSource(source: PageSource?) {
        pageSource = source
    }

    override var showInterceptBackPressDialog = true

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    override fun preInit(arguments: Bundle) {
        performManager.addPerformance(object : DurationPointPerformance() {
            override fun callback(durationSecond: Long) {
                MePointReporter.reportCarEditExpose(durationSecond)
            }

        })

    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initView() {
        mBinding.run {
            appbar.asBackButton {
                MePointReporter.reportCarEditClick("返回")
            }.setRightIcon(R.drawable.common_ic_help) {
                ProtocolHelper.parseProtocol(H5URL.URL_H5_CAR_FAQ.toCompleteH5Link())
                MePointReporter.reportCarEditClick("认证遇到问题icon")
            }
            //车主
            etInputOwner.addTextChangedListener {
                mViewModel.userName = it.toString().trim()
                checkSubmitButtonStatus()
                if (etInputOwner.text.toString().hasEmoji()) {
                    mViewModel.reportSubmitPoint(
                        false,
                        R.string.me_car_username_input_error_toast.toResourceString()
                    )
                    mViewModel.oneError.postValue(R.string.me_car_username_input_error_toast.toResourceString())
                } else {
                    mViewModel.oneError.postValue("")
                }
            }
            etInputOwner.setOnTouchListener { _, event ->
                if (event.action == MotionEvent.ACTION_DOWN) {
                    MePointReporter.reportCarEditClick("车辆所有人姓名")
                }
                false
            }
            //车牌号码
            etInputCertificateNumber.addTextChangedListener {
                mViewModel.licensePlateNumber = it.toString().trim()
                checkSubmitButtonStatus()
                if (!mViewModel.isLicensePlateNumberMatch() || mViewModel.licensePlateNumber.hasEmoji()) {
                    mViewModel.reportSubmitPoint(
                        false,
                        R.string.me_car_number_input_error_toast.toResourceString()
                    )
                    mViewModel.twoError.postValue(R.string.me_car_number_input_error_toast.toResourceString())
                } else {
                    mViewModel.twoError.postValue("")
                }
            }
            etInputCertificateNumber.setOnTouchListener { _, event ->
                if (event.action == MotionEvent.ACTION_DOWN) {
                    MePointReporter.reportCarEditClick("车牌号码")
                }
                false
            }
            //上传图片1
            btUpload1.clickWithTrigger {
                selectImage1()
                MePointReporter.reportCarEditClick("点击上传正面")

            }
            tvReUploadPositive.clickWithTrigger {
                selectImage1()
                MePointReporter.reportCarEditClick("重新上传正面")
            }
            //第一个图片点击放大
            vCarAuthPositive.clickWithTrigger {
                if (!mViewModel.imageUrl1.isNullOrBlank()) {
                    ivCarAuthPositive.imageViewer(mViewModel.imageUrl1 ?: "")
                }
            }

            //上传图片2
            btUpload2.clickWithTrigger {
                selectImage2()
                MePointReporter.reportCarEditClick("点击上传背面")

            }
            tvReUploadNegative.clickWithTrigger {
                selectImage2()
                MePointReporter.reportCarEditClick("重新上传背面")
            }

            //第二个图片点击放大
            vCarAuthNegative.clickWithTrigger {
                if (!mViewModel.imageUrl2.isNullOrBlank()) {
                    ivCarAuthNegative.imageViewer(mViewModel.imageUrl2 ?: "")
                }
            }
            //提交数据
            btnNext.clickWithTrigger {
                submit()
            }
            initInputOneError()
            initInputTwoError()
        }

        mBinding.btnNext.enableBlackButton(false)
        setCarAuthNote()


    }


    private fun MeFragmentCarAuthEditBinding.initInputOneError() {
        mViewModel.oneError.observe(this@CarAuthEditFragment) {
            if (it.isNullOrEmpty()) {
                idError1.gone()
                idLine1.setBackgroundColor(R.color.common_color_F0F0F0.toResourceColor())
            } else {
                idLine1.setBackgroundColor(R.color.common_color_FF0000.toResourceColor())
                idError1.text = mViewModel.oneError.value.toString()
                idError1.visible()
            }
        }
    }

    private fun MeFragmentCarAuthEditBinding.initInputTwoError() {
        mViewModel.twoError.observe(this@CarAuthEditFragment) {
            if (it.isNullOrEmpty()) {
                idError2.gone()
                idLine2.setBackgroundColor(R.color.common_color_F0F0F0.toResourceColor())
            } else {
                idLine2.setBackgroundColor(R.color.common_color_FF0000.toResourceColor())
                idError2.text = mViewModel.twoError.value.toString()
                idError2.visible()
            }
        }
    }

    private fun selectImage1() {
        PictureSelectorManager.startSelect(
            activity = requireActivity(),
            type = PictureSelectorManager.SelectType.CAR_1,
            enableAlbum = pageSource != PageSource.AUTH_RECERT_ACTIVITY,
        ) { cameraFileUri, _ ->
            SinglePictureUploadHelper(
                mViewModel,
                UploadSceneType.AUTH_SOURCE,
                mViewModel.uploadResult1
            ).upload(cameraFileUri)
        }
    }

    private fun selectImage2() {
        PictureSelectorManager.startSelect(
            activity = requireActivity(),
            type = PictureSelectorManager.SelectType.CAR_2,
            enableAlbum = pageSource != PageSource.AUTH_RECERT_ACTIVITY,
        ) { cameraFileUri, _ ->
            SinglePictureUploadHelper(
                mViewModel,
                UploadSceneType.AUTH_SOURCE,
                mViewModel.uploadResult2
            ).upload(cameraFileUri)
        }
    }


    private fun checkSubmitButtonStatus() {
        mBinding.btnNext.enableBlackButton(mViewModel.isInputComplete())
    }

    private fun submit() {

        if (mViewModel.canSubmit()) {
            showProgressDialog("提交中...")
            mViewModel.submit()
        }


    }

    override fun initData() {
        //第一个图片上传结果监听
        mViewModel.uploadResult1.observe(this) {
            when (it.status) {
                UploadStatus.SUCCESS -> { //上传成功
                    mBinding.vCarAuthPositive.visible()
                    mBinding.ivCarAuthPositive.load(it.result?.originImageUrl)
                    mViewModel.imgToken = it.result?.token
                    mViewModel.imageUrl1 = it.result?.originImageUrl
                    checkSubmitButtonStatus()
                    setUploadButtonStatus(mBinding.btUpload1, mBinding.tvReUploadPositive, 2)
                }

                UploadStatus.FAIL -> { //上传失败
                    setUploadButtonStatus(mBinding.btUpload1, mBinding.tvReUploadPositive, 2)
                }

                else -> { //上传中
                    setUploadButtonStatus(mBinding.btUpload1, mBinding.tvReUploadPositive, 1)

                }
            }

        }

        //第二个图片上传结果监听
        mViewModel.uploadResult2.observe(this) {
            when (it.status) {
                UploadStatus.SUCCESS -> { //上传成功
                    mBinding.vCarAuthNegative.visible()
                    mBinding.ivCarAuthNegative.load(it.result?.originImageUrl)
                    mViewModel.imgTokenSecond = it.result?.token
                    mViewModel.imageUrl2 = it.result?.originImageUrl
                    checkSubmitButtonStatus()
                    setUploadButtonStatus(mBinding.btUpload2, mBinding.tvReUploadNegative, 2)
                }

                UploadStatus.FAIL -> { //上传失败
                    setUploadButtonStatus(mBinding.btUpload2, mBinding.tvReUploadNegative, 2)
                }

                else -> { //上传中
                    setUploadButtonStatus(mBinding.btUpload2, mBinding.tvReUploadNegative, 1)

                }
            }

        }

        mViewModel.submitResult.observe(this) {
            dismissProgressDialog()
            if (it) {
                AuthFailDialogUtil.reset(AuthFailDialogType.CAR_AUTH_FAIL)
                jumpSubmitSuccessPage()
                LiveEventBus.post(LivedataKeyMe.AUTH_CENTER_ACTIVITY_REFRESH, true)
            }
        }

    }

    private fun jumpSubmitSuccessPage() {
        val parentViewModel = ViewModelProvider(requireActivity()).get(CarAuthViewModel::class.java)
        parentViewModel.carAuthStatusLiveData.value = CAR_AUTH_STATUS_SUBMIT
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null

    private fun setCarAuthNote() {
        mBinding.tvBottomNote.movementMethod = LinkMovementMethod.getInstance()
        mBinding.tvBottomNote.text = buildSpannedString {
            append("* 您可以上传车辆行驶证进行车产信息认证，信息仅用于审核并在平台进行标签展示，您和您车产证中的任何信息都不会被平台泄漏，可查看")
            appendClickable(
                "认证后展示效果",
                ContextCompat.getColor(requireContext(), R.color.common_color_005EFF),
                false
            ) {
                ProtocolHelper.parseProtocol(H5URL.URL_H5_CERT_CENTER_NO_HEAD_EFFECT.toCompleteLink())
                MePointReporter.reportCarEditClick("认证后展示效果")
            }
            append("\n\n* 认证材料不可以是网络截图，若您使用网络截图并经举报查证，平台将封禁您的平台账号\n\n")
            append("* 上传文件格式支持png，jpg等图片格式\n\n")
        }
    }

    /**
     * @param status 0 点击上传，1 上传中，2 重新上传
     */
    private fun setUploadButtonStatus(
        button: QMUIRoundButton,
        reUploadButton: TextView,
        status: Int
    ) {
        when (status) {
            2 -> {
                button.gone()
                reUploadButton.visible()
                button.isClickable = true
            }

            1 -> {
                button.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
                button.text = getString(R.string.common_up_loading)
                reUploadButton.gone()
                button.visible()
                button.isClickable = false
            }

            else -> {
                button.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    R.drawable.me_ic_icon_graduation_upload,
                    0,
                    0,
                    0
                )
                button.text = getString(R.string.me_click_and_upload)
                reUploadButton.gone()
                button.visible()
            }
        }
    }

    override fun needInterceptBackPress(): Boolean {
        return (showInterceptBackPressDialog && mViewModel.hasInput()).also {
            if (it) {
                showInterceptBackPressDialog = false
            }
        }
    }
}

class CarAuthEditViewModel : BaseViewModel() {
    var userName: String? = ""
    var licensePlateNumber: String? = ""
    var imgToken: String? = ""
    var imgTokenSecond: String? = ""

    var imageUrl1: String? = ""

    var imageUrl2: String? = ""

    //提交结果
    var submitResult: MutableLiveData<Boolean> = MutableLiveData()

    //第一个图片上传结果
    var uploadResult1: MutableLiveData<UploadResult> = MutableLiveData()

    //第二个图片上传结果
    var uploadResult2: MutableLiveData<UploadResult> = MutableLiveData()

    //失败次数，埋点用
    private var failCount = 0

    var oneError: MutableLiveData<String> = MutableLiveData()
    var twoError: MutableLiveData<String> = MutableLiveData()


    fun isInputComplete(): Boolean {
        return !userName.isNullOrBlank()
                && !licensePlateNumber.isNullOrBlank()
                && !imgToken.isNullOrBlank()
                && !imgTokenSecond.isNullOrBlank()
                && !userName.hasEmoji()
                && !licensePlateNumber.hasEmoji()
                && isLicensePlateNumberMatch()
    }

    fun hasInput(): Boolean {
        return !userName.isNullOrBlank()
                || !licensePlateNumber.isNullOrBlank()
                || !imgToken.isNullOrBlank()
                || !imgTokenSecond.isNullOrBlank()
    }

    fun isLicensePlateNumberMatch(): Boolean {
        val length = licensePlateNumber?.length ?: 0
        return length >= 5
    }

    fun canSubmit(): Boolean {
        if (userName.hasEmoji()) {
            T.ss(R.string.me_car_username_input_error_toast)
            return false
        }
        if (!isLicensePlateNumberMatch() || licensePlateNumber.hasEmoji()) {
            T.ss(R.string.me_car_number_input_error_toast)
            return false
        }
        return true
    }

    fun submit() {
        val observable = RetrofitManager.getInstance().createApi<MeApi>(MeApi::class.java)
            .certCarSubmit(userName, licensePlateNumber, imgToken, imgTokenSecond)
        HttpExecutor.execute<Any>(observable, object : BaseRequestCallback<Any?>(true) {
            override fun onSuccess(data: Any?) {
                submitResult.value = true
                reportSubmitPoint(true, "提交成功")
            }

            override fun dealFail(reason: ErrorReason?) {
                submitResult.value = false
                failCount++
                reportSubmitPoint(false, reason?.errReason)

            }
        })

    }

    /**
     * 上报提交埋点
     */
    fun reportSubmitPoint(success: Boolean, msg: String?) {
        MePointReporter.reportCarEditClickSubmit(success, errorCount = failCount, msg)
    }

}
