package com.kanzhun.marry.me.identify.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.TextureView;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import com.bzl.security.verify.interfaces.ICallbackCode;
import com.bzl.security.verify.interfaces.IIdentityCollectCallback;
import com.bzl.security.verify.interfaces.IOpenFaceDetectCallback;
import com.hpbr.apm.event.ApmAnalyzer;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.kotlin.constract.LivedataKeyTask;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.SecurityUtils;
import com.kanzhun.common.util.liveeventbus.LiveEventBus;
import com.kanzhun.foundation.BuildConfig;
import com.kanzhun.foundation.RequestCodeConstants;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.UserGuideBlockInfoBean;
import com.kanzhun.foundation.model.profile.FaceGetOrderIdBean;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.CertificationIdentifySource;
import com.kanzhun.foundation.utils.GlobalStatusUtil;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityResultBinding;
import com.kanzhun.marry.me.identify.ZPMegLiveUtil;
import com.kanzhun.marry.me.identify.callback.ZPLiveStillErrorCode;
import com.kanzhun.marry.me.identify.viewmodel.FaceVerifyViewModel;
import com.kanzhun.marry.me.point.MePointAction;
import com.kanzhun.safetyfacesdk.SafetyFaceDetectErrorCode;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.T;
import com.kanzhun.utils.rxbus.RxBus;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.techwolf.lib.tlog.TLog;
import com.twl.anti.CheatChecker;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * <AUTHOR>
 * @date 2022/4/6.
 */
@RouterUri(path = MePageRouter.FACE_VERIFY_ACTIVITY)
public class FaceVerifyActivity extends ResultActivity<MeActivityResultBinding, FaceVerifyViewModel> implements IIdentityCollectCallback, IOpenFaceDetectCallback {
    public static final int CERTIFICATION_SUCCESS = 0;//人脸识别认证成功
    public static final int CERTIFICATION_INFO_BIND = 1222;//该身份证已被绑定
    public static final int CERTIFICATION_INFO_ERROR = 1223;//身份信息有误
    public static final int CERTIFICATION_OVER_TIME = 1224;//人脸识别认超限
    public static final int CERTIFICATION_FAIL = 1225;//人脸识别认证失败,Sdk,server校验都是这个code
    public static final int CERTIFICATION_FAIL_1244 = 1244;//人脸识别失败


    String name;
    String idNum;
    String from;
    String certType;
    private UserGuideBlockInfoBean mUserGuideBlockInfoBean;

    public static Intent createIntent(Context context, String name, String idNum) {
        Intent intent = new Intent(context, FaceVerifyActivity.class);
        intent.putExtra(BundleConstants.BUNDLE_IDENTIFY_NAME, name);
        intent.putExtra(BundleConstants.BUNDLE_IDENTIFY_NUM, idNum);
        return intent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        name = getIntent().getStringExtra(BundleConstants.BUNDLE_IDENTIFY_NAME);
        idNum = getIntent().getStringExtra(BundleConstants.BUNDLE_IDENTIFY_NUM);
        from = getIntent().getStringExtra(BundleConstants.BUNDLE_FROM);
        certType = getIntent().getStringExtra(BundleConstants.BUNDLE_CERT_TYPE);
        if(TextUtils.isEmpty(certType)){
            certType = "1";//1-实名人脸；；2-二次人脸；3-更换设备人脸
        }
        if (getIntent().getSerializableExtra(BundleConstants.BUNDLE_USER_GUIDE_BLOCK_INFO_BEAN) instanceof UserGuideBlockInfoBean) {
            mUserGuideBlockInfoBean = (UserGuideBlockInfoBean) getIntent().getSerializableExtra(BundleConstants.BUNDLE_USER_GUIDE_BLOCK_INFO_BEAN);
        }
        PointHelperKt.reportPoint("facial-page-expo", new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                if("1".equals(certType)){
                    pointBean.setSource("实名人脸");
                }else {
                    pointBean.setSource("二次人脸认证");
                }
                return null;
            }
        });
        getViewModel().getOrderId(name,idNum,certType);
        getViewModel().mFaceGetOrderIdBean.observe(this, new Observer<FaceGetOrderIdBean>() {
            @Override
            public void onChanged(FaceGetOrderIdBean faceGetOrderIdBean) {
                if(TextUtils.isEmpty(name)){
                    name = SecurityUtils.rc4Decrypt(faceGetOrderIdBean.getName(), SettingBuilder.getInstance().getApmUidPassword());
                }
                if(TextUtils.isEmpty(idNum)){
                    idNum = SecurityUtils.rc4Decrypt(faceGetOrderIdBean.getIdCard(),SettingBuilder.getInstance().getApmUidPassword());
                }
                ZPMegLiveUtil.startFaceVerify(FaceVerifyActivity.this,
                        faceGetOrderIdBean.getOrderId(),FaceVerifyActivity.this,
                        faceGetOrderIdBean.getNeedPreRecord() == 1,
                        faceGetOrderIdBean.getUseNewModel() == 1,name,idNum,
                        faceGetOrderIdBean.getFaceType(),faceGetOrderIdBean.getFaceTicket(),
                        faceGetOrderIdBean.getModelType(),FaceVerifyActivity.this);
            }
        });
        getViewModel().setShowResult(false);
        getViewModel().getResultCode().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer code) {
//                dismissProgressDialog();
                getViewModel().hideShowProgressBar();
                if (code != null) {
                    if (!CertificationIdentifySource.JUMP_TO_FACE_AUTH.equals(from)) {

                        getViewModel().setShowResult(true);
                    }
                    String title = "";
                    String subTitle = "";
                    if (code == CERTIFICATION_SUCCESS) {
                        title = getResources().getString(R.string.me_identify_success);
                        subTitle = getResources().getString(R.string.me_go_identify_education);
                        getViewModel().setSubmit(getResources().getString(R.string.me_ok));
                        getDataBinding().ivResult.setImageAssetsFolder("register_success/images");
                        getDataBinding().ivResult.setAnimation("register_success/logindone.json");
                    } else if (code == CERTIFICATION_INFO_BIND) {
                        title = getResources().getString(R.string.me_info_bind);
                        subTitle = getResources().getString(R.string.me_info_bind_assist);
                        getViewModel().setSubmit(getResources().getString(R.string.me_ok));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    }else if (code == CERTIFICATION_INFO_ERROR) {
                        title = getResources().getString(R.string.me_certification_info_error);
                        subTitle = getResources().getString(R.string.me_certification_info_error_assist);
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("idfail/images");
                        getDataBinding().ivResult.setAnimation("idfail/idfail.json");
                    } else if (code == CERTIFICATION_OVER_TIME) {
                        title = getResources().getString(R.string.me_identify_over_time);
                        subTitle = getResources().getString(R.string.me_identify_over_time_assist);
                        getViewModel().setSubmit(getResources().getString(R.string.me_ok));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    } else if (code == CERTIFICATION_FAIL) {
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = getResources().getString(R.string.me_certification_fail_assist);
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    }
                    else if (code == CERTIFICATION_FAIL_1244) {
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = getResources().getString(R.string.me_certification_fail_assist_1244);
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    } else if(code == SafetyFaceDetectErrorCode.CANCEL_BY_USER){
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = getResources().getString(R.string.me_certification_enter_back);
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    }else if(code == SafetyFaceDetectErrorCode.LEAK_CAMERA_PERMISSION){
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = "请打开看准的摄像头使用权限后重试";
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    }else if(code == SafetyFaceDetectErrorCode.GO_TO_BACKGROUND){
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = "请不要中途切换其他App";
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    } else if (code == SafetyFaceDetectErrorCode.TIME_OUT) {
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = getResources().getString(R.string.me_certification_over_time);
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    } else if (code == SafetyFaceDetectErrorCode.DEVICE_MEMORY_TOO_LOW) {
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = "手机内存过低，请更换其他手机后重试";
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    } else if (code == SafetyFaceDetectErrorCode.SAVE_FILE_ERROR) {
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = "文件保存失败，请更换其他手机后重试";
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    } else if (code == SafetyFaceDetectErrorCode.CAMERA_OPEN_ERROR||code == SafetyFaceDetectErrorCode.CAMERA_PREVIEW_ERROR) {
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = "打开摄像头失败，请打开看准的摄像头使用权限后重试";
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    }else if(code == SafetyFaceDetectErrorCode.INIT_ERROR){
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = getResources().getString(R.string.me_certification_sdk_not_init);
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    }else {
                        title = getResources().getString(R.string.me_certification_fail);
                        subTitle = getResources().getString(R.string.me_certification_unknow_error);
                        getViewModel().setSubmit(getResources().getString(R.string.me_restart_identify));
                        getDataBinding().ivResult.setImageAssetsFolder("facefail/images");
                        getDataBinding().ivResult.setAnimation("facefail/facefail.json");
                    }

                    getViewModel().setTitle(title);
                    getViewModel().setAssist(subTitle);
                    String finalSubTitle = subTitle;
                    String finalTitle = title;
                    PointHelperKt.reportPoint(MePointAction.CERTIFY_REALNAME_RESULT_EXPO, new Function1<PointBean, Unit>() {
                        @Override
                        public Unit invoke(PointBean pointBean) {
                            pointBean.setMsg(finalSubTitle);
                            pointBean.setResult(finalTitle);
                            return null;
                        }
                    });

                    if (CertificationIdentifySource.JUMP_TO_FACE_AUTH.equals(from)) {
                        handleJumpFromFaceAuth(code, subTitle);
                    }


                }
            }
        });

        getViewModel().getFaceVideoLiveData().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String faceVideoUrl) {
                if (faceVideoUrl == null) {
                    return;
                }
                String faceImageUrl = getViewModel().getFaceImageLiveData().getValue();
                if (faceImageUrl != null) {
                    dismissProgressDialog();
                    getViewModel().getFaceVideoLiveData().postValue(null);
                    getViewModel().getFaceImageLiveData().postValue(null);
                    getViewModel().verifyFace(name, idNum, faceImageUrl, faceVideoUrl, certType);
                }
            }
        });
        getViewModel().getFaceImageLiveData().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String faceImageUrl) {
                if (faceImageUrl == null) {
                    return;
                }
                String faceVideoUrl = getViewModel().getFaceVideoLiveData().getValue();
                if (faceVideoUrl != null) {
                    dismissProgressDialog();
                    getViewModel().getFaceVideoLiveData().postValue(null);
                    getViewModel().getFaceImageLiveData().postValue(null);
                    getViewModel().verifyFace(name, idNum, faceImageUrl, faceVideoUrl, certType);
                }
            }
        });
    }


    @Override
    public void clickSubmit(View view) {
        int code = getViewModel().getResultCode().getValue();
        if (code == CERTIFICATION_SUCCESS) {
            doSuccess();
        } else if (code == CERTIFICATION_OVER_TIME) {
            postRefresh();
        } else if (code == CERTIFICATION_FAIL || code == CERTIFICATION_FAIL_1244
                || code == ZPLiveStillErrorCode.SDK_NOT_INIT || code == ZPLiveStillErrorCode.CANCEL_BY_USER || code == ZPLiveStillErrorCode.GO_TO_BACKGROUND || code == ZPLiveStillErrorCode.TIME_OUT
        ) {
            RxBus.getInstance().post(false, BundleConstants.BUNDLE_CERTIFICATION_INFO_ERROR);
        } else if (code == CERTIFICATION_INFO_ERROR) {
            RxBus.getInstance().post(true, BundleConstants.BUNDLE_CERTIFICATION_INFO_ERROR);
        } else if (code == CERTIFICATION_INFO_BIND) {
            postRefresh();
        } else {
            RxBus.getInstance().post(false, BundleConstants.BUNDLE_CERTIFICATION_INFO_ERROR);
        }
    }

    private void doSuccess() {
        if (CertificationIdentifySource.LOGIN_VERIFIED.equals(from)) {
            LiveEventBus.post(LivedataKeyTask.NEW_USER_TASK_REAL_USER_FINISH, GlobalStatusUtil.educationIdentifySource);
//            LoginPageRouter.jumpToSkipActivity(this,true);
//            AppUtil.finishActivity(this);
        } else if (CertificationIdentifySource.NEW_USER_TASK.equals(from)) {
            if (mUserGuideBlockInfoBean != null && mUserGuideBlockInfoBean.getAvatarCertBlock()) {
                MePageRouter.jumpToMeAvatarAuthActivity(this, PageSource.NEW_USER_TASK_ACTIVITY);
                LiveEventBus.post(LivedataKeyTask.NEW_USER_TASK_REAL_USER_FINISH, GlobalStatusUtil.educationIdentifySource);
                T.ss("信息填写成功，继续上传头像照片吧");
//                AppUtil.finishActivityDelay(this);
            }
        } else {
            LiveEventBus.post(LivedataKeyTask.NEW_USER_TASK_REAL_USER_FINISH, GlobalStatusUtil.educationIdentifySource);
            RxBus.getInstance().post("refresh", CertificationIdentifySource.CERTIFY_SUCCESS_GO_MAIN_TAB);
        }
        AppUtil.finishActivity(this);
    }

    private void handleJumpFromFaceAuth(int code, String msg) {
        Intent intent = new Intent();
        intent.putExtra(BundleConstants.BUNDLE_FACE_CODE, code);
        intent.putExtra(BundleConstants.BUNDLE_FACE_MSG, msg);
        setResult(Activity.RESULT_OK, intent);
        AppUtil.finishActivityDelay(FaceVerifyActivity.this);
        TLog.print("zl_log", "FaceVerifyActivity: handleJumpFromFaceAuth() status=%s", getViewModel().getStatusCode());
    }

    private boolean certificationSuccess() {
        if (getViewModel().getResultCode().getValue() == null) return false;
        int i = getViewModel().getResultCode().getValue();
        if (i == CERTIFICATION_SUCCESS) {
            doSuccess();

            return true;
        }
        return false;
    }


    @Override
    public void clickLeft(View view) {
        onBackPressed();
    }

    @Override
    public void onBackPressed() {
        if (certificationSuccess()) {
            return;
        }
        postRefresh();
        if (CertificationIdentifySource.JUMP_TO_FACE_AUTH.equals(from)) {
            handleJumpFromFaceAuth(-1, "");
            return;
        }
//        super.onBackPressed();
        AppUtil.finishActivity(this);
    }


//    @Override
//    public void onDetectFinish(int code, String message, String videoPath, String picPath) {
//
//        if (BuildConfig.DEBUG && AccountHelper.getInstance().getUserId().contains(ZPMegLiveUtil.TEST)) {
//            getViewModel().getResultCode().postValue(FaceVerifyActivity.CERTIFICATION_SUCCESS);
//            return;
//        }
////        TLog.error("Face", "errorCode=" + code + " errorMessage=" + message + " videoPath=" + videoPath + " picPath=" + picPath);
//        TLog.print("zl_log", "onDetectFinish(): errorCode=%s, errorMessage=%s, videoPath=%s, picPath=%s", code, message, videoPath, picPath);
//        switch (code) {
//            case ZPLiveStillErrorCode.CHECK_SUCCESS:
//                showProgressDialog("");
//                getViewModel().uploadImage(picPath);
//                getViewModel().uploadVideo(videoPath);
//                break;
//            case ZPLiveStillErrorCode.SDK_NOT_INIT:
//            case ZPLiveStillErrorCode.CANCEL_BY_USER:
//            case ZPLiveStillErrorCode.GO_TO_BACKGROUND:
//            case ZPLiveStillErrorCode.TIME_OUT:
//                getViewModel().getResultCode().postValue(code);
//                break;
//            default:
//                getViewModel().getResultCode().postValue(CERTIFICATION_FAIL);
//        }
//        ZPMegLiveManager.getInstance(FaceVerifyActivity.this).stopAndExit();
//    }

    @Override
    public void onResult(int verifyCode, int detailcode, String errMsg) {
        TLog.info("FaceVerifyActivity", "onResult() verifyCode=%d,detailcode=%d,errMsg=%s", verifyCode,detailcode,errMsg);
        ApmAnalyzer.create().action("action_face_verify").p2(String.format("onResult() verifyCode=%d,detailcode=%d,errMsg=%s", verifyCode,detailcode,errMsg)).report();
        if(detailcode == SafetyFaceDetectErrorCode.OPERATE_SUCCESS || detailcode < SafetyFaceDetectErrorCode.INIT_ERROR || detailcode > SafetyFaceDetectErrorCode.INIT_ERROR){
            if(getViewModel().mFaceGetOrderIdBean.getValue() != null && !TextUtils.isEmpty(getViewModel().mFaceGetOrderIdBean.getValue().getOrderId())){
                getViewModel().queryResult(getViewModel().mFaceGetOrderIdBean.getValue().getOrderId(),name,idNum,certType);
            }
        }else {
            getViewModel().getResultCode().postValue(detailcode);
        }
    }

    @Override
    public void onClickOpenAgreement() {

    }

    @Override
    public void onClickAgreementAndContinue() {

    }
}
