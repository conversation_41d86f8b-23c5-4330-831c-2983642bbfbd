package com.kanzhun.marry.me.identify.activity;

import static com.kanzhun.foundation.kotlin.ktx.BundleExtKt.parsePageSourceFromBundle;
import static com.kanzhun.foundation.utils.point.PointHelperKt.reportPoint;

import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.DigitsKeyListener;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.NonNull;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.kotlin.constract.LivedataKeyTask;
import com.kanzhun.common.kotlin.ui.statusbar.StatusBarKt;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.liveeventbus.LiveEventBus;
import com.kanzhun.foundation.api.base.HostConfig;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.H5Model;
import com.kanzhun.foundation.model.NoviceTaskType;
import com.kanzhun.foundation.model.UserGuideBlockInfoBean;
import com.kanzhun.foundation.model.WebViewBean;
import com.kanzhun.foundation.newtasktop.NewTaskTopFragment;
import com.kanzhun.foundation.permission.PermissionHelper;
import com.kanzhun.foundation.router.AppPageRouter;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.CertificationIdentifySource;
import com.kanzhun.foundation.utils.GlobalStatusUtil;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.BuildConfig;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityCertificationBinding;
import com.kanzhun.marry.me.identify.ZPMegLiveUtil;
import com.kanzhun.marry.me.identify.callback.CertificationCallback;
import com.kanzhun.marry.me.identify.view.NameCharInputFilter;
import com.kanzhun.marry.me.identify.viewmodel.CertificationViewModel;
import com.kanzhun.marry.me.point.MePointAction;
import com.kanzhun.utils.T;
import com.kanzhun.utils.rxbus.RxBus;
import com.sankuai.waimai.router.annotation.RouterUri;

/**
 * <AUTHOR>
 * @date 2022/3/18.
 * @noinspection JavadocDeclaration
 */
@RouterUri(path = MePageRouter.ME_CERTIFICATION_ACTIVITY)
public class CertificationActivity extends FoundationVMActivity<MeActivityCertificationBinding, CertificationViewModel> implements CertificationCallback {
    String from;
    CommonSystemCenterDialog unReachEighteenDialog;

    UserGuideBlockInfoBean mUserGuideBlockInfoBean;

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_certification;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarKt.fullScreenAndBlackText(this, null, false);

        from = getIntent().getStringExtra(BundleConstants.BUNDLE_FROM);
        if (getIntent().getSerializableExtra(BundleConstants.BUNDLE_USER_GUIDE_BLOCK_INFO_BEAN) instanceof UserGuideBlockInfoBean) {
            mUserGuideBlockInfoBean = (UserGuideBlockInfoBean) getIntent().getSerializableExtra(BundleConstants.BUNDLE_USER_GUIDE_BLOCK_INFO_BEAN);
        }
        GlobalStatusUtil.educationIdentifySource = !TextUtils.isEmpty(from) ? from : CertificationIdentifySource.MAIN_TAB;
        InputFilter[] nameFilters = new InputFilter[1];
        nameFilters[0] = new NameCharInputFilter(12);
        getDataBinding().layoutCertificationEditOptionName.editContent.setFilters(nameFilters);
        getDataBinding().layoutCertificationEditOptionName.editContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                getViewModel().getName().setIsError(false);
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        InputFilter[] numFilters = new InputFilter[1];
        numFilters[0] = new InputFilter.LengthFilter(18);
        getDataBinding().layoutCertificationEditOptionNum.editContent.setFilters(numFilters);
        getDataBinding().layoutCertificationEditOptionNum.editContent.setKeyListener(DigitsKeyListener.getInstance("1234567890Xx"));
        getDataBinding().layoutCertificationEditOptionNum.editContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                getViewModel().getNum().setIsError(false);
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        getViewModel().getVerifyIdNumLiveData().observe(this, aBoolean -> {
            if (aBoolean != null) {
                Intent intent = FaceVerifyActivity.createIntent(CertificationActivity.this, getViewModel().getReplaceName().toString(), getViewModel().getNum().getEditContent().get());
                intent.putExtra(BundleConstants.BUNDLE_FROM, from);
                if (mUserGuideBlockInfoBean != null) {
                    intent.putExtra(BundleConstants.BUNDLE_USER_GUIDE_BLOCK_INFO_BEAN, mUserGuideBlockInfoBean);
                }
                AppUtil.startActivity(CertificationActivity.this, intent);

            }
        });
        getViewModel().getUnReachEighteenLiveData().observe(this, aBoolean -> {
            if (aBoolean != null && aBoolean) {
                if (unReachEighteenDialog != null && unReachEighteenDialog.isShowing()) {
                    return;
                }
                showUnReachEighteenDialog();
            }
        });
        RxBus.getInstance().subscribe(this, BundleConstants.BUNDLE_CERTIFICATION_INFO_ERROR, new RxBus.Callback<Boolean>() {

            @Override
            public void onEvent(Boolean clear) {
                if (clear) {
                    getViewModel().getName().getEditContent().set("");
                    getViewModel().getNum().getEditContent().set("");
                }
                BaseApplication.getApplication().getActivityLifecycleCallbacks().popUpActivitiesOnTopOf(CertificationActivity.class);
            }
        });
        LiveEventBus.get(LivedataKeyTask.NEW_USER_TASK_REAL_USER_FINISH, String.class).observe(this, o -> AppUtil.finishActivity(CertificationActivity.this));
        ServiceManager.getInstance().getNoviceTaskService().updateNoviceTaskProgress(NoviceTaskType.NOVICE_TASK_TYPE_REAL_NAME);

        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(getString(R.string.me_read_and_agree));
        String protocolString = getResources().getString(R.string.me_face_verify_protocol);
        int protocolStart = spannableStringBuilder.length();
        int protocolEnd = protocolStart + protocolString.length();
        spannableStringBuilder.append(protocolString);
        spannableStringBuilder.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                H5Model.URL_H5_PROTOCOL_FACE.openWithFullScreen(CertificationActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(R.color.common_color_003580, null));
                ds.setUnderlineText(false);
            }
        }, protocolStart, protocolEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        getDataBinding().tvProtocol.setMovementMethod(LinkMovementMethod.getInstance());
        getDataBinding().tvProtocol.setText(spannableStringBuilder);
        reportPoint(MePointAction.CERTIFY_REALNAME_PAGE_EXPO, pointBean -> {
            pointBean.setSource(getSource());
            return null;
        });

        getDataBinding().idFragmentTop.setVisibility(View.VISIBLE);
        getSupportFragmentManager().beginTransaction()
                .replace(getDataBinding().idFragmentTop.getId(), new NewTaskTopFragment(true),CertificationActivity.class.getSimpleName())
                .commitAllowingStateLoss();
    }

    private String getSource() {
        if (getBeforePageSource() == PageSource.FIRST_NAME_VERIFIED_FRAGMENT) {
            return "首善流程实名认证";
        }
        if (getBeforePageSource() == PageSource.CHILD_F1_RECOMMEND_TOP_GUIDE) {
            return "F1新手顶部引导条";
        }
        if (getBeforePageSource() == PageSource.F1_RECOMMEND_CHILD_FRAGMENT_LOCK_CARS) {
            return "F1新手引导锁定卡片";
        }
        if (getBeforePageSource() == PageSource.NEW_USER_TASK_ACTIVITY) {
            return "新手个人信息完善流程";
        }

        if (getBeforePageSource() == PageSource.AUTH_CENTER_ACTIVITY) {
            return "认证中心";
        }
        if (getBeforePageSource() == PageSource.F4_ME_CHILD_FRAGMENT) {
            return "F4我的页面编辑资料拦截";
        }
        if (getBeforePageSource() == PageSource.LIKE_BLOCK) {
            return "新手发送喜欢阻断弹窗";
        }
        if (getBeforePageSource() == PageSource.PROTOCOL) {
            return getProtocolFrom();
        }
        if (getBeforePageSource() == PageSource.F2_SEEN_ME_CHILD_FRAGMENT) {
            return "F2孩子端互动看过我";
        }
        if (getBeforePageSource() == PageSource.F2_LIKE_ME_CHILD_FRAGMENT) {
            return "F2孩子端互动喜欢我";
        }

        return "";
    }

    private PageSource getBeforePageSource() {
        return parsePageSourceFromBundle(getIntent());
    }

    private String getProtocolFrom() {
        return getIntent().getStringExtra(BundleConstants.BUNDLE_PROTOCOL_FROM);
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickSubmit(View view) {
        if (BuildConfig.DEBUG && AccountHelper.getInstance().getUserId().contains(ZPMegLiveUtil.TEST)) {
            getViewModel().doSuccess();
            return;
        }
        String sourceName = getViewModel().getName().getEditContent().get();
        if (!TextUtils.isEmpty(sourceName)) {
            StringBuilder replaceName = getViewModel().getReplaceName();
            replaceName.setLength(0);
            for (int i = 0; i < sourceName.length(); i++) {
                char c = sourceName.charAt(i);
                if (c == 8226) {
                    replaceName.append('·');
                } else {
                    replaceName.append(c);
                }
            }
        }
        PermissionHelper.getCameraHelper(CertificationActivity.this).setPermissionCallback((yes, permission) -> {
            if (yes) {
                getViewModel().verifyIdNum();
            } else {
                T.ss(R.string.common_no_camera_permission);
            }
        }).requestPermission();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        RxBus.getInstance().unregister(this);
    }

    private void showUnReachEighteenDialog() {
        if (unReachEighteenDialog == null) {
            SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder("您未满18周岁，根据");
            String protocolString = getResources().getString(R.string.common_protocol_link);
            int protocolStart = spannableStringBuilder.length();
            int protocolEnd = protocolStart + protocolString.length();
            spannableStringBuilder.append(protocolString);
            spannableStringBuilder.append("看准暂不能为您提供服务，请按照");
            String policyString = getResources().getString(R.string.common_policy_link);
            int policyStart = spannableStringBuilder.length();
            int policyEnd = policyStart + policyString.length();
            spannableStringBuilder.append(policyString);
            spannableStringBuilder.append("说明注销账号");
            spannableStringBuilder.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    WebViewBean webViewBean = new WebViewBean();
                    webViewBean.setUrl(H5Model.URL_H5_PROTOCOL_USER.toH5Url());
                    webViewBean.setStyle(WebViewBean.STYLE_HAS_NO_TITLE_AND_TRANSLUCENT);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean);
                    AppUtil.startUri(CertificationActivity.this, AppPageRouter.WEB_VIEW_ACTIVITY, bundle);
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setColor(getResources().getColor(R.color.common_color_4C9CF8, null));
                    ds.setUnderlineText(false);
                }
            }, protocolStart, protocolEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

            spannableStringBuilder.setSpan(new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    WebViewBean webViewBean = new WebViewBean();
                    webViewBean.setUrl(H5Model.URL_H5_PROTOCOL_PRIVACY.toH5Url());
                    webViewBean.setStyle(WebViewBean.STYLE_HAS_NO_TITLE_AND_TRANSLUCENT);
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean);
                    AppUtil.startUri(CertificationActivity.this, AppPageRouter.WEB_VIEW_ACTIVITY, bundle);
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setColor(getResources().getColor(R.color.common_color_4C9CF8, null));
                    ds.setUnderlineText(false);
                }
            }, policyStart, policyEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

            CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
                .setTitle(getString(R.string.me_approve_remind))
                .setContentLikeText(spannableStringBuilder)
                .setPositiveText(getResources().getString(R.string.common_i_know_me))
                .setOnlyBtn(true)
                .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                    @Override
                    public void onPositiveClick(Dialog dialog, View view) {

                    }

                    @Override
                    public void onNegativeClick(Dialog dialog, View view) {

                    }
                });
            unReachEighteenDialog = builder.create();
        }
        unReachEighteenDialog.show();
    }

    @Override
    public void clickRight(View view) {
        // 跳转“认证遇到问题”页面
        WebViewBean webViewBean = new WebViewBean();
        webViewBean.setUrl(HostConfig.getH5Host(URLConfig.URL_H5_PROTOCOL_FAQ_FACE));
        webViewBean.setStyle(WebViewBean.STYLE_HAS_NO_TITLE_AND_TRANSLUCENT);
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean);
        AppUtil.startUri(this, AppPageRouter.WEB_VIEW_ACTIVITY, bundle);

        reportPoint("certify-realname-faq-click", null);
    }
}
