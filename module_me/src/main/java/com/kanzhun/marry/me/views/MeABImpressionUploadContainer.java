package com.kanzhun.marry.me.views;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.kanzhun.marry.me.R;

public class MeABImpressionUploadContainer extends LinearLayout {

    int initALeft;
    int initATop;
    int initARight;
    int initABottom;
    int initBLeft;
    int initBTop;
    int initBRight;
    int initBBottom;
    private boolean refresh;
    private boolean isANeedLayout;
    private boolean isBNeedLayout;

    public MeABImpressionUploadContainer(Context context) {
        super(context);
    }

    public MeABImpressionUploadContainer(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public MeABImpressionUploadContainer(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public MeABImpressionUploadContainer(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        if (refresh) {
            if (isANeedLayout || isBNeedLayout) {
                findViewById(R.id.ll_impression_a).layout(initBLeft, initBTop, initBRight, initBBottom);
                findViewById(R.id.ll_impression_a).setTranslationZ(0.001f);
                findViewById(R.id.ll_impression_b).layout(initALeft, initATop, initARight, initABottom);
                findViewById(R.id.ll_impression_b).setTranslationZ(0.002f);
            }
        }
    }

    public void setRefresh(boolean refresh) {
        this.refresh = refresh;
    }

    public void setInitLayout(int initALeft, int initATop, int initARight, int initABottom, int initBLeft, int initBTop, int initBRight, int initBBottom) {
        this.initALeft = initALeft;
        this.initATop = initATop;
        this.initARight = initARight;
        this.initABottom = initABottom;
        this.initBLeft = initBLeft;
        this.initBTop = initBTop;
        this.initBRight = initBRight;
        this.initBBottom = initBBottom;
    }

    public void setA() {
        isANeedLayout = true;
    }

    public void setB() {
        isBNeedLayout = true;
    }
}
