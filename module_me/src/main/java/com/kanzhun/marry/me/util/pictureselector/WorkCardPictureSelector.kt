package com.kanzhun.marry.me.util.pictureselector

import android.net.Uri
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.kanzhun.marry.me.databinding.MeLayoutWorkCardSelectRuleBinding

class WorkCardPictureSelector(
    val context: FragmentActivity,
    clickCallback: (type: PictureSelectorItemType) -> Unit = {},
    val callback: (cameraFileUri: Uri, isGallery: Boolean) -> Unit
) : BasePictureSelector(context, clickCallback, callback) {

    override fun getHeaderView(): View {
        return MeLayoutWorkCardSelectRuleBinding.inflate(context.layoutInflater).root
    }

}