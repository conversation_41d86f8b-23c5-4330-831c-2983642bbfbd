package com.kanzhun.marry.me.identify.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/18.
 */
public class EducationIdentifyBean implements Serializable {
    private static final long serialVersionUID = 7026311603641234879L;
    private int type;
    private String title;
    private String notice;
    private String recommended;
    private boolean isShowDivider;
    private Cert cert;

    public EducationIdentifyBean(Cert cert) {
        this.cert = cert;
        this.title = cert.getTitle();
        this.notice = cert.getContent();
        this.recommended = cert.getTag();
    }

    public Cert getCert() {
        return cert;
    }

    public boolean isShowDivider() {
        return isShowDivider;
    }

    public void setShowDivider(boolean showDivider) {
        isShowDivider = showDivider;
    }

    public int getType() {
        return type;
    }

    public String getTitle() {
        return title;
    }

    public String getNotice() {
        return notice;
    }

    public String isRecommended() {
        return recommended;
    }
}
