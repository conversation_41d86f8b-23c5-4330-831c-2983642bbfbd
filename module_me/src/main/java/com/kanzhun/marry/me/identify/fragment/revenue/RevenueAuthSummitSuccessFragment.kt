package com.kanzhun.marry.me.identify.fragment.revenue

import com.kanzhun.common.kotlin.ext.asBackButton
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.identify.fragment.BaseAuthSummitSuccessFragment

/**
 * 收入认证提交成功页面
 */
class RevenueAuthSummitSuccessFragment : BaseAuthSummitSuccessFragment() {
    override fun initView() {
        super.initView()
        mBinding.btnNext.asBackButton{
            onClickFinish("好的")
            reportPoint("certify-submit-page-click"){
                actionp2 = "年薪"
            }
        }
    }

}

