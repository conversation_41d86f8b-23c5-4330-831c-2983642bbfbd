package com.kanzhun.marry.me.identify.fragment;

import static com.kanzhun.common.kotlin.ext.ActivityExtKt.liveEventObserve;

import android.content.Context;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kanzhun.common.app.AppThreadFactory;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.model.TempSuggestBean;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.me.databinding.MeItemAuthSeachCompanyHelpBinding;
import com.kanzhun.marry.me.info.activity.MeUpdateIntroActivity;
import com.kanzhun.marry.me.point.MePointAction;
import com.kanzhun.utils.T;
import com.kanzhun.utils.views.MultiClickUtil;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.foundation.api.model.SuggestBean;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentCompanyAuthInputBinding;
import com.kanzhun.marry.me.databinding.MeItemCompanyAuthSeachCompanyBinding;
import com.kanzhun.marry.me.identify.callback.CompanyAuthInputCallback;
import com.kanzhun.marry.me.identify.viewmodel.CompanyAuthInputViewModel;
import com.kanzhun.marry.me.identify.viewmodel.CompanyAuthViewModel;
import com.kanzhun.utils.base.LList;

import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.reactivex.rxjava3.subjects.PublishSubject;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/7/5
 */
public class CompanyAuthInputFragment extends FoundationVMShareFragment<MeFragmentCompanyAuthInputBinding, CompanyAuthInputViewModel, CompanyAuthViewModel> implements CompanyAuthInputCallback {

    private BaseBinderAdapter adapter;
    //    private SearchCompanyItemBinder itemBinder;
//    private TempItemBinder tempBinder;
    private PublishSubject<String> searchSubject;
    private Disposable searchDisposable;

    @Override
    protected void initFragment() {
        initView();
        getViewModel().getSearchCompanyLiveData().observe(this, new Observer<List<SuggestBean>>() {
            @Override
            public void onChanged(List<SuggestBean> data) {
                adapter.setList(data);
            }
        });
    }

    private void initView() {
        initSearchSubject();

        String s = getActivityViewModel().companyNameObservable.get();
        if (SpManager.get().user().getInt(Constants.CUSTOM_COMPANY, 0) == 1) {
            getDataBinding().btnNext.setVisibility(View.GONE);
        }
        getDataBinding().btnNext.setEnabled(s != null && s.length() > 0);
        getDataBinding().btnNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickRight(v);
            }
        });
        QMUIKeyboardHelper.setVisibilityEventListener(getActivity(), new QMUIKeyboardHelper.KeyboardVisibilityEventListener() {
            @Override
            public boolean onVisibilityChanged(boolean isOpen, int heightDiff) {
                if (getActivity() != null && CompanyAuthInputFragment.this.isResumed()) {
                    getDataBinding().idTitle.setInputVisibility(isOpen);
                }
                return false;
            }
        });
        getDataBinding().idTitle.getBinding().icTitle.asBackButton(new Function0<Unit>() {
            @Override
            public Unit invoke() {
//                clickLeft(getDataBinding().idTitle.getBinding().icTitle);
                QMUIKeyboardHelper.hideKeyboard(getDataBinding().editText);
                return null;
            }
        });

        String content0 = "请填写";
        String content1 = "完整的公司或单位全称";
        String content2 = "，不规范的名称将会被审核驳回哦";
        SpannableString indexSpannableString = new SpannableString(content0 + content1 + content2);
        indexSpannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.common_color_0046BD)), content0.length(), (content0 + content1).length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        getDataBinding().idTitle.setSubTitle(indexSpannableString);

        getDataBinding().tvContentCount.setText(TextUtils.isEmpty(s) ? "0" : String.valueOf(s.length()));
        getDataBinding().editText.setText(TextUtils.isEmpty(s) ? "" : s);
        if (!TextUtils.isEmpty(s)) {
            getDataBinding().editText.setSelection(s.length());
        }
        getViewModel().setSearchContent(s);
        adapter = new BaseBinderAdapter();
//        itemBinder = new SearchCompanyItemBinder();
//        tempBinder = new TempItemBinder();
        adapter.addItemBinder(SuggestBean.class, new BaseDataBindingItemBinder<SuggestBean, MeItemCompanyAuthSeachCompanyBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_company_auth_seach_company;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemCompanyAuthSeachCompanyBinding> holder,
                                MeItemCompanyAuthSeachCompanyBinding binding, SuggestBean item) {
                setAllContentText(getContext(), binding.tvContent, item);
            }

            private void setAllContentText(Context context, TextView textView, SuggestBean item) {
                if (!LList.isEmpty(item.highlights)) {
                    SpannableString s = new SpannableString(item.name);
                    for (SuggestBean.SuggestLightBean light : item.highlights) {
                        int start = light.start;
                        int end = light.end;
                        if (s.length() >= start && end <= s.length() && start <= end) {
                            s.setSpan(new StyleSpan(Typeface.BOLD), start, end, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                        }
                    }
                    textView.setText(s);
                } else {
                    textView.setText(item.name);
                }

            }
        });
        adapter.addItemBinder(TempSuggestBean.class, new BaseDataBindingItemBinder<TempSuggestBean, MeItemAuthSeachCompanyHelpBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_auth_seach_company_help;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemAuthSeachCompanyHelpBinding> holder, MeItemAuthSeachCompanyHelpBinding binding, TempSuggestBean item) {
                binding.getRoot().setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (MultiClickUtil.isMultiClick()) return;
                        MePageRouter.jumpToCompanyNameCustomActivity(binding.getRoot().getContext(), getDataBinding().editText.getText().toString(),
                                PageSource.NONE, "");
                        PointHelperKt.reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
                            @Override
                            public Unit invoke(PointBean pointBean) {
                                pointBean.setSource("存量编辑");
                                pointBean.setType("去自定义");
                                return null;
                            }
                        });
                    }
                });
            }
        });
        getDataBinding().recyclerview.setLayoutManager(new LinearLayoutManager(activity));
        getDataBinding().recyclerview.setAdapter(adapter);
        getDataBinding().editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.toString().length() > 46) {
                    getDataBinding().editText.setText(s.toString().substring(0, 46));
                    getDataBinding().editText.setSelection(getDataBinding().editText.length());
                    T.ss("请输入46个字以内的公司名");
                } else {
                    searchObservable(s.toString().trim());
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        getDataBinding().editText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PointHelperKt.reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setSource("存量编辑");
                        pointBean.setType("输入公司名称");
                        return null;
                    }
                });
            }
        });
        getDataBinding().editText.post(new Runnable() {
            @Override
            public void run() {
                QMUIKeyboardHelper.showKeyboard(getDataBinding().editText, true);
                PointHelperKt.reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setSource("存量编辑");
                        pointBean.setType("输入公司名称");
                        return null;
                    }
                });
            }
        });

        liveEventObserve(this, "companyNameEdit", new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if(TextUtils.isEmpty(s))return;
                AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        temWriteName = s;
                        getDataBinding().editText.setText(s);
                        getDataBinding().editText.setSelection(getDataBinding().editText.length());
                        getDataBinding().editText.setEnabled(true);
                        if (SpManager.get().user().getInt(Constants.CUSTOM_COMPANY, 0) == 1) {
                            clickRight(getDataBinding().editText);
                        }

                    }
                }, 200);
            }
        });

        adapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@androidx.annotation.NonNull BaseQuickAdapter<?, ?> adapter, @androidx.annotation.NonNull View view, int position) {
                SuggestBean item = (SuggestBean) adapter.getItem(position);
                getActivityViewModel().companyNameObservable.set(item.name);

                if (!TextUtils.isEmpty(item.name)) {
                    getDataBinding().editText.setText(item.name);
                    getDataBinding().editText.setSelection(item.name.length());
                    temWriteName = item.name;
                }
                PointHelperKt.reportPoint(MePointAction.COMPANY_NAMEFILL_SUGGESTION_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setSource("存量编辑");
                        pointBean.setIdx(position);
                        return null;
                    }
                });
                if (SpManager.get().user().getInt(Constants.CUSTOM_COMPANY, 0) == 1) {
                    clickRight(view);
                }else {
                    getActivityViewModel().requestCompanyShort();
                }
            }
        });
        PointHelperKt.reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_EXPO, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setSource("存量编辑");
                return null;
            }
        });
    }

    private void initSearchSubject() {
        searchSubject = PublishSubject.create();
        searchSubject.debounce(500, TimeUnit.MILLISECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new io.reactivex.rxjava3.core.Observer<String>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        searchDisposable = d;
                    }

                    @Override
                    public void onNext(@NonNull String s) {
                        getViewModel().searchCompany(s);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {

                    }

                    @Override
                    public void onComplete() {

                    }
                });


    }

    String temWriteName = "";

    private void searchObservable(String content) {
        if (TextUtils.equals(content, getViewModel().getSearchContent())) {
            return;
        }
        if (SpManager.get().user().getInt(Constants.CUSTOM_COMPANY, 0) == 1) {
            if (content.equals(temWriteName)) {
                getDataBinding().btnNext.setEnabled(true);
            } else {
                getDataBinding().btnNext.setEnabled(false);
            }
        } else {
            getDataBinding().btnNext.setEnabled(content.length() > 0);
        }
        getDataBinding().tvContentCount.setText(String.valueOf(content.length()));
        getViewModel().setSearchContent(content);
        if (!TextUtils.isEmpty(content)) {
            searchSubject.onNext(content);
        } else {
            getViewModel().getSearchCompanyLiveData().setValue(null);
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (searchDisposable != null) {
            searchDisposable.dispose();
        }
        QMUIKeyboardHelper.hideKeyboard(getDataBinding().editText);
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_company_auth_input;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public void clickRight(View view) {
        getActivityViewModel().companyNameObservable.set(getViewModel().getSearchContent());
        getActivityViewModel().requestCompanyShort();
        if (getActivity() != null) {
            getActivity().onBackPressed();
        }
        PointHelperKt.reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setSource("存量编辑");
                pointBean.setType("下一步");
                return null;
            }
        });
        QMUIKeyboardHelper.hideKeyboard(getDataBinding().editText);
    }

    @Override
    public void clickLeft(View view) {
        if (getActivity() != null) {
            getActivity().onBackPressed();
        }
        PointHelperKt.reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setSource("存量编辑");
                pointBean.setType("返回");
                return null;
            }
        });
    }

}
