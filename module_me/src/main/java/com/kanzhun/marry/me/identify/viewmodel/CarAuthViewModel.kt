package com.kanzhun.marry.me.identify.viewmodel

import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.identify.activity.CAR_AUTH_STATUS_NONE
import com.kanzhun.marry.me.identify.model.CarAuthDetail
import com.kanzhun.utils.T

class CarAuthViewModel : BaseViewModel() {

    /**
     * 车产认证状态变更
     */
    val carAuthStatusLiveData: MutableLiveData<Int> = MutableLiveData()

    var authDetail: CarAuthDetail? = null

    /**
     * 车产认证状态
     * 0:未认证
     * 1:待审核
     * 2:已驳回
     * 3:认证成功
     */
    var carAuthStatus: Int = 0

    fun getCarAuthInfo() {
        val observable = RetrofitManager.getInstance().createApi<MeApi>(MeApi::class.java).certCarDetail()
        HttpExecutor.execute<CarAuthDetail>(observable, object : BaseRequestCallback<CarAuthDetail?>(false) {
            override fun onSuccess(data: CarAuthDetail?) {
                authDetail = data
                if (data == null) {
                    setAuthStatus(CAR_AUTH_STATUS_NONE)
                } else {
                    setAuthStatus(data.status)
                }

            }

            override fun dealFail(reason: ErrorReason?) {
                T.ss(reason?.errReason)
                showError(text = reason?.errReason)
            }
        })
    }

    fun setAuthStatus(status: Int) {
        TLog.debug("CarAuthActivity","setAuthStatus: $status")
        carAuthStatusLiveData.value = status

    }
}