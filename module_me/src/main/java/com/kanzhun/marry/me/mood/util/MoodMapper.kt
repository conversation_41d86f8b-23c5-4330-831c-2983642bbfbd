package com.kanzhun.marry.me.mood.util

import com.kanzhun.foundation.api.response.MoodPermissionType
import com.kanzhun.marry.me.R


fun Int?.toMoodViewPermissionText(): String {
    return when (this) {
        MoodPermissionType.PUBLIC -> "公开"
        MoodPermissionType.PRIVATE -> "仅对互相喜欢的人可见"
        else -> "公开"
    }
}

fun Int.toLikeHeartIcon(): Int {
    return when (this) {
        1 -> R.mipmap.common_ic_mood_liked
        else -> R.mipmap.common_ic_mood_no_like
    }
}
