package com.kanzhun.marry.me.setting.activity

import android.content.Context
import android.content.Intent
import android.text.Editable
import android.text.TextWatcher
import android.text.method.LinkMovementMethod
import androidx.core.text.buildSpannedString
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.showSoftInput
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.button.enableButton
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.model.UserSafeModel
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityModifyPhoneBinding
import com.kanzhun.marry.me.point.MePointReporter
import com.kanzhun.marry.me.setting.viewmodel.ModifyPhoneViewModel
import com.kanzhun.utils.string.appendClickable

class ModifyPhoneActivity : BaseBindingActivity<MeActivityModifyPhoneBinding, ModifyPhoneViewModel>() {

    private val pointHelper by lazy { ModifyPhonePointHelper(mViewModel) }
    override fun preInit(intent: Intent) {
        val info = intent.getSerializableExtra(BundleConstants.BUNDLE_DATA)
        if (info is UserSafeModel) {
            mViewModel.prePhoneNumber = info.phone
            mViewModel.modifyCount = info.phoneChangeCount
        }
        pointHelper.reportPageExpose()
    }

    override fun initView() {
        mBinding.titleBar.asBackButton()
        mBinding.tvModifyCount.text = "每30天可以修改1次，还可以修改${mViewModel.modifyCount}次"
        //发送验证码
        mBinding.tvSend.clickWithTrigger {
            sendVerifyCode(false)
            pointHelper.reportPageClick("发送验证码")
        }
        //手机号
        mBinding.etPhone.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                mViewModel.phoneNumber = s.toString().trim()
                checkButton()
            }
        })
        //验证码
        mBinding.etCode.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                mViewModel.verifyCode = s.toString().trim()
                checkButton()
            }

        })
        //下一步或者保存
        mBinding.tvSave.setOnClickListener {
            if (mViewModel.isVerifyMode()) {
                mViewModel.verifyPrePhone()
            } else {
                mViewModel.modifyPhone()
            }
            pointHelper.reportPageClick("下一步")
        }
        //语音验证码
        setVoiceCode()


    }

    override fun initData() {
        //模式切换
        mViewModel.isVerify.observe(this) {
            setMode(it)
            if(!it){
                pointHelper.reportPageExpose()
            }
        }
        //验证码倒计时
        mViewModel.countDown.getCount().observe(this) {
            mBinding.tvSendVoiceCode.enable(it == 0)
            mBinding.tvSend.text = if (it == 0) R.string.send.toResourceString() else "${it}秒后重试"
        }
        mViewModel.modifyResult.observe(this) {
            if (it) {
                finish()
            }
        }

    }

    private fun checkButton() {
        if (mViewModel.isVerifyMode()) {
            mBinding.tvSave.enableButton(!mViewModel.verifyCode.isNullOrBlank(), false)
        } else {
            mBinding.tvSave.enableButton(!mViewModel.verifyCode.isNullOrBlank() && !mViewModel.phoneNumber.isNullOrBlank(), false)
        }
    }

    override fun onRetry() {
    }

    private fun setMode(isVerify: Boolean) {
        mBinding.run {
            if (isVerify) {
                tvTitle.setText(R.string.me_verify_phone_number)
                tvSubTitle.text = "验证通过后，可以换绑手机号"
                tvPrePhone.textOrGone(mViewModel.prePhoneNumber)
                tvSave.setText(R.string.me_next)
                etPhone.gone()
                divider.gone()
            } else {
                tvTitle.setText(R.string.me_modify_phone_number)
                tvSubTitle.text = "当前手机号：${mViewModel.prePhoneNumber}"
                tvPrePhone.gone()
                tvSave.setText(R.string.me_save)
                etCode.setText("")
                etPhone.visible()
                etPhone.setText("")
                showSoftInput(etPhone, false)
                divider.visible()
            }
        }

    }

    private fun setVoiceCode() {
        mBinding.tvSendVoiceCode.movementMethod = LinkMovementMethod.getInstance()
        mBinding.tvSendVoiceCode.text = buildSpannedString {
            append("长时间接收不到验证码，可以试试")
            appendClickable("语音验证码", R.color.common_color_003580.toResourceColor(), false) {
                sendVerifyCode(true)
                pointHelper.reportPageClick("语音验证码")
            }
        }
    }

    private fun sendVerifyCode(isVoice: Boolean) {
        if (mBinding.tvSend.text.toString() != R.string.send.toResourceString()) {
            return
        }
        if (mViewModel.isVerifyMode()) {
            mViewModel.sendVerifyCode(isVoice)
        } else {
            mViewModel.sendVerifyCodeToNewPhone(isVoice)
        }
    }


    override fun getStateLayout() = null

    companion object {
        fun intent(context: Context, saveInfo: UserSafeModel) {
            Intent(context, ModifyPhoneActivity::class.java).apply {
                putExtra(BundleConstants.BUNDLE_DATA, saveInfo)
                context.startActivity(this)
            }
        }
    }

}

class ModifyPhonePointHelper(val viewModel: ModifyPhoneViewModel) {
    fun reportPageExpose() {
        if (viewModel.isVerifyMode()){
            MePointReporter.reportVerifyPhoneExpose()
        }else{
            MePointReporter.reportChangePhoneNumberExpose()
        }
    }

    /**
     * @type 0:发送验证码  1:语音验证码 2:下一步

     */
    fun reportPageClick(type:String) {
        if (viewModel.isVerifyMode()){
            MePointReporter.reportVerifyPhoneClick(type)

        }else{
            MePointReporter.reportChangePhoneNumberClick(type)
        }
    }
}