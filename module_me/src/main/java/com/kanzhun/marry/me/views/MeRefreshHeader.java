package com.kanzhun.marry.me.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshKernel;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.simple.SimpleComponent;
import com.kanzhun.marry.me.R;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/18
 */
public class MeRefreshHeader extends SimpleComponent implements RefreshHeader {

    protected TextView mTitleText;
    protected LinearLayout mLlCenter;
    protected ImageView mProgressView;
    protected ImageView mIvAbove;
    protected float showRate = 0.67f;
    private String errorText;

    public MeRefreshHeader(Context context) {
        this(context, null);
    }

    public MeRefreshHeader(Context context, AttributeSet attrs) {
        super(context, attrs, 0);
        View.inflate(context, R.layout.me_refresh_header, this);
        final View thisView = this;
        mProgressView = thisView.findViewById(R.id.iv_progress);
        mTitleText = thisView.findViewById(R.id.tv_title);
        mLlCenter = thisView.findViewById(R.id.ll_center);
        mIvAbove = thisView.findViewById(R.id.iv_above);
        mProgressView.setVisibility(GONE);

        errorText = getResources().getString(R.string.me_refresh_header_title_finish);
    }

    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {
        super.onInitialized(kernel, height, maxDragHeight);
    }

    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        switch (newState) {
            case None:
            case PullDownToRefresh:
                mTitleText.setText(getResources().getString(R.string.me_refresh_header_title));
                mLlCenter.setVisibility(VISIBLE);
                mIvAbove.setImageResource(R.drawable.me_ic_icon_match_user_above);
                break;
            case Refreshing:
            case RefreshReleased:
                //"正在刷新...";
                mLlCenter.setVisibility(INVISIBLE);
                progressStart();
                break;
            case ReleaseToRefresh:
                mTitleText.setText(getResources().getString(R.string.me_refresh_header_title_release));
                break;
            default:
                break;
        }
    }

    private void progressStart() {
        final View progressView = mProgressView;
        if (progressView.getVisibility() != VISIBLE) {
            progressView.setVisibility(VISIBLE);
//            Drawable drawable = mProgressView.getDrawable();
//            if (drawable instanceof Animatable) {
//                ((Animatable) drawable).start();
//            } else {
//                progressView.animate().rotation(36000).setDuration(100000);
//            }
        }
    }

    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {
        if (percent <= 0.67f) {
            mLlCenter.setAlpha(0.0f);
        } else if (percent < 1) {
            float alpha = (percent - showRate) * 3;
            mLlCenter.setAlpha(alpha);
        } else {
            mLlCenter.setAlpha(1.0f);
        }
    }

    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        super.onReleased(refreshLayout, height, maxDragHeight);
    }

    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        super.onStartAnimator(refreshLayout, height, maxDragHeight);
    }

    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        final View progressView = mProgressView;
//        Drawable drawable = mProgressView.getDrawable();
//        if (drawable instanceof Animatable) {
//            if (((Animatable) drawable).isRunning()) {
//                ((Animatable) drawable).stop();
//            }
//        } else {
//            progressView.animate().rotation(0).setDuration(0);
//        }

        progressView.setVisibility(GONE);
        if (success) {
            mLlCenter.setVisibility(INVISIBLE);
            mProgressView.setVisibility(GONE);
            return 0;
        } else {
            mLlCenter.setVisibility(VISIBLE);
            mIvAbove.setImageResource(R.drawable.me_ic_icon_match_no_user);
            mProgressView.setVisibility(GONE);
            mTitleText.setText(errorText);
            return 1000;
        }
    }

    public void setErrorText(String errorText) {
        this.errorText = errorText;
    }

}
