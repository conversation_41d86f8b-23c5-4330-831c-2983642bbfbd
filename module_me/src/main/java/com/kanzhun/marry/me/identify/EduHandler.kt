package com.kanzhun.marry.me.identify

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.bean.UserSchoolGetBean
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason

class EduHandler {

    fun goNext(context:Context,block:()->Unit, pageSource: PageSource = PageSource.NONE){
        val responseObservable = RetrofitManager.getInstance().createApi(
            FoundationApi::class.java
        ).querySchoolGet()
        HttpExecutor.execute<UserSchoolGetBean>(
            responseObservable,
            object : BaseRequestCallback<UserSchoolGetBean>() {
                override fun onSuccess(data: UserSchoolGetBean) {
                    if(data.certStatus == 3 || data.certStatus == 1){
                        if(data.eduCertStatus == 3){
                            return
                        }
                        if(data.schoolArea == 2){
                            MePageRouter.jumpRetentionServiceNumIdentifyActivity(
                                context = context,
                                mUserSchoolGetBean = data,
                                pageSource = pageSource
                            )
                        }else{
                            MePageRouter.jumpEducationIdentifyChooseActivity(
                                context = context,
                                mUserSchoolGetBean = data,
                                pageSource = pageSource
                            )
                        }

                        block()
                    }
                }

                override fun dealFail(reason: ErrorReason) {}
            })
    }
}