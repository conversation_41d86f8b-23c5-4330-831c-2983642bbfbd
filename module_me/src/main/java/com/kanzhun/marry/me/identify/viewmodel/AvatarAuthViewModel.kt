package com.kanzhun.marry.me.identify.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.marry.me.api.model.CerBean

class AvatarAuthViewModel:BaseViewModel() {

    var cerBean: CerBean? = null

    /**
     * 头像认证状态变更
     */
    val avatarAuthStatusLiveData: MutableLiveData<Int> = MutableLiveData()

    var protocolFrom:String? = ""

}