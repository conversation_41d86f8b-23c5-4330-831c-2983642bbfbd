package com.kanzhun.marry.me.identify.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.model.SearchSchoolResponse;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.foundation.model.profile.SchoolAreaModel;
import com.kanzhun.utils.T;

import java.util.List;

import io.reactivex.rxjava3.core.Observable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/6
 */
public class EducationSchoolInputViewModel extends FoundationViewModel {
    private String searchContent;
    public int schoolArea;
    private MutableLiveData<List<SearchSchoolResponse.SearchSchoolBean>> searchSchoolLiveData = new MutableLiveData<>();
    public ObservableField<String> schoolObservable = new ObservableField<>();
    public ObservableField<String> schoolErrorDescObservable = new ObservableField<>();
    public ObservableField<Integer> schoolErrorObservable = new ObservableField<>();//0没有错误  1红色错误 2黄色警告
    private MutableLiveData<Boolean> successLivaData = new MutableLiveData<>();

    public EducationSchoolInputViewModel(Application application) {
        super(application);
    }

    public MutableLiveData<List<SearchSchoolResponse.SearchSchoolBean>> getSearchSchoolLiveData() {
        return searchSchoolLiveData;
    }

    public void setSearchContent(String searchContent) {
        this.searchContent = searchContent;
    }

    public String getSearchContent() {
        return searchContent;
    }

    public void searchSchool(String content) {
        Observable<BaseResponse<SearchSchoolResponse>> responseObservable =
                RetrofitManager.getInstance().createApi(FoundationApi.class).searchSchool(content);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<SearchSchoolResponse>() {
            @Override
            public void onSuccess(SearchSchoolResponse data) {
                if (TextUtils.equals(content, searchContent)) {
                    searchSchoolLiveData.setValue(data.result);
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (TextUtils.equals(content, searchContent)) {
                    searchSchoolLiveData.setValue(null);
                }
            }
        });
    }

    public MutableLiveData<Boolean> getSuccessLivaData() {
        return successLivaData;
    }

    public void querySchool() {
        setShowProgressBar();
        Observable<BaseResponse<SchoolAreaModel>> responseObservable = RetrofitManager.getInstance().createApi(MeApi.class).requestQuerySchool(searchContent);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<SchoolAreaModel>() {
            @Override
            public void onSuccess(SchoolAreaModel data) {
                schoolArea = data.schoolArea;
                successLivaData.postValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason != null) {
                    if (reason.getErrCode() == 1107 || reason.getErrCode() == 1110) {
                        schoolErrorObservable.set(1);
                        schoolErrorDescObservable.set(reason.getErrReason());
                    } else {
                        T.ss(reason.getErrReason());
                    }
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }
}
