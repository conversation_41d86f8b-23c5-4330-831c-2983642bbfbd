package com.kanzhun.marry.me.info.activity

import android.content.Intent
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight.Companion.W400
import androidx.compose.ui.text.font.FontWeight.Companion.W700
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.base.compose.ui.O2Button
import com.kanzhun.common.base.compose.ui.O2Toolbar
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.ext.colorResource
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.kotlin.ext.sendObjectLiveEvent
import com.kanzhun.common.kotlin.ui.statusbar.fullScreenAndBlackText
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.kotlin.ktx.getPageSource
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.KMeApi
import com.kanzhun.marry.me.info.bean.CheckCompanyNameBean
import com.sankuai.waimai.router.annotation.RouterUri

//自定义公司名称
@RouterUri(path = [MePageRouter.ME_COMPANY_NAME_HELP])
class CompanyNameHelpActivity : BaseComposeActivity() {
    var str: String = ""
    var pageSource = PageSource.NONE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        str = intent.getStringExtra(BundleConstants.BUNDLE_STRING_1) ?: ""
        pageSource = intent.getPageSource()
        reportPoint("companyname-customize-page-expo") {
            source =
                if (pageSource == PageSource.NEW_USER_TASK_ACTIVITY) "新手阶段" else "存量编辑"
        }
    }

    @Composable
    override fun OnSetContent() {
        fullScreenAndBlackText()
        MyComposeApp(str)
    }

    @Composable
    @Preview(showBackground = true)
    fun PreviewMyComposeApp() {
        MyComposeApp("")
    }

    @Composable
    @Preview(showBackground = true)
    fun PreviewTopCard() {
        topCard()
    }

    @Composable
    fun topCard() {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = R.color.common_color_F5F5F5.colorResource(),
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(16.dp)
        ) {
            val (ref1, ref2) = createRefs()
            Row(verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.constrainAs(ref1) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                }) {
                Image(
                    painter = com.kanzhun.foundation.R.mipmap.chat_ic_notify_tips.painterResource(),
                    contentDescription = "",
                    contentScale = ContentScale.Fit,
                    modifier = Modifier
                        .width(16.dp)
                        .height(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = buildAnnotatedString {
                        append("请填写")
                        withStyle(
                            style = SpanStyle(
                                fontSize = 16.sp,
                                fontWeight = W700,
                                color = R.color.common_color_0046BD.colorResource()
                            )
                        ) {
                            append("完整的公司或单位全称")
                        }
                    },
                    fontSize = 16.sp,
                    fontWeight = W700,
                    color = R.color.common_color_191919.colorResource(),
                )

            }

            Text(text = buildAnnotatedString {
                append("不规范的名称将会被审核驳回，例如：某单位、某公司、XX街道、医院、保密公司、市一中、餐饮店等")
            },
                modifier = Modifier.constrainAs(ref2) {
                    top.linkTo(ref1.bottom, 10.dp)
                    start.linkTo(ref1.start)
                },
                color = R.color.common_color_858585.colorResource(),
                fontSize = 16.sp,
                fontWeight = W400
            )

        }
    }

    @Composable
    fun MyComposeApp(str: String) {
        var isError by remember { mutableStateOf(false) }
        var textStr by remember { mutableStateOf(str) }
        var errStr by remember { mutableStateOf("") }
        ConstraintLayout(
            modifier = Modifier.fillMaxSize(),
        ) {
            val (toolBar, ref1, ref2) = createRefs()

            O2Toolbar(modifier = Modifier.constrainAs(toolBar) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
            }, onBackClick = { onBackPressed()
                reportPoint("companyname-customize-page-click") {
                    source =
                        if (pageSource == PageSource.NEW_USER_TASK_ACTIVITY) "新手阶段" else "存量编辑"
                    type = "返回"
                }
            })

            Column(modifier = Modifier
                .constrainAs(ref1) {
                    top.linkTo(toolBar.bottom)
                    start.linkTo(parent.start)
                }
                .fillMaxSize()
                .padding(16.dp)) {
                // 卡片
                topCard()

                Spacer(modifier = Modifier.height(12.dp))

                // 输入框
                TextField(
                    modifier = Modifier
                        .fillMaxWidth()
                        .onFocusChanged {
                            if (it.isFocused){
                                reportPoint("companyname-customize-page-click") {
                                    source =
                                        if (pageSource == PageSource.NEW_USER_TASK_ACTIVITY) "新手阶段" else "存量编辑"
                                    type = "输入框"
                                }
                            }

                        },
                    value = textStr,
                    onValueChange = {
                        textStr = it
                        isError = false
                    },
                    placeholder = {
                        Text(
                            text = "请输入公司或单位全称",
                            color = R.color.common_color_B8B8B8.colorResource(),
                            style = TextStyle(fontWeight = W400, fontSize = 24.sp)
                        )
                    },
                    colors = TextFieldDefaults.colors(
                        focusedTextColor = R.color.common_color_191919.colorResource(),
                        unfocusedTextColor = R.color.common_color_191919.colorResource(),
                        focusedContainerColor = R.color.common_translate.colorResource(),
                        unfocusedContainerColor = R.color.common_translate.colorResource(),
                        cursorColor = R.color.common_color_191919.colorResource(),
                        focusedIndicatorColor = if (isError) R.color.common_color_FF3F4B.colorResource() else R.color.common_color_191919.colorResource(),
                        errorIndicatorColor = R.color.common_color_FF3F4B.colorResource()
                    ),
                    textStyle = TextStyle(fontWeight = W400, fontSize = 24.sp),
                )

                if (isError) {
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = buildAnnotatedString {
                            append("公司或单位名称不规范，请重新填写哦~ 如有问题可联系客服咨询")
                        },
                        color = R.color.common_color_FF3F4B.colorResource(),
                        fontSize = 12.sp,
                        fontWeight = W400
                    )
                }


            }
            O2Button(text = "保存",
                enabled = textStr.trim().length > 1,
                modifier = Modifier
                    .padding(20.dp)
                    .constrainAs(ref2) {
                        bottom.linkTo(parent.bottom)
                        end.linkTo(parent.end)
                        start.linkTo(parent.start)
                    },
                onClick = {
                    if (textStr.isNotEmpty() && textStr.trim().isNotEmpty()) {
                        val baseResponseObservable = RetrofitManager.getInstance().createApi(
                            KMeApi::class.java
                        ).checkCommonCompany(textStr,if (pageSource == PageSource.NEW_USER_TASK_ACTIVITY)"APP新手阶段" else "APP存量编辑")
                        HttpExecutor.execute(baseResponseObservable,
                            object : BaseRequestCallback<CheckCompanyNameBean>(true) {
                                override fun onSuccess(data: CheckCompanyNameBean?) {
                                    if (data?.status == 2) {
                                        isError = true
                                        errStr = data.alertText
                                    } else {
                                        sendObjectLiveEvent("companyNameEdit", textStr.trim())
                                        setResult(RESULT_OK,
                                            Intent().apply { putExtra("companyNameEdit", textStr.trim()) })
                                        AppUtil.finishActivity(this@CompanyNameHelpActivity)
                                    }
                                }

                                override fun dealFail(reason: ErrorReason?) {
                                }

                            })

                        reportPoint("companyname-customize-page-click") {
                            source =
                                if (pageSource == PageSource.NEW_USER_TASK_ACTIVITY) "新手阶段" else "存量编辑"
                            type = "保存"
                        }
                    }

                })
        }
    }
}


