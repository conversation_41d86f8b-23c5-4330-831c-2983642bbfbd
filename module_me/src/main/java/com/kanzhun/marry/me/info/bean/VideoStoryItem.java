package com.kanzhun.marry.me.info.bean;

import android.net.Uri;

import com.kanzhun.foundation.bean.BaseStoryShowItem;
import com.kanzhun.foundation.api.model.ProfileInfoModel;

/**
 * <AUTHOR>
 * @date 2022/4/15.
 */
public class VideoStoryItem extends BaseStoryShowItem {
    private int type = ProfileInfoModel.Story.TYPE_VIDEO;
    private int duration;////时长
    private Uri thumbnailUri;
    private String thumbnail;//视频缩略图
    private String thumbnailToken;

    public int getDuration() {
        return duration;
    }


    public void setDuration(int duration) {
        this.duration = duration;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getThumbnailToken() {
        return thumbnailToken;
    }

    public void setThumbnailToken(String thumbnailToken) {
        this.thumbnailToken = thumbnailToken;
    }

    public Uri getThumbnailUri() {
        return thumbnailUri;
    }

    public void setThumbnailUri(Uri thumbnailUri) {
        this.thumbnailUri = thumbnailUri;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
