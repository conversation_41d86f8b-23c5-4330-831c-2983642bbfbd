package com.kanzhun.marry.me.setting.fragment;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import com.chad.library.BR;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentUserReportSuccessBinding;
import com.kanzhun.marry.me.setting.callback.UserReportSuccessCallback;
import com.kanzhun.marry.me.setting.viewmodel.UserReportViewModel;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/6/16
 */
public class UserReportSuccessFragment extends FoundationVMShareFragment<MeFragmentUserReportSuccessBinding, FoundationViewModel, UserReportViewModel>
        implements UserReportSuccessCallback {
    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_user_report_success;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

    @Override
    public int getActivityBindingVariable() {
        return 0;
    }

    @Override
    public void clickLeft(View view) {
        ok();
    }

    @Override
    public void ok() {
        Intent intent = new Intent();
        if (!TextUtils.isEmpty(getActivityViewModel().resourceId)) {
            intent.putExtra(BundleConstants.BUNDLE_REPORT_RESOURCE_ID, getActivityViewModel().resourceId);
        }
        if (!TextUtils.isEmpty(getActivityViewModel().userId)) {
            intent.putExtra(BundleConstants.BUNDLE_REPORT_USERID, getActivityViewModel().userId);
        }
        activity.setResult(Activity.RESULT_OK, intent);
        activity.onBackPressed();
    }
}
