package com.kanzhun.marry.me.identify.viewmodel;

import android.app.Application;
import android.text.TextUtils;
import android.util.ArrayMap;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.SecurityUtils;
import com.kanzhun.foundation.api.callback.VideoUploadRequestCallback;
import com.kanzhun.foundation.api.model.ImageUploadModel;
import com.kanzhun.foundation.api.model.VideoUploadModel;
import com.kanzhun.foundation.model.profile.FaceGetOrderIdBean;
import com.kanzhun.foundation.model.profile.FaceGetResultBean;
import com.kanzhun.foundation.utils.UploadFileUtil;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.http.upload.UploadRequestCallback;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.foundation.model.profile.VerifyQueryModel;
import com.kanzhun.foundation.model.profile.VerifySubmitModel;
import com.kanzhun.marry.me.identify.activity.FaceVerifyActivity;
import com.kanzhun.utils.L;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.file.FileUtils;
import com.techwolf.lib.tlog.TLog;

import java.io.File;
import java.util.Arrays;

import io.reactivex.rxjava3.core.Observable;

/**
 * <AUTHOR>
 * @date 2022/4/6.
 */
public class FaceVerifyViewModel extends ResultViewModel {
    String queryId;
    int statusCode;
    MutableLiveData<Integer> resultCode = new MutableLiveData<>();
    MutableLiveData<String> faceImageLiveData = new MutableLiveData<>();
    MutableLiveData<String> faceVideoLiveData = new MutableLiveData<>();

    public FaceVerifyViewModel(Application application) {
        super(application);
    }

    public MutableLiveData<Integer> getResultCode() {
        return resultCode;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public MutableLiveData<String> getFaceImageLiveData() {
        return faceImageLiveData;
    }

    public MutableLiveData<String> getFaceVideoLiveData() {
        return faceVideoLiveData;
    }


    public MutableLiveData<FaceGetOrderIdBean> mFaceGetOrderIdBean = new MutableLiveData<>();
    /**
     *
     * @param name 加密姓名
     * @param idCard 加密身份证号
     * @param certType 1-实名人脸；；2-二次人脸；3-更换设备人脸
     */
    public void getOrderId(String name,String idCard,String certType){
        Observable<BaseResponse<FaceGetOrderIdBean>> baseResponseObservable = RetrofitManager.getInstance().createApi(MeApi.class).getOrderId(SecurityUtils.rc4Encrypt(name,SettingBuilder.getInstance().getApmUidPassword()),SecurityUtils.rc4Encrypt(idCard,SettingBuilder.getInstance().getApmUidPassword()),certType);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<FaceGetOrderIdBean>() {
            @Override
            public void onSuccess(FaceGetOrderIdBean data) {
                mFaceGetOrderIdBean.postValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                resultCode.postValue(reason.getErrCode());
            }
        });
    }

    public void queryResult(String orderId,String name,String idCard,String certType){
        Observable<BaseResponse<FaceGetResultBean>> baseResponseObservable = RetrofitManager.getInstance().createApi(MeApi.class).faceVerifyResult(orderId,SecurityUtils.rc4Encrypt(name,SettingBuilder.getInstance().getApmUidPassword()),SecurityUtils.rc4Encrypt(idCard,SettingBuilder.getInstance().getApmUidPassword()),certType);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<FaceGetResultBean>() {
            @Override
            public void onSuccess(FaceGetResultBean data) {
                resultCode.postValue(FaceVerifyActivity.CERTIFICATION_SUCCESS);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                resultCode.postValue(reason.getErrCode());
            }
        });
    }

    public void verifyFaceQuery() {
        Observable<BaseResponse<VerifyQueryModel>> baseResponseObservable = RetrofitManager.getInstance().createApi(MeApi.class).requestVerifyQuery(queryId);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<VerifyQueryModel>() {
            @Override
            public void onSuccess(VerifyQueryModel data) {
                if (data == null) {
                    resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                } else {
                    statusCode = data.status;
                    switch (data.status) {
                        case 1:
                            ExecutorFactory.execMainTaskDelay(new Runnable() {
                                @Override
                                public void run() {
                                    verifyFaceQuery();
                                }
                            }, data.retryWaitTime);
                            break;
                        case 2:
                            resultCode.postValue(FaceVerifyActivity.CERTIFICATION_SUCCESS);
                            break;
                        default:
                            resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                            break;
                    }
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason != null) {
                    if (reason.getErrCode() > 0) {
                        resultCode.postValue(reason.getErrCode());
                    } else {
                        resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                    }
                }
            }
        });
    }

    public void verifyFace(String name, String idNum, String faceImg, String faceVideo, String certType) {
        ArrayMap<String, Object> params = new ArrayMap<>();
        params.put("name", SecurityUtils.rc4Encrypt(name, SettingBuilder.getInstance().getApmUidPassword()));
        params.put("idNum", SecurityUtils.rc4Encrypt(idNum, SettingBuilder.getInstance().getApmUidPassword()));
        params.put("faceImg", faceImg);
        params.put("faceVideo", faceVideo);
        params.put("certType", !TextUtils.isEmpty(certType) ? certType : "1");
        setShowProgressBar("");
        Observable<BaseResponse<VerifySubmitModel>> baseResponseObservable = RetrofitManager.getInstance().createApi(MeApi.class).requestVerifySubmit(params);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<VerifySubmitModel>() {

            @Override
            public void onSuccess(VerifySubmitModel data) {
                if (data == null) {
                    resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                } else {
                    queryId = data.id;
                    verifyFaceQuery();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason != null) {
                    if (reason.getErrCode() > 0) {
                        resultCode.postValue(reason.getErrCode());
                    } else {
                        resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                    }
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public void uploadImage(String path) {
        File pic = new File(path);
        if (pic.exists()) {
            UploadFileUtil.uploadImage(UploadFileUtil.AUTH_SOURCE, pic, new UploadRequestCallback<ImageUploadModel>() {
                @Override
                public void onSuccess(ImageUploadModel data) {
                    if (data != null) {
                        faceImageLiveData.postValue(data.getOriginImageUrl());
                    } else {
                        resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                    }
                }

                @Override
                public void dealFail(ErrorReason reason) {
                    resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                }
            });
        }

    }

    public void uploadVideo(String path) {
        TLog.error("FaceVerifyViewModel","path:"+ path);
        File video = new File(path);
        if(!TextUtils.isEmpty(video.getParent())){
            File dir = new File(video.getParent());
            if(dir.isDirectory() && dir.exists() && dir.canRead()){
                TLog.error("FaceVerifyViewModel","dir.list:"+ Arrays.toString(dir.list()));
            }
        }
        if (video.exists()) {
            TLog.error("FaceVerifyViewModel","video canRead:"+video.canRead());
            TLog.error("FaceVerifyViewModel","video canWrite:,"+video.canWrite());
            TLog.error("FaceVerifyViewModel","video length:,"+video.length());
            File tmp = new File(video.getParent(),System.currentTimeMillis()+".mp4");
            FileUtils.copyFile(video,tmp);
            TLog.error("FaceVerifyViewModel","tmp path:"+ tmp.getPath());
            TLog.error("FaceVerifyViewModel","tmp canRead:"+tmp.canRead());
            TLog.error("FaceVerifyViewModel","tmp canWrite:,"+tmp.canWrite());
            TLog.error("FaceVerifyViewModel","tmp length:,"+tmp.length());

            UploadFileUtil.uploadVideo(UploadFileUtil.AUTH_SOURCE, tmp, new VideoUploadRequestCallback<VideoUploadModel>() {
                @Override
                public void onSuccess(VideoUploadModel data) {
                    if (data != null) {
                        faceVideoLiveData.postValue(data.url);
                    } else {
                        resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                    }
                    if(tmp.exists()){
                        FileUtils.delete(tmp);
                    }
                }

                @Override
                public void dealFail(ErrorReason reason) {
                    resultCode.postValue(FaceVerifyActivity.CERTIFICATION_FAIL);
                    File video = new File(path);
                    if(!TextUtils.isEmpty(video.getParent())){
                        File dir = new File(video.getParent());
                        if(dir.isDirectory() && dir.exists() && dir.canRead()){
                            TLog.error("FaceVerifyViewModel","dealFail dir.list:"+ Arrays.toString(dir.list()));
                        }
                    }
                    if (video.exists()) {
                        TLog.error("FaceVerifyViewModel","dealFail video canRead:"+video.canRead());
                        TLog.error("FaceVerifyViewModel","dealFail video canWrite:,"+video.canWrite());
                        TLog.error("FaceVerifyViewModel","dealFail video length:,"+video.length());
                    }
                    TLog.error("FaceVerifyViewModel","dealFail tmp path:"+ tmp.getPath());
                    TLog.error("FaceVerifyViewModel","dealFail tmp canRead:"+tmp.canRead());
                    TLog.error("FaceVerifyViewModel","dealFail tmp canWrite:,"+tmp.canWrite());
                    TLog.error("FaceVerifyViewModel","dealFail tmp length:,"+tmp.length());
                    if(tmp.exists()){
                        FileUtils.delete(tmp);
                    }
                }
            });
        }
    }

}
