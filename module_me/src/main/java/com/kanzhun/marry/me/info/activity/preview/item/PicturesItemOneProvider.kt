package com.kanzhun.marry.me.info.activity.preview.item

import android.text.TextUtils
import android.view.View
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.foundation.views.PreviewLikeView
import com.kanzhun.imageviewer.ext.ImageViewerData
import com.kanzhun.imageviewer.ext.storyViewer
import com.kanzhun.marry.me.databinding.MePreviewUserPicturesSectionOneBinding
import com.kanzhun.marry.me.point.MePointReporter
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.bean.BaseStoryShowItem
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel
import com.kanzhun.utils.base.LList

/**
 * 我的照片
 */
class PicturesItemOneProvider(val viewModel: MeUserInfoViewModel) : BaseItemProvider<UserPreviewItemBean, MePreviewUserPicturesSectionOneBinding>() {
    override fun onBindItem(binding: MePreviewUserPicturesSectionOneBinding, item: UserPreviewItemBean) {
        binding.idView.setData(item.data.storyList.get(item.childItemPosition),item.data.baseInfo.hideThumb,item.data.userId,PreviewLikeView.LIKE_TYPE.STORY_PHOTO,viewModel.getMSendLikeBean())
        binding.idView.onClick {
            MePointReporter.reportUserDetailModuleClick(item.data.userId, "我的生活照片")
            val params: MutableMap<String, Any?> = mutableMapOf()
            params["securityId"] = item.data.securityId
            params["type"] = 2
            params["resourceId"] = item.data.storyList.get(item.childItemPosition).id
            params["resourceType"] = 50
            HttpExecutor.requestSimplePost(
                URLConfig.URL_DATA_PUSH_BEHAVIOR,
                params,
                object : SimpleRequestCallback() {
                    override fun onSuccess() {
                    }

                    override fun dealFail(reason: ErrorReason) {

                    }
                })
            binding.idView.storyViewer(listOf( item.data.storyList.get(item.childItemPosition)).toImageViewList(binding.idView), 0) {
            }
        }
    }

    fun play() {
        mBinding?.idView?.startPlay()
    }

    fun stop() {
        mBinding?.idView?.stop()

    }

    fun release(){
        mBinding?.idView?.release()
    }

    private fun List<ProfileInfoModel.Story?>.toImageViewList(view: View?): List<ImageViewerData> {
        return mapIndexed { index, it ->
            if (it?.video.isNullOrBlank()) {
                ImageViewerData(it?.tinyPhoto ?:"", it?.photo ?:"", view, false, it?.text?:"")
            } else {
                ImageViewerData(it?.tinyPhoto?:"", StringUtil.getPlayVideoUrl(it?.video), view, true, it?.text?:"")

            }
        }
    }
}