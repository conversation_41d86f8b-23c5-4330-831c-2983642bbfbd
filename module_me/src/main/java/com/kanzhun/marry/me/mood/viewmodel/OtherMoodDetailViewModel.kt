package com.kanzhun.marry.me.mood.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.entity.StatePageBean
import com.kanzhun.common.kotlin.ui.statelayout.Status
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.response.MoodOtherDetailResponse
import com.kanzhun.foundation.model.matching.UserGuideBlockModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.api.KMeApi
import com.kanzhun.utils.T
import io.reactivex.rxjava3.disposables.Disposable

open class OtherMoodDetailViewModel : BaseViewModel() {

    var moodEditDetailLiveData: MutableLiveData<MoodOtherDetailResponse> = MutableLiveData()
    private var encUserId: String? = null
    var mButtonStatus: SendButtonStatus = SendButtonStatus.NORMAL
    var timeOutLiveData: MutableLiveData<Boolean> = MutableLiveData()
    var firstEnter = true

    fun setEncUserId(encUserId: String) {
        this.encUserId = encUserId
    }


    fun getMoodEditDetail() {
        if (encUserId.isNullOrEmpty()) {
            T.ss("数据异常")
            return
        }
        val observable = RetrofitManager.getInstance().createApi(KMeApi::class.java).getOtherMoodEditDetail(encUserId!!)
        HttpExecutor.execute(
            observable,
            object : BaseRequestCallback<MoodOtherDetailResponse?>() {
                override fun onStart(disposable: Disposable?) {
                    super.onStart(disposable)
                    if (firstEnter) {
                        firstEnter = false
                        showLoading()
                    }
                }

                override fun onSuccess(data: MoodOtherDetailResponse?) {
                    if (data != null) {
                        moodEditDetailLiveData.value = data!!
                    }
                    setButtonStatus(data?.relationStatus)
                    showContent()

//                    pageState.postValue(StatePageBean(Status.ERROR, text = "状态已过期", errCode = ErrorReason.QR_MOOD_TIME_OUT, btnText = "返回", lottieName = "mood_time_out/data.json", lottieDir = "mood_time_out/images"))
                }

                override fun dealFail(reason: ErrorReason?) {
                    if (reason?.errCode == ErrorReason.QR_MOOD_TIME_OUT) {
                        pageState.postValue(StatePageBean(Status.ERROR, text = "状态已过期", errCode = ErrorReason.QR_MOOD_TIME_OUT, btnText = "返回", lottieName = "mood_time_out/data.json", lottieDir = "mood_time_out/images"))
                    } else {
                        showError()
                    }
                    timeOutLiveData.postValue(reason?.errCode == ErrorReason.QR_MOOD_TIME_OUT)
                }

                override fun onComplete() {
                    super.onComplete()
                    //showContent()
                }
            })
    }


    fun likeMood(encMoodId: String?, callback: (Boolean) -> Unit) {

        val observable = RetrofitManager.getInstance().createApi(KMeApi::class.java).likeMood(encMoodId)
        HttpExecutor.execute(observable, object : BaseRequestCallback<Any?>(true) {

            override fun onStart(disposable: Disposable?) {
                super.onStart(disposable)
//                showLoading()
            }


            override fun onSuccess(data: Any?) {
                callback(true)
            }

            override fun dealFail(reason: ErrorReason?) {
                T.ss(reason?.errReason)
            }

            override fun onComplete() {
                super.onComplete()
//                showContent()
            }
        })
    }

    /**
     * 检查是否需要新手阻断
     */
    fun checkCanSendLikeForGuide(): LiveData<UserGuideBlockModel> {
        val result: MutableLiveData<UserGuideBlockModel> = MutableLiveData()
        val observable =
            RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java)
                .checkBeforeSendLike("")
        HttpExecutor.execute<UserGuideBlockModel>(
            observable,
            object : BaseRequestCallback<UserGuideBlockModel?>(false) {
                override fun onSuccess(data: UserGuideBlockModel?) {
                    data?.let {
                        result.value = it
                    }

                }

                override fun dealFail(reason: ErrorReason?) {

                }
            })
        return result
    }


    fun setButtonStatus(relationStatus: Int?) {
        mButtonStatus = when (relationStatus) {
            11 -> { //已发送
                SendButtonStatus.LIKE
            }

            20, 30, 40 -> { //好友
                SendButtonStatus.FRIEND
            }

            else -> { //隐藏
                SendButtonStatus.NORMAL
            }
        }

    }


}

enum class SendButtonStatus {
    LIKE, NORMAL, FRIEND
}
