package com.kanzhun.marry.me.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshFooter;
import com.scwang.smart.refresh.layout.api.RefreshKernel;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.constant.RefreshState;
import com.scwang.smart.refresh.layout.simple.SimpleComponent;
import com.kanzhun.marry.me.R;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/18
 */
public class MeRefreshFooter extends SimpleComponent implements RefreshFooter {

    protected TextView mTitleText;
    protected LinearLayout mLlCenter;
    protected ImageView mProgressView;
    protected ImageView mIvAbove;
    protected float showRate = 0.75f;
    private Listener listener;

    public MeRefreshFooter(Context context) {
        this(context, null);
    }

    public MeRefreshFooter(Context context, AttributeSet attrs) {
        super(context, attrs, 0);
        View.inflate(context, R.layout.me_refresh_footer, this);
        final View thisView = this;
        mProgressView = thisView.findViewById(R.id.iv_progress);
        mTitleText = thisView.findViewById(R.id.tv_title);
        mLlCenter = thisView.findViewById(R.id.ll_center);
        mIvAbove = thisView.findViewById(R.id.iv_above);
        mProgressView.setVisibility(GONE);


    }

    @Override
    public void onInitialized(@NonNull RefreshKernel kernel, int height, int maxDragHeight) {
        super.onInitialized(kernel, height, maxDragHeight);
    }

    @Override
    public void onStateChanged(@NonNull RefreshLayout refreshLayout, @NonNull RefreshState oldState, @NonNull RefreshState newState) {
        switch (newState) {
            case None:
            case PullUpToLoad:
                mTitleText.setText(getResources().getString(R.string.me_refresh_header_title));
                mLlCenter.setVisibility(VISIBLE);
                mIvAbove.setVisibility(VISIBLE);
                break;
            case Loading:
            case LoadReleased:
                //"正在刷新...";
                mLlCenter.setVisibility(INVISIBLE);
                progressStart();
                break;
            case ReleaseToLoad:
                mTitleText.setText(getResources().getString(R.string.me_refresh_header_title_release));
                break;
            default:
                break;
        }
    }

    private void progressStart() {
        final View progressView = mProgressView;
        if (progressView.getVisibility() != VISIBLE) {
            progressView.setVisibility(VISIBLE);
        }
    }

    @Override
    public void onMoving(boolean isDragging, float percent, int offset, int height, int maxDragHeight) {
        if (percent <= 0.75f) {
            mLlCenter.setAlpha(0.0f);
            if (listener != null) {
                listener.onRate(0.0f);
            }
        } else if (percent < 1) {
            float alpha = (percent - showRate) * 4;
            mLlCenter.setAlpha(alpha);
            if (listener != null) {
                listener.onRate(alpha);
            }
        } else {
            mLlCenter.setAlpha(1.0f);
            if (listener != null) {
                listener.onRate(1.0f);
            }
        }
    }

    @Override
    public void onReleased(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        super.onReleased(refreshLayout, height, maxDragHeight);
    }

    @Override
    public void onStartAnimator(@NonNull RefreshLayout refreshLayout, int height, int maxDragHeight) {
        super.onStartAnimator(refreshLayout, height, maxDragHeight);
    }

    @Override
    public int onFinish(@NonNull RefreshLayout refreshLayout, boolean success) {
        final View progressView = mProgressView;
        progressView.setVisibility(GONE);
        mLlCenter.setVisibility(INVISIBLE);
        mProgressView.setVisibility(GONE);
        return 0;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public interface Listener {
        //0-1
        void onRate(float rate);
    }

}
