package com.kanzhun.marry.me.info.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.BR;
import com.chad.library.adapter.base.BaseBinderAdapter;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.bindadapter.CommonBindingAdapters;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.widget.PopupUtils;
import com.kanzhun.common.views.AudioRecorderPlayView;
import com.kanzhun.common.views.textview.SingleUnderLineTextView;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.adapter.UserInfoStoryAdapter;
import com.kanzhun.foundation.api.bean.TextAnswerBean;
import com.kanzhun.foundation.api.bean.UserInfoMomentsBean;
import com.kanzhun.foundation.api.bean.UserInfoStoriesBean;
import com.kanzhun.foundation.api.bean.UserInfoStoryBean;
import com.kanzhun.foundation.api.bean.UserInfoTitleBean;
import com.kanzhun.foundation.api.bean.VoiceAnswerBean;
import com.kanzhun.foundation.api.model.ABFace;
import com.kanzhun.foundation.api.model.UserCertStatusModel;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.foundation.bean.MeBaseInfoShowBean;
import com.kanzhun.foundation.callback.FoundationSocialCallback;
import com.kanzhun.foundation.callback.LikeStoryCallback;
import com.kanzhun.foundation.databinding.CommonPopShowInfoBinding;
import com.kanzhun.foundation.databinding.CommonPopUserInfoApproveListBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewAbFaceBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewBaseInfoBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewInfoTitleBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewLovePersonalityBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewMomentBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewPrivateRecommendBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewStoryBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewTextAnswerBinding;
import com.kanzhun.foundation.databinding.MeItemInfoPreviewVoiceAnswerBinding;
import com.kanzhun.foundation.databinding.MeLayoutInfoPreviewHeaderBinding;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.profile.OpenScreen;
import com.kanzhun.foundation.model.profile.ProfileMetaModel;
import com.kanzhun.foundation.model.profile.UserTabModel;
import com.kanzhun.foundation.player.OPlayerHelper;
import com.kanzhun.foundation.router.MatchingPageRouter;
import com.kanzhun.foundation.router.SocialPageRouter;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.foundation.views.ABImpressionView;
import com.kanzhun.foundation.views.StoryVideoView;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentMeInfoPreviewBinding;
import com.kanzhun.marry.me.databinding.MeItemInfoPreviewInfoMomentTitleBinding;
import com.kanzhun.marry.me.info.bean.UserInfoMomentsTitleBean;
import com.kanzhun.marry.me.info.callback.MeInfoPreviewFragmentCallback;
import com.kanzhun.marry.me.info.viewmodel.MeInfoPreviewFragmentViewModel;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;
import com.kanzhun.utils.views.OnMultiClickListener;
import com.youth.banner.listener.OnPageChangeListener;

import org.alita.config.LogConfig;
import org.alita.core.AlitaMediaCore;
import org.alita.webrtc.SurfaceViewRenderer;

import java.util.List;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/10/10
 */
public class MeInfoPreviewFragment extends FoundationVMFragment<MeFragmentMeInfoPreviewBinding, MeInfoPreviewFragmentViewModel>
        implements MeInfoPreviewFragmentCallback, LikeStoryCallback, FoundationSocialCallback {
    private int recycleState;
    private BaseBinderAdapter adapter;
    private MeLayoutInfoPreviewHeaderBinding headerBinding;
    private boolean isFirstResume = true;
    private PopupUtils popupCertShow;
    private int mPageSource;
    private long resumeTime;

    public static MeInfoPreviewFragment newInstance(ProfileMetaModel model, int pageSource) {
        MeInfoPreviewFragment fragment = new MeInfoPreviewFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(BundleConstants.BUNDLE_DATA, model);
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT, pageSource);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static MeInfoPreviewFragment newInstance(ProfileMetaModel model) {
       return newInstance(model, 0);
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_me_info_preview;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        //设置日志
        AlitaMediaCore.getInstance().setLogLevelOfDebug(LogConfig.LOG_LEVEL_INFO);
        initView();
        initData();
    }

    private void initData() {
        getViewModel().getSuccessLiveData().observe(this, new Observer<List<Object>>() {
            @Override
            public void onChanged(List<Object> data) {
                updateData(data);
            }
        });

        getViewModel().getClickLikeLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean matchingBlockInfoModel) {
                //表示喜欢
                clickSendLike();
            }
        });
    }

    private void updateData(List<Object> data) {
        //我的恋爱性格
        adapter.addItemBinder(OpenScreen.class, new BaseDataBindingItemBinder<OpenScreen, MeItemInfoPreviewLovePersonalityBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_love_personality;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewLovePersonalityBinding> holder, MeItemInfoPreviewLovePersonalityBinding binding, OpenScreen item) {
                binding.setBean(item);
                if (!TextUtils.isEmpty(item.bgHeadImg)) {
                    binding.ovHead.loadRoundTop(item.bgHeadImg);
                }
                if (!LList.isEmpty(item.mbtiKeys)) {
                    binding.llKeys.removeAllViews();
                    for (String key : item.mbtiKeys) {
                        SingleUnderLineTextView textView = new SingleUnderLineTextView(binding.getRoot().getContext());
                        textView.setTextColor(getResources().getColor(R.color.common_color_191919));
                        textView.setTextSize(18);
                        textView.setText(key);
                        textView.setColor(item.bgColor);
                        binding.llKeys.addView(textView);
                        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) textView.getLayoutParams();
                        layoutParams.setMarginStart(getViewModel().do_11);
                        layoutParams.setMarginEnd(getViewModel().do_5);
                    }
                }
            }
        });

        //文字问答
        adapter.addItemBinder(TextAnswerBean.class, new BaseDataBindingItemBinder<TextAnswerBean, MeItemInfoPreviewTextAnswerBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_text_answer;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewTextAnswerBinding> holder, MeItemInfoPreviewTextAnswerBinding binding, TextAnswerBean item) {
                binding.setItem(item);
                binding.setReplyShow(getViewModel().getReplyShow());
                binding.tvQuestionIndex.setText((item.index + 1) + "");
                CommonBindingAdapters.showInvisible(binding.ivLike, getViewModel().isShowLike());
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_like);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemInfoPreviewTextAnswerBinding> holder, @NonNull View view, TextAnswerBean data, int position) {
                int id = view.getId();
                if (id == R.id.iv_like) {
                    if (MultiClickUtil.isMultiClick()) {
                        return;
                    }
                    likeText(holder.getDataBinding().qmuiTextAnswer, data.id);
                }
            }
        });

        //声音问答
        adapter.addItemBinder(VoiceAnswerBean.class, new BaseDataBindingItemBinder<VoiceAnswerBean, MeItemInfoPreviewVoiceAnswerBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_voice_answer;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewVoiceAnswerBinding> holder, MeItemInfoPreviewVoiceAnswerBinding binding, VoiceAnswerBean item) {
                binding.setReplyShow(getViewModel().getReplyShow());
                binding.tvContent.setText(item.question);
                if (getViewModel().getVoicePlaying() == MeInfoPreviewFragmentViewModel.STATUS_RECORD_PLAYING) {
                    binding.ivIcon.setImageResource(R.drawable.me_ic_icon_info_preview_voice_pause);
                } else {
                    binding.ivIcon.setImageResource(R.drawable.me_ic_icon_info_preview_voice_start);
                }
                if (item.voiceExtInfoBean != null) {
                    binding.vVoice.setWaveData(item.voiceExtInfoBean.wave);
                    if (getViewModel().getVoicePlayingLiveData().getValue() != null) {
                        binding.vVoice.setReadPercent(getViewModel().getVoicePlayingLiveData().getValue());
                    }
                }
                CommonBindingAdapters.showInvisible(binding.ivLike, getViewModel().isShowLike());
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_icon, R.id.iv_like);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MeItemInfoPreviewVoiceAnswerBinding> holder, @NonNull View view, VoiceAnswerBean data, int position) {
                int id = view.getId();
                if (id == R.id.iv_icon) {
                    voicePlayClick(holder.getDataBinding().getRoot(), data);
                } else if (id == R.id.iv_like) {
                    if (MultiClickUtil.isMultiClick()) {
                        return;
                    }
                    likeVoice(holder.getDataBinding().qmuiVoiceAnswer, data.id);
                }
            }
        });


        adapter.addItemBinder(UserInfoStoriesBean.class, new BaseDataBindingItemBinder<UserInfoStoriesBean, MeItemInfoPreviewStoryBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_story;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewStoryBinding> holder, MeItemInfoPreviewStoryBinding binding, UserInfoStoriesBean item) {
                getViewModel().releaseStoryVideo(item);
                binding.setItem(item);
                binding.tvTotalNum.setText(String.valueOf(item.storyBeanList.size()));
                int currBannerPosition = getViewModel().getCurrBannerPosition();
                binding.tvNum.setText((currBannerPosition + 1) + "/");
                binding.tvDesc.setText(item.storyBeanList.get(currBannerPosition).story.text);
                UserInfoStoryAdapter userInfoStoryAdapter = new UserInfoStoryAdapter(item.storyBeanList, getViewModel().isShowLike(), getViewModel().getReplyShow(), currBannerPosition, MeInfoPreviewFragment.this);
                binding.banner.setAdapter(userInfoStoryAdapter, false)
                        .isAutoLoop(false)
                        .addBannerLifecycleObserver(MeInfoPreviewFragment.this)
                        .setCurrentItem(currBannerPosition, false);
                if (item.storyBeanList.size() > 1) {
                    binding.banner.setBannerGalleryEffect(0, 38, 10, 0.9f);
                } else {
                    binding.banner.setBannerGalleryEffect(16, 32, 0, 1.0f);
                    binding.tvDesc.setPadding(QMUIDisplayHelper.dpToPx(16), 0, 0, 0);
                }
                binding.banner.addOnPageChangeListener(new OnPageChangeListener() {
                    @Override
                    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

                    }

                    @Override
                    public void onPageSelected(int position) {
                        int size = item.storyBeanList.size();
                        int realPosition = size == 1 ? position : position % size;
                        getViewModel().setCurrBannerPosition(realPosition);
                        UserInfoStoryBean storyBean = item.storyBeanList.get(realPosition);
                        binding.tvNum.setText((realPosition + 1) + "/");
                        binding.tvDesc.setText(storyBean.story.text);
                        if (!TextUtils.isEmpty(storyBean.playVideo)) {
                            //为了解决banner循环播放 视频不能播放bug
                            RecyclerView recyclerView = (RecyclerView) binding.banner.getViewPager2().getChildAt(0);
                            View viewByPosition = recyclerView.getLayoutManager().findViewByPosition(position);
                            if (viewByPosition != null) {
                                StoryVideoView storyVideoView = viewByPosition.findViewById(R.id.video_view);
                                storyVideoView.setVideo();
                            }
                        }
                        calculateVideoStoryPlay();
                    }

                    @Override
                    public void onPageScrollStateChanged(int state) {
                    }
                });

                if (getViewModel().getVideoHeight() <= 0) {
                    binding.banner.post(new Runnable() {
                        @Override
                        public void run() {
                            getViewModel().setVideoHeight(binding.banner.getHeight());
                            getViewModel().setVideoTopHeight(binding.tvTitleBig.getHeight() + QMUIDisplayHelper.dpToPx(36));
                        }
                    });
                }
            }
        });

        //AB面
        adapter.addItemBinder(ABFace.class, new BaseDataBindingItemBinder<ABFace, MeItemInfoPreviewAbFaceBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_ab_face;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewAbFaceBinding> holder, MeItemInfoPreviewAbFaceBinding binding, ABFace item) {
                binding.faceView.setData(item, false, getViewModel().isShowLike(), getViewModel().getReplyShow());
                binding.faceView.setLickListener(new ABImpressionView.LikeListener() {
                    @Override
                    public void likeABFace(boolean isAFace) {
                        likeFace(binding.faceView, item.id, isAFace);
                    }
                });
            }
        });

        //标题
        adapter.addItemBinder(UserInfoTitleBean.class, new BaseDataBindingItemBinder<UserInfoTitleBean, MeItemInfoPreviewInfoTitleBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_info_title;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewInfoTitleBinding> holder, MeItemInfoPreviewInfoTitleBinding binding, UserInfoTitleBean item) {
                binding.setItem(item);
                binding.ivIcon.setImageResource(item.resId);
            }
        });
        //动态标题
        adapter.addItemBinder(UserInfoMomentsTitleBean.class, new BaseDataBindingItemBinder<UserInfoMomentsTitleBean, MeItemInfoPreviewInfoMomentTitleBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_info_moment_title;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewInfoMomentTitleBinding> holder, MeItemInfoPreviewInfoMomentTitleBinding binding, UserInfoMomentsTitleBean item) {
                binding.setItem(item);
                binding.ivIcon.setImageResource(item.resId);
            }
        });

        //动态
        adapter.addItemBinder(UserInfoMomentsBean.class, new BaseDataBindingItemBinder<UserInfoMomentsBean, MeItemInfoPreviewMomentBinding>() {

            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_moment;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewMomentBinding> holder, MeItemInfoPreviewMomentBinding binding, UserInfoMomentsBean item) {
                binding.setMoments(item.moments);
                binding.setCallback(MeInfoPreviewFragment.this);
            }
        });

        adapter.setList(data);
    }

    /**
     * 声音故事播放
     */
    private void voicePlayClick(View view, VoiceAnswerBean data) {
        if (data.voiceExtInfoBean == null) {
            return;
        }
        getViewModel().voiceAnswerPlay(data);
        getViewModel().getVoicePlayLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean != null) {
                    View viewById = view.findViewById(R.id.iv_icon);
                    if (viewById instanceof ImageView) {
                        ImageView imageView = (ImageView) viewById;
                        imageView.setImageResource(aBoolean ? R.drawable.me_ic_icon_info_preview_voice_pause : R.drawable.me_ic_icon_info_preview_voice_start);
                    }
                    if (!aBoolean) {
                        calculateRecycleScroll();
                    }
                    getViewModel().getVoicePlayLiveData().setValue(null);
                }
            }
        });
        getViewModel().getVoicePlayingLiveData().observe(this, new Observer<Float>() {
            @Override
            public void onChanged(Float aFloat) {
                View vVoice = view.findViewById(R.id.v_voice);
                if (vVoice instanceof AudioRecorderPlayView) {
                    AudioRecorderPlayView playView = (AudioRecorderPlayView) vVoice;
                    playView.setReadPercent(aFloat);
                }
            }
        });
    }

    private void initView() {
        getDataBinding().recyclerview.setClipToPadding(false);
        addRecyclerViewScrollListener();
        Bundle arguments = getArguments();
        if (arguments != null) {
            mPageSource = arguments.getInt(BundleConstants.BUNDLE_DATA_INT);
            ProfileMetaModel infoModel = (ProfileMetaModel) arguments.getSerializable(BundleConstants.BUNDLE_DATA);
            if (infoModel != null) {
                getViewModel().setProfileMetaModel(infoModel);
                setData();
                getDataBinding().recyclerview.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        getViewModel().setRecycleHeight(getDataBinding().recyclerview.getHeight());
                        getViewModel().setUserInfoData(infoModel);
                        getViewModel().requestUserCertStatus(infoModel.userId);
                    }
                }, 500);
            }
        }
    }

    private void setData() {
        adapter = new BaseBinderAdapter();
        addHeaderData();
        getDataBinding().recyclerview.setAdapter(adapter);
    }

    private void addHeaderData() {
        headerBinding = MeLayoutInfoPreviewHeaderBinding.inflate(LayoutInflater.from(activity));
        headerBinding.setIsSelf(getViewModel().isSelf());
        headerBinding.setReplyShow(getViewModel().getReplyShow());
        headerBinding.setItem(getViewModel().getProfileMetaModel());
        String liveVideo = getViewModel().getProfileMetaModel().baseInfo.liveVideo;
        if (!TextUtils.isEmpty(liveVideo)) {
            String playVideoUrl = StringUtil.getPlayVideoUrl(liveVideo);
            //表示是视频封面
            OPlayerHelper oPlayerHelper = new OPlayerHelper(activity, new OPlayerHelper.CallBack() {
                @Override
                public void onFirstFrame() {
                    headerBinding.ivAvatar.setVisibility(View.GONE);
                }
            });
            oPlayerHelper.setPlayView(headerBinding.svVideo);
            oPlayerHelper.setFrameScaleType(SurfaceViewRenderer.RENDER_MODE_FULL_FILL_SCREEN);
            oPlayerHelper.setLooper(true);
            oPlayerHelper.setMute(true);
            oPlayerHelper.setPlayUrl(playVideoUrl);
            oPlayerHelper.start();
            getViewModel().setHeaderPlayerHelper(oPlayerHelper);
        }

        BaseBinderAdapter baseInfoAdapter = new BaseBinderAdapter();
        baseInfoAdapter.addItemBinder(MeBaseInfoShowBean.class, new BaseDataBindingItemBinder<MeBaseInfoShowBean, MeItemInfoPreviewBaseInfoBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_base_info;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewBaseInfoBinding> holder, MeItemInfoPreviewBaseInfoBinding binding, MeBaseInfoShowBean item) {
                binding.setItem(item);
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MeItemInfoPreviewBaseInfoBinding> holder, @NonNull View view, MeBaseInfoShowBean data, int position) {
                super.onClick(holder, view, data, position);
                if (data.resId == R.drawable.me_ic_occupation_basic_info && !TextUtils.isEmpty(getViewModel().getProfileMetaModel().baseInfo.companyName)) {
                    showCompanyNamePop(view, getViewModel().getProfileMetaModel().baseInfo.companyName);
                }
            }
        });
        headerBinding.rvInfo.setLayoutManager(new FlexboxLayoutManager(activity));
        baseInfoAdapter.setList(getViewModel().getBaseInfoData(getViewModel().getProfileMetaModel().baseInfo));
        headerBinding.rvInfo.setAdapter(baseInfoAdapter);

        BaseBinderAdapter privateAdapter = new BaseBinderAdapter();
        privateAdapter.addItemBinder(String.class, new BaseDataBindingItemBinder<String, MeItemInfoPreviewPrivateRecommendBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_item_info_preview_private_recommend;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeItemInfoPreviewPrivateRecommendBinding> holder, MeItemInfoPreviewPrivateRecommendBinding binding, String item) {
                binding.setItem(item);
            }
        });
        headerBinding.rvSec.setLayoutManager(new FlexboxLayoutManager(activity));
        privateAdapter.setList(getViewModel().getSecData(getViewModel().getProfileMetaModel().baseInfo));
        headerBinding.rvSec.setAdapter(privateAdapter);
        if (!getViewModel().isSelf()) {
            headerBinding.tvSecTitle.setText(getResources().getString(R.string.me_info_preview_private_title_2));
        }
        adapter.addHeaderView(headerBinding.getRoot());

        if (getViewModel().isShowLike()) {
            headerBinding.ivLike.setVisibility(View.VISIBLE);
            headerBinding.ivLike.setOnClickListener(new OnMultiClickListener() {
                @Override
                public void OnNoMultiClick(View v) {
                    likeAvatar(headerBinding.flAvatar);
                }
            });
        }

        if ((getViewModel().isSelf() && !AccountHelper.getInstance().isFormalUser())
                || (!getViewModel().isSelf() && getViewModel().getProfileMetaModel().abnormalUser())) {
            headerBinding.ivApprove.setVisibility(View.GONE);
        } else {
            headerBinding.ivApprove.setVisibility(View.VISIBLE);
        }

        headerBinding.ivApprove.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                if (getViewModel().getStatusModel() != null) {
                    showUserPop(v);
                }
            }
        });
    }

    //滑动监听
    private void addRecyclerViewScrollListener() {
        getDataBinding().recyclerview.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                recycleState = newState;
                // 当不滚动时，也就是停止滑动的情况下
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    calculateVideoStoryPlay();
                    calculateABFace();
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
            }
        });
    }

    private void calculateVideoStoryPlay() {
        int recycleHeight = getViewModel().getRecycleHeight();
        int videoHeight = getViewModel().getVideoHeight();
        int videoTopHeight = getViewModel().getVideoTopHeight();
        LinearLayoutManager manager = (LinearLayoutManager) getDataBinding().recyclerview.getLayoutManager();
        int firstPosition = manager.findFirstVisibleItemPosition();
        int lastPosition = manager.findLastVisibleItemPosition();
        boolean videoStoryPlay = false;
        boolean isStory = false;
        //有header 需要减1
        if (firstPosition > 0) {
            Object item = adapter.getItem(firstPosition - 1);
            if (item instanceof UserInfoStoriesBean) {
                //表示第一个是生活故事模块
                isStory = true;
                UserInfoStoriesBean storiesBean = (UserInfoStoriesBean) item;
                UserInfoStoryBean storyBean = storiesBean.storyBeanList.get(getViewModel().getCurrBannerPosition());
                if (!TextUtils.isEmpty(storyBean.playVideo)) {
                    //表示当前banner是视频
                    View firstView = manager.findViewByPosition(firstPosition);
                    int firstTop = firstView.getTop();
                    if (firstTop > -videoTopHeight) {
                        if (recycleHeight - firstTop >= videoHeight) {
                            if (!getViewModel().voiceIsPlaying()) {
                                setPlayVideoData(storyBean);
                            }
                            videoStoryPlay = true;
                        }
                    }
                }
            }
        }
        if (!isStory) {
            //第一个没有匹配到 继续匹配
            for (int i = firstPosition + 1; i <= lastPosition; i++) {
                Object item1 = adapter.getItem(i - 1);
                if (item1 instanceof UserInfoStoriesBean) {
                    View view = manager.findViewByPosition(i);
                    UserInfoStoriesBean storiesBean = (UserInfoStoriesBean) item1;
                    UserInfoStoryBean storyBean = storiesBean.storyBeanList.get(getViewModel().getCurrBannerPosition());
                    if (!TextUtils.isEmpty(storyBean.playVideo)) {
                        if (i != lastPosition) {
                            if (!getViewModel().voiceIsPlaying()) {
                                setPlayVideoData(storyBean);
                            }
                            videoStoryPlay = true;
                        } else {
                            //最后一个position
                            int top = view.getTop();
                            if (top > -videoTopHeight) {
                                if (recycleHeight - top - videoTopHeight >= videoHeight) {
                                    if (!getViewModel().voiceIsPlaying()) {
                                        setPlayVideoData(storyBean);
                                    }
                                    videoStoryPlay = true;
                                }
                            }
                        }
                    }
                }
            }
        }

        if (!videoStoryPlay) {
            getViewModel().pauseStoryVideo();
        }
    }

    private void setPlayVideoData(UserInfoStoryBean storyBean) {
        getViewModel().playStoryVideo(storyBean.story.id, storyBean.oPlayerHelper, storyBean.playVideo);
    }

    private void calculateABFace() {
        int recycleHeight = getViewModel().getRecycleHeight();
        LinearLayoutManager manager = (LinearLayoutManager) getDataBinding().recyclerview.getLayoutManager();
        int firstPosition = manager.findFirstVisibleItemPosition();
        int lastPosition = manager.findLastVisibleItemPosition();
        boolean haveFace = false;
        //有header 需要减1
        if (firstPosition > 0) {
            Object item = adapter.getItem(firstPosition - 1);
            if (item instanceof ABFace) {
                //表示第一个是AB面模块
                ABFace abFaceMeta = (ABFace) item;
                View firstView = manager.findViewByPosition(firstPosition);
                int firstTop = firstView.getTop();
                if (firstTop > 0) {
                    int height = firstView.getHeight();
                    if (recycleHeight - firstTop >= height) {
                        ABImpressionView view = firstView.findViewById(R.id.faceView);
                        if (!abFaceMeta.firstShowed) {
                            abFaceMeta.firstShowed = true;
                            view.showAnimator();
                        }
                        haveFace = true;
                    }
                }
            }
        }
        if (!haveFace) {
            //第一个没有匹配到 继续匹配
            for (int i = firstPosition + 1; i <= lastPosition; i++) {
                Object item1 = adapter.getItem(i - 1);
                if (item1 instanceof ABFace) {
                    ABFace abFaceMeta = (ABFace) item1;
                    View view = manager.findViewByPosition(i);
                    if (i != lastPosition) {
                        ABImpressionView abImpressionView = view.findViewById(R.id.faceView);
                        if (!abFaceMeta.firstShowed) {
                            abFaceMeta.firstShowed = true;
                            abImpressionView.showAnimator();
                        }
                        haveFace = true;
                    } else {
                        //最后一个position
                        int top = view.getTop();
                        if (top > 0) {
                            int height = view.getHeight();
                            if (recycleHeight - top >= height) {
                                ABImpressionView abImpressionView = view.findViewById(R.id.faceView);
                                if (!abFaceMeta.firstShowed) {
                                    abFaceMeta.firstShowed = true;
                                    abImpressionView.showAnimator();
                                }
                                haveFace = true;
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 语音停止时候要重新计算视频故事是否要播放
     */
    private void calculateRecycleScroll() {
        if (recycleState == RecyclerView.SCROLL_STATE_IDLE) {
            calculateVideoStoryPlay();
        }
    }

    private void likeAvatar(View view) {
        getViewModel().likeAvatar(view, getViewModel().getProfileMetaModel().baseInfo.avatar);

    }

    /**
     * 喜欢AB面
     */
    private void likeFace(View view, String resId, boolean isAFace) {
        getViewModel().likeFace(view, resId, isAFace);

    }

    @Override
    public void likeStory(UserInfoStoryBean infoStoryBean) {
        getViewModel().likeStory(null, infoStoryBean.story.id);
    }

    /**
     * 喜欢文本问答
     */
    private void likeText(View view, String resId) {
        getViewModel().likeText(view, resId);

    }

    /**
     * 喜欢声音
     */
    private void likeVoice(View view, String resId) {
        getViewModel().likeVoice(view, resId);

    }

    /**
     * 跳转发送喜欢页面
     */
    private void clickSendLike() {
        if (!getViewModel().userProfileLocked()) {
            getViewModel().onPause();
            if (getViewModel().getReplyShow().get()) {
                MatchingPageRouter.jumpSendLikeDialog((FragmentActivity) activity, getViewModel().getSendLikeModel(), Constants.MATCH_SEND_REPLY_TYPE, mPageSource);
            } else {
                MatchingPageRouter.jumpSendLikeDialog((FragmentActivity) activity, getViewModel().getSendLikeModel(), mPageSource);
            }
        }
    }

    private void showUserPop(View view) {
        UserCertStatusModel statusModel = getViewModel().getStatusModel();
        int popupWidth = QMUIDisplayHelper.dp2px(activity, 240);
        CommonPopUserInfoApproveListBinding binding = DataBindingUtil.inflate(LayoutInflater.from(activity), R.layout.common_pop_user_info_approve_list, null, false);

        if (statusModel.faceCert != null && statusModel.faceCert.status == 2) {
            binding.icName.getRoot().setVisibility(View.VISIBLE);
        } else {
            binding.icName.getRoot().setVisibility(View.GONE);
        }

        if (statusModel.avatarCert != null && statusModel.avatarCert.status == 3) {
            binding.icAvatar.getRoot().setVisibility(View.VISIBLE);
        } else {
            binding.icAvatar.getRoot().setVisibility(View.GONE);
        }


        if (statusModel.eduCert != null && statusModel.eduCert.status == 4) {
            binding.icSchool.getRoot().setVisibility(View.VISIBLE);
            switch (statusModel.eduCert.certType) {
                case 10:
                    binding.icSchool.setContent(getString(R.string.common_pop_approve_school_content_1));
                    break;
                case 20:
                    binding.icSchool.setContent(getString(R.string.common_pop_approve_school_content_2));
                    break;
                case 30:
                    binding.icSchool.setContent(getString(R.string.common_pop_approve_school_content_3));
                    break;
                default:
                    binding.icSchool.setContent(getString(R.string.common_pop_approve_school_content_4));
                    break;
            }
        } else {
            binding.icSchool.getRoot().setVisibility(View.GONE);
        }

        if (statusModel.companyCert != null && statusModel.companyCert.status == 3) {
            binding.icWork.getRoot().setVisibility(View.VISIBLE);
            if (statusModel.companyCert.certType == 1) {
                binding.icWork.setContent(getString(R.string.common_pop_approve_work_content_2));

            } else {
                binding.icWork.setContent(getString(R.string.common_pop_approve_work_content_1));
            }
        } else {
            binding.icWork.getRoot().setVisibility(View.GONE);
        }

        popupCertShow = new PopupUtils.Builder(activity).setContentView(binding.getRoot()).setShadow(activity.getWindow(), 0.6f)
                .setWidth(popupWidth).setHeight(ViewGroup.LayoutParams.WRAP_CONTENT).build();
        int dp_10 = QMUIDisplayHelper.dp2px(activity, 10);
        int xOff = -Math.abs(view.getWidth() / 2 - popupWidth / 2);
        popupCertShow.showAsDropDown(view, xOff, dp_10);

    }

    public PopupUtils getPopupCertShow() {
        return popupCertShow;
    }

    private void showCompanyNamePop(View view, String companyName) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        CommonPopShowInfoBinding binding = DataBindingUtil.inflate(LayoutInflater.from(activity), R.layout.common_pop_show_info, null, false);
        binding.tvContent.setText(companyName);
        PopupUtils popupUtils = new PopupUtils.Builder(activity).setContentView(binding.getRoot())
                .setWidth(ViewGroup.LayoutParams.WRAP_CONTENT).setHeight(ViewGroup.LayoutParams.WRAP_CONTENT).build();
        View contentView = popupUtils.getPopupWindow().getContentView();
        int[] locations = new int[2];
        contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        int measuredWidth = contentView.getMeasuredWidth();
        int measuredHeight = contentView.getMeasuredHeight();
        int px = QMUIDisplayHelper.dp2px(activity, 50);
        int dp_5 = QMUIDisplayHelper.dp2px(activity, 5);
        view.getLocationOnScreen(locations);
        popupUtils.showAtLocation(view, Gravity.NO_GRAVITY, px + (locations[0] + view.getWidth() / 2) - (measuredWidth / 2), locations[1] - measuredHeight + dp_5);
    }

    @Override
    public void toUserSelfDynamic() {
        SocialPageRouter.jumpToUserDynamicActivity(activity, getViewModel().getUserId());
        getViewModel().cleanUnReadMoment();
    }

    @Override
    public void toPublishDynamic(UserTabModel userTabModel) {

    }


    @Override
    public void onResume() {
        super.onResume();
        resumeTime = System.currentTimeMillis();
        if (isFirstResume) {
            isFirstResume = false;
        } else {
            if (getViewModel().getProfileMetaModel() != null) {
                ExecutorFactory.execMainTaskDelay(new Runnable() {
                    @Override
                    public void run() {
                        calculateRecycleScroll();
                    }
                }, 400);
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        getViewModel().onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getViewModel().release();
    }
}
