package com.kanzhun.marry.me.info.activity.preview.item

import android.view.View
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.flexbox.FlexboxLayoutManager
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.joinNotNull
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.BlurTextView
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.foundation.api.callback.SendLikeBeanItemData
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.facade.TempTaskType
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.ktx.getShowLength
import com.kanzhun.foundation.kotlin.ktx.toAgeString
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.model.profile.ext.getWeightStringMoreThan40
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.views.PreviewLikeView
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MePreviewUserBaseInfoSectionBinding
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel
import com.kanzhun.marry.me.personality.dialog.UserIdentityListDialog
import com.kanzhun.marry.me.point.MePointReporter

/**
 * 顶部基本信息
 */
class BaseInfoItemProvider(val activity: FragmentActivity, val viewModel: MeUserInfoViewModel) : BaseItemProvider<UserPreviewItemBean, MePreviewUserBaseInfoSectionBinding>() {
    override fun onBindItem(binding: MePreviewUserBaseInfoSectionBinding, item: UserPreviewItemBean) {
        binding.run {
            val profileMetaModel = item.data

            val moodInfo = profileMetaModel.moodInfo
//            moodInfo = UserMoodInfoBean("OAuKs_wOXfLHTxM_Br-W8M6Ruu_U2C3zQynC_SFewkY~",
//                "x72Fq3MBk-otYVMpkWVEmPuajXL_z_gsSZHpF_U2o8E~",
//                "https://lengjing-cdn.zhipin.com/system/public/P2b-9c9HFUa-CeiwdnzhCW-0TNdFv9OmFo9X-bNB9On6uOeNnzmo7tI1jvUYFmng_B44mHH8cecij69wO9J4-w~~.png",
//                "焦虑")

            if (item.block) {
                idLike.gone()
            } else {
                idLike.visible()
                idLike.setData(
                    item.data.baseInfo.hideThumb, item.data.thumbInfo?.avatar, true, item.data.userId,
                    PreviewLikeView.LIKE_TYPE.AVATAR, "",viewModel.getMSendLikeBean(),SendLikeBeanItemData(profileMetaModel.baseInfo?.tinyAvatar),
                )
            }

            if (moodInfo == null || moodInfo.encMoodId.isNullOrEmpty()) {
                llMood.gone()
            } else {
                llMood.visible()
                llMood.clickWithTrigger {
                    if (AccountHelper.getInstance().userId == profileMetaModel.userId) {
                        //主态
                        MePageRouter.jumpToMyMoodDetailActivity(activity, PageSource.ME_USER_INFO_PREVIEW_ACTIVITY)
                    } else {
                        //客态
                        MePageRouter.jumpToOtherMoodDetailActivity(activity, profileMetaModel.userId, PageSource.ME_USER_INFO_PREVIEW_ACTIVITY)
                        reportPoint("user-mood-click") {
                            actionp2 = moodInfo.moodTitle
                            type = "个人信息页心情状态"
                        }
                    }
                }
                ivMood.load(moodInfo.moodIcon)
                tvMoodName.text = moodInfo.moodTitle
            }

            if(AccountHelper.getInstance().userId == profileMetaModel.userId){
                ServiceManager.getInstance().tempTask.getTempTaskRepository()?.handlerView(binding.idLottieAnimationView,
                    TempTaskType.Preview)
            }else{
                idLottieAnimationView.gone()
            }

            tvUserName.text = profileMetaModel.baseInfo?.nickName.let {
                if(it?.isNotEmpty() == true){
                    it
                }else{
                    "昵称未填写"
                }
            }
            val identityAdapter = rlvIdentityTags.adapter
            if (identityAdapter is TabAdapter) {
                identityAdapter.setNewInstance(profileMetaModel.certInfo?.certTagList?.toMutableList() ?: mutableListOf())
            } else {
                rlvIdentityTags.layoutManager = FlexboxLayoutManager(root.context)
                rlvIdentityTags.adapter = TabAdapter(profileMetaModel.certInfo?.certTagList ?: listOf())
            }

            val baseInfoAdapter = rcvInformation.adapter
            if (baseInfoAdapter is BaseInfoAdapter) {
                baseInfoAdapter.setNewInstance(profileMetaModel.toBaseInfoBeanList())
            } else {
                val adapter = BaseInfoAdapter().also {
                    it.setNewInstance(profileMetaModel.toBaseInfoBeanList())
                }
                rcvInformation.layoutManager = GridLayoutManager(root.context, 2).also {
                    it.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                        override fun getSpanSize(position: Int): Int {
                            return if (adapter.getItem(position).isSingleLine()) 2 else 1
                        }
                    }
                }
                rcvInformation.adapter = adapter
            }
            showPrivateInfo(profileMetaModel)
            val identityClickListener = View.OnClickListener {
                MePointReporter.reportUserDetailModuleClick(item.data.userId, "我的认证")
                UserIdentityListDialog(activity, item.data.userId).show()
            }
            clickArea.setOnClickListener(identityClickListener)

            if (item.recommendInfoResponse != null && item.recommendInfoResponse.recommendInfo != null && viewModel.mSource == PageSource.F1_RECOMMEND_CHILD_FRAGMENT){
                idRecommendLayout.visible()
                idRecommendTitle.text = item.recommendInfoResponse.recommendInfo?.title?:""
                idRecommendContent.text = item.recommendInfoResponse.recommendInfo?.content?:""
                idRecommendPoint.text = "匹配度${item.recommendInfoResponse.recommendInfo?.score?:0}%"
                if (profileMetaModel.baseInfo?.gender == 1){//男
                    idRecommendPoint.setTextColor(idRecommendPoint.context.getColor(
                        R.color.common_color_265EBC))
                }else{
                    idRecommendPoint.setTextColor(idRecommendPoint.context.getColor(
                        R.color.common_color_882976))
                }
            }else{
                idRecommendLayout.gone()

            }
        }
    }

    /**
     * 隐私信息
     */
    private fun MePreviewUserBaseInfoSectionBinding.showPrivateInfo(data: ProfileMetaModel) {
        val isFriend = data.isFriend || data.userId == AccountHelper.getInstance().userId
        val companyName = data.baseInfo?.companyNameShort ?: data.baseInfo?.companyName
        //有公司名同时显示公司名称
        val showCompany = data.baseInfo?.localHideCompany() == true && data.baseInfo?.addCompanyName == true
        val incomeStr = data.baseInfo?.annualIncomeInfo
        val showSalary = (data.baseInfo?.localHideIncome() == true) && data.baseInfo?.addAnnualIncome == true
        //有公司公司信息或者隐藏了收入（否则收入在上面基本信息列表中展示）
        clPrivateInfo.visible(showCompany || showSalary)
        if(isFriend){
            tvPrivateInfoHint.text = "本信息仅对互相喜欢的人可见"
        }else{
            tvPrivateInfoHint.text = if (showSalary && showCompany) {
                "互相喜欢后可解锁就职公司和年薪信息"
            } else if (showCompany) {
                "互相喜欢后可解锁就职公司信息"
            } else if (showSalary) {
                "互相喜欢后可解锁年薪信息"
            } else {
                ""
            }
        }


        if (data.baseInfo?.localHideCompany() == true) {
            idLayoutCompany.visible()
            if (companyName.isNullOrBlank()) {
                if (data.baseInfo?.addCompanyName == true) {
                    idCompanyShadow.visible()
                } else {
                    idLayoutCompany.gone()
                }
            } else {
                if (isFriend) {
                    idCompanyShadow.visibility = View.INVISIBLE
                    tvCompany.text = companyName
                } else {
                    idCompanyShadow.visible()
                }
            }
        } else {
            idLayoutCompany.gone()
        }

        if (data.baseInfo?.localHideIncome() == true) {
            idLayoutSalary.visible()
            if (incomeStr.isNullOrBlank()) {
                if (data.baseInfo?.addAnnualIncome == true) {
                    idSalaryShadow.visible()
                } else {
                    idLayoutSalary.gone()
                }
            } else {
                if (isFriend) {
                    idSalaryShadow.visibility = View.INVISIBLE
                    tvSalary.text = "年薪${incomeStr}"
                } else {
                    idSalaryShadow.visible()
                }
            }
        } else {
            idLayoutSalary.gone()
        }

    }

    private fun ProfileMetaModel.toBaseInfoBeanList(): MutableList<BaseInfoBean> {
        return mutableListOf<BaseInfoBean>().also {
            //年龄、星座
            it.add(BaseInfoBean(listOf(this.baseInfo?.age.toAgeString(), this.baseInfo?.constellationInfo).joinNotNull(), R.drawable.me_ic_birthday_20))
            //身高体重
            it.add(BaseInfoBean(listOf("${this.baseInfo?.height}cm", this.baseInfo?.getWeightStringMoreThan40()).joinNotNull(), R.drawable.me_ic_stature))
            //学历、职业
            if(this.baseInfo?.industry?.isNotEmpty() == true || this.baseInfo?.career?.isNotEmpty() == true){
                it.add(BaseInfoBean(listOf(this.baseInfo?.industry, this.baseInfo?.career).joinNotNull(), R.drawable.me_ic_occupation_basic_info))
            }
//            //年薪
//            if (baseInfo?.localHideIncome() != true && !baseInfo?.annualIncomeInfo.isNullOrBlank()) {
//                it.add(BaseInfoBean("年薪${baseInfo?.annualIncomeInfo ?: ""}", R.drawable.me_ic_revenue))
//            }
            val address = listOf<String?>(this.baseInfo?.addressLevel1, this.baseInfo?.addressLevel2).joinNotNull("")
            if (address.isNotBlank()) {
                it.add(BaseInfoBean("现居${address}", R.drawable.me_ic_living_place))
            }
            val hkStr = listOf<String?>(this.baseInfo?.hukouLevel1, this.baseInfo?.hukouLevel2).joinNotNull("")
            val hometownStr = listOf(baseInfo?.hometownLevel1, baseInfo?.hometownLevel2).joinNotNull("")
            if (hkStr == hometownStr) {//户口和家乡相同，则只展示家乡
                if (hometownStr.isNotBlank()) {
                    //家乡
                    it.add(BaseInfoBean("家乡${hometownStr}", R.drawable.me_ic_hometown_basic_info))
                }

            } else {
                if (hometownStr.isNotBlank()) {
                    //家乡
                    it.add(BaseInfoBean("家乡${hometownStr}", R.drawable.me_ic_hometown_basic_info))
                }

                if (hkStr.isNotBlank()) {
                    //户口
                    it.add(BaseInfoBean("户口${hkStr}", R.drawable.me_ic_permanent_residence))
                }

            }
            //现居
            if (!this.baseInfo?.ethnicity.isNullOrBlank()) {
                //民族
                it.add(BaseInfoBean(this.baseInfo?.ethnicity ?: "", R.drawable.me_icon_ethnicity))
            }
            //抽烟
            if (!baseInfo?.smokingHabitInfo.isNullOrBlank()) {
                it.add(BaseInfoBean(baseInfo?.smokingHabitInfo ?: "", R.drawable.me_ic_smoke))
            }
            //喝酒
            if (!baseInfo?.drinkingHabitInfo.isNullOrBlank()) {
                it.add(BaseInfoBean(baseInfo?.drinkingHabitInfo ?: "", R.drawable.me_ic_drink))
            }
        }
    }

    data class BaseInfoBean(
        val string: String,
        val iconRes: Int
    ) {
        fun isSingleLine(): Boolean {
            return string.getShowLength() > 7
        }
    }

    class BaseInfoAdapter : BaseQuickAdapter<BaseInfoBean, BaseViewHolder>(R.layout.me_preview_user_base_info_item, mutableListOf()) {
        override fun convert(holder: BaseViewHolder, item: BaseInfoBean) {
            holder.setText(R.id.tvItemName, item.string)
            holder.setImageResource(R.id.ivInfoIcon, item.iconRes)
        }

    }

    class TabAdapter(val list: List<ProfileInfoModel.CertInfo.CertInfoBean>) : BaseQuickAdapter<ProfileInfoModel.CertInfo.CertInfoBean, BaseViewHolder>(R.layout.common_identity_info_item, list.toMutableList()) {

        override fun convert(holder: BaseViewHolder, item: ProfileInfoModel.CertInfo.CertInfoBean) {
            val blurTextView = holder.getView<BlurTextView>(R.id.tvTagName)
            blurTextView.setText(false, item.content)
            holder.getView<View>(R.id.ivTagIcon).visible(item.hasApprove())
        }

    }
}

