package com.kanzhun.marry.me.info.fragment

import android.graphics.Typeface
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import androidx.fragment.app.Fragment
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.kanzhun.common.kotlin.ui.onClick
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
import com.kanzhun.common.adpter.ViewPagerFragmentAdapter
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeFragmentLikeKeySelectBinding
import com.kanzhun.marry.me.info.bean.MeLikeLabelTagResponse
import com.kanzhun.marry.me.info.viewmodel.MeLikeLabelViewPagerSelectViewModel

class MeLikeLabelViewPagerSelectFragment :
    BaseBindingFragment<MeFragmentLikeKeySelectBinding, MeLikeLabelViewPagerSelectViewModel>() {
    lateinit var adapter:ViewPagerFragmentAdapter
     var list = mutableListOf<MeLabelItemSelectFragment>()
    override fun preInit(arguments: Bundle) {}

    fun setData(beans:List<MeLikeLabelTagResponse.Bean>){
        list.clear()
        for ((i, bean) in beans.withIndex()){
            list.add(MeLabelItemSelectFragment(bean.subTag,bean.id,mViewModel))
        }
        adapter.setNewData(list as List<Fragment>?)
        TabLayoutMediator(mBinding.tabs,mBinding.idViewPager,true,true){
                tab, position->
            val parentView = LayoutInflater.from(context).inflate(R.layout.me_item_label_key_label,null)
            val button = parentView.findViewById<QMUIRoundButton>(R.id.idLabel)
            button.text = beans[position].content
            tab.customView = parentView
        }.attach()
        adapter.notifyDataSetChanged()
    }
    override fun initView() {
        adapter = ViewPagerFragmentAdapter(this)

        mBinding.idViewPager.adapter = adapter

        mBinding.tabs.addOnTabSelectedListener(object :TabLayout.OnTabSelectedListener{
            override fun onTabSelected(tab: TabLayout.Tab?) {
                val view  = tab?.customView?.findViewById<QMUIRoundButton>(R.id.idLabel)
                view?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18f)
                view?.typeface = Typeface.DEFAULT_BOLD
                context?.getColor(R.color.common_color_141414)?.let { view?.setTextColor(it) }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                val view  = tab?.customView?.findViewById<QMUIRoundButton>(R.id.idLabel)
                view?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16f)
                view?.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
                context?.getColor(R.color.common_color_545454)?.let { view?.setTextColor(it) }
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }

        })

        mBinding.idSearch.onClick {
//            Navigation.findNavController(mBinding.idSearch)
//                .navigate(R.id.action_keywordFragment_to_searchFragment)
        }


    }
    override fun initData() {}
    override fun onRetry() {}
    override fun getStateLayout(): StateLayout? {
        return null
    }

    fun updateFragmentPosition(fromBean: MeLikeLabelTagResponse.Bean) {
        mViewModel.listData.postValue(fromBean)
//        fromBean.parentId.forEach{parant ->
//            list.forEach{
//                if(it.beanParentId == parant){
//                    it.updateItem(fromBean)
//                }
//            }
//        }
    }
}