package com.kanzhun.marry.me.dialog

import android.content.Context
import android.view.View
import com.kanzhun.common.base.AllBaseActivity
import com.kanzhun.common.dialog.BottomListDialogNew
import com.kanzhun.common.dialog.CommonBottomViewBindingDialog
import com.kanzhun.common.dialog.model.SelectBottomBean
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.bean.UserSettingBean
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.utils.GsonUtils
import com.kanzhun.utils.configuration.UserSettingConfig
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.foundation.kotlin.ktx.contentShowRange
import com.kanzhun.marry.me.databinding.MePrivateSettingDialogBinding

fun AllBaseActivity.showPrivateSettingDialog(
    companyHideInit: Int = 0,
    annualSalaryInit: Int = 0,
    runnable: Runnable?,
) {
    var companyHide = companyHideInit
    var annualSalary = annualSalaryInit
    val dialog = CommonBottomViewBindingDialog(this,
        cancelable = false,
        canceledOnTouchOutside = false,
        mOnInflateCallback = { inflater, dialog ->
            val binding = MePrivateSettingDialogBinding.inflate(inflater)
            binding.apply {
                idCompanySubText.text = companyHide.contentShowRange()
                idAnnualSalarySubText.text = annualSalary.contentShowRange()
                idCompany.onClick {
                    dialog.dismiss()
                    showCompanyRangeBottomDialog(this@showPrivateSettingDialog,companyHide,true,object :BottomListDialogNew.OnBottomItemClickListener{
                        override fun onBottomItemClick(
                            view: View?,
                            pos: Int,
                            bottomBean: SelectBottomBean?,
                        ) {
                            companyHide = pos
                        }

                    },{
                        showPrivateSettingDialog (companyHide,annualSalary,runnable)
                    })
                }

                idAnnualSalary.onClick {
                    dialog.dismiss()
                    showIncomeRangeBottomDialog(this@showPrivateSettingDialog,annualSalary,true,object :BottomListDialogNew.OnBottomItemClickListener{
                        override fun onBottomItemClick(
                            view: View?,
                            pos: Int,
                            bottomBean: SelectBottomBean?,
                        ) {
                            annualSalary = pos
                        }

                    },{
                        showPrivateSettingDialog (companyHide,annualSalary,runnable)
                    })
                }
            }

            binding.tvCancel.onClick {
                dialog.dismiss()
            }
            binding.tvSure.onClick {
                    val array = mutableListOf<UserSettingBean>()

                    array.add(UserSettingBean(
                        UserSettingConfig.PERSONALITY_HIDE_REVENUE,
                        annualSalary
                    ))
                    array.add( UserSettingBean(
                        UserSettingConfig.PERSONALITY_SETTING_COMPANY_INVISIBLE,
                        companyHide
                    ))

                    HttpExecutor.requestSimplePost(
                        URLConfig.URL_BATCH_UPDATE_SETTING,
                        mapOf("settingJson" to GsonUtils.getGson().toJson(array)),
                        object :
                            SimpleRequestCallback(true) {
                            override fun onSuccess() {
                                runnable?.run()
                                dialog.dismiss()
                            }

                            override fun dealFail(reason: ErrorReason?) {
                            }

                        })


            }

            binding
        })

    dialog.show()
}

fun showRangeList():MutableList<SelectBottomBean>{
    val list = mutableListOf<SelectBottomBean>()
    list.add(SelectBottomBean("公开展示"))
    list.add(SelectBottomBean("仅互相喜欢的人可见"))
    list.add(SelectBottomBean("仅自己可见"))
    return list
}

fun showCompanyRangeBottomDialog(context:Context,companyHideInit:Int,hindSure:Boolean = true,onBottomItemClickListener: BottomListDialogNew.OnBottomItemClickListener,block:()->Unit){
    var mDialog: BottomListDialogNew
    mDialog = BottomListDialogNew.Builder(context)
        .setData(showRangeList())
        .setPadding(0, 0)
        .setShowTitle("设置公司可见范围")
        .setSelect(companyHideInit)
        .setHindSure(hindSure)
        .setCanceledOnTouchOutside(true)
        .setOnBottomItemClickListener(onBottomItemClickListener)
        .create()
    mDialog.setOnDismissListener {
        block()
    }
    mDialog.show()
}

fun showIncomeRangeBottomDialog(context:Context,annualSalaryInit:Int,hindSure:Boolean = true,onBottomItemClickListener: BottomListDialogNew.OnBottomItemClickListener,block:()->Unit){
    var mDialog: BottomListDialogNew
    mDialog = BottomListDialogNew.Builder(context)
        .setData(showRangeList())
        .setPadding(0, 0)
        .setShowTitle("设置年薪可见范围")
        .setSelect(annualSalaryInit)
        .setHindSure(hindSure)
        .setCanceledOnTouchOutside(true)
        .setOnBottomItemClickListener(onBottomItemClickListener)
        .create()
    mDialog.setOnDismissListener {
        block()
    }
    mDialog.show()
}





