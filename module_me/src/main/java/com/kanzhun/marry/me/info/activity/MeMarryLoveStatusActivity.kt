package com.kanzhun.marry.me.info.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.color
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.common.kotlin.ui.statusbar.statusBarColor
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityMarryLoveStatusLayoutBinding
import com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallbackImp
import com.kanzhun.marry.me.info.viewmodel.MeMarryLoveStatusViewModel

/**
 * Created by Cha<PERSON><PERSON><PERSON>gpeng
 * Date: 2022/4/18
 */
class MeMarryLoveStatusActivity :
    BaseBindingActivity<MeActivityMarryLoveStatusLayoutBinding, MeMarryLoveStatusViewModel>() {
    val meInfoEditFragmentCallback = MeInfoEditFragmentCallbackImp(this)
    override fun preInit(intent: Intent) {
    }

    override fun initView() {
        liveEventBusObserve<Boolean>(LivedataKeyMe.LOVE_GOAL_UPDATE){
            mViewModel.getUserInfo()
            setResult(Activity.RESULT_OK)
        }
        liveEventBusObserve<Boolean>(LivedataKeyMe.LOVE_BABY_LOVE){
            mViewModel.getUserInfo()
            setResult(Activity.RESULT_OK)
        }
        liveEventBusObserve<Boolean>(LivedataKeyMe.LOVE_MARRY_STATUS){
            mViewModel.getUserInfo()
            setResult(Activity.RESULT_OK)
        }
        statusBarColor(color = color(R.color.common_color_F0F0F0))
        mBinding.idTitleBar.setTitle("婚恋状况")
        mBinding.idTitleBar.asBackButton()
        mViewModel.listData.observe(this) {
            it.baseInfo.apply {
                val bean = this
                mBinding.tvMarry.text = bean.maritalStatusInfo
                mBinding.clMarry.onClick { meInfoEditFragmentCallback.clickMarrayStatus(bean) }

                mBinding.tvBaby.text = bean.childbearingPreferenceInfo
                mBinding.clBaby.onClick { meInfoEditFragmentCallback.clickBabyPlan(bean) }

                mBinding.tvPlan.text = bean.loveGoalInfo
                mBinding.clPlan.onClick { meInfoEditFragmentCallback.clickLovePlan(bean,mViewModel.mLoveGoalListBean.value) }

            }

        }
        mViewModel.getUserInfo()
    }

    override fun initData() {}
    override fun onRetry() {
        mViewModel.getUserInfo()
    }
    override fun getStateLayout() = mBinding.idStateLayout

    companion object {
        fun createIntent(
            context: Context?
        ): Intent {
            val intent = Intent(context, MeMarryLoveStatusActivity::class.java)
            return intent
        }
    }

}