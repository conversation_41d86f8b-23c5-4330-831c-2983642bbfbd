package com.kanzhun.marry.me.info.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;

import com.kanzhun.foundation.base.FoundationViewModel;

import java.util.concurrent.atomic.AtomicBoolean;

public class ABImpressionGuideViewModel extends FoundationViewModel {

    public static final int SIDE_A = 0;
    public static final int SIDE_B = 1;
    public static final int MESSAGE_ANIMATION_START = 0;
    public static final int MESSAGE_TO_THEME_FRAGMENT = 1;

    private AtomicBoolean mIsRunning = new AtomicBoolean(false);
    private int mSide = SIDE_A;
    private long mInterval = 2000;
    private AtomicBoolean toThemeFragment = new AtomicBoolean(false);

    public ABImpressionGuideViewModel(@NonNull Application application) {
        super(application);
    }

    public AtomicBoolean getIsRunning() {
        return mIsRunning;
    }

    public void setIsRunning(boolean isRunning) {
        this.mIsRunning.set(isRunning);
    }

    public int getSide() {
        return mSide;
    }

    public void setSide(int side) {
        this.mSide = side;
    }

    public long getInterval() {
        return mInterval;
    }

    public AtomicBoolean getToThemeFragment() {
        return toThemeFragment;
    }
}