package com.kanzhun.marry.me.util;

import com.hpbr.apm.Apm;
import com.techwolf.lib.tlog.TLog;

/**
 * Created by wa<PERSON><PERSON> on 2017/11/9.
 */

public class FeedbackReport implements Runnable {

    public void report() {
        new Thread(this).start();
    }

    @Override
    public void run() {
//        ReportContextUtils.log();
//        ReportContextUtils.uploadDB();
        TLog.reportNow();

//        TxOssUploader.uploadFile(new File(Environment.getExternalStorageDirectory().getPath(), "bigFile"), new ApiRequestCallback<FileUploadResponse>() {
//            @Override
//            public void onSuccess(ApiData<FileUploadResponse> data) {
//                L.d("FeedbackReport", String.valueOf(data.resp.url));
//            }
//
//            @Override
//            public void onComplete() {
//
//            }
//
//            @Override
//            public void onFailed(ErrorReason reason) {
//                L.e("FeedbackReport", String.valueOf(reason));
//            }
//        });

        // 帮助与反馈，主动上传日志到APM后台
        Apm.uploadLogNow();
    }

}
