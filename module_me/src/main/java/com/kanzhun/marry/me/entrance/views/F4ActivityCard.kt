package com.kanzhun.marry.me.entrance.views

import android.R.attr.onClick
import android.R.attr.visibility
import android.annotation.SuppressLint
import android.view.View
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.rememberAsyncImagePainter
import com.kanzhun.foundation.model.profile.UserTabModel
import com.kanzhun.marry.me.R
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalConfiguration
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.utils.base.LList
import okhttp3.Protocol

@Composable
fun F4ActivityCardNormal(
    activityBeanList: List<UserTabModel.ActivityBean>? = null,
) {
    if (activityBeanList.isNullOrEmpty()) return

    // If only one item, display as a full width card
    if (activityBeanList.size == 1) {
        val activityBean = activityBeanList[0]
        SingleActivityCard(activityBean)
    } else {
        // If multiple items, create a horizontally scrollable layout
        val screenWidth = LocalConfiguration.current.screenWidthDp.dp
        val cardWidth = screenWidth - 40.dp - 24.dp

        LazyRow(
            contentPadding = androidx.compose.foundation.layout.PaddingValues(horizontal = 2.dp),
            horizontalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            items(activityBeanList) { activityBean ->
                SingleActivityCard(
                    activityBean = activityBean,
                    modifier = Modifier.width(cardWidth)
                )
            }
        }
    }
}

@Composable
private fun SingleActivityCard(
    activityBean: UserTabModel.ActivityBean,
    modifier: Modifier = Modifier.fillMaxWidth(),
) {
    androidx.compose.material3.Card(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp)),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFFFFF)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {
        ConstraintLayout {
            val (bgRes, contentRes) = createRefs()

            Column(
                modifier = Modifier
                    .constrainAs(contentRes) {

                    }
                    .fillMaxWidth()
                    .padding(top = 12.dp)
            ) {
                cardTop(activityBean, onClick = {
                    ProtocolHelper.parseProtocol(activityBean.jumpUrl)
                    reportPoint("my-activities-module-click") {
                        status = AccountHelper.getInstance().phase.toString()
                        actionp2 = activityBean.activityId
                        actionp3 = activityBean.showStatus
                        actionp4 = activityBean.activityRelationStatus.toString()
                        type = "活动详情"
                        actionp5 =
                            if (activityBean.avatarList?.isNotEmpty() == true) activityBean.avatarList.size.toString() else "0"
                    }
                })

                Spacer(modifier = Modifier.height(16.dp))

                if (activityBean.avatarList?.isNotEmpty() == true) {
                    cardAvatarsActivity(activityBean, onClick = {
                        ProtocolHelper.parseProtocol(activityBean.jumpUrl)
                        reportPoint("my-activities-module-click") {
                            status = AccountHelper.getInstance().phase.toString()
                            actionp2 = activityBean.activityId
                            actionp3 = activityBean.showStatus
                            actionp4 = activityBean.activityRelationStatus.toString()
                            type = "用户列表"
                            actionp5 = "0"
                        }
                    })
                } else {
                    cardAddActivity(activityBean, onClick = {
                        ProtocolHelper.parseProtocol(activityBean.jumpUrl)
                        reportPoint("my-activities-module-click") {
                            status = AccountHelper.getInstance().phase.toString()
                            actionp2 = activityBean.activityId
                            actionp3 = activityBean.showStatus
                            actionp4 = activityBean.activityRelationStatus.toString()
                            type = "用户列表"
                            actionp5 = "0"
                        }
                    })
                }
            }
        }
    }
}

@Composable
fun F4ActivityCard(
    activityBeans : List<UserTabModel.ActivityBean>? = null,
) {
    if (activityBeans == null) return

    if (activityBeans.size == 1) {
        F4ActivityCardItem(activityBeans[0], modifier = Modifier)
    }else{
        // If multiple items, create a horizontally scrollable layout
        val screenWidth = LocalConfiguration.current.screenWidthDp.dp
        val cardWidth = screenWidth - 40.dp - 24.dp

        LazyRow(
            contentPadding = androidx.compose.foundation.layout.PaddingValues(horizontal = 2.dp),
            horizontalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            items(activityBeans) { activityBean ->
                F4ActivityCardItem(activityBean,   modifier = Modifier.width(cardWidth))
            }
        }
    }


}

@Composable
fun F4ActivityCardItem(activityBean:UserTabModel.ActivityBean?,modifier: Modifier) {
    androidx.compose.material3.Card(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .noRippleClickable {
                ProtocolHelper.parseProtocol(activityBean?.jumpUrl)
            },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFFFFF)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp
        )
    ) {

        ConstraintLayout {

            val (bgRes, contentRes) = createRefs()

            Image(
                painter = painterResource(id = R.drawable.image_f4_me_fragment_item_card_bg),
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(bgRes) {
                        top.linkTo(contentRes.top)
                        bottom.linkTo(contentRes.bottom)
                        start.linkTo(contentRes.start)
                        end.linkTo(contentRes.end)
                        height = Dimension.Companion.fillToConstraints
                    },
                contentDescription = "image description",
                contentScale = ContentScale.FillBounds
            )

            Column(
                modifier = Modifier
                    .constrainAs(contentRes) {

                    }
                    .fillMaxWidth()
                    .padding(top = 12.dp)
            ) {
                cardTop(activityBean, useColorIcon = false, onClick = {
                    ProtocolHelper.parseProtocol(activityBean?.jumpUrl)
                })

                Spacer(modifier = Modifier.height(16.dp))

                cardAvatars(activityBean, modifier = Modifier.padding(horizontal = 12.dp))

                Spacer(modifier = Modifier.height(16.dp))

                cardLine()

                Spacer(modifier = Modifier.height(16.dp))

                cardButtom(activityBean)

                Spacer(modifier = Modifier.height(16.dp))

            }
        }
    }
}

@Composable
private fun cardAddActivity(
    activityBean: UserTabModel.ActivityBean? = null,
    onClick: () -> Unit = {},
) {
    ConstraintLayout(
        modifier = Modifier
            .noRippleClickable {
                onClick()
            }
            .height(56.dp)
            .padding(horizontal = 12.dp)) {

        val (dividerRes, rowRes) = createRefs()

        HorizontalDivider(
            modifier = Modifier
                .height(2.dp)
                .constrainAs(dividerRes) {
                    top.linkTo(parent.top)
                },
            color = Color(0xFFE5E5E5)
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(rowRes) {
                    top.linkTo(dividerRes.bottom)
                    bottom.linkTo(parent.bottom)
                }) {
            Image(
                painter = painterResource(id = R.drawable.me_icon_activity_btn_left),
                contentDescription = ""
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = activityBean?.buttonTip ?:"火热报名中",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF292929)
                ),
                modifier = Modifier.weight(1f)
            )
            Text(
                text = "",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFFB8B8B8)
                )
            )
        }
    }
}

@Composable
private fun cardAvatarsActivity(
    activityBean: UserTabModel.ActivityBean? = null,
    onClick: () -> Unit = {},
) {
    ConstraintLayout(
        modifier = Modifier
            .noRippleClickable {
                onClick()
            }
            .height(56.dp)
            .padding(horizontal = 12.dp)) {

        val (dividerRes, rowRes) = createRefs()

        HorizontalDivider(
            modifier = Modifier
                .height(2.dp)
                .constrainAs(dividerRes) {
                    top.linkTo(parent.top)
                },
            color = Color(0xFFE5E5E5)
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(rowRes) {
                    top.linkTo(dividerRes.bottom)
                    bottom.linkTo(parent.bottom)
                }, verticalAlignment = Alignment.CenterVertically
        ) {
            cardAvatars(
                activityBean = activityBean,
                modifier = Modifier.weight(1f)
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = activityBean?.buttonTip?:"查看报名活动嘉宾",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFFB8B8B8)
                )
            )

            Image(painter = R.drawable.common_ic_gray_right_arrow.painterResource(),
                contentDescription = "")
        }
    }
}

@Composable
private fun cardButtom(activityBean: UserTabModel.ActivityBean? = null) {
    // "Enter event" button
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp),
    ) {

        val (buttonRes, imageRes) = createRefs()
        Box(
            modifier = Modifier
                .constrainAs(buttonRes) {
                    top.linkTo(parent.top, 15.dp)
                }
                .fillMaxWidth()
                .height(56.dp)
                .clip(RoundedCornerShape(28.dp))
                .background(Color.Black)
                ,
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "进入活动现场",
                    style = TextStyle(
                        brush = Brush.horizontalGradient(
                            listOf(
                                Color(0xFF61EEFF),
                                Color(0xFFFFFFFF),
                                Color(0xFFFFA8EC)
                            )
                        ),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "👉",
                    style = TextStyle(
                        fontSize = 18.sp
                    )
                )
            }
        }

        Image(
            painter = painterResource(id = R.drawable.image_f4_me_fragment_activity_item_btn_pic),
            contentDescription = "", modifier = Modifier
                .constrainAs(imageRes) {
                    end.linkTo(parent.end)
                    top.linkTo(parent.top)
                }
                .size(76.dp, 27.dp)
        )

    }
}

@Composable
private fun cardLine() {
    // Row with dots and half-circles
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(24.dp),
        contentAlignment = Alignment.Center
    ) {
        val dotSpacing = 10.dp // Slightly increased from reference value of 10dp
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // Left half-circle
            Box(
                modifier = Modifier
                    .size(12.dp, 24.dp)
                    .background(
                        color = Color(0xFFF5F5F5),
                        shape = GenericShape { size, _ ->
                            // Create a half-circle path aligned with left edge
                            moveTo(0f, 0f)
                            arcTo(
                                rect = Rect(
                                    left = -size.width,
                                    top = 0f,
                                    right = size.width,
                                    bottom = size.height
                                ),
                                startAngleDegrees = 270f,
                                sweepAngleDegrees = 180f,
                                forceMoveTo = false
                            )
                            lineTo(0f, 0f)
                            close()
                        }
                    )
            )
            Spacer(Modifier.width(dotSpacing))

            // Dots between half-circles
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(dotSpacing)
                ) {
                    val width =
                        LocalDensity.current.run { LocalConfiguration.current.screenWidthDp.dp.toPx() }
                    val dotsCount = (width / 20).toInt()
                    repeat(dotsCount) {
                        Box(
                            modifier = Modifier
                                .size(10.dp)
                                .clip(CircleShape)
                                .background(Color(0xFFEBEBEB))
                        )
                    }
                }
            }

            Spacer(Modifier.width(dotSpacing))

            // Right half-circle
            Box(
                modifier = Modifier
                    .size(12.dp, 24.dp)
                    .background(
                        color = Color(0xFFF5F5F5),
                        shape = GenericShape { size, _ ->
                            // Create a half-circle path aligned with right edge
                            moveTo(size.width, 0f)
                            arcTo(
                                rect = Rect(
                                    left = 0f,
                                    top = 0f,
                                    right = size.width * 2,
                                    bottom = size.height
                                ),
                                startAngleDegrees = 90f,
                                sweepAngleDegrees = 180f,
                                forceMoveTo = false
                            )
                            lineTo(size.width, size.height)
                            close()
                        }
                    )
            )
        }
    }
}

@Composable
private fun cardAvatars(
    activityBean: UserTabModel.ActivityBean? = null,
    modifier: Modifier = Modifier,
) {
    val avatarList = activityBean?.avatarList ?: emptyList()
    if (avatarList.isEmpty()) {
        return
    }

    BoxWithConstraints(modifier = modifier.fillMaxWidth()) {
        // Calculate available width and determine how many avatars can fit
        val avatarSize = 20 // Avatar size in dp plus some extra margin
        val availableWidth = this.maxWidth.value
        var maxAvatars = (availableWidth / avatarSize).toInt()
        // Subtract 1 to leave room for ellipsis, ensure we have at least 1 avatar
        maxAvatars = maxAvatars.coerceAtLeast(2) - 1

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy((-8).dp)
        ) {
            // Only show up to maxAvatars
            val itemCount = minOf(avatarList.size, maxAvatars)

            items(itemCount) { index ->
                val avatarUrl = avatarList[index]

                Image(
                    painter = rememberAsyncImagePainter(
                        model = avatarUrl,
                        placeholder = painterResource(id = R.drawable.common_female_level_two_guide)
                    ),
                    contentDescription = "Attendee",
                    modifier = Modifier
                        .size(28.dp)
                        .clip(CircleShape)
                        .border(1.5.dp, Color.White, CircleShape),
                    contentScale = ContentScale.Crop
                )
            }

            item {
                Image(
                    painter = painterResource(R.drawable.icon_activity_more_avatar),
                    contentDescription = "Attendee",
                    modifier = Modifier
                        .size(28.dp)
                        .clip(CircleShape)
                        .border(1.5.dp, Color.White, CircleShape),
                    contentScale = ContentScale.Crop
                )
            }
        }
    }
}


@Composable
fun cardTop(
    activityBean: UserTabModel.ActivityBean? = null,
    useColorIcon: Boolean = true,
    onClick: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .noRippleClickable {
                onClick()
            }
            .padding(horizontal = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left event poster/banner
        Image(
            painter = rememberAsyncImagePainter(
                model = activityBean?.showImages?.firstOrNull() ?: "",
            ),
            contentDescription = "Event Poster",
            modifier = Modifier
                .size(78.dp)
                .clip(RoundedCornerShape(8.dp)),
            contentScale = ContentScale.Crop
        )

        Spacer(modifier = Modifier.width(12.dp))

        // Right content with event details
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                val iconModifier =
                    if (useColorIcon && ("已报名".equals(activityBean?.showStatus) || "已预约".equals(
                            activityBean?.showStatus
                        ))
                    ) {
                        Modifier
                            .wrapContentSize()
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color(0xFF61EEFF),
                                        Color(0xFFFFFFFF),
                                        Color(0xFFFE6FDD)
                                    ),
                                ),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 4.dp, vertical = 2.dp)
                    } else {
                        Modifier
                            .wrapContentSize()
                            .background(
                                color = Color(0xFFFFFFFF),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 4.dp, vertical = 2.dp)
                    }
                Box(
                    modifier = iconModifier
                ) {
                    Text(
                        text = activityBean?.showStatus ?: "已签到",
                        style = TextStyle(
                            color = Color(0xFF292929),
                            fontSize = 11.sp,
                            fontWeight = FontWeight.Medium
                        )
                    )
                }

                Spacer(modifier = Modifier.width(4.dp))


                // Event title
                Text(
                    text = activityBean?.title ?: "而立首届心动大会大会会",
                    style = TextStyle(
                        color = Color(0xFF333333),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }


            Spacer(modifier = Modifier.height(8.dp))

            // Event time
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "时间：",
                    style = TextStyle(
                        color = Color(0xFF7F7F7F),
                        fontSize = 13.sp
                    )
                )
                Text(
                    text = activityBean?.timeStr ?: "周六，12月24日 13:30",
                    style = TextStyle(
                        color = Color(0xFF7F7F7F),
                        fontSize = 13.sp
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // Event location
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "地点：",
                    style = TextStyle(
                        color = Color(0xFF7F7F7F),
                        fontSize = 13.sp
                    )
                )
                Text(
                    text = activityBean?.address
                        ?: "北京奥加美术馆酒店北京奥加美术馆酒店（东直门）",
                    style = TextStyle(
                        color = Color(0xFF7F7F7F),
                        fontSize = 13.sp
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

@Preview
@Composable
fun F4ActivityCardPreview() {
    F4ActivityCard(activityBeans = listOf<UserTabModel.ActivityBean>(getMockBean(),getMockBean()))
}

@Preview
@Composable
fun PreviewF4ActivityCardNormal() {
    F4ActivityCardNormal(listOf<UserTabModel.ActivityBean>(getMockBean().apply {
        avatarList = listOf()
    }))
}

@Preview
@Composable
fun PreviewListF4ActivityCardNormal() {
    F4ActivityCardNormal(
        listOf<UserTabModel.ActivityBean>(
            getMockBean(),
            getMockBean(),
            getMockBean(),
            getMockBean()
        )
    )
}

fun getMockBean(): UserTabModel.ActivityBean {
    return object : UserTabModel.ActivityBean() {
        init {
            title = "而立首届心动大会大会会"
            timeStr = "周六，12月24日 13:30"
            address = "北京奥加美术馆酒店（东直门）"
            showStatus = "已签到"
            showImages = List(25) {
                "https://img.techwolf.cn/techwolf/2022/03/08/f3f9f0a0-f0f3-11ec-b0a2-00163e0e0f0f.png"
            }
            avatarList = List(25) {
                "https://img.techwolf.cn/techwolf/2022/03/08/f3f9f0a0-f0f3-11ec-b0a2-00163e0e0f0f.png"
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewCardTop() {
    cardTop(activityBean = getMockBean())
}

@Preview(showBackground = true)
@Composable
fun PreviewCardLine() {
    cardLine()
}

@Preview(showBackground = true)
@Composable
fun PreviewCardButtom() {
    cardButtom(activityBean = getMockBean())
}

@Preview(showBackground = true)
@Composable
fun PreviewCardAvatars() {
    cardAvatars(activityBean = getMockBean())
}

@Preview(showBackground = true)
@Composable
fun PreviewCardAddActivity() {
    cardAddActivity(activityBean = getMockBean())
}

