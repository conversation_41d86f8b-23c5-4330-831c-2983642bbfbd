package com.kanzhun.marry.me.setting

import android.content.Context
import android.content.Intent
import com.hpbr.apm.Apm
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.apm.ApmManager
import com.kanzhun.foundation.router.KmpPageRouter
import com.kanzhun.marry.me.setting.activity.MeAboutActivity


fun jumpToAboutActivity(context: Context){
    //关于
    var useKmp = false
    try {
       useKmp = Apm.getPublicConfigJsonObject()?.optJSONObject("kuikly")?.optBoolean("aboutpage",false)?:false
    }catch (e: Exception){

    }
    if (useKmp) {
        KmpPageRouter.jumpKmpActivity(
            context = context,
            pageName = "AboutPage")
    } else {
        val intent = Intent(context, MeAboutActivity::class.java)
        AppUtil.startActivity(context, intent)
    }
}