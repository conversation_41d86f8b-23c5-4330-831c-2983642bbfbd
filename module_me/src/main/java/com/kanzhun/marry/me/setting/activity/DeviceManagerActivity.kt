package com.kanzhun.marry.me.setting.activity

import android.content.Intent
import android.text.TextUtils
import androidx.recyclerview.widget.LinearLayoutManager
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.ui.recyclerview.CommonListAdapter
import com.kanzhun.common.kotlin.ui.statusbar.fullScreenAndBlackText
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.api.bean.DeviceBean
import com.kanzhun.marry.me.databinding.MeActivityDeviceManagerBinding
import com.kanzhun.marry.me.setting.provider.DeviceItemProvider
import com.kanzhun.marry.me.setting.viewmodel.DeviceManagerViewModel
import com.sankuai.waimai.router.annotation.RouterUri

@RouterUri(path = [MePageRouter.ME_DEVICE_MANAGER_ACTIVITY])
class DeviceManagerActivity : BaseBindingActivity<MeActivityDeviceManagerBinding, DeviceManagerViewModel>() {
    private val mAdapter: CommonListAdapter<DeviceBean> = CommonListAdapter()

    override fun preInit(intent: Intent) {
    }

    override fun initView() {
        fullScreenAndBlackText(marginView = mBinding.titleBar, marginFalsePaddingTrue = true)
        mBinding.titleBar.asBackButton()
        mBinding.rvList.layoutManager = LinearLayoutManager(this)
        mAdapter.registerItemProvider(0, DeviceItemProvider(this) {
            if (it.currentDevice) return@DeviceItemProvider

            showTwoButtonDialog(
                content = "删除后再次登录时需要进行设备验证",
                negativeText = "取消",
                positiveText = "确定删除",
                negativeButtonClick = {

                },
                positiveButtonClick = {
                    if (!TextUtils.isEmpty(it.id)) {
                        mViewModel.deleteUserDevice(it.id!!)
                    }
                })

            reportPoint("device-management-page-click") {
                actionp2 = it.deviceInfo
                actionp3 = it.id
                type = "删除"
            }

        })
        mBinding.rvList.adapter = mAdapter

        reportPoint("device-management-page-expo")
    }

    override fun initData() {
        mViewModel.responseLiveData.observe(this) {
            val filterList = it?.deviceList?.filterNotNull()
            if (filterList?.isNotEmpty() == true) {
                mViewModel.showContent()
                mAdapter.setNewInstance(filterList.toMutableList())
            } else {
                mViewModel.showEmpty()
            }
        }
        mViewModel.showLoading()
        mViewModel.getUserDeviceList()

    }


    override fun onRetry() {
    }


    override fun getStateLayout() = mBinding.stateLayout


}
