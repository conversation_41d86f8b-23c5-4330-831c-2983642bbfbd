package com.kanzhun.marry.me.util.pictureselector

import android.annotation.SuppressLint
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeCarIndentitySelectorHeaderBinding

/**
 * 房产认证选择图片
 */
class RevenuePictureSelector(
    val context: FragmentActivity,
    private val enableAlbum: Boolean = true,
    clickCallback: (type: PictureSelectorItemType) -> Unit = {},
    val callback: (cameraFileUri: Uri, isGallery: Boolean) -> Unit
) : BasePictureSelector(context, clickCallback, callback) {
    @SuppressLint("SetTextI18n")
    override fun getHeaderView(): View {
        val binding =
            MeCarIndentitySelectorHeaderBinding.inflate(LayoutInflater.from(context)).also {
                it.run {
                    this.tvTitle.text = "纳税记录上传示范"
                    this.tvDesc.text = "文字信息清晰,证明文件姓名与本人一致\n纳税金额部分清晰"
                    this.ivCarAuthExample.loadResource(R.mipmap.me_revenue_auth_example1)
                }

            }
        return binding.root
    }

    override fun isGalleryEnabled(): Boolean {
        return enableAlbum
    }
}