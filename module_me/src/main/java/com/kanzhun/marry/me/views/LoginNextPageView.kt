package com.kanzhun.marry.me.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.content.withStyledAttributes
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeLayoutLoginNextPageViewBinding

class LoginNextPageView(context: Context, attrs: AttributeSet? = null) :
    FrameLayout(context, attrs) {
    private var mBinding: MeLayoutLoginNextPageViewBinding? = null

    init {
        mBinding =
            MeLayoutLoginNextPageViewBinding.inflate(LayoutInflater.from(context), this, true)
        if (attrs != null) {
            context.withStyledAttributes(attrs, R.styleable.me_MyCustomView) {
                enable(getBoolean(R.styleable.me_MyCustomView_android_enabled, true))
            }
        }
    }

    fun setAnimClickListener(l: OnClickListener?) {
        mBinding?.root?.setOnClickListener {
            if (isEnabled) {
                setState(STATE.LOADING)
                l?.onClick(mBinding?.root)
            }
        }
    }

    enum class STATE {
        IDLE,
        ERROR,
        LOADING,
    }

    fun setState(state: STATE) {
        when (state) {
            STATE.IDLE -> {
                mBinding?.idBtnNext?.visible()
                mBinding?.idBtnNextLottie?.cancelAnimation()
                mBinding?.idBtnNextLottieParent?.gone()
            }

            STATE.ERROR -> {
                mBinding?.idBtnNext?.visible()
                mBinding?.idBtnNextLottie?.cancelAnimation()
                mBinding?.idBtnNextLottieParent?.gone()
            }

            STATE.LOADING -> {
                mBinding?.idBtnNext?.gone()
                mBinding?.idBtnNextLottieParent?.visible()
                mBinding?.idBtnNextLottie?.setAnimation("login_btn/loop.json")
                @Suppress("DEPRECATION")
                mBinding?.idBtnNextLottie?.loop(true)
                mBinding?.idBtnNextLottie?.playAnimation()
            }
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        setEnableColor(enabled)
    }

    private fun setEnableColor(enabled: Boolean) {
        mBinding?.idBtnNext?.enable(enabled)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
    }

    override fun performClick(): Boolean {
        return mBinding?.root?.performClick() == true || super.performClick()
    }
}