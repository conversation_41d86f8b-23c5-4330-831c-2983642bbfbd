package com.kanzhun.marry.me.info.viewmodel;

import android.app.Application;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.foundation.model.profile.GuideItemBean;
import com.kanzhun.foundation.model.profile.ProfileMetaModel;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.utils.T;

import java.util.HashMap;
import java.util.Map;

public class MeStatureEditViewModel extends FoundationViewModel {

    public int initStature;
    public ObservableInt indexObservable = new ObservableInt();
    public ObservableInt countObservable = new ObservableInt();
    public ObservableBoolean skipObservable = new ObservableBoolean();
    // 更新身高成功
    private MutableLiveData<Integer> updateSuccessLivaData = new MutableLiveData<>();

    public MeStatureEditViewModel(Application application) {
        super(application);
    }

    public void initData() {
        ProfileMetaModel profileMetaModel = ProfileHelper.getInstance().getProfileMetaModel();
        if (profileMetaModel != null && profileMetaModel.baseInfo != null) {
            initStature = profileMetaModel.baseInfo.height;
        }
        GuideItemBean targetGuideItem = ProfileHelper.getInstance().getTargetGuideItem();
        if (targetGuideItem != null && indexObservable.get() > 0) {
            skipObservable.set(targetGuideItem.isCanSkip());
        }
    }

    /**
     * 更新身高
     */
    public void updateStature(int stature) {
        Map<String, Object> params = new HashMap<>();
        params.put("height", stature);

        setShowProgressBar();
        HttpExecutor.requestSimplePost(URLConfig.URL_ME_STATURE_UPDATE, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                updateSuccessLivaData.setValue(stature);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public void updateWeight(int stature) {
        Map<String, Object> params = new HashMap<>();
        params.put("weight", stature);

        setShowProgressBar();
        HttpExecutor.requestSimplePost(URLConfig.URL_ME_WEIGHT_UPDATE, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                updateSuccessLivaData.setValue(stature);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<Integer> getUpdateSuccessLivaData() {
        return updateSuccessLivaData;
    }

}