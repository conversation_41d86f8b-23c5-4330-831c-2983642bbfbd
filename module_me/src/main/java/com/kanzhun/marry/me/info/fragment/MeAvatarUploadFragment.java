package com.kanzhun.marry.me.info.fragment;

import static android.app.Activity.RESULT_OK;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;

import com.chad.library.BR;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.foundation.utils.PathUtil;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeFragmentMeAvatarUploadBinding;
import com.kanzhun.marry.me.info.activity.MeAvatarUploadCropActivity;
import com.kanzhun.marry.me.info.bean.AvatarUploadSuccessBean;
import com.kanzhun.marry.me.info.callback.MeAvatarUploadCallback;
import com.kanzhun.marry.me.info.viewmodel.MeAvatarUploadFragmentViewModel;
import com.kanzhun.marry.me.util.pictureselector.PictureSelectorItemType;
import com.kanzhun.marry.me.util.pictureselector.PictureSelectorManager;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;
import com.yalantis.ucrop.UCrop;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/12
 */
public class MeAvatarUploadFragment extends FoundationVMFragment<MeFragmentMeAvatarUploadBinding, MeAvatarUploadFragmentViewModel> implements MeAvatarUploadCallback {
    private static final int ME_UPLOAD_AVATAR_REQUEST_CODE = 101;

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_me_avatar_upload;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public void clickLeft(View view) {
        if (activity != null) {
            activity.onBackPressed();
        }
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        initView();
    }

    private void initView() {
        int count = LList.getCount(ProfileHelper.getInstance().getGuideItems());
        int index = getArguments().getInt(BundleConstants.BUNDLE_PAGE_INDEX, 0);
        getViewModel().countObservable.set(count);
        getViewModel().indexObservable.set(index);
        getViewModel().initData();
        getViewModel().getAvatarErrorDesc();
        getViewModel().getBaseInfoLiveData().observe(this, new Observer<ProfileInfoModel.BaseInfo>() {
            @Override
            public void onChanged(ProfileInfoModel.BaseInfo baseMeta) {
                if (baseMeta != null && !TextUtils.isEmpty(baseMeta.avatarCertInfo)) {
                    getViewModel().avatarPassObservable.set(false);
                    getViewModel().enableNextObservable.set(false);
                    getDataBinding().tvError.setText(baseMeta.avatarCertInfo);
                    getDataBinding().ivUpload.setVisibility(View.GONE);
                    getDataBinding().scorllAvatar.setVisibility(View.VISIBLE);
                    getDataBinding().ivAvatar.load(baseMeta.avatar);
                }
            }
        });
    }

    @Override
    public void clickNext() {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        ProfileHelper.getInstance().checkJump(activity, getViewModel().indexObservable.get() + 1);
    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        ProfileHelper.getInstance().checkJump(activity, getViewModel().indexObservable.get() + 1);
    }

    @Override
    public void clickSelectPhoto() {
        PictureSelectorManager.startSelect((FragmentActivity) activity, PictureSelectorManager.SelectType.AVATAR, true, new Function1<PictureSelectorItemType, Unit>() {
            @Override
            public Unit invoke(PictureSelectorItemType pictureSelectorItemType) {
                return null;
            }
        }, new Function2<Uri, Boolean, Unit>() {
            @Override
            public Unit invoke(Uri cameraFileUri, Boolean isGallery) {
//                Uri destination = Uri.fromFile(PathUtils.getPictureOutputFile(activity));
                Uri destination = Uri.fromFile(PathUtil.getPictureOutputFile(activity, cameraFileUri, ""));
                Bundle mCropOptionsBundle = new Bundle();
                mCropOptionsBundle.putParcelable(UCrop.EXTRA_INPUT_URI, cameraFileUri);
                mCropOptionsBundle.putParcelable(UCrop.EXTRA_OUTPUT_URI, destination);
                mCropOptionsBundle.putBoolean(BundleConstants.BUNDLE_DATA_BOOLEAN, isGallery);
                Intent intent = new Intent(activity, MeAvatarUploadCropActivity.class);
                intent.putExtras(mCropOptionsBundle);
                AppUtil.startActivityForResultOfFragment(activity, MeAvatarUploadFragment.this, intent, ME_UPLOAD_AVATAR_REQUEST_CODE);
                return null;
            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && requestCode == ME_UPLOAD_AVATAR_REQUEST_CODE) {
            if (data != null) {
                AvatarUploadSuccessBean bean = data.getParcelableExtra(BundleConstants.BUNDLE_AVATAR_UPLOAD_DATA);
                if (bean != null) {
                    if (bean.pass) {
                        getViewModel().avatarPassObservable.set(true);
                        getViewModel().enableNextObservable.set(true);
                    } else {
                        getViewModel().avatarPassObservable.set(false);
                        getViewModel().enableNextObservable.set(TextUtils.isEmpty(bean.errorInfo));
                        getDataBinding().tvError.setText(TextUtils.isEmpty(bean.errorInfo) ? "" : bean.errorInfo);
                    }
                    getDataBinding().ivUpload.setVisibility(View.GONE);
                    getDataBinding().scorllAvatar.setVisibility(View.VISIBLE);
                    getDataBinding().ivAvatar.load(bean.uri.getPath());
                }
            }
        }
    }

}
