package com.kanzhun.marry.me.info.fragment;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.kanzhun.common.util.ActivityAnimType;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;

import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.kanzhun.foundation.model.profile.ABThemeItem;
import com.kanzhun.marry.me.databinding.MeFragmentABImpressionThemeSelectBinding;
import com.kanzhun.marry.me.info.adapter.ABImpressionThemeAdapter;
import com.kanzhun.marry.me.info.viewmodel.ABImpressionThemeSelectViewModel;
import com.kanzhun.marry.me.info.callback.ABImpressionThemeSelectCallback;
import com.kanzhun.marry.me.info.viewmodel.ABImpressionViewModel;
import com.kanzhun.utils.base.LList;

import java.util.List;

public class ABImpressionThemeSelectFragment extends FoundationVMShareFragment<MeFragmentABImpressionThemeSelectBinding, ABImpressionThemeSelectViewModel, ABImpressionViewModel> implements ABImpressionThemeSelectCallback {
    public static final String TAG = "ABImpressionThemeSelectFragment";
    private BaseBinderAdapter mAdapter;

    public static Fragment getInstance() {
        ABImpressionThemeSelectFragment fragment = new ABImpressionThemeSelectFragment();
        return fragment;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_fragment_a_b_impression_theme_select;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(activity, ActivityAnimType.UP_GLIDE);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    protected void initFragment() {
        super.initFragment();
        getDataBinding().fragmentMain.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        mAdapter = new BaseBinderAdapter();
        mAdapter.addItemBinder(ABThemeItem.class, new ABImpressionThemeAdapter.ThemeItemBinder(activity, ABImpressionThemeSelectFragment.this), null);
        getDataBinding().recyclerView.setLayoutManager(new LinearLayoutManager(activity));
        getDataBinding().recyclerView.setAdapter(mAdapter);
        getViewModel().requestThemes();
        getViewModel().getThemes().observe(this, new Observer<List<ABThemeItem>>() {
            @Override
            public void onChanged(List<ABThemeItem> abThemeItems) {
                if (!LList.isEmpty(abThemeItems)) {
                    abThemeItems.add(new ABThemeItem());
                    mAdapter.setList(abThemeItems);
                }
            }
        });
    }

    @Override
    public void selectTheme(ABThemeItem item) {
        getActivityViewModel().setThemeText(item);
        ((FragmentActivity) activity).getSupportFragmentManager().beginTransaction()
                .setCustomAnimations(R.anim.common_activity_slide_from_right_to_left_enter,
                        0,
                        0,
                        R.anim.common_activity_slide_from_left_to_right_exit)
                .add(R.id.fragment_container, ABImpressionUploadFragment.getInstance())
                .addToBackStack(null)
                .commitAllowingStateLoss();
    }
}