package com.kanzhun.marry.me.identify.fragment.avatar

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.kanzhun.common.kotlin.base.EmptyViewModel
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.imageviewer.ext.imageViewer
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeFragmentAvatarAuthExamineBinding
import com.kanzhun.marry.me.identify.viewmodel.AvatarAuthViewModel
import com.kanzhun.common.kotlin.ui.onClick

/**
 * <AUTHOR>
 * 头像认证审核页
 */
class AvatarAuthExamineFragment : BaseBindingFragment<MeFragmentAvatarAuthExamineBinding, EmptyViewModel>() {

    private val parentViewModel: AvatarAuthViewModel by lazy {
        ViewModelProvider(requireActivity())[AvatarAuthViewModel::class.java]
    }

    override fun preInit(arguments: Bundle) {

    }

    override fun initView() {
        mBinding.run {
            titleBar.asBackButton()
            parentViewModel.cerBean?.run {
                ivAvatar.load(value)
                ivAvatar.setOnClickListener {
                    ivAvatar.imageViewer(value!!)
                }
            }
            idBtn.onClick {
                reportPoint("certify-inreview-page-click"){
                    actionp2 = "头像"
                }
                activity?.onBackPressed()
            }
        }
    }

    override fun initData() {
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null
}

