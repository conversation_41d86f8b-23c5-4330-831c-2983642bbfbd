package com.kanzhun.marry.me.setting.activity;

import android.os.Bundle;

import androidx.navigation.NavController;
import androidx.navigation.NavGraph;
import androidx.navigation.Navigation;

import com.kanzhun.marry.me.point.MePointReporter;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.UserReportSource;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityUserReportBinding;
import com.kanzhun.marry.me.setting.viewmodel.UserReportViewModel;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>gpeng
 * Date: 2022/6/15
 * 用户举报页面
 */
@RouterUri(path = MePageRouter.ME_USER_REPORT_ACTIVITY)
public class UserReportActivity extends FoundationVMActivity<MeActivityUserReportBinding, UserReportViewModel> {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        String userId = getIntent().getStringExtra(BundleConstants.BUNDLE_USER_ID);
        int source = getIntent().getIntExtra(BundleConstants.BUNDLE_DATA_INT, UserReportSource.SOURCE_CHAR);
        String resourceId = getIntent().getStringExtra(BundleConstants.BUNDLE_DATA_STRING);
        int relationStatus = getIntent().getIntExtra(BundleConstants.BUNDLE_RELATION_STATUS,0);
        boolean isGotoSecond = getIntent().getBooleanExtra(BundleConstants.BUNDLE_DATA_BOOLEAN,false);
        getViewModel().initParams(userId, source, resourceId);
        if(isGotoSecond){
            NavController navController= Navigation.findNavController(UserReportActivity.this,R.id.fragment);
            NavGraph navGraph =  navController.getNavInflater().inflate(R.navigation.me_user_report_navigation);
            String name = getIntent().getStringExtra(BundleConstants.BUNDLE_DATA_STRING_1);
            String subTitle = getIntent().getStringExtra(BundleConstants.BUNDLE_DATA_STRING_2);
            int code = getIntent().getIntExtra(BundleConstants.BUNDLE_DATA_INT, 0);
            getViewModel().initSecondPage(name,subTitle,code);
            navGraph.setStartDestination(R.id.reportSubmitFragment);
            navController.setGraph(navGraph);
        }else {
            getViewModel().requestReportReasons();
        }
        MePointReporter.Companion.reportReportDialogExpose(userId, source, relationStatus);
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_user_report;
    }

    @Override
    public int getCallbackVariable() {
        return 0;
    }

    @Override
    public Object getCallback() {
        return null;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }
}
