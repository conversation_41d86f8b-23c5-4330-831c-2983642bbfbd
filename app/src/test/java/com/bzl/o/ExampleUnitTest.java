package com.bzl.o;

import static org.junit.Assert.assertEquals;

import android.util.Log;

import com.hpbr.apm.common.utils.Base64;
import com.hpbr.apm.common.utils.RC4;
import com.techwolf.lib.tlog.TLog;

import org.junit.Test;

import java.io.UnsupportedEncodingException;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class ExampleUnitTest {
    @Test
    public void addition_isCorrect() {
        assertEquals(4, 2 + 2);
        String bbbb = rc4Decrypt("b6b5e4ba6e9fb95bDzOvwcmfJAg~", "LAKDF0WLEIOIWERLDSOFIJQWEO123OW");
        TLog.error("TAG", "bbb = " + bbbb);
    }

    public static String rc4Decrypt(String encryptStr, String rc4Password) {
        if (encryptStr == null || rc4Password == null) return null;
        encryptStr = encryptStr.replaceAll("_", "/").replaceAll("-", "+").replaceAll("\\~", "=");
        String decrypt = "";
        try {
            decrypt = new String(RC4.RC4encrypt(rc4Password.getBytes("UTF-8"),
                    Base64.decode(encryptStr)), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return decrypt;
    }
}