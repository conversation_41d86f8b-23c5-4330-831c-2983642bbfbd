package com.kanzhun.marry.demo;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;


import com.kanzhun.common.views.slanted.SlantedTextPathMode;
import com.kanzhun.common.views.slanted.SlantedTextView;
import com.kanzhun.marry.R;

/**
 * <AUTHOR>
 * @date 2022/3/8.
 */
public class SlantedActivity extends AppCompatActivity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_slanted);
        RecyclerView recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new GridLayoutManager(this, 2));
        recyclerView.setAdapter(new Adapter());
    }

    public static class Adapter extends RecyclerView.Adapter<ViewHolder> {

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View item = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_slanted, parent, false);
            return new ViewHolder(item);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            SlantedTextView slantedTextView = holder.getSlantedTextView();
            SlantedTextPathMode mode;
            int modeType = position % 7;
            int backGroundColor;
            switch (modeType) {
                case 0:
                    mode = SlantedTextPathMode.MODE_LEFT;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.common_color_0D0D1A);
                    break;
                case 1:
                    mode = SlantedTextPathMode.MODE_RIGHT;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.common_color_881D1D37);
                    break;
                case 2:
                    mode = SlantedTextPathMode.MODE_LEFT_BOTTOM;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.common_color_5D68E8);
                    break;
                case 3:
                    mode = SlantedTextPathMode.MODE_RIGHT_BOTTOM;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.common_color_55D5DB);
                    break;
                case 4:
                    mode = SlantedTextPathMode.MODE_LEFT_TRIANGLE;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.common_color_50AEAEAE);
                    break;
                case 5:
                    mode = SlantedTextPathMode.MODE_RIGHT_TRIANGLE;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.common_color_9999A3);
                    break;
                case 6:
                    mode = SlantedTextPathMode.MODE_LEFT_BOTTOM_TRIANGLE;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.image_color_red);
                    break;
                case 7:
                    mode = SlantedTextPathMode.MODE_RIGHT_BOTTOM_TRIANGLE;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.image_color_yellow);
                    break;
                default:
                    mode = SlantedTextPathMode.MODE_LEFT;
                    backGroundColor = slantedTextView.getResources().getColor(R.color.common_color_0D0D1A);
            }
            slantedTextView.setMode(mode);
            slantedTextView.setSlantedBackgroundColor(backGroundColor);

        }

        @Override
        public int getItemCount() {
            return 40;
        }
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        SlantedTextView slantedTextView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            slantedTextView = itemView.findViewById(R.id.slanted_text);
        }

        public SlantedTextView getSlantedTextView() {
            return slantedTextView;
        }
    }
}
