package com.kanzhun.marry.demo;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.techwolf.lib.tlog.TLog;
import com.kanzhun.keyboard.switchpanel.PanelSwitchHelper;
import com.kanzhun.keyboard.switchpanel.interfaces.ContentScrollMeasurer;
import com.kanzhun.keyboard.switchpanel.interfaces.TriggerViewClickInterceptor;
import com.kanzhun.keyboard.switchpanel.interfaces.listener.OnPanelChangeListener;
import com.kanzhun.keyboard.switchpanel.view.panel.IPanelView;
import com.kanzhun.keyboard.switchpanel.view.panel.PanelView;
import com.kanzhun.marry.R;
import com.kanzhun.marry.databinding.ActivtySwitchPanelBinding;

import java.util.ArrayList;
import java.util.List;

public class SwitchPanelActivity extends AppCompatActivity {
    private static final String TAG = SwitchPanelActivity.class.getSimpleName();
    private List<String> msgList = new ArrayList<String>();
    RecyclerView recycler_view;
    private ActivtySwitchPanelBinding mBinding;
    private PanelSwitchHelper mHelper;
    private LinearLayoutManager mLinearLayoutManager;
    MsgListAdapter mAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        for (int i = 0; i < 20; i++) {
            msgList.add("Msg+i=" + i);
        }
        initView();
    }

    private void initView() {
        mBinding = DataBindingUtil.setContentView(this, R.layout.activty_switch_panel);
        mLinearLayoutManager = new LinearLayoutManager(this);
        mBinding.recyclerView.setLayoutManager(mLinearLayoutManager);
        mAdapter = new MsgListAdapter();
        mBinding.recyclerView.setAdapter(new MsgListAdapter());
        mBinding.send.setOnClickListener(v -> {
            this.finish();
            String content = mBinding.editText.getText().toString();
            if (TextUtils.isEmpty(content)) {
                Toast.makeText(SwitchPanelActivity.this, "当前没有输入", Toast.LENGTH_SHORT).show();
                return;
            }
            mBinding.editText.setText(null);
            scrollToBottom();
        });
    }


    private void scrollToBottom() {
        mBinding.getRoot().post(() -> mLinearLayoutManager.scrollToPosition(mAdapter.getItemCount() - 1));
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (mHelper == null) {
            mHelper = new PanelSwitchHelper.Builder(this)
                    //可选
                    .addKeyboardStateListener((visible, height) -> TLog.debug(TAG, "系统键盘是否可见 : " + visible + " 高度为：" + height))
                    .addEditTextFocusChangeListener((view, hasFocus) -> {
                        if (hasFocus) {
                            scrollToBottom();
                        }
                    })
                    .setTriggerViewClickInterceptor((TriggerViewClickInterceptor) triggerId -> {
                        if (R.id.emotion_btn == triggerId) {
                            Toast.makeText(SwitchPanelActivity.this, "R.id.emotion_btn 被拦截，可在 setTriggerViewClickInterceptor 解除", Toast.LENGTH_SHORT).show();
                            return true;
                        }
                        return false;
                    })
                    //可选
                    .addViewClickListener(view -> {
                        switch (view.getId()) {
                            case R.id.edit_text:
                            case R.id.add_btn:
                            case R.id.emotion_btn: {
                                scrollToBottom();
                            }
                        }
                    })
                    //可选
                    .addPanelChangeListener(new OnPanelChangeListener() {

                        @Override
                        public void onKeyboard() {
                            mBinding.emotionBtn.setSelected(false);
                            scrollToBottom();
                        }

                        @Override
                        public void onNone() {
                            mBinding.emotionBtn.setSelected(false);
                        }

                        @Override
                        public void onPanel(IPanelView view) {
                            if (view instanceof PanelView) {
                                mBinding.emotionBtn.setSelected(((PanelView) view).getId() == R.id.panel_emotion ? true : false);
                                scrollToBottom();
                            }
                        }

                        @Override
                        public void onPanelSizeChange(IPanelView panelView, boolean portrait, int oldWidth, int oldHeight, int width, int height) {
                            if (panelView instanceof PanelView) {
                                switch (((PanelView) panelView).getId()) {
                                    case R.id.panel_emotion: {

                                        break;
                                    }
                                    case R.id.panel_addition: {
                                        break;
                                    }
                                }
                            }
                        }
                    })
                    .addContentScrollMeasurer(new ContentScrollMeasurer() {
                        @Override
                        public int getScrollDistance(int defaultDistance) {
                            return defaultDistance - unfilledHeight;
                        }

                        @Override
                        public int getScrollViewId() {
                            return R.id.recycler_view;
                        }
                    })
                    .logTrack(true)             //output log
                    .build();
            mBinding.recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);
                    RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
                    if (layoutManager instanceof LinearLayoutManager) {
                        int childCount = recyclerView.getChildCount();
                        if (childCount > 0) {
                            View lastChildView = recyclerView.getChildAt(childCount - 1);
                            int bottom = lastChildView.getBottom();
                            int listHeight = mBinding.recyclerView.getHeight() - mBinding.recyclerView.getPaddingBottom();
                            unfilledHeight = listHeight - bottom;
                        }
                    }
                }
            });
        }
    }

    private int unfilledHeight = 0;


    @Override
    public void onBackPressed() {
        if (mHelper != null && mHelper.hookSystemBackByPanelSwitcher()) {
            return;
        }
        super.onBackPressed();
    }

    private class MsgListAdapter extends RecyclerView.Adapter<MsgListAdapter.ViewHolder> {

        public MsgListAdapter() {
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_msg, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.msgTextView.setText(msgList.get(position));
        }

        @Override
        public int getItemCount() {
            return msgList.size();
        }


        private class ViewHolder extends RecyclerView.ViewHolder {
            TextView msgTextView;

            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                msgTextView = itemView.findViewById(R.id.tv_msg);
            }
        }
    }
}