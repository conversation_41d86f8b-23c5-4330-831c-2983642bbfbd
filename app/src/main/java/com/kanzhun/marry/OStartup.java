package com.kanzhun.marry;

import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.text.TextUtils;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.backends.okhttp3.OkHttpImagePipelineConfigFactory;
import com.facebook.imagepipeline.core.ImagePipelineConfig;
import com.facebook.imagepipeline.listener.RequestListener;
import com.kanzhun.common.AppBuildConfig;
import com.kanzhun.common.app.AppThreadFactory;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.base.LBase;
import com.kanzhun.common.blurkit.BlurKit;
import com.kanzhun.common.kotlin.ui.statelayout.StateLayoutManager;
import com.kanzhun.foundation.api.base.HttpClientInit;
import com.kanzhun.foundation.apm.ApmManager;
import com.kanzhun.foundation.apm.TwlAppProvider;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.kernel.account.AccountLifecycle;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.ui.Navigation;
import com.kanzhun.foundation.utils.ChannelUtils;
import com.kanzhun.foundation.utils.DeviceIdUtils;
import com.kanzhun.foundation.utils.NotifyUtil;
import com.kanzhun.foundation.utils.ProcessHelper;
import com.kanzhun.foundation.utils.ProcessInfo;
import com.kanzhun.http.OkHttpClientFactory;
import com.kanzhun.imageviewer.ImageViewer;
import com.kanzhun.imageviewer.glide.GlideImageLoader;
import com.kanzhun.imageviewer.sketch.SketchImageLoadFactory;
import com.kanzhun.marry.chat.activity.SingleChatActivity;
import com.kanzhun.marry.config.UrlConfigManager;
//import com.kanzhun.marry.kmp.KRApplication;
import com.kanzhun.marry.login.OSplashActivity;
import com.kanzhun.marry.login.activity.LoginEntryActivity;
import com.kanzhun.marry.main.MainActivity;
import com.kanzhun.marry.push.PushSdkConfig;
import com.kanzhun.marry.push.PushSdkManager;
import com.kanzhun.marry.start.StartPoint;
import com.kanzhun.marry.videomeeting.VideoAndAudioChatActivity;
import com.kanzhun.marry.videomeeting.VideoAudioFloatPage;
import com.kanzhun.utils.L;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.process.ProcessUtil;
import com.petterp.floatingx.FloatingXProviderO;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.common.DefaultRootUriHandler;
import com.sankuai.waimai.router.components.DefaultLogger;
import com.sankuai.waimai.router.components.DefaultOnCompleteListener;
import com.sankuai.waimai.router.core.Debugger;
import com.techwolf.lib.tlog.TLog;
import com.techwolf.lib.tlog.config.TLogConfig;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.startup.Startup;
import com.twl.startup.StartupLifecycle;
import com.zhihu.matisse.Matisse;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import cn.shuzilm.core.Listener;
import cn.shuzilm.core.Main;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/3/8
 */
public final class OStartup {
    private static final String TAG = "OStartup";

    private static class OStartupHolder {
        private static final OStartup INSTANCE = new OStartup();
    }

    public static OStartup getInstance() {
        return OStartupHolder.INSTANCE;
    }

    private OStartup() {
    }

    public void init(Application application) {
        Startup.StartupStrategy startupStrategy = new Startup.StartupStrategy(OSplashActivity.class);
        startupStrategy.withLifecycle(mStartupLifecycle);
        Startup.initialize(application, ProcessUtil.getCurProcessName(application), startupStrategy);
    }

    private StartupLifecycle mStartupLifecycle = new StartupLifecycle() {
        private boolean isMainProcess;

        @Override
        public void onAppCreate(boolean isMainProcess) {
            L.e(TAG,"onAppCreate:"+isMainProcess);
            this.isMainProcess = isMainProcess;
            Application context = (Application) ProcessHelper.getContext();
            new SettingBuilder.Builder()
                    .setFlavor(BuildConfig.FLAVOR)
                    .setDebug(BuildConfig.DEBUG)
                    .setVersionCode(BuildConfig.VERSION_CODE)
                    .setVersionName(BuildConfig.VERSION_NAME)
                    .setConfigType(BuildConfig.CONFIG)
                    .build();
            initRouter(context);

            LBase.setBaseApplication((BaseApplication) context);

            AppBuildConfig appBuildConfig = new AppBuildConfig();
            appBuildConfig.CONFIG = BuildConfig.CONFIG;
            appBuildConfig.DEBUG = BuildConfig.DEBUG;
            LBase.setAppBuildConfig(appBuildConfig);

            BlurKit.init(context);
            ImageViewer.initialize(
                    GlideImageLoader.Companion.with(context),
                    new SketchImageLoadFactory()
            );
            StateLayoutManager.Companion.init();
        }

        @Override
        public void onUpgrade() {

        }

        @Override
        public void onBackground() {
            L.e(TAG,"onBackground");
            Application context = (Application) ProcessHelper.getContext();
            // url的配置，必须是第一位的！！！
            UrlConfigManager.initConfig(context);
            initLBase(context);
            if (isMainProcess) {//主进程中配置
                L.e(TAG,"onBackground:isMainProcess");
                initBugly(context);
                initShuMeng(context);
                initTLog(context);
                initPushChannel(context);
                AccountHelper.initialized(new ProcessInfo() {
                    @Override
                    public List<AccountLifecycle> getLifecycleList() {
                        return Arrays.asList(ServiceManager.getInstance().getAccountLifecycle());
                    }
                });
                HttpClientInit.init(context);
                Router.lazyInit();
                Matisse.initialize(context);
                //设置 glide okHttp
                ImageViewer.initialize(
                        GlideImageLoader.Companion.with(context, OkHttpClientFactory.getInstance().getClientImageLoader()),
                        new SketchImageLoadFactory()
                );
                initFresco(context);

                Navigation.initialized(new Navigation.Info().setHomeClass(MainActivity.class).setLoginClass(LoginEntryActivity.class).setChatClass(SingleChatActivity.class).setVideoChatClass(VideoAndAudioChatActivity.class).setFloatPageClass(VideoAudioFloatPage.class));
                //依赖 HiKernel
                initPushSdk(context);
                // initPushSdk 一定要在 NotifyUtil.registerNotify(); 之前
                NotifyUtil.registerNotify();
                // 处理由点击push消息触发的冷启动时，点击无法及时跳转聊天页面的缓存任务
                PushSdkManager.getInstance().processPushCache();
                initApm(context);

                new StartPoint().point(context);
                FloatingXProviderO.INSTANCE.start(context);

//                KRApplication.Companion.setApplication(context);
            }
        }

        @Override
        public boolean onComplete() {
            return false;
        }
    };

    private void  initFresco(Application application){
        Fresco.initialize(application, OkHttpImagePipelineConfigFactory.newBuilder(application, OkHttpClientFactory.getInstance().getGeneralClient()).build());
    }

    private void initPushChannel(Application application) {
        NotificationManager manager = (NotificationManager)
                application.getSystemService(Context.NOTIFICATION_SERVICE);
        NotificationChannel channel = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            channel = new NotificationChannel("IM", "聊天消息", NotificationManager.IMPORTANCE_DEFAULT);
            channel.setDescription("");
            manager.createNotificationChannel(channel);
        }
        L.e(TAG,"initPushChannel");
    }

    private void initApm(Application context) {
        ApmManager.start(context);
        ApmManager.sync();
    }

    private void initRouter(Context context) {
        // 自定义Logger
        DefaultLogger logger = new DefaultLogger() {
            @Override
            protected void handleError(Throwable t) {
                super.handleError(t);
                // 此处上报Fatal级别的异常
                CrashReport.postCatchedException(t);
            }
        };

        // 设置Logger
        Debugger.setLogger(logger);

        // Log开关，建议测试环境下开启，方便排查问题。
        Debugger.setEnableLog(BuildConfig.DEBUG);

        // 调试开关，建议测试环境下开启。调试模式下，严重问题直接抛异常，及时暴漏出来。
        Debugger.setEnableDebug(BuildConfig.DEBUG);

        // 创建RootHandler
        DefaultRootUriHandler rootHandler = new DefaultRootUriHandler(context);

        // 设置全局跳转完成监听器，可用于跳转失败时统一弹Toast提示，做埋点统计等。
        rootHandler.setGlobalOnCompleteListener(DefaultOnCompleteListener.INSTANCE);

        // 初始化
        Router.init(rootHandler);

//        // 懒加载后台初始化（可选）
//        new AsyncTask<Void, Void, Void>() {
//            @Override
//            protected Void doInBackground(Void... voids) {
//
//                return null;
//            }
//        }.execute();
    }

    private void initLBase(Application context) {
        TwlAppProvider twlAppProvider = new TwlAppProvider();
        LBase.setTwlAppProvider(twlAppProvider);
        HttpClientInit.init(context);
    }

    private void initBugly(Context context) {
        // Bugly处理器
        CrashReport.UserStrategy userStrategy = new CrashReport.UserStrategy(context);
        userStrategy.setCrashHandleCallback(new CrashReport.CrashHandleCallback(){
            @Override
            public synchronized Map<String, String> onCrashHandleStart(int i, String s, String s1, String s2) {
                TLog.debug("Bugly","onCrashHandleStart");
                return super.onCrashHandleStart(i, s, s1, s2);
            }

            @Override
            public synchronized byte[] onCrashHandleStart2GetExtraDatas(int i, String s, String s1, String s2) {
                TLog.debug("Bugly","onCrashHandleStart2GetExtraDatas");

                return super.onCrashHandleStart2GetExtraDatas(i, s, s1, s2);
            }
        });
        CrashReport.initCrashReport(context, BuildConfig.DEBUG? "cd6cdd585f" : "a9d6f7bf9e", BuildConfig.DEBUG, userStrategy);
    }

    private void initTLog(Application application) {
        TLogConfig config = TLogConfig.create(application);
        config.setDebug(SettingBuilder.getInstance().isDebug());
        String tLogRootPath = ServiceManager.getInstance().getFileService().getTLogRootPath();
        config.setFilePath(tLogRootPath);
        TLog.initializer(config);
    }


    /**
     * 数盟初始化
     *
     * @param context
     */
    private void initShuMeng(Context context) {
        try {
            Main.init(context, "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMfN84K/ZL2Y/Evzhlmxm4IRWkawCTdB6ENm3cTDCnDaqjI8m9Z/tQnFTZi7a/Rz2JbmcmFJBfQXzN9YhC/4Ky8CAwEAAQ==");
            Main.setConfig("url", "uni-shumeng.zhipin.com");
            Main.setConfig("cdlmt", "1");//配置，不采集IMEI、IMSI、MEID、SN、MAC、ICCID相关信息
            Main.setConfig("pkglist", "1");
            Main.getQueryID(context, ChannelUtils.getChannel(), "message", 1, new Listener() {
                @Override
                public void handler(String s) {
                    L.d("appinfo", "======initShuMeng====ss:" + s);
                    if (!TextUtils.isEmpty(s)) {
                        L.d("appinfo", "======initShuMeng====:" + s);
                        DeviceIdUtils.putDid(s);
                    }
                }
            });

            // 调用获取OAID接口，需要在调用初始化方法5s后再调用，因为有的设备运行效率比较差，得先确保完成初始化，防止代码冲突。
            AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    Main.getOpenAnmsID(context, oaid -> {
                        L.d("appinfo", "OAID : " + oaid);
                        DeviceIdUtils.putOAID(oaid);

                    });
                    Main.getHMSOpenAnmsID(context, oaid -> {
                        L.d("appinfo", "HMSOpenAnmsID : " + oaid);
                        DeviceIdUtils.putHMSOID(oaid);
                    });
                }
            }, 5000);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initPushSdk(Application application) {
        PushSdkConfig pushSdkConfig = new PushSdkConfig();
        pushSdkConfig.pushCallback = new PushCallback();
        PushSdkManager.getInstance().init(application, pushSdkConfig, new PushRegisterToken());
        PushSdkManager.getInstance().start();
        NotifyUtil.clearNotifications();
        PushSdkManager.getInstance().setForeground(true);
    }
}
