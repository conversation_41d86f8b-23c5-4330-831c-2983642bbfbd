package com.kanzhun.marry.httpdemo.retrofit;

import androidx.annotation.CheckResult;
import androidx.annotation.NonNull;

import com.trello.rxlifecycle4.LifecycleTransformer;
import com.trello.rxlifecycle4.OutsideLifecycleException;
import com.trello.rxlifecycle4.RxLifecycle;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.functions.Function;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON>gpeng
 * Date: 2022/2/11
 */
public class RxLifecycleViewModel {

    private static final Function<ViewModelEvent, ViewModelEvent> VIEW_MODEL_LIFECYCLE = new Function<ViewModelEvent, ViewModelEvent>() {
        @Override
        public ViewModelEvent apply(@NonNull ViewModelEvent viewModelEvent) throws Exception {
            switch (viewModelEvent) {
                case VIEW_MODEL_ACTIVE:
                    return ViewModelEvent.VIEW_MODEL_ACTIVE;
                case VIEW_MODEL_CANCEL:
                    throw new OutsideLifecycleException("Cannot bind to ViewModel lifecycle when outside of it.");
                default:
                    throw new UnsupportedOperationException("Binding to " + viewModelEvent + " not yet implemented");
            }
        }
    };

    @NonNull
    @CheckResult
    public static <T> LifecycleTransformer<T> bindViewModel(@NonNull Observable<ViewModelEvent> lifecycle) {
        return RxLifecycle.bind(lifecycle, VIEW_MODEL_LIFECYCLE);
    }

}
