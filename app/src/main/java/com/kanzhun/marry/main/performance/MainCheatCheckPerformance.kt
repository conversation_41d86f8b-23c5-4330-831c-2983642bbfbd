package com.kanzhun.marry.main.performance

import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.base.BaseApplication
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.twl.anti.CheatChecker

class MainCheatCheckPerformance :AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        CheatChecker(BaseApplication.getApplication(),null).start { _, _, _ ->  }
    }
}