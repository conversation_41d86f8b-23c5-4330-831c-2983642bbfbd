package com.kanzhun.marry;

import android.util.Log;

import androidx.annotation.NonNull;

import com.facebook.imagepipeline.listener.BaseRequestListener;
import com.facebook.imagepipeline.request.ImageRequest;
import com.hpbr.apm.event.ApmAnalyzer;
import com.kanzhun.common.util.LText;

public class BzlFrescoRequestListener extends BaseRequestListener {

//    private static final String TAG = "BzlFrescoRequestListener";
//
//    @Override
//    public void onRequestStart(ImageRequest request, Object callerContext, String requestId, boolean isPrefetch) {
//        super.onRequestStart(request, callerContext, requestId, isPrefetch);
////        L.debug(TAG, "[onRequestStart] >>> %s", String.valueOf(request));
//    }
//
//    @Override
//    public void onRequestSuccess(ImageRequest request, String requestId, boolean isPrefetch) {
//        super.onRequestSuccess(request, requestId, isPrefetch);
////        L.debug(TAG, "%s <<< [onRequestSuccess]", String.valueOf(request));
//    }

    @Override
    public void onRequestFailure(ImageRequest request, String requestId, Throwable throwable, boolean isPrefetch) {
        super.onRequestFailure(request, requestId, throwable, isPrefetch);
//        L.debug(TAG, "[onRequestFailure] --- %s --- [onRequestFailure]", String.valueOf(request));

        final String stackTraceString = getStackTraceString(throwable);
        if (!LText.empty(stackTraceString)) {
            ApmAnalyzer.create()
                    .action("action_fresco_image_request_failure")
                    .p2(String.valueOf(request))
                    .p3(stackTraceString)
                    .report();
        }
    }

    @NonNull
    public static String getStackTraceString(@NonNull Throwable e) {
        String stackTraceString;
        try {
            if (e instanceof StackOverflowError) {
                final StackTraceElement[] stackTrace = e.getStackTrace();
                StringBuilder builder = new StringBuilder();
                builder.append(e.getMessage());
                builder.append("\n");
                int len = Math.min(stackTrace.length, 20); // 最多获取前20帧
                for (int i = 0; i < len; i++) {
                    builder.append(stackTrace[i].toString());
                    builder.append("\n");
                }
                stackTraceString = builder.toString();
            } else {
                stackTraceString = Log.getStackTraceString(e);
            }
        } catch (Throwable ignored) {
            stackTraceString = e.getMessage();
        }

        return stackTraceString == null ? "" : stackTraceString;
    }

}
