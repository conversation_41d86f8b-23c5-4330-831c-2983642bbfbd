package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.utils.base.LText

/**
 * 主态我的心情详情页面
 */
class MyMoodDetailAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        val openThumb = params["openThumb"]
        if (LText.getBoolean(openThumb)) {
            MePageRouter.jumpToMyMoodDetailActivity(context, PageSource.PROTOCOL_OPEN_MOOD_DETAIL_WITH_THUMB)
        }

    }
}