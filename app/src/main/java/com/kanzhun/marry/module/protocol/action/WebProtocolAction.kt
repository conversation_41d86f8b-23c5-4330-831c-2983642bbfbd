package com.kanzhun.marry.module.protocol.action

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import com.kanzhun.common.app.AppThreadFactory
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.map.MapUtils
import com.kanzhun.foundation.model.WebViewBean
import com.kanzhun.foundation.router.AppPageRouter
import com.kanzhun.utils.L
import com.kanzhun.utils.URLUtils
import com.techwolf.lib.tlog.TLog
import java.net.URLDecoder

class WebProtocolAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        L.e("WebProtocolAction","encode:"+params["url"])
        val url = URLDecoder.decode(params["url"], "utf-8")
        L.e("WebProtocolAction","decode"+url)
        var browser = Constants.BROWSER_INNER
        try {
            browser = params["browser"]!!.toInt()
        } catch (e: Exception) {
            TLog.info("WebAction", "browser is not set")
        }
        if (browser == Constants.BROWSER_OUTER) {
            val uri = Uri.parse(url)
            val intent = Intent()
            intent.action = Intent.ACTION_VIEW
            intent.data = uri
            context.startActivity(intent)
        } else {
            var noHead = 0
            try {
                noHead = params["noHead"]!!.toInt()
            } catch (e: Exception) {
                TLog.info("WebAction", "noHead is not set")
            }

            val urlParams:Map<String?,String?> = URLUtils.parseUrlParam(url)

            val delayProgress = urlParams.get("delayProgress") //v1221 延迟显示进度条（毫秒）
            val bgColor = urlParams.get("bgColor")

            val webViewBean = WebViewBean()
            webViewBean.url = url
            webViewBean.style = if (noHead == 1) WebViewBean.STYLE_HAS_NO_TITLE_AND_TRANSLUCENT else WebViewBean.STYLE_HAS_NORMAL
            val bundle = Bundle()
            bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean)

            bundle.putInt("delayProgress", delayProgress?.toIntOrNull()?:0)
            bundle.putString("bgColor", bgColor)
            AppUtil.startUri(context, AppPageRouter.WEB_VIEW_ACTIVITY, bundle)

        }
    }
}