package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.foundation.SocialConstants
import com.kanzhun.foundation.router.SocialPageRouter


class MomentDetailAction:IProtocolAction{
    override fun startAction(context: Context, params: Map<String, String>) {
        SocialPageRouter.jumpToDynamicDetailActivity(
            context,
            params["momentId"],
            0
        )
    }
}