package com.kanzhun.marry.alphaplayerdemo.player;

//import android.content.Context;
//import android.net.Uri;
//import android.view.Surface;
//
//import com.google.android.exoplayer2.ExoPlaybackException;
//import com.google.android.exoplayer2.MediaItem;
//import com.google.android.exoplayer2.Player;
//import com.google.android.exoplayer2.SimpleExoPlayer;
//import com.google.android.exoplayer2.source.LoopingMediaSource;
//import com.google.android.exoplayer2.source.MediaSource;
//import com.google.android.exoplayer2.source.ProgressiveMediaSource;
//import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
//import com.google.android.exoplayer2.util.Util;
//import com.google.android.exoplayer2.video.VideoListener;
//import com.ss.ugc.android.alpha_player.model.VideoInfo;
//import com.ss.ugc.android.alpha_player.player.AbsPlayer;
//
//import java.io.IOException;
//
///**
// * Created by ChaiJiangpeng
// * Date: 2022/2/16
// */
//public class ExoPlayerImpl extends AbsPlayer {
//    private Context context;
//    private SimpleExoPlayer exoPlayer;
//    private DefaultDataSourceFactory dataSourceFactory;
//    private int currVideoWidth = 0;
//    private int currVideoHeight = 0;
//    private MediaSource videoSource;
//    private boolean isLooping;
//    private Player.EventListener exoPlayerListener = new Player.EventListener() {
//        @Override
//        public void onPlayerError(ExoPlaybackException error) {
//            if (getErrorListener() != null) {
//                getErrorListener().onError(0, 0, "ExoPlayer on error: " + Log.getStackTraceString(error));
//            }
//        }
//
//        @Override
//        public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
//            switch (playbackState) {
//                case Player.STATE_READY:
//                    if (playWhenReady) {
//                        if (getPreparedListener() != null) {
//                            getPreparedListener().onPrepared();
//                        }
//                    }
//                    break;
//                case Player.STATE_ENDED:
//                    if (getCompletionListener() != null) {
//                        getCompletionListener().onCompletion();
//                    }
//                    break;
//                default:
//                    break;
//            }
//        }
//    };
//
//    private VideoListener exoVideoListener = new VideoListener() {
//        @Override
//        public void onVideoSizeChanged(int width, int height, int unappliedRotationDegrees, float pixelWidthHeightRatio) {
//            currVideoWidth = width;
//            currVideoHeight = height;
//        }
//
//
//        @Override
//        public void onRenderedFirstFrame() {
//            if (getFirstFrameListener() != null) {
//                getFirstFrameListener().onFirstFrame();
//            }
//        }
//    };
//
//    public ExoPlayerImpl(Context context) {
//        super(context);
//        this.context = context;
//        init(context);
//    }
//
//    private void init(Context context) {
//        dataSourceFactory = new DefaultDataSourceFactory(context, Util.getUserAgent(context, "player"));
//    }
//
//    @Override
//    public String getPlayerType() {
//        return "ExoPlayerImpl";
//    }
//
//    @Override
//    public VideoInfo getVideoInfo() throws Exception {
//        return new VideoInfo(currVideoWidth, currVideoHeight);
//    }
//
//    @Override
//    public void initMediaPlayer() throws Exception {
//        SimpleExoPlayer.Builder builder = new SimpleExoPlayer.Builder(context);
//        exoPlayer = builder.build();
//        exoPlayer.addListener(exoPlayerListener);
//        exoPlayer.addVideoListener(exoVideoListener);
//    }
//
//    @Override
//    public void pause() {
//        exoPlayer.setPlayWhenReady(false);
//    }
//
//    @Override
//    public void prepareAsync() {
//        exoPlayer.setMediaSource(videoSource);
//        exoPlayer.prepare();
//        exoPlayer.setPlayWhenReady(true);
//    }
//
//    @Override
//    public void release() {
//        exoPlayer.release();
//    }
//
//    @Override
//    public void reset() {
//        exoPlayer.stop(true);
//    }
//
//    @Override
//    public void setDataSource(String dataPath) throws IOException {
//        if (isLooping) {
//            ProgressiveMediaSource.Factory factory = new ProgressiveMediaSource.Factory(dataSourceFactory);
//            ProgressiveMediaSource extractorMediaSource = factory.createMediaSource(MediaItem.fromUri(Uri.parse(dataPath)));
//            videoSource = new LoopingMediaSource(extractorMediaSource);
//        } else {
//            ProgressiveMediaSource.Factory factory = new ProgressiveMediaSource.Factory(dataSourceFactory);
//             videoSource = factory.createMediaSource(MediaItem.fromUri(Uri.parse(dataPath)));
//        }
//        reset();
//    }
//
//    @Override
//    public void setLooping(boolean looping) {
//        this.isLooping = looping;
//    }
//
//    @Override
//    public void setScreenOnWhilePlaying(boolean onWhilePlaying) {
//
//    }
//
//    @Override
//    public void setSurface(Surface surface) {
//        exoPlayer.setVideoSurface(surface);
//    }
//
//    @Override
//    public void start() {
//        exoPlayer.setPlayWhenReady(true);
//    }
//
//    @Override
//    public void stop() {
//        exoPlayer.stop();
//    }
//}
