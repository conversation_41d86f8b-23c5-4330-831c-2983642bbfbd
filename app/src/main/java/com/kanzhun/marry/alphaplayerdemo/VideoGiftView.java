package com.kanzhun.marry.alphaplayerdemo;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/2/16
 */
//public class VideoGiftView extends FrameLayout {
//    private RelativeLayout mVideoContainer;
//    private IPlayerController mPlayerController;
//
//    public VideoGiftView(@NonNull Context context) {
//        this(context, null);
//    }
//
//    public VideoGiftView(@NonNull Context context, @Nullable AttributeSet attrs) {
//        this(context, attrs, 0);
//    }
//
//    public VideoGiftView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
//        super(context, attrs, defStyleAttr);
//        init(context);
//    }
//
//    private void init(Context context) {
//        LayoutInflater.from(context).inflate(R.layout.view_video_gift, this);
//        mVideoContainer = findViewById(R.id.rl);
//    }
//
//    public void initPlayerController(Context context, LifecycleOwner owner, IPlayerAction playerAction, IMonitor monitor) {
//        Configuration configuration = new Configuration(context, owner);
//        //  GLTextureView supports custom display layer, but GLSurfaceView has better performance, and the GLSurfaceView is default.
////        configuration.setAlphaVideoViewType(AlphaVideoViewType.GL_TEXTURE_VIEW);
//        configuration.setAlphaVideoViewType(AlphaVideoViewType.GL_SURFACE_VIEW);
//        mPlayerController = new PlayerController(context, owner, configuration.getAlphaVideoViewType(), new ExoPlayerImpl(context));
//        mPlayerController.setPlayerAction(playerAction);
//        mPlayerController.setMonitor(monitor);
//    }
//
//    public void attachView() {
//        if (mPlayerController != null) {
//            mPlayerController.attachAlphaView(mVideoContainer);
//            T.ss("attach alphaVideoView");
//        }
//    }
//
//    public void detachView() {
//        if (mPlayerController != null) {
//            mPlayerController.detachAlphaView(mVideoContainer);
//            T.ss("detach alphaVideoView");
//        }
//    }
//
//    public void startVideoGift(String filePath) {
//        if (TextUtils.isEmpty(filePath)) {
//            return;
//        }
//        ConfigModel configModel = JsonUtil.parseConfigModel(filePath);
//        DataSource dataSource = new DataSource();
//        if (!filePath.endsWith(File.separator)) {
//            filePath = filePath + File.separator;
//        }
//        dataSource.baseDir = filePath;
//        dataSource.setPortraitPath(configModel.getPortraitItem().getPath(), configModel.getPortraitItem().getAlignMode());
//        dataSource.setLandscapePath(configModel.getLandscapeItem().getPath(), configModel.getLandscapeItem().getAlignMode());
////        dataSource.setLooping(false);
//
//        startDataSource(dataSource);
//    }
//
//    private void startDataSource(DataSource dataSource) {
//        if (!dataSource.isValid()) {
//            L.e("BossO", "startDataSource: dataSource is invalid.");
//        }
//        if (mPlayerController != null) {
//            mPlayerController.start(dataSource);
//        }
//    }
//
//}
