package com.kanzhun.marry.login.fragment

import android.graphics.Typeface
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.Navigation
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.kanzhun.common.kotlin.ui.onClick
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
import com.kanzhun.common.adpter.ViewPagerFragmentAdapter
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.FragmentLoginKeySelectBinding
import com.kanzhun.marry.login.api.model.OrangeDicProfileTagResponse
import com.kanzhun.marry.login.viewmodel.LoginLabelViewPagerSelectViewModel

class LoginLabelViewPagerSelectFragment :
    BaseBindingFragment<FragmentLoginKeySelectBinding, LoginLabelViewPagerSelectViewModel>() {
    
    private lateinit var adapter: OptimizedFragmentAdapter
    private var list = mutableListOf<LoginLabelItemSelectFragment>()
    private var tabLayoutMediator: TabLayoutMediator? = null
    private var currentBeans: List<OrangeDicProfileTagResponse.Bean> = emptyList()
    
    /**
     * 优化的Fragment适配器，解决状态同步问题
     */
    private inner class OptimizedFragmentAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {
        private val fragments = mutableListOf<LoginLabelItemSelectFragment>()
        
        fun updateFragments(newFragments: List<LoginLabelItemSelectFragment>) {
            val oldSize = fragments.size
            fragments.clear()
            fragments.addAll(newFragments)
            
            // 智能通知数据变化
            when {
                oldSize == 0 -> notifyDataSetChanged()
                oldSize != fragments.size -> notifyDataSetChanged()
                else -> {
                    // 如果数量相同，只通知内容变化
                    for (i in fragments.indices) {
                        notifyItemChanged(i)
                    }
                }
            }
        }
        
        override fun createFragment(position: Int): Fragment {
            return if (position < fragments.size) {
                fragments[position]
            } else {
                // 防止越界，返回空Fragment
                Fragment()
            }
        }
        
        override fun getItemCount(): Int = fragments.size
        
        override fun getItemId(position: Int): Long {
            return if (position < fragments.size) {
                fragments[position].hashCode().toLong()
            } else {
                position.toLong()
            }
        }
        
        override fun containsItem(itemId: Long): Boolean {
            return fragments.any { it.hashCode().toLong() == itemId }
        }
    }
    
    override fun preInit(arguments: Bundle) {}

    fun setData(beans: List<OrangeDicProfileTagResponse.Bean>) {
        // 避免重复设置相同数据
        if (currentBeans == beans) return
        currentBeans = beans
        
        // 先detach之前的mediator
        tabLayoutMediator?.detach()
        
        // 清空并重新构建fragment列表
        list.clear()
        for ((i, bean) in beans.withIndex()) {
            list.add(LoginLabelItemSelectFragment(bean.subTag, bean.id, mViewModel))
        }
        
        // 更新适配器数据
        adapter.updateFragments(list)
        
        // 等待适配器更新完成后再创建TabLayoutMediator
        mBinding.idViewPager.post {
            // 创建新的TabLayoutMediator
            tabLayoutMediator = TabLayoutMediator(
                mBinding.tabs, 
                mBinding.idViewPager, 
                true, 
                true
            ) { tab, position ->
                if (position < beans.size) {
                    val parentView = LayoutInflater.from(context).inflate(
                        R.layout.item_label_login_key_label, 
                        null
                    )
                    val button = parentView.findViewById<QMUIRoundButton>(R.id.idLabel)
                    button.text = beans[position].content
                    tab.customView = parentView
                    
                    // 优化背景设置
                    parentView.post {
                        (parentView.parent as? ViewGroup)?.background = null
                    }
                }
            }
            
            // 重新attach
            tabLayoutMediator?.attach()
            
            // 确保初始状态正确
            if (beans.isNotEmpty()) {
                mBinding.tabs.getTabAt(0)?.let { firstTab ->
                    updateTabAppearance(firstTab, isSelected = true)
                }
            }
        }
    }
    
    override fun initView() {
        adapter = OptimizedFragmentAdapter(this)
        
        // 设置ViewPager2的缓存页面数，减少重建开销
        mBinding.idViewPager.apply {
            adapter = <EMAIL>
            offscreenPageLimit = 2 // 缓存左右各2个页面
            // 减少过度滚动效果，提升性能
            isUserInputEnabled = true
            // 设置页面切换动画时长，减少卡顿感
            reduceDragSensitivity()
        }

        // 优化TabLayout选择监听器
        mBinding.tabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                updateTabAppearance(tab, isSelected = true)
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                updateTabAppearance(tab, isSelected = false)
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                // 重选时不需要特殊处理
            }
        })

        mBinding.idSearch.onClick {
            Navigation.findNavController(mBinding.idSearch)
                .navigate(R.id.action_keywordFragment_to_searchFragment)
        }
    }
    
    /**
     * 减少ViewPager2的拖拽敏感度，提升滑动体验
     */
    private fun androidx.viewpager2.widget.ViewPager2.reduceDragSensitivity() {
        try {
            val recyclerViewField = androidx.viewpager2.widget.ViewPager2::class.java.getDeclaredField("mRecyclerView")
            recyclerViewField.isAccessible = true
            val recyclerView = recyclerViewField.get(this) as androidx.recyclerview.widget.RecyclerView
            
            val touchSlopField = androidx.recyclerview.widget.RecyclerView::class.java.getDeclaredField("mTouchSlop")
            touchSlopField.isAccessible = true
            val touchSlop = touchSlopField.get(recyclerView) as Int
            touchSlopField.set(recyclerView, touchSlop * 3) // 增加触摸阈值
        } catch (e: Exception) {
            // 忽略反射异常
        }
    }
    
    private fun updateTabAppearance(tab: TabLayout.Tab?, isSelected: Boolean) {
        val view = tab?.customView?.findViewById<QMUIRoundButton>(R.id.idLabel) ?: return
        
        if (isSelected) {
            view.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18f)
            view.typeface = Typeface.DEFAULT_BOLD
            context?.getColor(R.color.common_color_141414)?.let { view.setTextColor(it) }
        } else {
            view.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16f)
            view.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
            context?.getColor(R.color.common_color_545454)?.let { view.setTextColor(it) }
        }
    }
    
    override fun initData() {}
    
    override fun onRetry() {}
    
    override fun getStateLayout(): StateLayout? {
        return null
    }

    override fun onDestroyView() {
        // 防止内存泄漏
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        super.onDestroyView()
    }

    fun updateFragmentPosition(fromBean: OrangeDicProfileTagResponse.Bean) {
        mViewModel.listData.postValue(fromBean)
    }
}