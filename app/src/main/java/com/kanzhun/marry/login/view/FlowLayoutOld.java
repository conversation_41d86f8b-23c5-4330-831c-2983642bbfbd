package com.kanzhun.marry.login.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

import com.kanzhun.marry.R;

import java.util.ArrayList;
import java.util.List;

public class FlowLayoutOld extends ViewGroup {
    private static final String TAG = FlowLayoutOld.class.getSimpleName();
    private int mMaxLine = Integer.MAX_VALUE;
    private BeyondMaxLineCallBack beyondMaxLineCallBack;
    //    private float widthSpace;
//    private float heightSpace;
    private final static float DEFAULT_WIDTH_SPACE = 16;
    private final static float DEFAULT_HEIGHT_SPACE = 8;

    public FlowLayoutOld(Context context) {
        this(context, null);
    }

    public FlowLayoutOld(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FlowLayoutOld(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.FlowLayoutOld);
        mMaxLine = ta.getInt(R.styleable.FlowLayoutOld_maxLine, Integer.MAX_VALUE);
//        heightSpace = ta.getDimension(R.styleable.FlowLayout_height_space, DEFAULT_HEIGHT_SPACE);
//        widthSpace = ta.getDimension(R.styleable.FlowLayout_width_space, DEFAULT_WIDTH_SPACE);
        ta.recycle();
        init();
    }

    private void init() {

    }

    @Override
    public void addView(View child) {
        addView(child, new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int widthMode = View.MeasureSpec.getMode(widthMeasureSpec);
        int widthSize = View.MeasureSpec.getSize(widthMeasureSpec);
        int heightMode = View.MeasureSpec.getMode(heightMeasureSpec);
        int heightSize = View.MeasureSpec.getSize(heightMeasureSpec);
        int currentLines = 0;
        int width = 0;
        int height = 0;
        //每一行的宽度和高度
        int lineWidth = 0;
        int lineHeight = 0;
        for (int i = 0; i < getChildCount(); i++) {
            View childView = getChildAt(i);
            measureChild(childView, widthMeasureSpec, heightMeasureSpec);

            ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) childView.getLayoutParams();

            int childWidth = childView.getMeasuredWidth() + lp.leftMargin + lp.rightMargin;
            int childHeight = childView.getMeasuredHeight() + lp.topMargin + lp.bottomMargin;

            //换行
            if (lineWidth + childWidth > widthSize - getPaddingLeft() - getPaddingRight()) {
                //对比得到最大的宽度
                width = Math.max(width, lineWidth);
                //重置lineWidth
                lineWidth = childWidth;
                //记录行高
                lineHeight = childHeight;
                currentLines++;
                if (currentLines < mMaxLine) {
                    // 如果超过了最大行，则不再换行
                    height += lineHeight;
//                    height += lineHeight + heightSpace;
                } else {
                    if (beyondMaxLineCallBack != null) {
                        beyondMaxLineCallBack.beyondMaxLine();
                    }
                }
            } else {
                //未换行
                lineWidth += childWidth;
//              lineWidth += childWidth + widthSpace;
                lineHeight = Math.max(lineHeight, childHeight);
            }
            //最后一个控件
            if (i == getChildCount() - 1) {
                width = Math.max(lineWidth, width);
                height += lineHeight;
            }

        }

        setMeasuredDimension(
                widthMode == View.MeasureSpec.EXACTLY ? widthSize
                        : width - getPaddingLeft() - getPaddingRight(),
                heightMode == View.MeasureSpec.EXACTLY ? heightSize
                        : height + getPaddingTop() + getPaddingBottom());
    }

    private List<List<View>> mAllViews = new ArrayList<>();
    private List<Integer> mLineHeight = new ArrayList<>();

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        mAllViews.clear();
        mLineHeight.clear();
        int width = getWidth();
        int height = getHeight();
        int lineWidth = 0;
        int lineHeight = 0;
        List<View> lineViews = new ArrayList<>();
        for (int i = 0; i < getChildCount(); i++) {
            View childView = getChildAt(i);
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) childView.getLayoutParams();
            int childWidth = childView.getMeasuredWidth() + params.leftMargin + params.rightMargin;
            int childHeight =
                    childView.getMeasuredHeight() + params.topMargin + params.bottomMargin;
            if (childWidth + lineWidth > width - getPaddingLeft() - getPaddingRight()) {
                mLineHeight.add(lineHeight);
                mAllViews.add(lineViews);
                // 重置行宽和行高
                lineWidth = 0;
                lineHeight = childHeight;
                lineViews = new ArrayList<>();
            }
            lineWidth += childWidth;
//            lineWidth += childWidth + params.leftMargin + params.rightMargin + widthSpace;
            lineHeight = Math.max(lineHeight, childHeight);
            lineViews.add(childView);
        }
        // 处理最后一行
        mLineHeight.add(lineHeight);
        mAllViews.add(lineViews);
        int top = getPaddingTop();
        int left = getPaddingLeft();

        for (int i = 0; i < mAllViews.size(); i++) {
            lineViews = mAllViews.get(i);
            lineHeight = mLineHeight.get(i);
            for (int j = 0; j < lineViews.size(); j++) {
                final View child = lineViews.get(j);
                if (child.getVisibility() == View.GONE) {
                    continue;
                }
                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) child.getLayoutParams();
                int cl = left + params.leftMargin;
                int ct = top + params.topMargin;
                int cr = cl + child.getMeasuredWidth();
                int cb = ct + child.getMeasuredHeight();
                child.layout(cl, ct, cr, cb);
                left += child.getMeasuredWidth() + params.leftMargin + params.rightMargin;
//                left += child.getMeasuredWidth() + params.leftMargin + params.rightMargin + widthSpace;
//                child.setOnClickListener(this);
            }
            left = getPaddingLeft();
            top += lineHeight;
//            top += lineHeight + heightSpace;
        }
    }

    @Override
    public ViewGroup.LayoutParams generateLayoutParams(AttributeSet attrs) {
        return new ViewGroup.MarginLayoutParams(getContext(), attrs);
    }

//    private OnItemClickListener mOnItemClickListener;
//
//    public void setOnItemClickListener(OnItemClickListener listener) {
//        this.mOnItemClickListener = listener;
//    }

//    @Override
//    public void onClick(View v) {
//        for (int i = 0; i < getChildCount(); i++) {
//            if (getChildAt(i) == v) {
//                if (mOnItemClickListener != null) {
//                    mOnItemClickListener.getIndexClicked(i);
//                    return;
//                }
//            }
//        }
//    }

    public void setMaxLine(int maxLine) {
        if (mMaxLine != maxLine) {
            mMaxLine = maxLine;
            requestLayout();
        }
    }

    public void setBeyondMaxLineCallBack(BeyondMaxLineCallBack callBack) {
        this.beyondMaxLineCallBack = callBack;
    }

//    public interface OnItemClickListener {
//        void getIndexClicked(int index);
//    }

    public interface BeyondMaxLineCallBack {
        void beyondMaxLine();
    }
}
