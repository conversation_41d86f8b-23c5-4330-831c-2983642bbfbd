package com.kanzhun.marry.login.fragment

import android.app.Activity
import android.graphics.Typeface
import android.os.Bundle
import android.text.Editable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.kanzhun.common.kotlin.ui.onClick
import com.qmuiteam.qmui.util.QMUIKeyboardHelper
import com.kanzhun.common.kotlin.constract.LivedataKeyLogin
import com.kanzhun.common.kotlin.ext.color
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.FragmentLoginActivateSearchBinding
import com.kanzhun.marry.login.api.model.OrangeDicProfileTagResponse

/**
 * 首善—关键词
 */
class LoginActivateSearchFragment :
    LoginActivateBaseFragment<FragmentLoginActivateSearchBinding>() {
    val triple = mutableListOf<Triple<Int, OrangeDicProfileTagResponse.Bean, String>>()
    val set = mutableSetOf<Int>()
    lateinit var mSearchItemAdapter: SearchItemAdapter

    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        mBinding.icTitle.asBackButton()
        mBinding.editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                triple.clear()
                set.clear()
                val text = s.toString()
                if (!s.isNullOrEmpty()) {
                    searchWord(text)
                    if (triple.isEmpty()) {
                        mBinding.tvErrorDesc.visible()
                    } else {
                        mBinding.tvErrorDesc.gone()
                    }
                }
                mSearchItemAdapter.notifyDataSetChanged()
            }

        })
        mSearchItemAdapter = SearchItemAdapter(triple)
        mBinding.idRecyclerView.adapter = mSearchItemAdapter
        mBinding.idImgClose.onClick {
            triple.clear()
            mBinding.editText.setText("")
            mSearchItemAdapter.notifyDataSetChanged()
        }
    }

    private fun searchWord(text: String) {
        var i = 0
        activityViewModel.beanList?.forEach { it ->
            it.subTag.forEach { j ->
                run Loop@{
                    text.forEach { k: Char ->
                        if (j.content.contains(k) && !set.contains(j.id)) {
                            set.add(j.id)
                            triple.add(Triple(i, j, text))
                            return@Loop
                        }
                    }
                }
            }
        }
    }

    override fun initData() {
    }

    override fun onRetry() {
    }

    override fun getStateLayout(): StateLayout? = null

    class SearchItemAdapter(_list: List<Triple<Int, OrangeDicProfileTagResponse.Bean, String>>) :
        RecyclerView.Adapter<SearchItemAdapter.SearchItemHolder>() {
        var list: List<Triple<Int, OrangeDicProfileTagResponse.Bean, String>>

        init {
            list = _list
        }

        class SearchItemHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            var textView: TextView

            init {
                textView = itemView.findViewById(R.id.idTextView)
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchItemHolder {
            val item = LayoutInflater.from(parent.context)
                .inflate(R.layout.layout_item_keyword_search, parent, false)
            return SearchItemHolder(item)
        }

        override fun getItemCount(): Int {
            return list.size
        }

        override fun onBindViewHolder(holder: SearchItemHolder, position: Int) {
            val sb = SpannableStringBuilder(list[position].second.content)
            var i = 0
            list[position].second.content.forEach { key: Char ->
                list[position].third.forEach { jKey: Char ->
                    if (key == jKey) {
                        sb.setSpan(
                            ForegroundColorSpan(holder.itemView.context.color(R.color.common_color_005EFF)),
                            i,
                            i + 1,
                            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                        )
                        sb.setSpan(
                            StyleSpan(Typeface.BOLD), i, i + 1,
                            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                        )
                    }
                }
                i++
            }
            holder.textView.text = sb
            holder.itemView.onClick {
                QMUIKeyboardHelper.hideKeyboard(holder.itemView)
                list[position].second.isLocalSelect = true
                LiveEventBus.post(LivedataKeyLogin.LOGIN_KEY_WOLD_ADD_END, list[position].second)
                if (holder.itemView.context is Activity) {
                    (holder.itemView.context as Activity).onBackPressed()
                }
            }
        }
    }


}