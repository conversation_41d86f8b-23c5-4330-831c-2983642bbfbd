package com.kanzhun.marry.login.viewmodel;

import android.app.Application;
import android.text.TextUtils;
import android.util.ArrayMap;
import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;
import com.hpbr.apm.event.ApmAnalyzer;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.LText;
import com.kanzhun.common.util.SecurityUtils;
import com.kanzhun.common.util.liveeventbus.LiveEventBus;
import com.kanzhun.foundation.BuildConfig;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.bean.SMSLoginModel;
import com.kanzhun.foundation.kernel.account.Account;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.GeeCaptchaRegisterResponse;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.utils.DeviceIdUtils;
import com.kanzhun.foundation.utils.MyClipboardManager;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.R;
import com.kanzhun.marry.helper.LoginHelper;
import com.kanzhun.marry.httpdemo.retrofit.RxLifecycleViewModel;
import com.kanzhun.marry.httpdemo.retrofit.ViewModelEvent;
import com.kanzhun.marry.kotlin.router.LoginResultRouter;
import com.kanzhun.marry.login.api.LoginApi;
import com.kanzhun.marry.login.point.LoginPointAction;
import com.kanzhun.marry.push.PushSdkManager;
import com.kanzhun.utils.L;
import com.kanzhun.utils.RxHelper;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.T;
import com.trello.rxlifecycle4.LifecycleProvider;
import com.trello.rxlifecycle4.LifecycleTransformer;
import com.trello.rxlifecycle4.RxLifecycle;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.subjects.BehaviorSubject;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import org.apache.commons.lang3.text.StrBuilder;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import static com.kanzhun.marry.login.activity.LoginActivity.ACTION_GEETEST;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/10
 */
public class LoginViewModel extends FoundationViewModel implements LifecycleProvider<ViewModelEvent> {

    public final int CODE_TIME = 60;
    public ObservableInt timeCount = new ObservableInt(CODE_TIME);
    public int type;
    private Disposable mTimeDisposable;
    public ObservableField<String> phoneNumberObservable = new ObservableField<>();
    private String mLastNumber;
    public ObservableField<String> phoneErrorDescObservable = new ObservableField<>();
    public ObservableField<Boolean> phoneErrorObservable = new ObservableField<>();
    public MutableLiveData<Boolean> phoneErrorLiveData = new MutableLiveData<>();
    private String challenge;
    private String validate;
    private String seccode;
    private String wechatCode;//微信的code 如果此值不为空，需要走注册流程,用于标识微信登录后注册的用户
    private MutableLiveData<String> gt3GeetestStartLivaData = new MutableLiveData<>();
    private MutableLiveData<Boolean> inputSuccessLivaData = new MutableLiveData<>();
    public MutableLiveData<Integer> geetestonClosedLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> inputSmsCodeSuccessLivaData = new MutableLiveData<>();
    private MutableLiveData<Boolean> selectModelCodeSuccessLivaData = new MutableLiveData<>();
    private MutableLiveData<Integer> loginSuccessLivaData = new MutableLiveData<>();
    public ObservableField<String> inviteErrorDescObservable = new ObservableField<>();
    public ObservableField<Boolean> inviteErrorObservable = new ObservableField<>();

    public MutableLiveData<String> wechatCodeData = new MutableLiveData<>();//解决fragment比activity的数据先初始化的问题
    private MutableLiveData<Boolean> wechatHasBind = new MutableLiveData<>();


    public LoginViewModel(Application application) {
        super(application);
        lifecycleSubject.onNext(ViewModelEvent.VIEW_MODEL_ACTIVE);
    }

    public String getEncryptPhoneNumber() {
        String number = phoneNumberObservable.get();
        if (TextUtils.isEmpty(number)) {
            return "";
        }
        StrBuilder strBuilder = new StrBuilder();
        for (int i = 0; i < number.length(); i++) {
            if (i > 2 && i < 7) {
                strBuilder.append("*");
            } else {
                strBuilder.append(number.charAt(i));
            }
        }
        return strBuilder.toString();
    }

    public void setWechatCode(String code) {
        L.e("LoginViewModel :", " setWechatCode:" + code);
        this.wechatCode = code;
        wechatCodeData.postValue(code);
    }

    public String getWechatCode() {
        return wechatCode;
    }

    public void toCodeFragment() {
        String phoneNumberString = phoneNumberObservable.get();
        if (!TextUtils.isEmpty(phoneNumberString)) {
            phoneNumberString = phoneNumberString.replace(" ", "");
        }
        if (!LText.isMobile("86", phoneNumberString)) {
            phoneErrorDescObservable.set(getResources().getString(R.string.login_input_right_phone_number));
            phoneErrorObservable.set(true);
            phoneErrorLiveData.setValue(true);
            PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_PHONENUMBER_EXPO, new Function1<PointBean, Unit>() {
                @Override
                public Unit invoke(PointBean pointBean) {
                    pointBean.setMsg(getResources().getString(R.string.login_input_right_phone_number));
                    pointBean.setActionp2(getSource());
                    return null;
                }
            });
        } else {
            checkAccountStatus();
        }
    }

    /**
     * 查询账号异常状态
     */
    private void checkAccountStatus() {
        String phoneNumber = phoneNumberObservable.get();
        if (!TextUtils.isEmpty(phoneNumber)) {
            phoneNumber = phoneNumber.replace(" ", "");
        }
        if (timeCount.get() < CODE_TIME && timeCount.get() > 0 && TextUtils.equals(phoneNumber, mLastNumber)) {
            inputSuccessLivaData.postValue(true);
            return;
        }

        if (BuildConfig.DEBUG) {
            LiveEventBus.post("debug_phone", phoneNumber);
        }

        Map<String, Object> map = new HashMap();
        map.put("regionCode", "86");
        map.put("phone", SecurityUtils.rc4Encrypt(phoneNumber, SettingBuilder.getInstance().getApmUidPassword()));
        setShowProgressBar();
        HttpExecutor.requestSimpleGet(URLConfig.URL_REGISTER_ACCOUNT_STATUS, map, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                sendCode();
            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                if (reason != null) {
                    if (reason.getErrCode() == 1010) {
                        //1010 被封禁
                        phoneErrorDescObservable.set(reason.getErrReason());
                        phoneErrorObservable.set(true);
                        phoneErrorLiveData.setValue(true);

                    } else {
                        T.ss(reason.getErrReason());
                    }
                    PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_PHONENUMBER_EXPO, new Function1<PointBean, Unit>() {
                        @Override
                        public Unit invoke(PointBean pointBean) {
                            pointBean.setMsg(reason.getErrReason());
                            pointBean.setActionp2(getSource());
                            return null;
                        }
                    });

                }
            }
        });
    }


    /**
     * 获取验证码：先极验初始化校验
     */
    public void sendCode() {
        String phoneNumber = phoneNumberObservable.get();
        if (!TextUtils.isEmpty(phoneNumber)) {
            phoneNumber = phoneNumber.replace(" ", "");
        }

        if (timeCount.get() < CODE_TIME && timeCount.get() > 0 && TextUtils.equals(phoneNumber, mLastNumber)) {
            inputSuccessLivaData.postValue(true);
            return;
        }
        if (!TextUtils.equals(phoneNumber, mLastNumber)) {
            challenge = null;
        }
        ArrayMap<String, Object> map = new ArrayMap<>();
        map.put("regionCode", 86);
        map.put("phone", SecurityUtils.rc4Encrypt(phoneNumber, SettingBuilder.getInstance().getApmUidPassword()));
        if (!TextUtils.isEmpty(challenge)) {
            map.put("authSkip", 1);
        } else {
            map.put("authSkip", 0);
        }
        setShowProgressBar();
        Observable<BaseResponse<GeeCaptchaRegisterResponse>> baseResponseObservable = RetrofitManager.getInstance().createApi(LoginApi.class).geeCaptchaRegister(map);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<GeeCaptchaRegisterResponse>() {
            @Override
            public void onSuccess(GeeCaptchaRegisterResponse data) {
                if (data.skip == 1) {
                    //跳过
                    realSendCode(true, challenge, validate, seccode);
                } else {
                    hideShowProgressBar();
                    //去认证
                    startCustomVerify(data.jsonInfo);
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason != null) {
                    T.ss(reason.getErrReason());
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });

    }


    /**
     * 开启 GeeTest 验证
     *
     * @param startCaptcha 急验需要的一个参数，通常是服务器端生成
     */
    public void startCustomVerify(String startCaptcha) {
        gt3GeetestStartLivaData.setValue(startCaptcha);
    }

    public MutableLiveData<String> getGt3GeetestStartLivaData() {
        return gt3GeetestStartLivaData;
    }

    public void gt3GeetestSuccess(String result) {
        try {
            JSONObject object = new JSONObject(result);
            challenge = object.optString("geetest_challenge");
            validate = object.optString("geetest_validate");
            seccode = object.optString("geetest_seccode");
            realSendCode(false, challenge, validate, seccode);
        } catch (Throwable e) {
            e.printStackTrace();

            ApmAnalyzer.create().action(ACTION_GEETEST, "type_gt3GeetestSuccess")
                    .p2(result)
                    .p3(e.toString())
                    .report();
        }

    }

    /**
     * 真正获取验证码
     *
     * @param isSkip true 跳过极验直接获取验证码
     */
    public void realSendCode(boolean isSkip, String challengeTemp, String validate, String seccode) {
        mLastNumber = phoneNumberObservable.get();
        Map<String, Object> params = new HashMap();
        params.put("regionCode", "86");
        params.put("type", "101");
        params.put("phone", SecurityUtils.rc4Encrypt(phoneNumberObservable.get(), SettingBuilder.getInstance().getApmUidPassword()));
        if (!isSkip) {
            params.put("challenge", challengeTemp);
            params.put("validate", validate);
            params.put("seccode", seccode);
        }
        setShowProgressBar();
        HttpExecutor.requestSimplePost(URLConfig.URL_GEE_CAPTCHA_VALIDATE, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                inputSuccessLivaData.postValue(true);
                T.ss(getResources().getString(R.string.login_send_sms_code_success));
                timeCount.set(CODE_TIME);
                countDown();
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason != null && reason.getErrCode() > 0) {
                    if (reason.getErrCode() == 1208) {
                        // 1208 为验证次数过多请重试
                        challenge = null;
                    }
                    T.ss(reason.getErrReason());
                }
//                if (isSkip) {
//                    if (reason != null && reason.getErrCode() == 1203) {//验证码已在一分钟内发送过
//                        inputSuccessLivaData.postValue(true);
//                        timeCount.set(CODE_TIME);
//                        countDown();
//                    }
//                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    /**
     * 倒计时
     */
    private void countDown() {
        stopCountDown();
        Observable<Long> countdown = RxHelper.countdown(CODE_TIME);
        mTimeDisposable = countdown.subscribe(new Consumer<Long>() {
            @Override
            public void accept(Long time) {
                timeCount.set(time.intValue());
            }
        }, new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Throwable {
                timeCount.set(0);
            }
        });
    }

    private void stopCountDown() {
        if (mTimeDisposable != null) {
            mTimeDisposable.dispose();
        }
    }

    public MutableLiveData<Boolean> getInputSuccessLivaData() {
        return inputSuccessLivaData;
    }

    public MutableLiveData<Boolean> getWechatHasBind() {
        return wechatHasBind;
    }

    private CompositeDisposable compositeDisposable;

    public void addDisposable(Disposable disposable) {
        if (compositeDisposable == null) {
            compositeDisposable = new CompositeDisposable();
        }
        compositeDisposable.add(disposable);
    }

    public void dispose() {
        if (compositeDisposable != null) compositeDisposable.dispose();
    }

    public void login(String smsCode) {
        ArrayMap<String, Object> map = new ArrayMap<>();
        String phone = phoneNumberObservable.get();
        map.put("phone", SecurityUtils.rc4Encrypt(phone, SettingBuilder.getInstance().getApmUidPassword()));
        map.put("regionCode", "86");
        map.put("smsCode", smsCode);
        map.put("challenge", !TextUtils.isEmpty(challenge) ? challenge : "");
        map.put("validate", !TextUtils.isEmpty(validate) ? validate : "");
        map.put("seccode", !TextUtils.isEmpty(seccode) ? seccode : "");
        map.put("platform", 2); //1-ios, 2-android
        if (!TextUtils.isEmpty(DeviceIdUtils.getDid())) {
            map.put("duid", DeviceIdUtils.getDid());
        }
        if (!TextUtils.isEmpty(wechatCode)) {
            map.put("wechatCode", wechatCode);
        }
        map.put("accountType", "1");
        if (MyClipboardManager.Companion.getClipBean() != null && !TextUtils.isEmpty(MyClipboardManager.Companion.getClipBean().getSourceInfo())) {
            map.put("sourceInfo", MyClipboardManager.Companion.getClipBean().getSourceInfo());
        }
        if (MyClipboardManager.Companion.getClipBean() != null && MyClipboardManager.Companion.getClipBean().getSourceType() != 0) {
            map.put("sourceType", MyClipboardManager.Companion.getClipBean().getSourceType() + "");
        }

        setShowProgressBar();
        Observable<BaseResponse<SMSLoginModel>> baseResponseObservable = RetrofitManager.getInstance().createApi(LoginApi.class).smsLogin(map);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<SMSLoginModel>() {

            @Override
            public void handleInChildThread(SMSLoginModel response) {
//                loginResultHandler(response);
                LoginResultRouter.routerAfterLoginSuccess(response);

            }

            @Override
            public void onSuccess(SMSLoginModel response) {
                if (BuildConfig.DEBUG) {
                    // 保存登录信息到已登录过用户列表
                    if (phone != null && response != null) {
                        new LoginHelper().saveLoginUser(phone, response);
                    }
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason != null) {
                    int errCode = reason.getErrCode();
                    if (errCode == 1201 || errCode == 1202 || errCode == 1206 || errCode == 1208 || errCode == 3800) {
                        inputSmsCodeSuccessLivaData.postValue(false);
                    }
                    if (errCode == 3800) {
                        wechatHasBind.postValue(true);
                    } else {
                        T.ss(reason.getErrReason());
                    }
                    PointHelperKt.reportPoint(LoginPointAction.REGISTER_LOGIN_VERIFICTIONCODE_EXPO, new Function1<PointBean, Unit>() {
                        @Override
                        public Unit invoke(PointBean pointBean) {
                            pointBean.setMsg(reason.getErrReason());
                            return null;
                        }
                    });
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });

    }

    public void loginResultHandler(SMSLoginModel response) {
        if (response.accountType == 0) {
            //去选择模式
//            selectModelCodeSuccessLivaData.postValue(true);
            //暂时下掉选择模式 直接去激活
            ArrayMap<String, Object> params = new ArrayMap<>();
            params.put("accountType", 1);
            Observable<BaseResponse<SMSLoginModel>> baseResponseObservable = RetrofitManager.getInstance().createApi(LoginApi.class).changeAccountModel(params);
            HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<SMSLoginModel>() {
                @Override
                public void handleInChildThread(SMSLoginModel data) {
                    super.handleInChildThread(data);
                    ServiceManager.changeModeUpdateAccount(data);
                    loginResultHandler(data);
                }

                @Override
                public void onSuccess(SMSLoginModel data) {

                }

                @Override
                public void dealFail(ErrorReason reason) {

                }


            });
        } else if (response.accountType == 1 && response.phase == Account.ACCOUNT_STATUS_REGISTER) {
            //去激活
            inputSmsCodeSuccessLivaData.postValue(true);
        } else {
            //去首善或者主界面
            loginSuccessLivaData.postValue(Account.ACCOUNT_STATUS_REGISTER_INVITE);
        }
        PushSdkManager.getInstance().start();

    }


    public MutableLiveData<Boolean> getInputSmsCodeSuccessLivaData() {
        return inputSmsCodeSuccessLivaData;
    }

    public MutableLiveData<Integer> getLoginSuccessLivaData() {
        return loginSuccessLivaData;
    }

    private final BehaviorSubject<ViewModelEvent> lifecycleSubject = BehaviorSubject.create();

    @NonNull
    @Override
    public Observable<ViewModelEvent> lifecycle() {
        return lifecycleSubject.hide();
    }

    @NonNull
    @Override
    public <T> LifecycleTransformer<T> bindUntilEvent(@NonNull ViewModelEvent event) {
        return RxLifecycle.bindUntilEvent(lifecycleSubject, event);
    }

    @NonNull
    @Override
    public <T> LifecycleTransformer<T> bindToLifecycle() {
        return RxLifecycleViewModel.bindViewModel(lifecycleSubject);
    }

    public MutableLiveData<Boolean> getSelectModelCodeSuccessLivaData() {
        return selectModelCodeSuccessLivaData;
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        lifecycleSubject.onNext(ViewModelEvent.VIEW_MODEL_CANCEL);
    }

    public void loginInputInviteCode(String inviteCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("code", inviteCode);
        //输入邀请码之前临时给account userId
//        Account account = new Account(AccountHelper.getInstance().getUserId(), AccountHelper.getInstance().getAccount().getSk());
//        AccountHelper.getInstance().setAccount(account);
        setShowProgressBar("");
        HttpExecutor.requestSimplePost(URLConfig.URL_CHECK_INVITE, params, new SimpleRequestCallback() {

            @Override
            public void onSuccess() {
                ExecutorFactory.execLocalTask(new Runnable() {
                    @Override
                    public void run() {
                        //输入邀请码成功后 清楚account userId
                        AccountHelper.getInstance()
                                .refreshActivateConfig(Account.ACCOUNT_STATUS_REGISTER_INVITE);
                        Account account = AccountHelper.getInstance().getAccount();
                        User user = ServiceManager.getInstance().getDatabaseService().getUserDao().getUser(AccountHelper.getInstance().getUserId());
                        user.setPhase(account.getPhase());
                        ServiceManager.getInstance().getDatabaseService().getUserDao().insert(user);
                        loginSuccessLivaData.postValue(Account.ACCOUNT_STATUS_REGISTER_INVITE_ACTIVATE);
                    }
                });
            }

            @Override
            public void dealFail(ErrorReason reason) {
                //输入邀请码失败后 清楚account userId
                if (reason != null) {
                    if (reason.getErrCode() == Constants.CHECK_INVITE_ERROR_ERROR_CODE || reason.getErrCode() == Constants.CHECK_INVITE_USED_ERROR_CODE || reason.getErrCode() == Constants.CHECK_INVITE_USED_OVER_TIME) {
                        inviteErrorObservable.set(true);
                        inviteErrorDescObservable.set(reason.getErrReason());
                    } else {
                        T.ssd(reason.getErrReason());
                    }
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public String getSource() {
        L.e("LoginViewModel :", " getSource wechatCode:" + wechatCode);
        L.e("LoginViewModel :", " getSource wechatCode2:" + wechatCodeData.getValue());
        if (!TextUtils.isEmpty(wechatCode)) {
            return "绑定关联手机号";
        } else {
            return "手机号注册";
        }
    }
}
