package com.kanzhun.marry.login.activity

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Intent
import android.os.Build
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.util.ArrayMap
import android.view.KeyEvent
import android.view.ViewGroup
import com.kanzhun.common.base.BaseApplication
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.entity.LoadingDialogBean
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.statusbar.fullScreenAndBlackText
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.widget.PopupUtils
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.bean.SMSLoginModel
import com.kanzhun.foundation.kotlin.common.performance.PerformManager
import com.kanzhun.foundation.kotlin.common.performance.impl.WechatLoginPerformance
import com.kanzhun.foundation.permission.PermissionData
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.router.LoginPageRouter
import com.kanzhun.foundation.utils.BadgeUtils
import com.kanzhun.foundation.utils.MyClipboardManager
import com.kanzhun.foundation.utils.ProtocolStringProvider.Companion.getLoginProtocolString
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.databinding.ActivityLoginEntryBinding
import com.kanzhun.marry.databinding.PopShowTipsForCheckLinkBinding
import com.kanzhun.marry.kotlin.router.LoginResultRouter
import com.kanzhun.marry.login.api.LoginApi
import com.kanzhun.marry.login.bean.PublicConfigBean
import com.kanzhun.marry.login.point.LoginPointReport
import com.kanzhun.utils.L
import com.kanzhun.utils.T
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import com.sankuai.waimai.router.annotation.RouterUri


/**
 * 登录入口页面
 */
@RouterUri(path = [LoginPageRouter.APP_LOGIN_ENTRY_ACTIVITY])
class LoginEntryActivity : BaseBindingActivity<ActivityLoginEntryBinding, LoginEntryViewModel>() {

    val animator : ValueAnimator = ValueAnimator.ofFloat(0f,1f)
    override var setStatusBar = {
        fullScreenAndBlackText()
    }
    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    private val wxLoginPerformance: WechatLoginPerformance by lazy {
        WechatLoginPerformance(this) { wxCode ->
            if(wxCode.isNotBlank()){
                mViewModel.showLoadingDialog(true)
                mViewModel.startWechatLogin(wxCode)
            }
        }
    }

    var lastBackTimer = 0L
    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 执行退出操作
            val timer = System.currentTimeMillis()
            if (timer - lastBackTimer > 2000) {
                T.ss("再按一次退出程序")
                lastBackTimer = timer
                return true
            }
            //推出程序 停止播放
            finish()
            AppUtil.exitApp()
            return true
        }
        return super.onKeyUp(keyCode, event)
    }


    override fun preInit(intent: Intent) {
        L.e("LoginEntryActivity:", "LoginEntryActivity readClipboard")
        MyClipboardManager.checkClipBeanAndRead(this)
        val observable =
            RetrofitManager.getInstance().createApi(LoginApi::class.java).queryPublicConfig()
        HttpExecutor.execute(observable, object : BaseRequestCallback<PublicConfigBean?>() {
            override fun onSuccess(data: PublicConfigBean?) {
                if(!<EMAIL>){
                    mBinding.idOfficePhone.text = data?.customerPhone.toString()
                }
            }

            override fun dealFail(reason: ErrorReason?) {
            }
        })

    }

    fun setClickAble(b:Boolean){
        mBinding.tvLink.enable(b)
        mBinding.checkLink.enable(b)
        mBinding.tvWeChatLogin.enable(b)
        mBinding.tvPhoneLogin.enable(b)
        if(b){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                PermissionHelper.getPostNotificationHelperOnce(this)
                    .setPermissionCallback { yes: Boolean, permission: PermissionData? -> }
                    .requestPermission()
            }
        }
    }

    override fun initView() {
        setClickAble(false)
        mBinding.idLottieAnimationView.addAnimatorListener(object : Animator.AnimatorListener{
            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                animator.addUpdateListener {
                    val f = it.animatedValue as Float
                    mBinding.layout1.alpha = f
                    mBinding.layout2.alpha = 1-f
                    if(f == 1f){
                        setClickAble(true)
                    }
                }

                animator.setDuration(1000)
                animator.start()

            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationRepeat(animation: Animator) {
            }

        })
        mBinding.tvPhoneLogin.clickWithTrigger {
            onPhoneLoginClick()
            LoginPointReport.reportLoginPageClick("手机号登录")
        }
        mBinding.tvWeChatLogin.clickWithTrigger {
            LoginPointReport.reportLoginPageClick("微信登录")
            onWechatLoginClick()
        }
        mBinding.checkLink.setOnCheckedChangeListener { _, isChecked ->
            LoginPointReport.reportLoginPageClick(if(isChecked)"勾选协议" else "取消勾选协议")

        }
        mBinding.tvLink.movementMethod = LinkMovementMethod.getInstance()
        mBinding.tvLink.text = getLoginProtocolString(this){
            LoginPointReport.reportLoginPageClick("查看协议")
        }
        performManager.addPerformance(wxLoginPerformance)
        //知道要到了登录首页，都消除对应数字
        BadgeUtils.setBadge(BaseApplication.getApplication(), 0)
    }

    override fun onStart() {
        super.onStart()
        LoginPointReport.reportLoginPageExpose()
    }
    override fun initData() {
    }

    override fun onRetry() {
    }

    override fun onStop() {
        super.onStop()
        mViewModel.showLoadingDialog(false)
    }

    override fun onResume() {
        super.onResume()
//        T.ss("补丁应用成功！")
    }

    override fun getStateLayout() = null


    private fun onWechatLoginClick() {
        if (hasCheckProtocol()) {
            wechatLogin()
        } else {
            showProtocolTipsDialog()
        }
    }

    private fun wechatLogin() {
        if(wxLoginPerformance.isWechatInstalled()){
//            mViewModel.showLoadingDialog(true, cancelable = true)
            wxLoginPerformance.startWechatLogin()
        }
    }

    private fun onPhoneLoginClick() {
        if (hasCheckProtocol()) {
            LoginPageRouter.jumpToPhoneLoginActivity(this@LoginEntryActivity, false)
        } else {
            showProtocolTipsDialog()
        }
    }

    private fun hasCheckProtocol() = mBinding.checkLink.isChecked

    private fun showProtocolTipsDialog() {
        val binding = PopShowTipsForCheckLinkBinding.inflate(layoutInflater, null, false)
        val popupUtils = PopupUtils.Builder(this).setContentView(binding.root)
            .setWidth(ViewGroup.LayoutParams.WRAP_CONTENT).setHeight(ViewGroup.LayoutParams.WRAP_CONTENT).build()
        popupUtils.showUpWithoutXOffset(mBinding.checkLink, popupUtils.popupWindow.contentView, QMUIDisplayHelper.dp2px(this, 10), 0)
    }

}


class LoginEntryViewModel : BaseViewModel() {

    fun startWechatLogin(wxCode: String) {
        val map = ArrayMap<String, Any>()
        map["platform"] = 2 //1-ios, 2-android
        map["code"] = wxCode
        map["accountType"] = "1"
        if (!TextUtils.isEmpty(MyClipboardManager.clipBean?.sourceInfo)) {
            map["sourceInfo"] = MyClipboardManager.clipBean?.sourceInfo
        }
        if (!TextUtils.isEmpty(MyClipboardManager.clipBean?.sourceType.toString())) {
            map["sourceType"] = MyClipboardManager.clipBean?.sourceType.toString()
        }


        val baseResponseObservable = RetrofitManager.getInstance().createApi(LoginApi::class.java).wechatLogin(map)
        HttpExecutor.execute<SMSLoginModel>(baseResponseObservable, object : BaseRequestCallback<SMSLoginModel?>() {
            override fun handleInChildThread(response: SMSLoginModel?) {
            }

            override fun onSuccess(response: SMSLoginModel?) {
                LoginResultRouter.routerAfterLoginSuccess(response)
            }

            override fun dealFail(reason: ErrorReason) {
                T.ss(reason.errReason)
            }

            override fun onComplete() {
                super.onComplete()
                loadingDialog.postValue(LoadingDialogBean(false))
            }

        })
    }

}

