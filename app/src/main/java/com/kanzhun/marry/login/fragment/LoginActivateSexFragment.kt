package com.kanzhun.marry.login.fragment

import android.os.Bundle
import androidx.navigation.Navigation.findNavController
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.FragmentLoginActivateSexBinding
import com.kanzhun.marry.me.views.LoginNextPageView

class LoginActivateSexFragment : LoginActivateBaseFragment<FragmentLoginActivateSexBinding>() {
    var select = 0
    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        select = 0
        mBinding.btnNext.enable(false)
        val name = let {
            if(activityViewModel.user?.nickName.isNullOrEmpty()){
                resources.getString(R.string.login_activate_sex_title_no_name)
            }else{
                resources.getString(R.string.login_activate_sex_title, activityViewModel.user?.nickName)
            }
        }
        mBinding.tvPhoneTitle.setTitle(name)
        mBinding.flMale.onClick {
            selectMale()
        }
        mBinding.flFemale.onClick {
            selectFemale()
        }
        mBinding.btnNext.setState(LoginNextPageView.STATE.IDLE)
        mBinding.btnNext.setAnimClickListener {
            if (activityViewModel.user?.gender == 0) return@setAnimClickListener
//            if(activityViewModel.user?.gender == select){
//                findNavController(mBinding.btnNext).navigate(R.id.action_sexFragment_to_birthdayFragment)
//            }else{
                activityViewModel.saveGender {
                    findNavController(mBinding.btnNext).navigate(R.id.action_sexFragment_to_birthdayFragment)
                }
//            }
            reportClick(mBinding.tvPhoneTitle.getTitle())

        }
        if (activityViewModel.user?.gender == 1) {
            selectMale()
            select = 1
        } else if (activityViewModel.user?.gender == 2) {
            selectFemale()
            select = 2
        }
    }

    private fun selectFemale() {
        mBinding.btnNext.enable(true)
        activityViewModel.user?.gender = 2
        mBinding.ivFemaleSelect.setImageResource(R.drawable.login_icon_activate_sex_sel)
        mBinding.ivMaleSelect.setImageResource(R.drawable.login_icon_activate_sex_un)
        mBinding.ivMaleSelectRectangle.gone()
        mBinding.ivFemaleSelectRectangle.visible()

    }

    private fun selectMale() {
        activityViewModel.user?.gender = 1
        mBinding.btnNext.enable(true)
        mBinding.ivMaleSelect.setImageResource(R.drawable.login_icon_activate_sex_sel)
        mBinding.ivFemaleSelect.setImageResource(R.drawable.login_icon_activate_sex_un)
        mBinding.ivMaleSelectRectangle.visible()
        mBinding.ivFemaleSelectRectangle.gone()
    }

    override fun initData() {
        reportExp(mBinding.tvPhoneTitle.getTitle())
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null
}