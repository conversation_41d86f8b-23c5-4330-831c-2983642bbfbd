package com.kanzhun.marry.login.fragment

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.kanzhun.common.kotlin.base.EmptyViewModel
import com.kanzhun.common.kotlin.constract.LivedataKeyLogin
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.common.views.image.OImageView
import com.kanzhun.foundation.adapter.buildMultiTypeAdapterByType
import com.kanzhun.foundation.adapter.replaceData
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.marry.databinding.FragmentLoginKeySelectItemBinding
import com.kanzhun.marry.databinding.ItemLabelLoginKeySelectBinding
import com.kanzhun.marry.login.api.model.OrangeDicProfileTagResponse
import com.kanzhun.marry.login.viewmodel.LoginLabelViewPagerSelectViewModel
import com.kanzhun.common.kotlin.ui.onClick
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton


class LoginLabelItemSelectFragment(
    _beans: List<OrangeDicProfileTagResponse.Bean>,
    _beanParentId: Int,
    _viewModel: LoginLabelViewPagerSelectViewModel
) :
    BaseBindingFragment<FragmentLoginKeySelectItemBinding, EmptyViewModel>() {
    private val beans: List<OrangeDicProfileTagResponse.Bean>
    var beanParentId: Int
    var parentViewModel: LoginLabelViewPagerSelectViewModel

    init {
        beanParentId = _beanParentId
        beans = _beans
        parentViewModel = _viewModel
    }


    override fun preInit(arguments: Bundle) {
    }

    fun setText(bind: ItemLabelLoginKeySelectBinding, text: String?) {
        val button: QMUIRoundButton = bind.idLabel
        button.text = text
        val button2: QMUIRoundButton = bind.idLabel2
        button2.text = text
    }

    fun setImage(bind: ItemLabelLoginKeySelectBinding, icon: String?) {
        val imageView: OImageView = bind.idImageView
        val imageView2: OImageView = bind.idImageView2
        if (TextUtils.isEmpty(icon)) {
            imageView.visibility = View.GONE
            imageView2.visibility = View.GONE
        } else {
            imageView.visibility = View.VISIBLE
            imageView2.visibility = View.VISIBLE
            imageView.load(icon)
            imageView2.load(icon)
        }
    }

    override fun initView() {
        var adapter = buildMultiTypeAdapterByType {
            layout(ItemLabelLoginKeySelectBinding::inflate) { _, bean: OrangeDicProfileTagResponse.Bean ->
                binding.apply {
                    setText(binding,if (bean.icon == null) "" else bean.icon + bean.content)
                    if (bean.isLocalSelect) {
                        idUnSelectLabel.setBackgroundColor(context!!.getColor(com.kanzhun.marry.me.R.color.common_color_E9F1FF))
                        idUnSelectLabel.setBorderColor(context!!.getColor(com.kanzhun.marry.me.R.color.common_color_E9F1FF))
                        idLabel.setTextColor(context!!.getColor(com.kanzhun.marry.me.R.color.common_color_0046BD))
                    } else {
                        idUnSelectLabel.setBorderColor(context!!.getColor(com.kanzhun.marry.me.R.color.common_color_FFE0E0E0))
                        idUnSelectLabel.setBackgroundColor(context!!.getColor(com.kanzhun.marry.me.R.color.common_translate))
                        idLabel.setTextColor(context!!.getColor(com.kanzhun.marry.me.R.color.common_color_5E5E5E))
                    }


                    root.onClick {
                        bean.isLocalSelect = !bean.isLocalSelect
                        if (bean.isLocalSelect) {
                            LiveEventBus.post(LivedataKeyLogin.LOGIN_KEY_WOLD_ADD, bean)
                        } else {
                            LiveEventBus.post(LivedataKeyLogin.LOGIN_KEY_WOLD_SUB, bean)
                        }
                    }
                }
            }
        }
        mBinding.idRecyclerview.adapter = adapter
        adapter.replaceData(beans)
        val flexboxLayoutManager = FlexboxLayoutManager(context)
        flexboxLayoutManager.flexDirection = FlexDirection.ROW
        flexboxLayoutManager.flexWrap = FlexWrap.WRAP
        mBinding.idRecyclerview.layoutManager = flexboxLayoutManager

        parentViewModel.listData.observe(this) { fromBean ->
            beans.forEach {
                if (it.id == fromBean.id) {
                    it.isLocalSelect = fromBean.isLocalSelect
                }
            }

            adapter.replaceData(beans)
        }
    }

    override fun initData() {
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null
}