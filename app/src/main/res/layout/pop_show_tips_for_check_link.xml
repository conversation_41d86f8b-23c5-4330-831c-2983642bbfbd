<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        >

        <TextView
            android:textSize="@dimen/common_text_sp_14"
            android:id="@+id/tv_read_link"
            android:padding="14dp"
            android:textColor="@color/common_white"
            android:text="@string/login_read_link"
            android:background="@drawable/bg_corner_12_color_black_80"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            android:layout_width="29dp"
            android:layout_height="15dp"
            android:layout_marginTop="-3px"
            android:src="@mipmap/icon_arrow_down"
            android:layout_marginStart="12dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_read_link"
            app:layout_constraintLeft_toLeftOf="parent"
            />

</androidx.constraintlayout.widget.ConstraintLayout>

