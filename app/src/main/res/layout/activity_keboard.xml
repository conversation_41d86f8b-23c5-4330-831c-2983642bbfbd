<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_bar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/keyboard_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="#333333" />

            <com.kanzhun.keyboard.CInputPanel
                android:id="@+id/chat_input_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <com.kanzhun.keyboard.CExpressionPanel
            android:id="@+id/expression_panel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#333333"
            android:visibility="gone" />

        <com.kanzhun.keyboard.CMorePanel
            android:id="@+id/more_panel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#0072ff"
            android:visibility="gone" />
    </LinearLayout>

    <TextView
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="#00bfcf"
        android:gravity="center"
        android:text="TopBar"
        android:textColor="#000000"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>