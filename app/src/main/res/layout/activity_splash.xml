<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/common_white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        android:layout_width="82dp"
        android:id="@+id/idTop"
        android:layout_marginTop="157dp"
        android:src="@mipmap/icon_splash_top"
        android:layout_height="82dp"/>

    <ImageView
        app:layout_constraintTop_toBottomOf="@+id/idTop"
        android:layout_marginTop="26dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:src="@mipmap/icon_splash_center"
        android:layout_height="wrap_content"/>


    <ImageView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="match_parent"
        android:background="@mipmap/ic_splash_bg"
        android:layout_height="wrap_content"/>

    <ImageView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_width="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="78dp"
        android:src="@mipmap/ic_splash_bottom"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>
