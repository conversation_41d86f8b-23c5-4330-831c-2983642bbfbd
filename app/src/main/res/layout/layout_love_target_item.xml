<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idQMUIConstraintLayout"
        android:layout_width="match_parent"
        app:qmui_radius="27dp"
        android:layout_marginLeft="@dimen/app_layout_page_left_padding"
        android:layout_marginRight="@dimen/app_layout_page_right_padding"
        android:background="@color/common_color_F5F5F5"
        app:qmui_backgroundColor="@color/common_color_F5F5F5"
        android:layout_height="wrap_content">

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/idSelect"
            android:layout_width="wrap_content"
            android:drawablePadding="6dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:drawableRight="@mipmap/me_icon_love_target_item_recommend"
            android:text="没想好什么时候结婚"
            android:textColor="@color/common_color_191919"
            android:textSize="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
           />
    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>



</FrameLayout>