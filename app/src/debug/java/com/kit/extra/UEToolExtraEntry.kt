package com.kit.extra

import com.kit.baselibrary.CheckableItem
import com.kit.floatpage.UEToolShowStateHolder

class UEToolExtraEntry : CheckableItem {
    override fun setChecked(checked: Boolean) {
        UEToolShowStateHolder.toggleUET(checked)
    }

    override fun isChecked(): Boolean {
        return UEToolShowStateHolder.isUETShowing() ?: false
    }

    override fun getName(): String {
        return "UETool"
    }
}