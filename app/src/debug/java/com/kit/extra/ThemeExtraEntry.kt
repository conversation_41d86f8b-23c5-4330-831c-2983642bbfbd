package com.kit.extra

import androidx.fragment.app.FragmentActivity
import com.kanzhun.AppThemeManager
import com.kit.baselibrary.IExtraBean

class ThemeExtraEntry : IExtraBean {
    override fun getName(): String {
        return "主题切换"
    }

    override fun onClick(context: FragmentActivity) {
//        when(AppTheme.getTheme()){
//            THEME.NORMAL -> {
//                AppTheme.setTheme(THEME.CHRISTMAS)
//            }
//            THEME.CHRISTMAS -> {
//                AppTheme.setTheme(THEME.NORMAL)
//            }
//        }
//        App.getApplication().finishAll()
//        MainPageRouter.jumpToMainActivity(context)
//        AppThreadFactory.getMainHandler().postDelayed({MainPageRouter.jumpToMainActivity(context)},1000)

        AppThemeManager().changeToChristmas(context as FragmentActivity)


    }
}