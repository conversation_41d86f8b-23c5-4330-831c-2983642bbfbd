package com.kit.extra

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import com.kanzhun.foundation.bean.LikeEachOtherListItemBean
import com.kanzhun.foundation.dialog.ComposeDialogFragment
import com.kanzhun.foundation.router.KmpPageRouter
import com.kanzhun.foundation.facade.ActionHandlerK
import com.kanzhun.foundation.kotlin.ktx.getPageSourceBundle
import com.kanzhun.foundation.kotlin.ktx.simplePost
import com.kanzhun.foundation.model.WebViewBean
import com.kanzhun.foundation.permission.PermissionData
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.router.ActivityPageRouter
import com.kanzhun.foundation.router.AppPageRouter
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.TaskPageRouter
import com.kanzhun.foundation.utils.CalendarCommon
import com.kanzhun.foundation.views.NewUserTaskFinishDialog
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.imageviewer.tools.ScreenUtils
import com.kanzhun.localNotify.MeetingPlanProtectNotificationManager
import com.kanzhun.marry.chat.bindadapter.ReviewsSelectDetailDialog
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeAuthEduCertErrorType10Binding
import com.kit.baselibrary.IExtraBean


class ZhangzhengExtraEntry : IExtraBean {
    override fun getName(): String {
        return "张政"
    }

    var dialog: ComposeDialogFragment? = null
    override fun onClick(context: FragmentActivity) {

//        ActivityPageRouter.jumpToDemoActivity(context)

//        KmpPageRouter.jumpKmpActivity(context = context, pageName = "AboutPage")

//        ActionHandlerK().handler10070x(3)

//        MePageRouter.jumpToXueXinCodeActivity(context)

//        handler100701()

        //兴趣爱好
//        jumpToPersonEditActivity(context, "textAnswer", 0)

//        handler100703()

//        //跳转微信企业客服
//        val api = WXAPIFactory.createWXAPI(context, ShareKey.WE_CHAT_APP_ID)
//        if (!api.isWXAppInstalled) {
//            T.ssd("未安装微信")
//            return
//        }
//        val req = WXOpenCustomerServiceChat.Req()
//        req.corpId = "ww8843c261b0587a17" // 企业ID
//        req.url = "https://work.weixin.qq.com/ca/cawcded18cb122b8e2?customer_channel=123" // 客服URL
//        api.sendReq(req)

//        val activity = context
//        dialog = ComposeDialogFragment.shouldShow(activity) {
//            ThemeChristmasStartDialog(onClosed = {
//                dialog?.dismiss()
//            }, onStart = {
//                dialog?.dismiss()
//            })
//        }

//        TaskPageRouter.jumpToLoverTagActivity(context)

//        jumpToPersonEditActivity(context, "", 0);

        //小球
//        val bundle = getPageSourceBundle(PageSource.NONE)
//        bundle.putString(BundleConstants.BUNDLE_PROTOCOL_FROM, "")
//        bundle.putString(BundleConstants.BUNDLE_CONTENT_DATA, "")
//        bundle.putString("certInfo", "")
//        bundle.putString("certStatus","")
//        bundle.putInt(BundleConstants.BUNDLE_DATA_INT, 2)
//        TaskPageRouter.jumpToLoverTagActivity(context, bundleFrom = bundle)

//        val protocol = "og://og.app/open?type=227"
//        ProtocolHelper.parseProtocol(protocol)

//        ChatPageRouter.jumpToSantaClausActivity(context, PageSource.F1_RECOMMEND_CHILD_FRAGMENT)

//        val upgradeDialogUtil = UpgradeDialogUtil(context)
//        val mQueryVersionBean = QueryVersionBean()
//        mQueryVersionBean.type = 1
//        mQueryVersionBean.picUrl =
//            "https://lengjing-cdn.zhipin.com/azeroth/system/public/241204/wvzSjy8I95boOqO2jhvYoFIteo3VQAg6p7pjljH8DznIQ7cWyvnZT61G3u4_dn3cSp8LOZLK83JN1MOhoyglgsqN3AVzaEGNqt4d-Guhokw~.png"
//
//        upgradeDialogUtil?.showUpgradeDialog(mQueryVersionBean){
//        }

        //u_otO6PWFAOJX8MyCa8GnSI4Mqe2QKxISKcwIz9jQJw~
//        MePageRouter.jumpToInfoPreviewActivity(context, userId = "M5Ia6yACL02hOPtRoha2Zv0zB0I1Dd8wC3sRS-Ut97w~")
//        MePageRouter.jumpToMarryActivity(context, PageSource.AUTH_CENTER_ACTIVITY)
//        TLog.print("isOldPhone",PhoneUtil.isOldPhone(context).toString())
        //首善
//        val intent = Intent(context, LoginActivateActivity::class.java)
//        val intent = Intent(context, LoginSkipActivity::class.java)

        //
//        val intent = Intent(context, MyInfoEditActivity::class.java)
//        context.startActivity(intent)

        //个人信息页
        //7QcpLQ595i_TLUv9ce6VZEacXmKaUZ_BZrgAOtYR3XM~
//        SocialPageRouter.jumpToUserDynamicActivity(context, "7QcpLQ595i_TLUv9ce6VZEacXmKaUZ_BZrgAOtYR3XM~")


        //父母模式首善
//        val defaultUriRequest = AppUtil.getDefaultUriRequest(
//            context,
//            LoginPageRouter.LOGIN_PARENT_FIRST_EDIT_INFO_ACTIVITY
//        )
//        defaultUriRequest.start()

        //父母模式修改孩子信息
//        AppUtil.startUri(context, LoginPageRouter.LOGIN_PARENT_SEE_CHILD_INFO_ACTIVITY)

//        LoginPageRouter.jumpToParentFirstEdit(context,false)
        //选择模式
//        LoginPageRouter.jumpToSelectModel(context,false)

        //新手引导
//        val bean = UserGuideBlockInfoBean(true,true,true,true,true,true,0,0)
//        TaskPageRouter.jumpToTaskNewUserActivity(context,bean)

        //实名认证
//        val intent = Intent(context, CertificationActivity::class.java)
//        intent.putExtra(BundleConstants.BUNDLE_FROM, CertificationIdentifySource.ME_AUTH)
//        AppUtil.startActivity(context, intent)

//        AppUtil.startActivityForResult(
//            context,
//            MeBaseInfoEditActivity.createIntent(context),
//            RequestCodeConstants.REQUEST_CODE_BASE_INFO
//        )

//        MePageRouter.jumpToMeCompanyAuthActivity(context)

//        val protocol = "og://og.app/open?type=411&activityId=x72Fq3MBk-otYVMpkWVEmPuajXL_z_gsSZHpF_U2o8E~"
//        ProtocolHelper.parseProtocol(protocol)
//        CommonPageRouter.jumpToScanActivity(context)
//
//        LoginPageRouter.jumpMarryPlan(context, false)


//        val  webViewBean = WebViewBean()
////        webViewBean.url = TEST_JS_BRIDGE
//        webViewBean.url = "https://orange-qa.weizhipin.com/h5/active.html#/active/home/<USER>"
//        webViewBean.style = WebViewBean.STYLE_HAS_NORMAL
//        val bundle = Bundle()
//        bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean)
//        AppUtil.startUri(context, AppPageRouter.WEB_VIEW_ACTIVITY, bundle)
        //任务完成拍手动画
//        NoviceTaskApproveSuccessDialog(context as FragmentActivity).show()

//        PermissionHelper.getCalendarHelper(context as FragmentActivity)
//            .setPermissionCallback { yes: Boolean, permission: PermissionData? ->
//                if (yes) {
//                    val eventId = CalendarCommon(context).addEvent2(
//                        "title",
//                        "des",
//                        System.currentTimeMillis() + 100 * 1000,
//                        System.currentTimeMillis() + 200 * 1000,
//                        1
//                    )
//                }
//            }.requestPermission()


//        val bean1 = LocalNotifyMessageBean(0, mutableListOf(),"","title1","content1","subContent1","")

//        val like = "{\"avatarList\":[\"https://o2-qa.weizhipin.com/api/media/download/-KmIEJ07rTTs1Vh-9zv2d2mWGATsiXgUTLYvqLRSTolYb5R6WhzWT0lir0lzP3LT.jpg\"],\"nickName\":\"@002173\",\"protocol\":\"og://og.app/open?type=226\",\"title\":\"给你的照片点了赞\uD83D\uDC4D\uD83C\uDFFB\"}"
//        val like = "{\"avatarList\":[\"https://lengjing-cdn.zhipin.com/azeroth/avatar/240801/86ooLJCgrYv7O6I5XObz2aIAu-viqb8C6_dQf94h464e1vDS0jEavT04DxlSff0gPDbZCB9BjT0-4D91r-USCGde4mf1yfxZRIbnw6R78LI~.jpg\",\"https://lengjing-cdn.zhipin.com/avatar/38YKXncca15xJ-suDvZhAnO0_BOJjWE2RL_72BbDQVLSudE1NWz2SxJiIfEkYWqdhGoEjedMbIW_s9SOlOrodg~~_s2750.png\"],\"nickName\":\"恭喜你和@14062\",\"protocol\":\"og://og.app/open?type=102&from=zwPOeagR-TsTuguoryGvtPJZaORb_AvzVnQ30Wn8n9o~&to=xNiTKY4wfTJVf7wEMsRruimHQ1HuVwmTXT5HE1Im3_g~&mid=0&msgType=0\",\"subtitle\":\"双向奔赴是最浪漫的事\",\"title\":\"达成了互相喜欢\uD83D\uDC93\",\"type\":2}"
//        val like = "{\"avatarList\":[\"https://lengjing-cdn.zhipin.com/azeroth/avatar/240801/86ooLJCgrYv7O6I5XObz2aIAu-viqb8C6_dQf94h464e1vDS0jEavT04DxlSff0gPDbZCB9BjT0-4D91r-USCGde4mf1yfxZRIbnw6R78LI~.jpg\",\"https://lengjing-cdn.zhipin.com/avatar/38YKXncca15xJ-suDvZhAnO0_BOJjWE2RL_72BbDQVLSudE1NWz2SxJiIfEkYWqdhGoEjedMbIW_s9SOlOrodg~~_s2750.png\"],\"nickName\":\"恭喜你和@14062\",\"protocol\":\"og://og.app/open?type=102&from=zwPOeagR-TsTuguoryGvtPJZaORb_AvzVnQ30Wn8n9o~&to=xNiTKY4wfTJVf7wEMsRruimHQ1HuVwmTXT5HE1Im3_g~&mid=0&msgType=0\",\"subtitle\":\"双向奔赴是最浪漫的事\",\"title\":\"达成了互相喜欢\uD83D\uDC93\",\"type\":1}"
//        val like = "{\"avatarList\":[\"https://marry-media.themarryapp.com/api/media/download/JJzs6Akt-gtVxxNVkRS5fzfmCq9x-sTkZxX94rh0ixNegeMdMn-49_x-MFrO70GizFTqpopuIoghsJSVCGrZDz4NqCQWw_NL_YFcTHyOCa4~\"],\"nickName\":\"@038636\",\"protocol\":\"og://og.app/open?type=408&securityId=W23pO2Pter2lSRcYyslTP67MO0v16qfuNuYHU%2BO5iZSCa9EH9Vo3zjbDR3P9TvZYKu6qucKd1VtZ7OLtGx%2BHCtAdemvkFFzoFr51Yz%2BlWeAqSkmxVt16RG%2BlPhP6xZJc%2BiizVQ%3D%3D&source=头部卡片\",\"title\":\"刚刚回看了你\uD83D\uDC40\",\"type\":3,\"typeDesc\":\"看过的人回看我\"}"
//        val bean: LocalNotifyMessageBean = GsonUtils.getGson().fromJson<LocalNotifyMessageBean>(
//            like,
//            LocalNotifyMessageBean::class.java
//        )
//        val bean2: LocalNotifyMessageBean = GsonUtils.getGson().fromJson<LocalNotifyMessageBean>(
//            like,
//            LocalNotifyMessageBean::class.java
//        )
//        SwipeNotificationManager.getInstance(BaseApplication.getApplication().getTopContext()).addNotification(bean)


//        MePageRouter.jumpToXueXinWevViewActivity(context)
//        MePageRouter.jumpToResetEmailActivity(context)

//        if(context.window.decorView.findViewById<View>(R.id.idNotificationDialog) == null){
//            context.addContentView(ComposeView(context).apply {
//                id = R.id.idNotificationDialog
//                setContent {
//
//                    RevertLikeDialog()
//                    val showDialogStateLiveData by dialogStateLiveData.observeAsState()
//                    if ((showDialogStateLiveData ?: 0) > 0) {
//                        NotificationDialog((showDialogStateLiveData ?: 0),
//                            {
//                                dialogStateLiveData.postValue(0)
//                                SpManager.putGlobalLong(Constants.NOTIFY_TIPS_SHOW_TIME, System.currentTimeMillis())
//                            },{
//                                if ((showDialogStateLiveData ?: 0) ==1){
//                                    PermissionHelper.requestNotificationPermission(
//                                        context,
//                                        null
//                                    )
//                                }else if ((showDialogStateLiveData ?: 0) ==2){
//                                    MePageRouter.jumpToMeNotifySettingActivity(context)
//                                }
//                                dialogStateLiveData.postValue(0)
//                                SpManager.putGlobalLong(Constants.NOTIFY_TIPS_SHOW_TIME, System.currentTimeMillis())
//                            }
//                        )
//                    }
//
//
//                }
//            }, ViewGroup.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
//        }
//        dialogStateLiveData.postValue(2)


//        jumpFilmRelationShipDetailActivity(context,"5O6DEvH3rj3t6z0jdsP_U8vYmTl1Zbn9asGJ0CQ6E2U~")
//        jumpFilmRelationShipDetailActivity(context,"QULXZVrUt9UEjSROOaolZ3AtSR9_znqn7R9zl2_-Oek~")
//        MePreviewStarMatchingDialog(context as FragmentActivity).show(null)

//        SendLikeResultHandler().handleResult(MatchingLikeModel().also {
//            it.likeEachOther = LikeEachOtherBean().also { o ->
//                o.otherInfo = LikeInfo().also { info -> info.tinyPhoto = "https://img2.baidu.com/it/u=2088193715,437091952&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500" }
//                o.myInfo = LikeInfo().also { info -> info.tinyPhoto = "https://img2.baidu.com/it/u=1043407016,3396810233&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500" }
//            }
//        })

//         ComposeDialogFragment.Companion.shouldShow(context as FragmentActivity){
//            NotificationDialog(1,
//                onClosed = {
//
//                }, onSetting = {
//
//                }
//            )
//        }


//        if(context.window.decorView.findViewById<View>(com.kanzhun.foundation.R.id.idDoubleLikeDialog) == null){
//            context.addContentView(ComposeView(context).apply {
//                id = com.kanzhun.foundation.R.id.idDoubleLikeDialog
//                setContent {
//                    DoubleLikedDialog(likeDialogStateLiveData,itemBean, onDismiss = {
//                        likeDialogStateLiveData.value = 0
//                    }, onClick = {
//                        ChatPageRouter.jumpToSingleChatActivityForH5(context, "")
//                    })
//                }
//            }, ViewGroup.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
//        }
//
//        ExecutorFactory.getMainHandler().postDelayed({
//            likeDialogStateLiveData.value = 1
//        }, 300)


//        screenShotSDialogFragment = ComposeDialogFragment.Companion.shouldShow(context as FragmentActivity){
//            ComposeScreenShotShareView(listOf(
//                BottomSheetItem(
//                    "分享截图",
//                    R.mipmap.icon_screen_shot_share_pic.painterResource()
//                ) {
//                    ScreenShotImageHandler().shareImageAndQRCode(context, Uri.parse("/storage/emulated/0/Pictures/IMG_20241120_104943.jpg"),"xNiTKY4wfTJVf7wEMsRruimHQ1HuVwmTXT5HE1Im3_g~")
//
//                },
//                BottomSheetItem(
//                    "分享主页链接",
//                    R.mipmap.icon_screen_shot_share_link.painterResource()
//                ) { }
//            ), imageUri = Uri.parse(""), onCancel = {screenShotSDialogFragment?.dismissAllowingStateLoss()})
//        }
    }

    var screenShotSDialogFragment: ComposeDialogFragment? = null

    val likeDialogStateLiveData = mutableStateOf<Int>(0)
    val itemBean: MutableState<LikeEachOtherListItemBean?> = mutableStateOf(null)

    val dialogStateLiveData = MutableLiveData<Int>()

}