package com.kit.activity

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.kit.baselibrary.IExtraBean
import com.kit.extra.CrashListEntry
import com.kit.extra.CustomConfigExtraEntry
import com.kit.extra.LoginExtraEntry
import com.kit.extra.PageInfoExtraEntry
import com.kit.extra.ProtocolHelperExtraEntry
import com.kit.extra.TestEntry
import com.kit.extra.UEToolExtraEntry
import com.kit.extra.UnlockExtraEntry
import com.kit.extra.ZhangzhengExtraEntry

/**
 * 自定义的测试页面放到：
 * @see com.balsikandar.crashreporter.test.TestViewModel
 */
class KitMainViewModel : ViewModel() {
    private val _kits = MutableLiveData<List<IExtraBean>>()
    val kits: MutableLiveData<List<IExtraBean>> = _kits

    init {
        _kits.value = listOf(
            TestEntry(),
            ZhangzhengExtraEntry(),
            PageInfoExtraEntry(),
            UEToolExtraEntry(),

            ProtocolHelperExtraEntry(),
            CustomConfigExtraEntry(),
            CrashListEntry(),
            UnlockExtraEntry(),

            LoginExtraEntry(),
        )
    }

    fun setKitList(list: List<IExtraBean>) {
        _kits.value = list
    }
}