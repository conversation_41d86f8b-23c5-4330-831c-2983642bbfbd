package com.kit.activity.login.data.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.kit.activity.login.data.entity.LoginUserEntity;

import java.util.List;

import kotlinx.coroutines.flow.Flow;

@Dao
public interface LoginUserDao {
    @Query("SELECT * FROM login_users ORDER BY lastLoginTime DESC")
    Flow<List<LoginUserEntity>> getAllUsers();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertOrUpdateUser(LoginUserEntity user);

    @Delete
    void deleteUser(LoginUserEntity user);

    @Query("DELETE FROM login_users")
    void deleteAllUsers();

    @Query("SELECT * FROM login_users WHERE userId = :userId")
    LoginUserEntity getUserById(String userId);
}