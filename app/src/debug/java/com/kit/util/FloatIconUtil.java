package com.kit.util;


import static com.kit.floatpage.PageIntent.ICON_TAG;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kit.Kit;
import com.kit.floatpage.FloatIconPage;
import com.kit.floatpage.FloatPageManager;
import com.kit.floatpage.PageIntent;
import com.kit.floatpage.UEToolShowStateHolder;
import com.kit.toppageinfo.TopPageInfoFinder;

import java.lang.ref.WeakReference;

/**
 * create by guofeng
 * date on 2021/7/27
 */
public class FloatIconUtil {

    private static final FloatIconUtil INSTANCE = new FloatIconUtil();

    private FloatIconUtil() {
    }

    public static FloatIconUtil getInstance() {
        return INSTANCE;
    }

    private int actCount;

    @Nullable
    private WeakReference<Activity> currentTopActivity;
    private boolean sHasRequestPermission;

    public void registerMainLifeCircle() {
        Kit.getInstance().getApplication().registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, Bundle savedInstanceState) {
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                currentTopActivity = new WeakReference<>(activity);

                actCount++;
                showFloatIcon();
            }


            @Override
            public void onActivityPaused(@NonNull Activity activity) {

            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                actCount--;
                checkAppRunningBackGround();
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {

            }
        });
    }

    // 检测应用是否在后台
    private void checkAppRunningBackGround() {
        // 应用在后台 隐藏icon
        if (actCount == 0) {
            hideFloatIcon();
        }
    }

    // 显示入口图标
    private void showFloatIcon() {
        // 是否有漂浮顶部权限
        if (KitPermissionUtil.canDrawOverlays(Kit.getInstance().getApplication())) {
            PageIntent intent = new PageIntent(FloatIconPage.class);
            intent.tag = ICON_TAG;
            intent.mode = PageIntent.MODE_SINGLE_INSTANCE;
            FloatPageManager.getInstance().add(intent);
        } else {
            if (!sHasRequestPermission) {
                KitPermissionUtil.requestDrawOverlays(Kit.getInstance().getApplication());
                sHasRequestPermission = true;
            }
        }

        UEToolShowStateHolder.INSTANCE.shouldShowUET();
        TopPageInfoFinder.getInstance().shouldShowTopPageInfo();
    }

    private void hideFloatIcon() {
        FloatPageManager.getInstance().remove(ICON_TAG);
        UEToolShowStateHolder.INSTANCE.hideTemporarily();
        TopPageInfoFinder.getInstance().hideTemporary();
    }

}