<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".SocialNotifyCommentFragment">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.social.viewmodel.SocialNotifyCommentViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.social.callback.SocialNotifyCommentCallback" />
    </data>

    <FrameLayout
        android:id="@+id/fragmentMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smart_layout"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
        <include
            android:id="@+id/ll_empty"
            android:visibility="gone"
            layout="@layout/common_layout_empty_view"/>
    </FrameLayout>

</layout>