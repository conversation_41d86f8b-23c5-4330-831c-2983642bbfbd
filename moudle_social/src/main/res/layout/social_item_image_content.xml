<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.kanzhun.utils.base.LList" />

        <variable
            name="bean"
            type="com.kanzhun.foundation.api.bean.MomentListItemBean" />

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.qmuiteam.qmui.layout.QMUIFrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:visibleGone="@{LList.getCount(bean.pictures) == 1}"
            app:qmui_radius="10dp"
            tools:visibility="visible">
            <com.kanzhun.common.views.image.OImageView
                android:layout_width="183dp"
                android:layout_height="239dp"
                app:imageUrl='@{LList.getCount(bean.pictures) > 0 ? bean.pictures.get(0).url : ""}'
                app:singleImage="@{bean}"
                android:scaleType="centerCrop"/>
        </com.qmuiteam.qmui.layout.QMUIFrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:visibleGone="@{LList.getCount(bean.pictures) == 2}"
            tools:visibility="gone">
            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/fl_two_first"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintDimensionRatio="h,1:1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/fl_two_second"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginRight="5dp"
                app:qmui_radius="10dp">
                <com.kanzhun.common.views.image.OImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:imageUrl='@{LList.getCount(bean.pictures) > 0 ? bean.pictures.get(0).url : ""}'
                    android:scaleType="centerCrop"/>
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>
            
            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/fl_two_second"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintHorizontal_weight="1"
                app:layout_constraintDimensionRatio="h,1:1"
                app:layout_constraintLeft_toRightOf="@+id/fl_two_first"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginLeft="5dp"
                app:qmui_radius="10dp">
                <com.kanzhun.common.views.image.OImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:imageUrl='@{LList.getCount(bean.pictures) > 1 ? bean.pictures.get(1).url : ""}'
                    android:scaleType="centerCrop"/>
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:visibleGone="@{LList.getCount(bean.pictures) > 2}"
            tools:visibility="gone">
            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/fl_multi1"
                android:layout_width="0dp"
                app:layout_constraintWidth_percent="0.58"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="h,160:180"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:qmui_radius="10dp">
                <com.kanzhun.common.views.image.OImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:imageUrl='@{LList.getCount(bean.pictures) > 0 ? bean.pictures.get(0).url : ""}'
                    android:scaleType="centerCrop"/>
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>

            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/fl_multi2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintLeft_toRightOf="@+id/fl_multi1"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/fl_multi1"
                app:layout_constraintBottom_toTopOf="@+id/fl_multi3"
                app:layout_constraintVertical_weight="1"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="4dp"
                app:qmui_radius="10dp">
                <com.kanzhun.common.views.image.OImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:imageUrl='@{LList.getCount(bean.pictures) > 1 ? bean.pictures.get(1).url : ""}'
                    android:scaleType="centerCrop"/>
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>

            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/fl_multi3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintVertical_weight="1"
                app:layout_constraintLeft_toLeftOf="@+id/fl_multi2"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/fl_multi2"
                app:layout_constraintBottom_toBottomOf="@+id/fl_multi1"

                android:layout_marginTop="4dp"
                app:qmui_radius="10dp">
                <com.kanzhun.common.views.image.OImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:imageUrl='@{LList.getCount(bean.pictures) > 2 ? bean.pictures.get(2).url : ""}'
                    android:scaleType="centerCrop"/>
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>

            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintRight_toRightOf="@+id/fl_multi3"
                app:layout_constraintBottom_toBottomOf="@+id/fl_multi3"
                android:background="@drawable/common_bg_image_label_color_96000000"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                app:visibleGone="@{LList.getCount(bean.pictures) > 3}">
                <com.kanzhun.common.views.OTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/common_white"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium"
                    android:textSize="16dp"
                    android:text="@{@string/common_num_plus(LList.getCount(bean.pictures) - 3)}"/>
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>
