<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.kanzhun.marry.social.SocialNotifyActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.social.viewmodel.SocialNotifyViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.social.callback.SocialNotifyCallback" />
    </data>

    <LinearLayout
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">

        <include
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:title="@{@string/social_dynamic_notify}" />

        <net.lucode.hackware.magicindicator.MagicIndicator
            android:id="@+id/magic_indicator"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_marginTop="8dp"
            android:background="@color/common_white"
            android:visibility="gone" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:background="@color/common_color_F8F8F8" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

</layout>