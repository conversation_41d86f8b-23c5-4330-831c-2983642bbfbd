<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="avatar"
            type="String" />

        <variable
            name="title"
            type="String" />

        <variable
            name="content"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_bg_corner_20_top_2_color_white">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:text="加入圈子即可评论"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/qcl_content"
            android:layout_width="0dp"
            android:layout_height="77dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="27dp"
            android:layout_marginRight="16dp"
            android:background="@color/common_color_F4F4F6"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:qmui_radius="10dp">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/ov_avatar"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_marginLeft="23dp"
                app:common_circle="true"
                app:imageUrl="@{avatar}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_circle_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:paddingLeft="18dp"
                android:paddingRight="18dp"
                android:singleLine="true"
                android:text="@{title}"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_18"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@+id/tv_circle_assist"
                app:layout_constraintLeft_toRightOf="@id/ov_avatar"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="互联网搬砖互联网搬砖互联网搬砖互联网搬砖" />

            <TextView
                android:id="@+id/tv_circle_assist"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:paddingLeft="18dp"
                android:paddingRight="18dp"
                android:singleLine="true"
                android:text="@{content}"
                android:textColor="@color/common_color_B7B7B7"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/ov_avatar"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_circle_name"
                tools:text="1800位在互联网搬砖圈人互圈1800位在互联网搬砖圈人互圈" />

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/tv_negative"
            android:layout_width="wrap_content"
            android:layout_height="46dp"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="50dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:paddingLeft="50dp"
            android:paddingRight="50dp"
            android:text="@string/common_cancel"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_positive"
            app:layout_constraintTop_toBottomOf="@+id/qcl_content"
            app:qmui_backgroundColor="@color/common_color_F4F4F6"
            app:qmui_radius="10dp" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/tv_positive"
            android:layout_width="0dp"
            android:layout_height="46dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="50dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:text="@string/social_go_to_see"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tv_negative"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/qcl_content"
            app:qmui_backgroundColor="@color/common_color_7171F6"
            app:qmui_radius="10dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>