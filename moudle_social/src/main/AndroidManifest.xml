<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.VIBRATE" />
    <application>
        <activity
            android:name="com.kanzhun.marry.social.activity.PublishActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.kanzhun.marry.social.activity.SocialCircleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.social.activity.SocialNotifyActivity"
            android:configChanges="screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.social.activity.DynamicDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.kanzhun.marry.social.activity.UserDynamicActivity"
            android:screenOrientation="portrait" />
    </application>

</manifest>