package com.kanzhun.marry.social.views;

import android.content.Context;
import android.content.res.Resources;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kanzhun.common.views.EllipsizeLinearLayout;
import com.kanzhun.foundation.Constants;
import com.kanzhun.marry.social.callback.ScrollKeyboardListener;


/**
 * <AUTHOR>
 * @date 2022/7/20.
 */
public class ScrollCloseKeyboardLayout extends EllipsizeLinearLayout implements View.OnClickListener {
    float downY;
    boolean canClick;

    public ScrollCloseKeyboardLayout(@NonNull Context context) {
        this(context, null);
    }

    public ScrollCloseKeyboardLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ScrollCloseKeyboardLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setOnClickListener(this);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        Log.e("ScrollCloseKeyboardLayout", "action=" + ev.getAction());
        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                downY = ev.getY();
                canClick = true;
                break;
            case MotionEvent.ACTION_UP:
                float upY = ev.getY();
                if (Math.abs(upY - downY) > Resources.getSystem().getDisplayMetrics().density * Constants.SOCIAL_PUBLISH_SCROLL_SLOP) {
                    if (scrollKeyboardListener != null) {
                        scrollKeyboardListener.closeKeyboard();
                        canClick = false;
                    }
                }
                break;
        }
        return super.dispatchTouchEvent(ev);
    }

    private ScrollKeyboardListener scrollKeyboardListener;

    public void setScrollKeyboardListener(ScrollKeyboardListener scrollKeyboardListener) {
        this.scrollKeyboardListener = scrollKeyboardListener;
    }

    @Override
    public void onClick(View v) {
        if (canClick && scrollKeyboardListener != null) {
            scrollKeyboardListener.openKeyboard();
        }
    }
}
