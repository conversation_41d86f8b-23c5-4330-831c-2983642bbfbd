package com.kanzhun.marry.social.views;

import android.content.Context;
import android.graphics.PointF;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/8/24
 */
public class GesturesScaleView extends View {
    /**
     * 0.不处理事件
     * 1.放大位移模式
     * 2.位移模式
     */
    private int eventModel = 0;

    private PointF point1;
    private PointF point2;
    private GesturesScaleViewListener listener;
    private long firstTime;

    public GesturesScaleView(Context context) {
        this(context, null);
    }

    public GesturesScaleView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GesturesScaleView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setListener(GesturesScaleViewListener listener) {
        this.listener = listener;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getActionMasked() == MotionEvent.ACTION_DOWN) {
            return true;
        }

        // 两个手指触摸 才做处理事件
        if (eventModel == 0 && event.getPointerCount() == 2) {
            float curX1 = event.getX(0);
            float curY1 = event.getY(0);
            float curX2 = event.getX(1);
            float curY2 = event.getY(1);
            Rect rect = new Rect();
            getHitRect(rect);
            boolean contains = rect.contains((int) curX1, (int) curY1) && rect.contains((int) curX2, (int) curY2);
            if (contains) {
                eventModel = 1;
                getParent().requestDisallowInterceptTouchEvent(true);
                if (listener != null) {
                    firstTime = System.currentTimeMillis();
                    listener.onTouchStart(Math.abs((curX2 + curX1) / 2), Math.abs((curY2 + curY1)) / 2);
                }
            }
        }
        if (eventModel == 0) {
            return super.onTouchEvent(event);
        }
        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_POINTER_UP:
                eventModel = 2;
                point1 = null;
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                if (eventModel == 2) {
                    point1 = null;
                }
                break;
            case MotionEvent.ACTION_MOVE:
                moveDell(event);
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                eventModel = 0;
                point1 = null;
                point2 = null;
                if (listener != null) {
                    listener.onTouchEnd();
                }
                break;
        }
        return super.onTouchEvent(event);
    }

    private void moveDell(MotionEvent event) {
        if (System.currentTimeMillis() - firstTime < 200) {
            return;
        }
        if (eventModel == 1) {
            if (event.getPointerCount() < 2) {
                return;
            }
            if (point1 == null) {
                point1 = new PointF(event.getX(0), event.getY(0));
            }
            if (point2 == null) {
                point2 = new PointF(event.getX(1), event.getY(1));
            }
            float curX1 = event.getX(0);
            float curY1 = event.getY(0);

            float curX2 = event.getX(1);
            float curY2 = event.getY(1);

            double originalDistance = Math.sqrt(
                    ((point2.x - point1.x) * (point2.x - point1.x))
                            + ((point2.y - point1.y) * (point2.y - point1.y)));
            double curDistance = Math.sqrt(
                    ((curX2 - curX1) * (curX2 - curX1))
                            + ((curY2 - curY1) * (curY2 - curY1)));
            double scale = curDistance / originalDistance;

            float dx = curX1 - point1.x;
            float dy = curY1 - point1.y;

//            L.e("TAG", "dx = " + dx + " , dy = " + dy + " , scale = " + scale);

            if (listener != null) {
                listener.move(dx, dy, scale);
            }


            point1.set(event.getX(0), event.getY(0));
            point2.set(event.getX(1), event.getY(1));
        } else if (eventModel == 2) {
            if (point1 == null) {
                point1 = new PointF(event.getX(0), event.getY(0));
            }

            float dx = event.getX(0) - point1.x;
            float dy = event.getY(0) - point1.y;


//            L.e("TAG", "dx = " + dx + " , dy = " + dy + " , scale = " + 1);

            if (listener != null) {
                listener.move(dx, dy, 1.0);
            }
            point1.set(event.getX(0), event.getY(0));
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (listener != null) {
            listener.onViewRemoved();
        }
    }

    public interface GesturesScaleViewListener {
        void move(float dx, float dy, double scale);

        void onViewRemoved();

        void onTouchEnd();

        void onTouchStart(float dx, float dy);
    }
}
