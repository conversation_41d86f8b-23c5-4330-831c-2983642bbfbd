package com.kanzhun.marry.social.dialog;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.dialog.BottomSheetBehaviorBaseFragment;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.marry.social.BR;
import com.kanzhun.marry.social.R;
import com.kanzhun.marry.social.api.model.CircleInfoModel;
import com.kanzhun.marry.social.callback.SocialCircleAddCallback;
import com.kanzhun.marry.social.databinding.SocialDialogCircleAddBinding;
import com.kanzhun.marry.social.viewmodel.SocialCircleViewModel;
import com.kanzhun.marry.social.views.CircleIntroItemView;
import com.kanzhun.utils.base.LList;

/**
 * <AUTHOR>
 * @date 2022/7/14.
 */
public class SocialCircleAddDialog extends BottomSheetBehaviorBaseFragment<SocialDialogCircleAddBinding, FoundationViewModel> implements SocialCircleAddCallback {
    public static final String TAG = "SocialCircleAddDialog";
    SocialCircleViewModel socialCircleViewModel;

    public SocialCircleAddDialog(SocialCircleViewModel socialCircleViewModel) {
        this.socialCircleViewModel = socialCircleViewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.social_dialog_circle_add;
    }

    @Override
    protected void initFragment() {
        getDataBinding().setCircleInfo(socialCircleViewModel.getCircleInfo());
        CircleInfoModel.IntroJsonBean introJson = socialCircleViewModel.getCircleInfo().introJson;
        if (introJson != null && !LList.isEmpty(introJson.intro)) {
            for (CircleInfoModel.IntroBean bean : introJson.intro) {
                if (bean == null) continue;
                CircleIntroItemView view = new CircleIntroItemView(getContext());
                view.setData(bean);
                getDataBinding().llContentItems.addView(view);
            }
        }
    }

    @Override
    public int getBottomSheetLayoutParamsHeight() {
        return (int) (QMUIDisplayHelper.getScreenHeight(activity) * 0.85f);
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public void cancel() {
        dismiss();
    }

    @Override
    public void addSocialCircle() {
        dismiss();
        socialCircleViewModel.joinCircle();
    }
}
