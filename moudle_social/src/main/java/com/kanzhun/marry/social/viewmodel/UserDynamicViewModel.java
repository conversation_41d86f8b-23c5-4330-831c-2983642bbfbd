package com.kanzhun.marry.social.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.ICommonCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.foundation.SocialConstants;
import com.kanzhun.marry.social.api.SocialApi;
import com.kanzhun.foundation.api.bean.MomentListItemBean;
import com.kanzhun.marry.social.api.model.UserDynamicModel;
import com.kanzhun.marry.social.api.model.UserProfileInfoModel;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.configuration.UserSettingConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;

public class UserDynamicViewModel extends FoundationViewModel {
    private static final String TAG = "UserDynamicViewModel";
    private String userId;
    private String securityId;
    private boolean isSelf;
    private MutableLiveData<UserProfileInfoModel> infoLiveData = new MutableLiveData<>();
    private MutableLiveData<String> infoErrorLiveData = new MutableLiveData<>();
    private UserProfileInfoModel infoModel;
    private String offsetId = "";
    private boolean hasMore;
    private boolean isRefresh = true;
    private MutableLiveData<List<MomentListItemBean>> dynamicsLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> sendLikeLiveData = new MutableLiveData<>();
    private MomentListItemBean tempData;
    private MutableLiveData<Boolean> visibleTypeLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> deleteLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> hideLiveData = new MutableLiveData<>();

    public UserDynamicViewModel(Application application) {
        super(application);
    }

    public String getUserId() {
        return userId;
    }

    public void setSecurityId(String securityId) {
        this.securityId = securityId;
    }

    public String getSecurityId() {
        return securityId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
        isSelf = TextUtils.equals(userId, AccountHelper.getInstance().getUserId());
    }

    public UserProfileInfoModel getInfoModel() {
        return infoModel;
    }

    public boolean isSelf() {
        return isSelf;
    }

    public void requestProfileInfo() {
        Observable<BaseResponse<UserProfileInfoModel>> responseObservable;
        if (isSelf) {
            responseObservable = RetrofitManager.getInstance().createApi(SocialApi.class).requestMomentProfileMe();
        } else {
            responseObservable = RetrofitManager.getInstance().createApi(SocialApi.class).requestMomentProfileUser(userId);
        }
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<UserProfileInfoModel>() {
            @Override
            public void onSuccess(UserProfileInfoModel data) {
                infoModel = data;
                infoLiveData.setValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                infoErrorLiveData.setValue(reason.getErrReason());
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public MutableLiveData<UserProfileInfoModel> getInfoLiveData() {
        return infoLiveData;
    }

    public MutableLiveData<String> getInfoErrorLiveData() {
        return infoErrorLiveData;
    }

    public void refreshHistory() {
        isRefresh = true;
        offsetId = "";
        requestHistory();
    }

    public void getMoreHistory() {
        isRefresh = false;
        requestHistory();
    }

    private void requestHistory() {
        Observable<BaseResponse<UserDynamicModel>> responseObservable;
        if (isSelf) {
            responseObservable = RetrofitManager.getInstance().createApi(SocialApi.class).requestMomentHistoryMe(offsetId);
        } else {
            responseObservable = RetrofitManager.getInstance().createApi(SocialApi.class).requestMomentHistoryUser(userId, offsetId);
        }
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<UserDynamicModel>() {
            @Override
            public void onSuccess(UserDynamicModel data) {
                offsetId = data.offsetId;
                hasMore = data.hasMore == 1;
                dynamicsLiveData.setValue(data.moments);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }


    public MutableLiveData<List<MomentListItemBean>> getDynamicsLiveData() {
        return dynamicsLiveData;
    }

    public boolean isHasMore() {
        return hasMore;
    }

    public boolean isRefresh() {
        return isRefresh;
    }

    public void sendLike(String content) {
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("reason", content);
        HttpExecutor.requestSimplePost(URLConfig.URL_COMMUNITY_INTEREST_SEND, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                sendLikeLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                sendLikeLiveData.setValue(false);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<Boolean> getSendLikeLiveData() {
        return sendLikeLiveData;
    }

    public MomentListItemBean getTempData() {
        return tempData;
    }

    public void setTempData(MomentListItemBean tempData) {
        this.tempData = tempData;
    }

    public MutableLiveData<Boolean> getVisibleTypeLiveData() {
        return visibleTypeLiveData;
    }

    public void updateVisible(int visibleType, String momentId) {
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("visibleType", visibleType);
        params.put("momentId", momentId);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_VISIBLE_UPDATE, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                tempData.visibleType = visibleType;
                visibleTypeLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<Boolean> getDeleteLiveData() {
        return deleteLiveData;
    }

    public void deleteDynamic() {
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("momentId", tempData.momentId);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_DELETE, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                deleteLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public void momentThumbAdd(String momentId, ICommonCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("momentId", momentId);
        if (isSelf){
            params.put("source", SocialConstants.SOURCE_SOCIAL_DYNAMIC_SELF);
        }else {
            params.put("source", SocialConstants.SOURCE_SOCIAL_DYNAMIC_FRIEND);
        }
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_THUMB_ADD, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }
        });
    }


    public void momentThumbCancel(String momentId, ICommonCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("momentId", momentId);
        if (isSelf){
            params.put("source", SocialConstants.SOURCE_SOCIAL_DYNAMIC_SELF);
        }else {
            params.put("source", SocialConstants.SOURCE_SOCIAL_DYNAMIC_FRIEND);
        }
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_THUMB_CANCEL, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }
        });
    }

    public MutableLiveData<Boolean> getHideLiveData() {
        return hideLiveData;
    }

    public void requestMatchHide() {
        setShowProgressBar();
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(FoundationApi.class).settingUpdate(UserSettingConfig.PERSONALITY_SETTING_INVISIBLE, 0);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                new SettingBuilder.Builder().addUserSetting(UserSettingConfig.PERSONALITY_SETTING_INVISIBLE, new UserSettingConfig(UserSettingConfig.PERSONALITY_SETTING_INVISIBLE, String.valueOf(0))).build();
                ServiceManager.getInstance().getSettingService().getMatchVisibleLiveData().setValue(false);
                hideLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }
}