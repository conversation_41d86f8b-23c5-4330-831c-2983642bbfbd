package com.kanzhun.marry.social.model;

import androidx.databinding.ObservableInt;

import com.kanzhun.foundation.api.bean.MomentListItemBean;
import com.kanzhun.foundation.api.bean.PictureListItemBean;
import com.kanzhun.foundation.api.bean.MomentModel;
import com.kanzhun.utils.base.LList;

/**
 * <AUTHOR>
 * @date 2022/7/16.
 */
public class CircleMomentBean {
    private MomentModel momentModel;
    private ObservableInt replyCount = new ObservableInt(0);// 评论数量
    private ObservableInt thumbCount = new ObservableInt(0);// 点赞数量
    private ObservableInt thumbStatus = new ObservableInt(MomentModel.UN_DID_THUMB);


    public CircleMomentBean(MomentModel momentModel) {
        this.momentModel = momentModel;
        replyCount.set(momentModel.replyCount);
        thumbCount.set(momentModel.thumbCount);
        thumbStatus.set(momentModel.thumbStatus);
    }

    public MomentModel getMomentModel() {
        return momentModel;
    }

    public String getUserId() {
        if (momentModel != null) {
            return momentModel.userId;
        }
        return "";
    }

    public String getAvatar() {
        if (momentModel != null) {
            return momentModel.avatar;
        }
        return "";
    }

    public String getTinyAvatar() {
        if (momentModel != null) {
            return momentModel.tinyAvatar;
        }
        return "";
    }

    public String getContent() {
        if (momentModel != null) {
            return momentModel.content;
        }
        return "";
    }

    public long getCreateTime() {
        if (momentModel != null) {
            return momentModel.createTime;
        }
        return 0L;
    }

    public String getNickName() {
        if (momentModel != null) {
            return momentModel.nickName;
        }
        return "";
    }

    public String getImpId() {
        if (momentModel != null) {
            return momentModel.impId;
        }
        return "";
    }

    public ObservableInt getReplyCount() {
        return replyCount;
    }

    public void setReplyCount(int replyCount) {
        this.replyCount.set(replyCount);
    }

    public ObservableInt getThumbCount() {
        return thumbCount;
    }

    public void setThumbCount(int thumbCount) {
        this.thumbCount.set(thumbCount);
    }

    public String getPicUrl() {
        if (momentModel != null && !LList.isEmpty(momentModel.pictures)) {
            return momentModel.pictures.get(0).url;
        }
        return "";
    }

    public PictureListItemBean getSinglePic() {
        if (momentModel != null && !LList.isEmpty(momentModel.pictures)) {
            return momentModel.pictures.get(0);
        }
        return null;
    }


    public String getMomentId() {
        if (momentModel != null) {
            return momentModel.momentId;
        }
        return "";
    }

    public ObservableInt getThumbStatus() {
        return thumbStatus;
    }

    public void setThumbStatus(int thumbStatus) {
        this.thumbStatus.set(thumbStatus);
    }

    /**
     * 更新bean
     */
    public void refresh(MomentListItemBean bean) {
        if (bean == null) {
            return;
        }
        if (momentModel != null) {
            momentModel.momentId = bean.momentId;
            momentModel.userId = bean.userId;
            momentModel.avatar = bean.avatar;
            momentModel.tinyAvatar = bean.tinyAvatar;
            momentModel.liveVideo = bean.liveVideo;
            momentModel.nickName = bean.nickName;
            momentModel.content = bean.content;
            momentModel.pictures = bean.pictures;
            momentModel.replyCount = bean.replyCount;
            momentModel.thumbCount = bean.thumbCount;
            momentModel.thumbStatus = bean.thumbStatus;
            momentModel.ipLocation = bean.ipLocation;
            momentModel.createTime = bean.createTime;
            momentModel.visibleType = bean.visibleType;
            momentModel.impId = bean.impId;
            momentModel.gender = bean.gender;
        }
        replyCount.set(momentModel.replyCount);
        thumbCount.set(momentModel.thumbCount);
        thumbStatus.set(momentModel.thumbStatus);
    }
}
