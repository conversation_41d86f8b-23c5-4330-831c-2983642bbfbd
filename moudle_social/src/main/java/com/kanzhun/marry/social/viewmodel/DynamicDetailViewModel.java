package com.kanzhun.marry.social.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.SocialConstants;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.bean.MomentListItemBean;
import com.kanzhun.foundation.api.bean.PictureListItemBean;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.User;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.ICommonCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.social.R;
import com.kanzhun.marry.social.api.SocialApi;
import com.kanzhun.marry.social.api.model.DynamicDetailCommentHeaderBean;
import com.kanzhun.marry.social.api.model.DynamicReplyBean;
import com.kanzhun.marry.social.api.model.DynamicReplyModel;
import com.kanzhun.marry.social.api.model.DynamicThumbUpBean;
import com.kanzhun.marry.social.api.model.DynamicThumbUpModel;
import com.kanzhun.marry.social.api.model.UserDynamicDetailModel;
import com.kanzhun.utils.base.LList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import io.reactivex.rxjava3.core.Observable;

public class DynamicDetailViewModel extends FoundationViewModel {
    private static final String TAG = "DynamicDetailViewModel";
    private String momentId;
    private String userId;
    private boolean isSelf;
    private UserDynamicDetailModel detailModel;
    private MutableLiveData<List<Object>> headerLiveData = new MutableLiveData<>();
    private final List<Object> headerList = new ArrayList<>();

    private MutableLiveData<UserDynamicDetailModel> titleBarLiveData = new MutableLiveData<>();
    private String offsetId = "";
    private String thumbOffsetId = "";
    private boolean hasMore;
    private boolean isRefresh = true;
    private final MutableLiveData<DynamicReplyBean> showReplyFragmentLiveData = new MutableLiveData<>();
    private final MutableLiveData<List<DynamicReplyBean>> replyListLiveData = new MutableLiveData<>();
    private final List<DynamicReplyBean> replyList = new ArrayList<>();

    private final MutableLiveData<List<DynamicThumbUpBean>> dynamicThumbUpListLiveData = new MutableLiveData<>();
    private final List<DynamicThumbUpBean> dynamicThumbUpList = new ArrayList<>();
    private boolean isThumbRefresh = true;
    private boolean hasMoreThumb;

    private boolean isThumbList = false;

    private MutableLiveData<Boolean> deleteLiveData = new MutableLiveData<>();
    private ObservableField<String> hint = new ObservableField<String>("");
    public ObservableField<String> inputContent = new ObservableField<String>("");
    private MutableLiveData<DynamicReplyBean> inputSendLiveData = new MutableLiveData();
    private MutableLiveData<DynamicReplyBean> inputReplySendLiveData = new MutableLiveData();
    private String sendReplyId;
    private int sendReplyPosition;
    private int headerPosition; //头部和评论分离
    private int deleteCommentPosition;
    private MutableLiveData<DynamicReplyBean> deleteCommentLiveData = new MutableLiveData<>();

    private MutableLiveData<Boolean> infoDeletedLiveData = new MutableLiveData<>();
    private MomentListItemBean listItemBean = new MomentListItemBean();
    private boolean isDelete;
    private boolean showComment; //是否滚动到评论
    private boolean showInput;
    private int source;

    //高亮评论id
    private String highlightReplyTd;
    private String highlightTopReplyId;

    public DynamicDetailViewModel(Application application) {
        super(application);
        Random random = new Random();
        int index = random.nextInt(4);
        String[] stringArray = BaseApplication.getApplication().getResources().getStringArray(R.array.social_array_send_comment_hint);
        hint.set(stringArray[index]);
    }

    public List<Object> getHeaderList() {
        return headerList;
    }

    public List<DynamicReplyBean> getReplyList() {
        return replyList;
    }

    public List<DynamicThumbUpBean> getDynamicThumbUpList() {
        return dynamicThumbUpList;
    }

    public boolean isThumbList() {
        return isThumbList;
    }

    public void setIsThumbList(boolean isThumbList) {
        this.isThumbList = isThumbList;
    }

    public String getMomentId() {
        return momentId;
    }

    public void setMomentId(String momentId) {
        this.momentId = momentId;
        listItemBean.momentId = momentId;
    }

    public boolean isShowComment() {
        return showComment;
    }

    public void setShowComment(boolean showComment) {
        this.showComment = showComment;
    }

    public boolean isShowInput() {
        return showInput;
    }

    public void setShowInput(boolean showInput) {
        this.showInput = showInput;
    }

    public void setSource(int source) {
        this.source = source;
    }

    public int getSource() {
        return source;
    }

    public ObservableField<String> getHint() {
        return hint;
    }

    public MutableLiveData<List<Object>> getHeaderLiveData() {
        return headerLiveData;
    }

    public MutableLiveData<UserDynamicDetailModel> getTitleBarLiveData() {
        return titleBarLiveData;
    }

    public int getHeaderPosition() {
        return headerPosition;
    }

    public void setHeaderPosition(int headerPosition) {
        this.headerPosition = headerPosition;
    }

    public UserDynamicDetailModel getDetailModel() {
        return detailModel;
    }

    public void requestMomentInfo() {
        showEmptyLoading();
        Observable<BaseResponse<UserDynamicDetailModel>> responseObservable = RetrofitManager.getInstance().createApi(SocialApi.class).requestMomentInfo(momentId, source);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<UserDynamicDetailModel>(true) {
            @Override
            public void onSuccess(UserDynamicDetailModel data) {

            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void handleInChildThread(UserDynamicDetailModel data) {
                super.handleInChildThread(data);
                detailModel = data;
                setHeaderData();
                showEmptySuccess();
            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                super.handleErrorInChildThread(reason);
                if (reason.getErrCode() == Constants.SOCIAL_CONTENT_DELETED_ERROR_CODE) {
                    //内容已被删除
                    infoDeleted();
                } else {
                    showEmptyError();
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public void infoDeleted() {
        //内容已被删除
        infoDeletedLiveData.postValue(true);
        showEmptyNoData(getResources().getString(R.string.social_dynamic_detail_empty_delete));
        isDelete = true;
    }

    public MutableLiveData<Boolean> getInfoDeletedLiveData() {
        return infoDeletedLiveData;
    }


    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    public String getUserId() {
        return userId;
    }

    private void setHeaderData() {
        userId = detailModel.userId;
        parseListItemData(detailModel);
        isSelf = TextUtils.equals(AccountHelper.getInstance().getUserId(), detailModel.userId);
        List<Object> list = new ArrayList<>();
        list.add(detailModel);
        if (!LList.isEmpty(detailModel.pictures)) {
            for (PictureListItemBean bean : detailModel.pictures) {
                list.add(bean);
            }
        }
        DynamicDetailCommentHeaderBean headerBean = new DynamicDetailCommentHeaderBean();
        headerBean.replyCount = detailModel.replyCount;
        headerBean.thumbStatus = detailModel.thumbStatus;
        headerBean.thumbCount = detailModel.thumbCount;
        headerBean.circleId = detailModel.circleId;
        headerBean.circleName = detailModel.circleName;
        list.add(headerBean);
        headerLiveData.postValue(list);
        titleBarLiveData.postValue(detailModel);

        headerList.clear();
        headerList.addAll(list);
    }

    private void parseListItemData(UserDynamicDetailModel detailModel) {
        listItemBean.thumbStatus = detailModel.thumbStatus;
        listItemBean.thumbCount = detailModel.thumbCount;
        listItemBean.replyCount = detailModel.replyCount;
        listItemBean.createTime = detailModel.createTime;
        listItemBean.visibleType = detailModel.visibleType;
        listItemBean.avatar = detailModel.avatar;
        listItemBean.tinyAvatar = detailModel.tinyAvatar;
        listItemBean.liveVideo = detailModel.liveVideo;
        listItemBean.content = detailModel.content;
        listItemBean.ipLocation = detailModel.ipLocation;
        listItemBean.pictures = detailModel.pictures;
        listItemBean.nickName = detailModel.nickName;
        listItemBean.userId = detailModel.userId;
    }

    public void refreshList() {
        if (isThumbList) {
            refreshThumbList();
        } else {
            refreshReplyList();
        }
    }

    public void getMoreList() {
        if (isThumbList) {
            getMoreThumbList();
        } else {
            getMoreReplyList();
        }
    }

    private void refreshReplyList() {
        isRefresh = true;
        offsetId = "";
        requestReplyList();
    }

    private void getMoreReplyList() {
        isRefresh = false;
        requestReplyList();
    }

    public boolean isRefresh() {
        return isRefresh;
    }

    public boolean isSelf() {
        return isSelf;
    }

    public boolean isHasMore() {
        return hasMore;
    }

    public boolean isThumbRefresh() {
        return isThumbRefresh;
    }

    public boolean isHasMoreThumb() {
        return hasMoreThumb;
    }

    public MutableLiveData<List<DynamicReplyBean>> getReplyListLiveData() {
        return replyListLiveData;
    }

    private void requestReplyList() {
        Observable<BaseResponse<DynamicReplyModel>> responseObservable = RetrofitManager.getInstance().createApi(SocialApi.class).requestMomentReplyList(momentId, offsetId);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<DynamicReplyModel>() {
            @Override
            public void onSuccess(DynamicReplyModel data) {
                offsetId = data.offsetId;
                hasMore = data.hasMore == 1;
                List<DynamicReplyBean> replies = data.replyList;
                replyListLiveData.setValue(replies);

                if (replies != null && !replies.isEmpty()) {
                    if (isRefresh) {
                        replyList.clear();
                        replyList.addAll(replies);
                    } else {
                        replyList.addAll(replies);
                    }
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
//                dynamicsLiveData.setValue(null);
//                DynamicReplyModel data = getMockListData();
//                offsetId = data.offsetId;
//                hasMore = data.hasMore == 1;
//                replyListLiveData.setValue(data.replyList);
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public void refreshThumbList() {
        isThumbRefresh = true;
        thumbOffsetId = "";
        requestThumbList();
    }

    public void getMoreThumbList() {
        isThumbRefresh = false;
        requestThumbList();
    }

    public MutableLiveData<List<DynamicThumbUpBean>> getDynamicThumbUpListLiveData() {
        return dynamicThumbUpListLiveData;
    }

    private void requestThumbList() {
        Observable<BaseResponse<DynamicThumbUpModel>> responseObservable = RetrofitManager.getInstance().createApi(SocialApi.class).requestThumbList(momentId, thumbOffsetId, 50);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<>(true) {
            @Override
            public void onSuccess(DynamicThumbUpModel data) {
                showData(data);
            }

            private void showData(DynamicThumbUpModel data) {
                if (data != null) {
                    hasMoreThumb = data.getHasMore();

                    List<DynamicThumbUpBean> thumbUpBeanList = data.getList();
                    dynamicThumbUpListLiveData.setValue(thumbUpBeanList);

                    if (thumbUpBeanList != null && !thumbUpBeanList.isEmpty()) {
                        DynamicThumbUpBean lastBean = LList.getElement(thumbUpBeanList, thumbUpBeanList.size() - 1);
                        if (lastBean != null) {
                            thumbOffsetId = lastBean.id;
                        }

                        if (isRefresh) {
                            dynamicThumbUpList.clear();
                        }
                        dynamicThumbUpList.addAll(thumbUpBeanList);
                    } else {
                        if (isRefresh) {
                            dynamicThumbUpList.clear();
                        }
                    }
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
//                if (BuildInfoUtils.isDebug()) {
//                    dynamicThumbUpListLiveData.setValue(null);
//
//                    DynamicThumbUpModel data = getMockThumbListData();
//                    showData(data);
//                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    private DynamicReplyModel getMockListData() {
        DynamicReplyModel data = new DynamicReplyModel();
        data.hasMore = 1;
        data.offsetId = "111";
        data.replyList = new ArrayList<>();

        DynamicReplyBean replyBean = new DynamicReplyBean();
        User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
        replyBean.avatar = user.getAvatar();
        replyBean.nickName = user.getNickName();
        replyBean.tinyAvatar = user.getTinyAvatar();
        replyBean.content = "元宝和金条真是可可爱爱的阿猫阿狗";
        replyBean.isOwner = 0;
        replyBean.createTime = System.currentTimeMillis();
        replyBean.replyId = "sss";
        replyBean.ipLocation = "北京";
        replyBean.userId = user.getUserId();
        replyBean.subCount = 28;
        replyBean.replyNickName = user.getNickName();

        replyBean.subList = new ArrayList<>();
        DynamicReplyBean replyBean2 = new DynamicReplyBean();
        replyBean2.avatar = user.getAvatar();
        replyBean2.nickName = user.getNickName();
        replyBean2.tinyAvatar = user.getTinyAvatar();
        replyBean2.content = "元宝和金条真是可可爱爱的阿猫阿狗";
        replyBean2.isOwner = 1;
        replyBean2.createTime = System.currentTimeMillis();
        replyBean2.replyId = "sss1111";
        replyBean2.ipLocation = "北京";
        replyBean2.userId = user.getUserId();
        replyBean2.subCount = 28;
        replyBean2.replyNickName = user.getNickName();
        replyBean.subList.add(replyBean2);
        replyBean.subList.add(replyBean2);
        replyBean.subList.add(replyBean2);

        data.replyList.add(replyBean);
        data.replyList.add(replyBean);
        data.replyList.add(replyBean);
        return data;
    }

    private DynamicThumbUpModel getMockThumbListData() {
        /*
            {
                "id": "LXFOsez2s3z6qH2yLBr0jykdxVVI4VHZVhOubXInAow~",//点赞id
                "securityId": "iauyRwCCG6Hy6E9DoyiB6mF3qD91%2BxA8Scs1f284eK2nFYHApaI%2BfKpmkWLsK8rjB9bfdkQIP9UKGCs%2B9YSkfRNCPa2NO6hunOqOpjQNcLptm7gsu87n6YMC",
                "nickName": "程龙",
                "age": 27,
                "address": "北京",
                "avatar": "https://lengjing-cdn.zhipin.com/avatar/2oA0JuTSgwNV-klfUiz5ReY9tqbmIqt5bEd9Kpp8bHMHBTThyF_9S-ca7PpNkNyQ1JOJxlREePaXPIt5zVLcwA~~.png"
            }
         */
        List<DynamicThumbUpBean> thumbUpBeanList = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            thumbUpBeanList.add(new DynamicThumbUpBean(
                "LXFOsez2s3z6qH2yLBr0jykdxVVI4VHZVhOubXInAow~",
                "LXFOsez2s3z6qH2yLBr0jykdxVVI4VHZVhOubXInAow~",
                "iauyRwCCG6Hy6E9DoyiB6mF3qD91+xA8Scs1f284eK2nFYHApaI+fKpmkWLsK8rjB9bfdkQIP9UKGCs+9YSkfRNCPa2NO6hunOqOpjQNcLptm7gsu87n6YMC",
                "程龙",
                27,
                "北京",
                "https://lengjing-cdn.zhipin.com/avatar/2oA0JuTSgwNV-klfUiz5ReY9tqbmIqt5bEd9Kpp8bHMHBTThyF_9S-ca7PpNkNyQ1JOJxlREePaXPIt5zVLcwA~~.png"
            ));
        }
        return new DynamicThumbUpModel(true, thumbUpBeanList);
    }

    private void getMockData() {
        detailModel = new UserDynamicDetailModel();
        detailModel.content = "元宝和金条真是可可爱爱的阿猫阿狗～";
        User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
        detailModel.avatar = user.getAvatar();
        detailModel.nickName = user.getNickName();
        detailModel.createTime = System.currentTimeMillis();
        detailModel.ipLocation = "北京";
        detailModel.userId = user.getUserId();

        detailModel.replyCount = 36;
        detailModel.thumbCount = 111;
        detailModel.thumbStatus = 0;

        List<PictureListItemBean> picBeans = new ArrayList<>();
        PictureListItemBean bean = new PictureListItemBean();
        bean.url = user.getAvatar();
        bean.width = 100;
        bean.height = 50;

        PictureListItemBean bean2 = new PictureListItemBean();
        bean2.url = user.getAvatar();
        bean2.width = 100;
        bean2.height = 200;
        picBeans.add(bean);
        picBeans.add(bean2);
        picBeans.add(bean2);
        picBeans.add(bean);
        detailModel.pictures = picBeans;
    }

    public void updateVisible(int visibleType) {
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("visibleType", visibleType);
        params.put("momentId", momentId);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_VISIBLE_UPDATE, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {

            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<Boolean> getDeleteLiveData() {
        return deleteLiveData;
    }

    public void deleteDynamic() {
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("momentId", momentId);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_DELETE, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                deleteLiveData.setValue(true);
                isDelete = true;
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<DynamicReplyBean> getInputSendLiveData() {
        return inputSendLiveData;
    }

    public void sendComment() {
        if (TextUtils.isEmpty(inputContent.get().trim())) {
            return;
        }
        setShowProgressBar();
        Observable<BaseResponse<DynamicReplyBean>> replyBeanObservable = RetrofitManager.getInstance().createApi(SocialApi.class)
            .requestSendComment(momentId, inputContent.get(), SocialConstants.SOURCE_SOCIAL_DYNAMIC_DETAIL_COMMENT);
        HttpExecutor.execute(replyBeanObservable, new BaseRequestCallback<DynamicReplyBean>(true) {
            @Override
            public void onSuccess(DynamicReplyBean data) {
                inputSendLiveData.setValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
//                if (reason.getErrCode() == Constants.SOCIAL_CONTENT_DELETED_ERROR_CODE) {
//                    infoDeleted();
//                }

//                DynamicReplyBean data = new DynamicReplyBean();
//                data.content = "元宝和金条真是可可爱爱的阿猫阿狗～元宝和金条真是可可爱爱的阿猫阿狗元宝和金条真是可可爱爱的阿猫阿狗";
//                User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
//                data.avatar = user.getAvatar();
//                data.nickName = user.getNickName();
//                data.createTime = System.currentTimeMillis();
//                data.ipLocation = "北京";
//                data.userId = user.getUserId();
//                data.thumbCount = 111;
//                data.thumbStatus = 0;
//
//                inputSendLiveData.setValue(data);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public String getSendReplyId() {
        return sendReplyId;
    }

    public void setSendReplyId(String sendReplyId) {
        this.sendReplyId = sendReplyId;
    }

    public MutableLiveData<DynamicReplyBean> getInputReplySendLiveData() {
        return inputReplySendLiveData;
    }

    public int getSendReplyPosition() {
        return sendReplyPosition;
    }

    public void setSendReplyPosition(int sendReplyPosition) {
        this.sendReplyPosition = sendReplyPosition;
    }

    public void sendCommentReply() {
        if (TextUtils.isEmpty(inputContent.get().trim())) {
            return;
        }
        setShowProgressBar();
        Observable<BaseResponse<DynamicReplyBean>> replyBeanObservable = RetrofitManager.getInstance().createApi(SocialApi.class)
            .requestSendReplyComment(sendReplyId, inputContent.get(), SocialConstants.SOURCE_SOCIAL_DYNAMIC_DETAIL_COMMENT);
        HttpExecutor.execute(replyBeanObservable, new BaseRequestCallback<DynamicReplyBean>(true) {
            @Override
            public void onSuccess(DynamicReplyBean data) {
                inputReplySendLiveData.setValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
//                DynamicReplyBean data = new DynamicReplyBean();
//                data.content = "元宝和金条真是可可爱爱的阿猫阿狗～元宝和金条真是可可爱爱的阿猫阿狗元宝和金条真是可可爱爱的阿猫阿狗";
//                User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
//                data.avatar = user.getAvatar();
//                data.nickName = user.getNickName();
//                data.createTime = System.currentTimeMillis();
//                data.ipLocation = "北京";
//                data.userId = user.getUserId();
//                data.thumbCount = 111;
//                data.thumbStatus = 0;
//
//                inputReplySendLiveData.setValue(data);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public void momentThumbAdd(String momentId, ICommonCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("momentId", momentId);
        params.put("source", SocialConstants.SOURCE_SOCIAL_DYNAMIC_DETAIL);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_THUMB_ADD, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }
        });
    }


    public void momentThumbCancel(String momentId, ICommonCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("momentId", momentId);
        params.put("source", SocialConstants.SOURCE_SOCIAL_DYNAMIC_DETAIL);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_THUMB_CANCEL, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }
        });
    }

    public void momentCommentThumbAdd(String momentId, ICommonCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("replyId", momentId);
        params.put("source", SocialConstants.SOURCE_SOCIAL_DYNAMIC_DETAIL_COMMENT);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_COMMENT_THUMB_ADD, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }
        });
    }


    public void momentCommentThumbCancel(String momentId, ICommonCallback callback) {
        Map<String, Object> params = new HashMap<>();
        params.put("replyId", momentId);
        params.put("source", SocialConstants.SOURCE_SOCIAL_DYNAMIC_DETAIL_COMMENT);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_COMMENT_THUMB_CANCEL, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (callback != null) {
                    callback.onFail(reason.getErrCode(), reason.getErrReason());
                }
            }
        });
    }

    public int getDeleteCommentPosition() {
        return deleteCommentPosition;
    }

    public MutableLiveData<DynamicReplyBean> getDeleteCommentLiveData() {
        return deleteCommentLiveData;
    }

    public void deleteComment(DynamicReplyBean replyBean, int position) {
        setShowProgressBar();
        deleteCommentPosition = position;
        Map<String, Object> params = new HashMap<>();
        params.put("replyId", replyBean.replyId);
        HttpExecutor.requestSimplePost(URLConfig.URL_MOMENT_COMMENT_DELETE, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                deleteCommentLiveData.setValue(replyBean);
            }

            @Override
            public void dealFail(ErrorReason reason) {
//                deleteCommentLiveData.setValue(replyBean);
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MomentListItemBean getListItemBean() {
        return listItemBean;
    }

    public void setListItemBean(MomentListItemBean listItemBean) {
        this.listItemBean = listItemBean;
    }

    public MutableLiveData<DynamicReplyBean> getShowReplyFragmentLiveData() {
        return showReplyFragmentLiveData;
    }

    public String getHighlightReplyTd() {
        return highlightReplyTd;
    }

    public void setHighlightReplyTd(String highlightReplyTd) {
        this.highlightReplyTd = highlightReplyTd;
    }

    public String getHighlightTopReplyId() {
        return highlightTopReplyId;
    }

    public void setHighlightTopReplyId(String highlightTopReplyId) {
        this.highlightTopReplyId = highlightTopReplyId;
    }

    public void autoOpenDetailReply(List<DynamicReplyBean> replyBeans) {
        boolean isFind = false;
        if (!LList.isEmpty(replyBeans)) {
            for (DynamicReplyBean bean : replyBeans) {
                if (TextUtils.equals(bean.replyId, highlightTopReplyId)) {
                    isFind = true;
                    showReplyFragmentLiveData.setValue(bean);
                    break;
                }
            }
        }
        if (!isFind) {
            Observable<BaseResponse<DynamicReplyBean>> responseObservable = RetrofitManager.getInstance().createApi(SocialApi.class).requestReplyInfo(highlightTopReplyId);
            HttpExecutor.execute(responseObservable, new BaseRequestCallback<DynamicReplyBean>() {
                @Override
                public void onSuccess(DynamicReplyBean data) {
                    showReplyFragmentLiveData.setValue(data);
                }

                @Override
                public void dealFail(ErrorReason reason) {

                }
            });
        }
    }
}