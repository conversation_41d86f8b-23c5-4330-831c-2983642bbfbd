package com.kanzhun.marry.social.activity;

import static com.kanzhun.foundation.kotlin.ktx.StringExtKt.getIpLocationName;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.AbsoluteSizeSpan;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.scwang.smart.refresh.layout.api.RefreshHeader;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.simple.SimpleMultiListener;
import com.techwolf.lib.tlog.TLog;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.dialog.CommonBaseDialog;
import com.kanzhun.common.dialog.CommonBindingDialog;
import com.kanzhun.common.dialog.CommonSystemBottomDialog;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.common.views.edittext.LengthNoticeFilter;
import com.kanzhun.common.views.expand.SimpleExpandableTextView;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.RequestCodeConstants;
import com.kanzhun.foundation.api.bean.MomentListItemBean;
import com.kanzhun.foundation.api.bean.MomentModel;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.router.SocialPageRouter;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.foundation.utils.UserReportSource;
import com.kanzhun.http.callback.ICommonCallback;
import com.kanzhun.marry.social.BR;
import com.kanzhun.marry.social.R;
import com.kanzhun.foundation.SocialConstants;
import com.kanzhun.marry.social.api.model.UserProfileInfoModel;
import com.kanzhun.marry.social.callback.UserDynamicCallback;
import com.kanzhun.marry.social.databinding.SocialActivityUserDynamicBinding;
import com.kanzhun.marry.social.databinding.SocialDialogUserDynamicLikeBinding;
import com.kanzhun.marry.social.databinding.SocialItemUserDynamicBinding;
import com.kanzhun.marry.social.viewmodel.UserDynamicViewModel;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.configuration.UserSettingConfig;
import com.kanzhun.utils.rxbus.RxBus;
import com.kanzhun.utils.views.MultiClickUtil;
import com.kanzhun.utils.views.OnMultiClickListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户动态聚合页
 */
@RouterUri(path = SocialPageRouter.USER_DYNAMIC_ACTIVITY)
public class UserDynamicActivity extends FoundationVMActivity<SocialActivityUserDynamicBinding, UserDynamicViewModel> implements UserDynamicCallback {
    private static final String TAG = "UserDynamicActivity";
    private int mOffset = 0;
    private int mScrollY = 0;
    private BaseBinderAdapter adapter;
    private int h;
    private CommonBindingDialog.Builder<SocialDialogUserDynamicLikeBinding> likeBuilder;
    private CommonBaseDialog hideDialog;

    private int mPreFirstPosition;
    private int mPreLastPosition;
    private int mIdleFirstPosition;
    private int mIdleLastPosition;
    private List<String> mExposureIds = new ArrayList<>();

    private boolean isNotifyDark = false;

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.social_activity_user_dynamic;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        h = QMUIDisplayHelper.dpToPx(160);
        initView();
        initData();
    }

    private void initData() {
        ServiceManager.getInstance().getSocialService().getSocialUnreadCount().observe(this, unRead -> {
            if (unRead != null) {
                int interactUnreadCount = unRead.interactUnreadCount;
                int visibility = interactUnreadCount > 0 ? View.VISIBLE : View.GONE;
                getDataBinding().tvNotifyCount.setVisibility(visibility);
                getDataBinding().viewHasNewMsg.setVisibility(visibility);
                getDataBinding().tvNotifyCount.setText(getString(R.string.social_n_new_notifications, interactUnreadCount));

                if (interactUnreadCount <= 0) {
                    getDataBinding().llNotify.setBackground(null);
                } else {
                    getDataBinding().llNotify.setBackgroundResource(isNotifyDark ? R.drawable.social_bg_notify2 : R.drawable.social_bg_notify);
                }
            }
        });

        getViewModel().getInfoErrorLiveData().observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                getDataBinding().ivBackNoData.setVisibility(View.VISIBLE);
                showEmptyNoData(s);
            }
        });

        getViewModel().getInfoLiveData().observe(this, new Observer<UserProfileInfoModel>() {
            @Override
            public void onChanged(UserProfileInfoModel userProfileInfoModel) {
                setHeaderInfo(userProfileInfoModel);
            }
        });

        getViewModel().getDynamicsLiveData().observe(this, new Observer<List<MomentListItemBean>>() {
            @Override
            public void onChanged(List<MomentListItemBean> userDynamicBeans) {
                if (getViewModel().isRefresh()) {
                    if (getViewModel().isHasMore()) {
                        getDataBinding().refreshLayout.setNoMoreData(false);
                        getDataBinding().refreshLayout.setEnableLoadMore(true);
                    } else {
                        getDataBinding().refreshLayout.setEnableLoadMore(false);
                    }
                    adapter.setList(userDynamicBeans);
                    adapter.setEmptyView(R.layout.common_layout_list_empty);
                } else {
                    if (!LList.isEmpty(userDynamicBeans)) {
                        adapter.addData(userDynamicBeans);
                    }
                    if (getViewModel().isHasMore()) {
                        getDataBinding().refreshLayout.finishLoadMore();
                    } else {
                        getDataBinding().refreshLayout.finishLoadMoreWithNoMoreData();
                    }
                }
                if (!LList.isEmpty(userDynamicBeans)) {
                    try {
                        List<String> exposureIds = new ArrayList<>(userDynamicBeans.size());
                        List<String> exposureImpIds = new ArrayList<>(userDynamicBeans.size());
                        for (MomentListItemBean bean : userDynamicBeans) {
                            exposureIds.add(bean.momentId);
                            exposureImpIds.add(bean.impId);
                        }
                        exposureSync(exposureIds, exposureImpIds);
                    } catch (Exception e) {
                        TLog.error(TAG, "Point error : %s", e.toString());
                    }
                }
            }
        });

        getViewModel().getSendLikeLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (likeBuilder != null && likeBuilder.getDialog() != null) {
                    likeBuilder.getDialog().dismiss();
                    if (aBoolean) {
                        T.ss(R.string.social_dynamic_send_like_success);
                        UserProfileInfoModel infoModel = getViewModel().getInfoModel();
                        infoModel.knockStatus = SocialConstants.STATUS_SOCIAL_KNOCK_NO_FRIEND_SEND;
                        infoModel.interestLimitNum--;
                        setHeaderInfo(infoModel);
                    }
                }
            }
        });

        getViewModel().getVisibleTypeLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                MomentListItemBean tempData = getViewModel().getTempData();
                int itemPosition = adapter.getItemPosition(tempData);
                if (itemPosition > -1) {
                    adapter.notifyItemChanged(itemPosition);
                }
            }
        });

        getViewModel().getDeleteLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                MomentListItemBean tempData = getViewModel().getTempData();
                int itemPosition = adapter.getItemPosition(tempData);
                if (itemPosition > -1) {
                    adapter.removeAt(itemPosition);
                }
            }
        });

        // 内容已被删除
        RxBus.getInstance().subscribe(this, Constants.SOCIAL_MOMENT_DELETED, new RxBus.Callback<MomentListItemBean>() {
            @Override
            public void onEvent(MomentListItemBean bean) {
                if (bean == null) return;
                int position = findListPosition(bean);
                if (position >= 0) {
                    adapter.removeAt(position);
                }
            }
        });
        // 内容更新
        RxBus.getInstance().subscribe(this, Constants.SOCIAL_MOMENT_UPDATE, new RxBus.Callback<MomentListItemBean>() {
            @Override
            public void onEvent(MomentListItemBean bean) {
                if (bean == null) return;
                int position = findListPosition(bean);
                if (position >= 0) {
                    MomentListItemBean item = (MomentListItemBean) adapter.getItem(position);
                    item.refresh(bean);
                    adapter.notifyItemChanged(position);
                }
            }
        });

        // 假写
        RxBus.getInstance().subscribe(this, StringUtil.getPublishDraftId(Constants.PUBLISH_TYPE_USER_DYNAMIC, ""), new RxBus.Callback<MomentModel>() {
            @Override
            public void onEvent(MomentModel momentModel) {
                MomentListItemBean bean = MomentListItemBean.parse(momentModel);
                if (bean == null) return;
                adapter.addData(0, bean);
                adapter.notifyDataSetChanged();
                // 滑动到顶部
                getDataBinding().recyclerview.scrollToPosition(0);
            }
        });

        getViewModel().getHideLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (hideDialog != null && hideDialog.isShowing()) {
                    hideDialog.dismiss();
                }
            }
        });
    }

    private int findListPosition(MomentListItemBean bean) {
        if (LList.isEmpty(adapter.getData()) || bean == null || TextUtils.isEmpty(bean.momentId))
            return -1;
        for (int i = 0; i < adapter.getData().size(); i++) {
            MomentListItemBean item = (MomentListItemBean) adapter.getData().get(i);
            if (TextUtils.equals(bean.momentId, item.momentId)) {
                return i;
            }
        }
        return -1;
    }

    private void setHeaderInfo(UserProfileInfoModel userProfileInfoModel) {
        getDataBinding().activityMain.setVisibility(View.VISIBLE);
        showEmptySuccess();
        getDataBinding().setInfoModel(userProfileInfoModel);
        getDataBinding().tvIp.setText(getResources().getString(R.string.social_dynamic_detail_ip, getIpLocationName(userProfileInfoModel.ipLocation)));

    }


    @Override
    public boolean isShowEmptyViewForRoot() {
        return true;
    }

    private void initView() {
        getDataBinding().clTitle.setPadding(0, StatusBarUtil.getStatusBarHeight(this), 0, 0);
        String userId = getIntent().getStringExtra(BundleConstants.BUNDLE_USER_ID);
        String securityId = getIntent().getStringExtra(BundleConstants.BUNDLE_SECURITY_ID);
        getViewModel().setUserId(userId);
        getViewModel().setSecurityId(securityId);
        if (getViewModel().isSelf()) {
            getDataBinding().flAdd.setVisibility(View.VISIBLE);
        }
        showEmptyLoading();
        getViewModel().requestProfileInfo();
        getViewModel().refreshHistory();
        // 去掉recyclerview加载动画，否则点赞刷新时闪
        ((SimpleItemAnimator) getDataBinding().recyclerview.getItemAnimator()).setSupportsChangeAnimations(false);
        adapter = new BaseBinderAdapter();
        adapter.addItemBinder(MomentListItemBean.class, new BaseDataBindingItemBinder<MomentListItemBean, SocialItemUserDynamicBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.social_item_user_dynamic;
            }

            @Override
            protected void bind(BinderDataBindingHolder<SocialItemUserDynamicBinding> holder, SocialItemUserDynamicBinding binding, MomentListItemBean item) {
                binding.setItem(item);
                binding.setCallback(UserDynamicActivity.this);
                binding.stvContent.setContent(item.content);
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {
                addChildClickViewIds(R.id.iv_more);
            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<SocialItemUserDynamicBinding> holder, @NonNull View view, MomentListItemBean data, int position) {
                super.onChildClick(holder, view, data, position);
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                if (view.getId() == R.id.iv_more) {
                    if (getViewModel().isSelf()) {
                        showVisibleDialog(data);
                    } else {
                        showReportDialog(data);
                    }

                }
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<SocialItemUserDynamicBinding> holder, @NonNull View view, MomentListItemBean data, int position) {
                super.onClick(holder, view, data, position);
                if (MultiClickUtil.isMultiClick()) return;
                SocialPageRouter.jumpToDynamicDetailActivity(UserDynamicActivity.this, data.momentId, false, true,
                    getViewModel().isSelf() ? SocialConstants.SOURCE_SOCIAL_DYNAMIC_SELF : SocialConstants.SOURCE_SOCIAL_DYNAMIC_FRIEND);
                int length = 0;
                if (!TextUtils.isEmpty(data.content)) {
                    length = data.content.length();
                }
                int pageSource = TextUtils.equals(AccountHelper.getInstance().getUserId(), getViewModel().getUserId()) ? 3 : 2;
            }
        });
        getDataBinding().recyclerview.setLayoutManager(new LinearLayoutManager(UserDynamicActivity.this));
        getDataBinding().recyclerview.setAdapter(adapter);

        getDataBinding().tvTitle.setAlpha(0);
        getDataBinding().clTitle.setBackgroundColor(0);
        QMUIStatusBarHelper.setStatusBarDarkMode(UserDynamicActivity.this);

        getDataBinding().refreshLayout.setEnableRefresh(false);
        getDataBinding().refreshLayout.setEnableOverScrollDrag(true);
        getDataBinding().refreshLayout.setOnMultiListener(new SimpleMultiListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                refreshLayout.finishRefresh(false);
            }

            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                getViewModel().getMoreHistory();
            }

            @Override
            public void onHeaderMoving(RefreshHeader header, boolean isDragging, float percent, int offset, int headerHeight, int maxDragHeight) {
                mOffset = offset / 3;
                getDataBinding().flAvatarBg.setTranslationY(mOffset - mScrollY);
                getDataBinding().flAvatarBg.setScaleX(1 + (1.0f * mOffset / h));
                getDataBinding().flAvatarBg.setScaleY(1 + (1.0f * mOffset / h));
                getDataBinding().clTitle.setAlpha(1 - Math.min(percent, 1));
            }
        });

        getDataBinding().scrollView.setOnScrollChangeListener(new View.OnScrollChangeListener() {
            private int lastScrollY = 0;
            private final int color = ContextCompat.getColor(getApplicationContext(), R.color.color_white) & 0x00ffffff;

            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                if (lastScrollY < h) {
                    scrollY = Math.min(h, scrollY);
                    mScrollY = Math.min(scrollY, h);
                    getDataBinding().flAvatarBg.setTranslationY(mOffset - mScrollY);
                    float alpha = 1f * mScrollY / h;
                    getDataBinding().tvTitle.setAlpha(alpha);
                    getDataBinding().clTitle.setBackgroundColor(((255 * mScrollY / h) << 24) | color);
                    if (alpha > 0.5) {
                        getDataBinding().ivBack.setImageResource(R.drawable.common_ic_black_back);
                        QMUIStatusBarHelper.setStatusBarLightMode(UserDynamicActivity.this);

                        if (getDataBinding().llNotify.getBackground() != null) {
                            getDataBinding().llNotify.setBackgroundResource(R.drawable.social_bg_notify2);
                        }
                        getDataBinding().tvNotifyCount.setTextColor(ContextCompat.getColor(getApplicationContext(), R.color.common_color_292929));
                        getDataBinding().ivNotify.setImageResource(R.mipmap.social_notify2);
                        isNotifyDark = true;
                    } else {
                        getDataBinding().ivBack.setImageResource(R.drawable.common_ic_white_back);
                        QMUIStatusBarHelper.setStatusBarDarkMode(UserDynamicActivity.this);

                        if (getDataBinding().llNotify.getBackground() != null) {
                            getDataBinding().llNotify.setBackgroundResource(R.drawable.social_bg_notify);
                        }
                        getDataBinding().tvNotifyCount.setTextColor(ContextCompat.getColor(getApplicationContext(), R.color.color_white));
                        getDataBinding().ivNotify.setImageResource(R.mipmap.social_notify);
                        isNotifyDark = false;
                    }
                }
                lastScrollY = scrollY;
            }
        });

        getDataBinding().tvIntro.setExpandableListener(new SimpleExpandableTextView.ExpandableListener() {
            @Override
            public void showExpandable(boolean showExpandable) {
                getDataBinding().flOpen.setVisibility(showExpandable ? View.VISIBLE : View.GONE);
            }

            @Override
            public void onExpandableChange(boolean expanded) {
                getDataBinding().tvExpand.setText(expanded ? getResources().getString(R.string.social_close) : getResources().getString(R.string.social_expand));
                getDataBinding().ivArrow.setImageResource(expanded ? R.drawable.social_ic_arrow_up : R.drawable.social_ic_arrow_down);
            }
        });
        getDataBinding().flOpen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (MultiClickUtil.isMultiClick()) {
                    return;
                }
                getDataBinding().tvIntro.doAction();
            }
        });

//        getDataBinding().recyclerview.addOnScrollListener(new RecyclerView.OnScrollListener() {
//            @Override
//            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
//                super.onScrollStateChanged(recyclerView, newState);
//                if (LList.isEmpty(adapter.getData())) {
//                    return;
//                }
//                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
//                    int lastVisibleItemPosition = ((LinearLayoutManager) getDataBinding().recyclerview.getLayoutManager()).findLastVisibleItemPosition();
//                    if (lastVisibleItemPosition > mPreLastPosition) {
//                        try {
//                            List<String> exposureIds = new ArrayList<>(lastVisibleItemPosition - mPreLastPosition + 1);
//                            List<String> exposureImpIds = new ArrayList<>(lastVisibleItemPosition - mPreLastPosition + 1);
//                            for (int i = mPreLastPosition; i <= lastVisibleItemPosition; i++) {
//                                MomentListItemBean bean = (MomentListItemBean)adapter.getData().get(i);
//                                exposureIds.add(bean.momentId);
//                                exposureImpIds.add(bean.impId);
//                            }
//                            exposureSync(exposureIds, exposureImpIds);
//                            mPreLastPosition = lastVisibleItemPosition;
//                        } catch (Exception e) {
//                            TLog.error(TAG, "Point error : %s", e.toString());
//                        }
//                    }
//
//                }
////                if (newState == RecyclerView.SCROLL_STATE_DRAGGING || newState == RecyclerView.SCROLL_STATE_IDLE) {
////                    int headerCount = adapter.getHeaderLayoutCount();
////                    int firstVisibleItemPosition = ((LinearLayoutManager) getDataBinding().recyclerview.getLayoutManager()).findFirstVisibleItemPosition() - headerCount;
////                    if (firstVisibleItemPosition < 0) {
////                        firstVisibleItemPosition = 0;
////                    }
////                    int lastVisibleItemPosition = ((LinearLayoutManager) getDataBinding().recyclerview.getLayoutManager()).findLastVisibleItemPosition() - headerCount;
////                    if (lastVisibleItemPosition < 0) {
////                        lastVisibleItemPosition = 0;
////                    }
////                    int dataFirstPosition = getFirstMomentVisiblePosition(firstVisibleItemPosition, lastVisibleItemPosition);
////                    int dataLastPosition = getLastMomentVisiblePosition(firstVisibleItemPosition, lastVisibleItemPosition);
////                    if (dataFirstPosition >= 0) {
////                        View view = ((LinearLayoutManager) getDataBinding().recyclerview.getLayoutManager()).findViewByPosition(dataFirstPosition + headerCount);
////                        if (!((view.getTop() < 0 && Math.abs(view.getTop()) < view.getMeasuredHeight() * 1.f / 3) || view.getTop() > 0)) {
////                            dataFirstPosition++;
////                            if (dataFirstPosition >= adapter.getData().size()) {
////                                dataFirstPosition = -1;
////                            }
////                        }
////                    }
////                    if (dataLastPosition >= 0) {
////                        View view = ((LinearLayoutManager) getDataBinding().recyclerview.getLayoutManager()).findViewByPosition(dataLastPosition + headerCount);
////                        if (!(view.getBottom() - getDataBinding().recyclerview.getMeasuredHeight() <= view.getMeasuredHeight() * 1.f / 3)) {
////                            dataLastPosition--;
////                        }
////                    }
////
////                    if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
////                        mPreFirstPosition = dataFirstPosition;
////                        mPreLastPosition = dataLastPosition;
////                    } else if (newState == RecyclerView.SCROLL_STATE_IDLE) {
//////                        if (mSocialHandler != null) {
//////                            mSocialHandler.removeCallbacksAndMessages(null);
//////                        }
////                        mIdleFirstPosition = dataFirstPosition;
////                        mIdleLastPosition = dataLastPosition;
////                        if (mPreLastPosition < 0 || mPreFirstPosition < 0 || mIdleLastPosition < 0 || mIdleFirstPosition < 0) {
////                            return;
////                        }
////                        //feed流，阅读埋点，本期暂时去掉
//////                        List<String> ids = new ArrayList<>(dataLastPosition - dataFirstPosition + 1);
//////                        for (int i = dataFirstPosition; i <= dataLastPosition; i++) {
//////                            ids.add(((MomentListItemBean)listAdapter.getData().get(i)).momentId);
//////                        }
//////                        readSync(ids);
////                        if (!LList.isEmpty(adapter.getData()) &&
////                                mPreFirstPosition == 0 &&
////                                mIdleLastPosition == 0 &&
////                                mIdleFirstPosition == 0 &&
////                                mPreLastPosition == 0) {
////                            List<String> exposureIds = new ArrayList<>(1);
////                            List<String> exposureImpIds = new ArrayList<>(1);
////                            MomentListItemBean bean = (MomentListItemBean) adapter.getData().get(0);
////                            exposureIds.add(bean.momentId);
////                            exposureImpIds.add(bean.impId);
////                            exposureSync(exposureIds, exposureImpIds);
////                        }
////                        if (Math.abs(mPreFirstPosition - mIdleLastPosition) != Math.abs(mIdleFirstPosition - mPreLastPosition)) {
////                            boolean scrollToBottom = Math.abs(mPreFirstPosition - mIdleLastPosition) > Math.abs(mIdleFirstPosition - mPreLastPosition);
////                            int first;
////                            int last;
////                            try {
////                                if (scrollToBottom) {
////                                    first = mPreFirstPosition;
////                                    last = mIdleLastPosition;
////                                } else {
////                                    first = mIdleFirstPosition;
////                                    last = mPreLastPosition;
////                                }
////                                List<String> exposureIds = new ArrayList<>(last - first + 1);
////                                List<String> exposureImpIds = new ArrayList<>(last - first + 1);
////                                for (int i = first; i <= last; i++) {
////                                    MomentListItemBean bean = (MomentListItemBean) adapter.getData().get(i);
////                                    exposureIds.add(bean.momentId);
////                                    exposureImpIds.add(bean.impId);
////                                }
////                                exposureSync(exposureIds, exposureImpIds);
////                            } catch (Exception e) {
////                                TLog.error(TAG, "Point error : %s", e.toString());
////                            }
////                        }
////                    }
////
////                }
//            }
//        });
    }

    private void showSendLikeDialog(Context context, int limitNum) {
        likeBuilder = new CommonBindingDialog.Builder<>(context, R.layout.social_dialog_user_dynamic_like);
        likeBuilder.setCanceledOnTouchOutside(true);
        likeBuilder.setPadding(39, 39);
        likeBuilder.getBinding().ivAvatar.load(getViewModel().getInfoModel().tinyAvatar);
        QMUIKeyboardHelper.showKeyboard(likeBuilder.getBinding().etText, true);
        InputFilter[] inputFilters = new InputFilter[1];
        LengthNoticeFilter filter = new LengthNoticeFilter(30);
        inputFilters[0] = filter;
        likeBuilder.getBinding().etText.setFilters(inputFilters);
        if (limitNum == 1) {
            String content = getResources().getString(R.string.social_dynamic_send_like_send_tip_1);
            SpannableString spannableString = new SpannableString(content);
            spannableString.setSpan(new AbsoluteSizeSpan(18, true), content.indexOf("1"), content.indexOf("1") + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            likeBuilder.getBinding().tvNum.setText(spannableString);
        } else if (limitNum == 2) {
            String content = getResources().getString(R.string.social_dynamic_send_like_send_tip_2);
            SpannableString spannableString = new SpannableString(content);
            spannableString.setSpan(new AbsoluteSizeSpan(18, true), content.indexOf("2"), content.indexOf("2") + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            likeBuilder.getBinding().tvNum.setText(spannableString);
        } else {
            String content = getResources().getString(R.string.social_dynamic_send_like_send_tip_3);
            SpannableString spannableString = new SpannableString(content);
            spannableString.setSpan(new AbsoluteSizeSpan(18, true), content.indexOf("3"), content.indexOf("3") + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            likeBuilder.getBinding().tvNum.setText(spannableString);
        }
        likeBuilder.getBinding().etText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                likeBuilder.getBinding().tvCount.setText(String.valueOf((30 - s.length())));
            }
        });
        likeBuilder.getBinding().btnCancel.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                likeBuilder.getDialog().dismiss();
            }
        });
        likeBuilder.getBinding().btnSendLike.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                if (TextUtils.isEmpty(likeBuilder.getBinding().etText.getText().toString())) {
                    T.ss(getResources().getString(R.string.social_dynamic_send_like_send_content_empty_tip));
                    return;
                }
                getViewModel().sendLike(likeBuilder.getBinding().etText.getText().toString());
            }
        });
        likeBuilder.createDialog().show();
    }


    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(UserDynamicActivity.this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void toNotify() {
        SocialNotifyActivity.jumpToSocialNotifyActivity(this, 0);
    }

    @Override
    public void clickLike() {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        if (!checkFormalUser()) return;
        if (checkUserCommunityLocked()) return;
        if (checkUserInfoPerfectLocked()) return;
        UserProfileInfoModel infoModel = getViewModel().getInfoModel();
        switch (infoModel.knockStatus) {
            case SocialConstants.STATUS_SOCIAL_KNOCK_GAY:
                T.ss(R.string.social_dynamic_send_like_ban_gay);
                break;
            case SocialConstants.STATUS_SOCIAL_KNOCK_NO_FRIEND_AGREE:
            case SocialConstants.STATUS_SOCIAL_KNOCK_FRIEND:
                MePageRouter.jumpToInfoPreviewActivity(this, getViewModel().getUserId(), "", PageSource.NONE, "", "", "", getViewModel().getSecurityId(), "",false);
                break;
            case SocialConstants.STATUS_SOCIAL_KNOCK_FRIEND_BAN:
                T.ss(R.string.social_dynamic_send_like_friend_ban);
                break;
            case SocialConstants.STATUS_SOCIAL_KNOCK_NO_FRIEND_SEND:
                T.ss(R.string.social_dynamic_send_like_send_end);
                break;
            case SocialConstants.STATUS_SOCIAL_KNOCK_NO_FRIEND_NO_SEND:
                boolean matchHide = TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_INVISIBLE), "1");
                if (matchHide) {
                    showHidingDialog();
                    return;
                }
                if (infoModel.interestLimitNum == 0) {
                    T.ss(R.string.social_dynamic_send_like_no_change);
                    return;
                }
                showSendLikeDialog(UserDynamicActivity.this, infoModel.interestLimitNum);
                break;
            default:
                break;
        }

    }

    public void showVisibleDialog(MomentListItemBean data) {

        getViewModel().setTempData(data);
        CommonSystemBottomDialog.Builder builder = new CommonSystemBottomDialog.Builder(this)
            .setCancelContent(getString(R.string.social_dynamic_delete))
            .setCancelColorRed(true)
            .setOnBottomItemClickListener(new CommonSystemBottomDialog.OnBottomItemClickListener() {
                @Override
                public void onBottomItemClick(Dialog dialog, View view, int pos) {
                    dialog.dismiss();
                    switch (pos) {
                        case 0:
                            getViewModel().updateVisible(SocialConstants.DYNAMIC_VISIBLE_ALL, data.momentId);
                            break;
                        case 1:
                            getViewModel().updateVisible(SocialConstants.DYNAMIC_VISIBLE_SELF, data.momentId);
                            break;
                        case 2:
                            getViewModel().updateVisible(SocialConstants.DYNAMIC_VISIBLE_FRIEND, data.momentId);
                            break;
                        default:
                            break;
                    }
                }

                @Override
                public void onCancelClick(Dialog dialog) {
                    showDeleteDialog();
                }
            });
        boolean isCircle = !TextUtils.isEmpty(data.circleId);
        if (!isCircle) {
            builder.setData(getString(R.string.social_dynamic_all_visible), getString(R.string.social_dynamic_self_visible), getString(R.string.social_dynamic_friend_visible));
        }
        builder.create().show();
    }

    private void showDeleteDialog() {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
            .setTitle(getString(R.string.social_dynamic_delete_content))
            .setPositiveText(getResources().getString(R.string.social_cancel_dialog_confirm))
            .setNegativeText(getResources().getString(R.string.social_cancel_dialog_cancel))
            .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                @Override
                public void onPositiveClick(Dialog dialog, View view) {
                    getViewModel().deleteDynamic();
                }

                @Override
                public void onNegativeClick(Dialog dialog, View view) {

                }
            });
        builder.create().show();
    }

    public void showReportDialog(MomentListItemBean data) {
        CommonSystemBottomDialog.Builder builder = new CommonSystemBottomDialog.Builder(this)
            .setCancelContent(getString(R.string.social_report_dismiss))
            .setData(getString(R.string.social_report))
            .setOnBottomItemClickListener(new CommonSystemBottomDialog.OnBottomItemClickListener() {
                @Override
                public void onBottomItemClick(Dialog dialog, View view, int pos) {
                    dialog.dismiss();
                    MePageRouter.jumpToUserReportActivity(UserDynamicActivity.this, getViewModel().getUserId(), -1, UserReportSource.SOURCE_SOCIAL_DYNAMIC, data.momentId, RequestCodeConstants.REQUEST_CODE_0);
                }

                @Override
                public void onCancelClick(Dialog dialog) {

                }
            });
        builder.create().show();
    }

    @Override
    public void addDynamic() {
        if (MultiClickUtil.isMultiClick()) return;
        if (!checkFormalUser()) return;
        if (checkUserCommunityLocked()) return;
        if (checkUserInfoPerfectLocked()) return;
        SocialPageRouter.jumpToPublishActivity(this, "", Constants.PUBLISH_TYPE_USER_DYNAMIC);
    }

    @Override
    public void clickAvatar() {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        if (!checkFormalUser()) return;
        if (checkUserCommunityLocked()) return;
        if (checkUserInfoPerfectLocked()) return;
//        if (getViewModel().isSelf()) {
//            MePageRouter.jumpToInfoPreviewActivity(this, getViewModel().getUserId(),"", PageSource.NONE,"","","",getViewModel().getSecurityId());
//        } else {
//            UserProfileInfoModel infoModel = getViewModel().getInfoModel();
//            if (infoModel.knockStatus == SocialConstants.STATUS_SOCIAL_KNOCK_NO_FRIEND_AGREE || infoModel.knockStatus == SocialConstants.STATUS_SOCIAL_KNOCK_FRIEND) {
//                MePageRouter.jumpToInfoPreviewActivity(this, getViewModel().getUserId(), "",PageSource.NONE,"","","",getViewModel().getSecurityId());
//            }
//        }
    }

    @Override
    public void jumpToUserDynamic(MomentListItemBean bean) {

    }

    @Override
    public void jumpToDynamicDetail(MomentListItemBean bean, boolean showComment) {
        if (MultiClickUtil.isMultiClick()) return;
        SocialPageRouter.jumpToDynamicDetailActivity(this, bean.momentId, showComment, true,
            getViewModel().isSelf() ? SocialConstants.SOURCE_SOCIAL_DYNAMIC_SELF : SocialConstants.SOURCE_SOCIAL_DYNAMIC_FRIEND);
    }

    @Override
    public void toggleApplaud(MomentListItemBean bean) {
        if (MultiClickUtil.isMultiClick()) return;
        if (!checkFormalUser()) return;
        if (checkUserCommunityLocked()) return;
        if (checkUserInfoPerfectLocked()) return;
        int position = adapter.getItemPosition(bean);

        if (bean.thumbStatus == 1) {// 取消点赞
            bean.thumbStatus = 0;
            bean.thumbCount -= 1;
            adapter.notifyItemChanged(position);

            getViewModel().momentThumbCancel(bean.momentId, new ICommonCallback() {
                @Override
                public void onSuccess() {
                }

                @Override
                public void onFail(int code, String msg) {
                    if (code == Constants.SOCIAL_CONTENT_DELETED_ERROR_CODE) {
                        T.ss(R.string.social_comment_deleted);
                        RxBus.getInstance().post(bean, Constants.SOCIAL_MOMENT_DELETED);
                    }
                }
            });
        } else {// 点赞
            bean.thumbStatus = 1;
            bean.thumbCount += 1;
            adapter.notifyItemChanged(position);

            getViewModel().momentThumbAdd(bean.momentId, new ICommonCallback() {
                @Override
                public void onSuccess() {
                }

                @Override
                public void onFail(int code, String msg) {
                    if (code == Constants.SOCIAL_CONTENT_DELETED_ERROR_CODE) {
                        T.ss(R.string.social_comment_deleted);
                        RxBus.getInstance().post(bean, Constants.SOCIAL_MOMENT_DELETED);
                    }
                }
            });
        }
    }

    private boolean checkFormalUser() {
        if (!AccountHelper.getInstance().isFormalUser()) {
            showUnFormalUserDialog();
            return false;
        }
        return true;
    }

    /**
     * 非正式用户dialog
     */
    private void showUnFormalUserDialog() {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
            .setTitle(getString(R.string.social_un_formal_user_title))
            .setPositiveText(getResources().getString(R.string.social_jump_to_finish))
            .setNegativeText(getResources().getString(R.string.social_just_have_a_look))
            .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                @Override
                public void onPositiveClick(Dialog dialog, View view) {
                    // 跳转到我的tab
                    RxBus.getInstance().post("switch", Constants.POST_TAG_MATCHING_REQUIREMENT);
                }

                @Override
                public void onNegativeClick(Dialog dialog, View view) {

                }
            });
        builder.create().show();
    }

    private boolean checkUserCommunityLocked() {
        if (AccountHelper.getInstance().isUserCommunityLocked()) {
            T.ss(R.string.social_function_locked);
            return true;
        }
        return false;
    }

    /**
     * 判断用户是否被锁定
     */
    private boolean checkUserInfoPerfectLocked() {
        return getViewModel().userProfileLocked();
    }

    /**
     * 关闭隐身弹框
     */
    private void showHidingDialog() {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
            .setTitle(getString(R.string.social_hiding_title))
            .setPositiveText(getResources().getString(R.string.social_close_hiding))
            .setNegativeText(getResources().getString(R.string.social_just_have_a_look))
            .setAutoDismiss(false)
            .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                @Override
                public void onPositiveClick(Dialog dialog, View view) {
                    getViewModel().requestMatchHide();
                }

                @Override
                public void onNegativeClick(Dialog dialog, View view) {
                    dialog.dismiss();
                }
            });
        hideDialog = builder.create();
        hideDialog.show();
    }

    private int getFirstMomentVisiblePosition(int firstPosition, int lastPosition) {
        if (firstPosition >= adapter.getData().size()) {
            return -1;
        }
        if (adapter.getData().get(firstPosition) instanceof MomentListItemBean) {
            return firstPosition;
        } else {
            firstPosition++;
            if (firstPosition > lastPosition) {
                return -1;
            }
            return getFirstMomentVisiblePosition(firstPosition, lastPosition);
        }
    }

    private int getLastMomentVisiblePosition(int firstPosition, int lastPosition) {
        if (lastPosition >= adapter.getData().size()) {
            return -1;
        }
        if (adapter.getData().get(lastPosition) instanceof MomentListItemBean) {
            return lastPosition;
        } else {
            lastPosition--;
            if (firstPosition > lastPosition) {
                return -1;
            }
            return getLastMomentVisiblePosition(firstPosition, lastPosition);
        }
    }

    private void exposureSync(List<String> readIds, List<String> exposureImpIds) {
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder exposureImpStringBuilder = new StringBuilder();
        for (int i = 0; i < readIds.size(); i++) {
            String id = readIds.get(i);
            if (!mExposureIds.contains(id)) {
                mExposureIds.add(id);
                stringBuilder.append(id);
                stringBuilder.append(",");
                exposureImpStringBuilder.append(exposureImpIds.get(i));
                exposureImpStringBuilder.append(",");
            }
        }

        if (!TextUtils.isEmpty(stringBuilder.toString())) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            exposureImpStringBuilder.deleteCharAt(exposureImpStringBuilder.length() - 1);
            int pageSource = TextUtils.equals(AccountHelper.getInstance().getUserId(), getViewModel().getUserId()) ? 3 : 2;
        }
    }
}