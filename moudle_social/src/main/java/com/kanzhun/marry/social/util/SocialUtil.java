package com.kanzhun.marry.social.util;

import android.text.TextUtils;

import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.CommentDraft;
import com.kanzhun.marry.social.callback.CommentDraftCallback;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/7/25
 */
public class SocialUtil {
    public static void findCommentDraft(String id, CommentDraftCallback callback) {
        Observable.just(id).map(new Function<String, String>() {
            @Override
            public String apply(String url) {
                String content = "";
                CommentDraft commentDraft = ServiceManager.getInstance().getSocialService().findCommentDraftById(id);
                if (commentDraft != null && !TextUtils.isEmpty(commentDraft.getContent())) {
                    content = commentDraft.getContent();
                }
                return content;
            }
        }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<String>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(String content) {
                        if (callback != null) {
                            callback.content(content);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        if (callback != null) {
                            callback.content(null);
                        }
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }
}
