package com.kanzhun.marry.social.api.model;

import com.kanzhun.foundation.api.bean.PictureListItemBean;

import java.util.List;

/**
 * Created by Cha<PERSON><PERSON>iangpeng
 * Date: 2022/7/14
 */
public class UserDynamicDetailModel {
    /**
     * "momentId": "zzzzzzz", // 动态id
     * "userId": "xxxxx", // 用户id
     * "avatar":"https://xxxxxxx", // 头像地址
     * "tinyAvatar":"https://xxxxxxx", // 缩略图头像地址
     * "liveVideo":"https://xxxxxxx", // 头像 livePhoto的视频 地址 可空
     * "nickName":"张三", // 昵称
     * "createTime": 1642374343634, // 创建时间
     * "content": "走着走着", // 内容
     * "pictures": [{ // 图片列表
     * "url": "https://orange-qa.weizhipin.com/api/media/download/TsH7Q5gxELKzib1FaJ__S3_zPKZIGPksyGkh.jpeg",
     * "width": 1920,
     * "height": 1080
     * }],
     * "replyCount": 1123, // 评论数量
     * "thumbCount": 122, // 点赞数量
     * "thumbStatus": 1, // 是否已赞，1-是，0-否
     * "ipProvince": "北京", // 发布时的省或直辖市
     * "visibleType": 1, // 可见范围，1-全部可见，2-好友可见，3-仅自己可见   其他人查看时，该字段不返回
     * "joinStatus": 1 // 圈子加入状态 1 未加入 2 已加入，只在动态详情页接口有
     * "circleId": "xxxxxxx", // 圈子id，只在动态详情页接口有，非圈子动态不返回该字段
     * "circleName": "zzzz", // 圈子名称，只在动态详情页接口有，非圈子动态不返回该字段
     */

    public String momentId;
    public String userId;
    public String avatar;
    public String tinyAvatar;
    public String liveVideo;
    public String nickName;
    public long createTime;
    public String content;
    public String ipLocation;
    public int replyCount;
    public int thumbCount;
    public int thumbStatus;
    public int visibleType;
    public List<PictureListItemBean> pictures;
    public int joinStatus;
    public String circleId;
    public String circleName;
    public String circleAvatar; //圈子头像
    public String circleIntro;//圈子简介
    public int circleUserNum;// 圈子成员数量
    public int gender;

}
