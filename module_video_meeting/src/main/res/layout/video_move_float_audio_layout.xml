<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="contact"
            type="com.kanzhun.foundation.model.Contact" />
    </data>

    <FrameLayout
        android:id="@+id/small_size_frame_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="@dimen/video_meeting_window_audio_width"
            android:layout_height="@dimen/video_meeting_window_audio_height"
            android:layout_gravity="center"
            android:layout_margin="@dimen/video_option_elevation_12"
            android:background="@drawable/common_bg_conor_12_color_white"
            android:elevation="@dimen/video_option_elevation_12"
            android:gravity="center"
            android:orientation="vertical">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/ov_avatar"
                android:layout_width="46dp"
                android:layout_height="46dp"
                android:layout_marginTop="12dp"
                app:common_circle="true"
                app:common_placeholder="@mipmap/common_default_avatar"
                app:imageUrl="@{contact.avatar}" />

            <com.kanzhun.marry.videomeeting.view.VideoChatClockView
                android:id="@+id/tv_clock"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="5dp"
                android:textColor="@color/video_00c782"
                android:textSize="@dimen/common_text_sp_14"
                tools:text="12:54" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:textColor="@color/video_00c782"
                android:textSize="@dimen/common_text_sp_14"
                tools:text="呼叫中" />
        </LinearLayout>
    </FrameLayout>
</layout>