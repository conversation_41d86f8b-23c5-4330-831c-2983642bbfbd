package com.kanzhun.http.transformer;

import com.kanzhun.http.BuildConfig;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.error.LoginException;
import com.kanzhun.http.error.ResponseException;
import com.kanzhun.http.error.SecurityNoticeException;
import com.kanzhun.utils.L;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/24
 */
public class SimpleBaseObserver implements Observer<Boolean> {


    private final SimpleRequestCallback callback;

    public SimpleBaseObserver(SimpleRequestCallback callback) {
        this.callback = callback;
    }

    @Override
    public void onSubscribe(@NonNull Disposable d) {
        if (callback != null) {
            callback.onStart(d);
        }
    }

    @Override
    public void onNext(@androidx.annotation.NonNull Boolean aBoolean) {
        if (callback != null) {
            callback.onSuccess();
        }
    }

    @Override
    public void onError(@NonNull Throwable e) {
        if (e instanceof LoginException) {
            onLoginError();
            onComplete();
            return;
        }
        if (e instanceof SecurityNoticeException) {
            onSecurityNoticeError((SecurityNoticeException) e);
//            onComplete();
//            return;
        }
        if (e instanceof Exception && callback != null) {
//            if(BuildConfig.DEBUG){
//                L.e("fatal",e.getMessage());
//                for (int i = 0; i < e.fillInStackTrace().getStackTrace().length; i++) {
//                    L.e("fatal",e.fillInStackTrace().getStackTrace()[i].toString()+"");
//                }
//            }
            ErrorReason errorReason;
            if (e instanceof ResponseException) {
                ResponseException exception = (ResponseException) e;
                errorReason = new ErrorReason(exception.getErrCode(), exception.getErrMsg(), exception);
            } else {
                errorReason = new ErrorReason(ResponseException.ERROR_UNKNOWN_CODE, ErrorReason.ERROR_UNKNOWN, (Exception) e);
            }
            callback.dealFail(errorReason);
            callback.showFailed(errorReason);
        }
        onComplete();
    }

    @Override
    public void onComplete() {
        if (callback != null) {
            callback.onComplete();
        }
    }

    /**
     * 登录异常
     */
    public void onLoginError() {
        BaseRequestCallback.OnLoginError onLoginError = BaseObserver.gOnLoginError;
        if (onLoginError != null) {
            onLoginError.onLoginError();
        }
    }

    /**
     * 安全处置
     */
    public void onSecurityNoticeError(SecurityNoticeException e) {
        BaseRequestCallback.OnSecurityNoticeError onSecurityNoticeError = BaseObserver.gOnSecurityNoticeError;
        if (onSecurityNoticeError != null) {
            onSecurityNoticeError.onSecurityNoticeError(e);
        }
    }
}
