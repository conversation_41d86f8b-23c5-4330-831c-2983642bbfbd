package com.kanzhun.http.error;

import com.google.gson.JsonParseException;
import com.tencent.bugly.crashreport.CrashReport;

import org.json.JSONException;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

import retrofit2.HttpException;


/**
 * Created by Cha<PERSON>Jiangpeng
 * Date: 2022/2/24
 */
public class ResponseException extends Exception {
    private static final long serialVersionUID = 4445372052583535987L;

    public static final int ERROR_UNKNOWN_CODE = -1000;
    public static final int ERROR_NETWORK_FAILED_CODE = -1001;
    public static final int ERROR_JSON_CODE = -1002;
    public static final int ERROR_SERVER_CODE = -1003;


    private int errCode;
    private String errMsg;

    public ResponseException(int errCode, String errMsg, String message) {
        super(message);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public int getErrCode() {
        return errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }


    public static ResponseException handleException(Throwable e) {
        ResponseException exception;
        if (e instanceof JsonParseException || e instanceof JSONException) {
            exception = new ResponseException(ERROR_JSON_CODE, ErrorReason.ERROR_JSON, e.getMessage());
        } else if (e instanceof ConnectException || e instanceof UnknownHostException || e instanceof SocketTimeoutException) {
            if (e instanceof SocketTimeoutException) {
                exception = new ResponseException(ERROR_NETWORK_FAILED_CODE, ErrorReason.ERROR_NETWORK_TIME_FAILED, e.getMessage());
            } else {
                exception = new ResponseException(ERROR_NETWORK_FAILED_CODE, ErrorReason.ERROR_NETWORK_FAILED, e.getMessage());
            }
        } else if (e instanceof HttpException) {
            exception = new ResponseException(ERROR_SERVER_CODE, ErrorReason.ERROR_SERVER_FAILED, e.getMessage());
        } else {
            exception = new ResponseException(ERROR_UNKNOWN_CODE, ErrorReason.ERROR_UNKNOWN, e.getMessage());
        }
        CrashReport.postCatchedException(e);
        return exception;
    }
}
