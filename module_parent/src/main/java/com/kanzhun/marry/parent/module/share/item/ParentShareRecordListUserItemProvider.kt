package com.kanzhun.marry.parent.module.share.item

import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ext.toShareTime
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.kotlin.ktx.toGenderString
import com.kanzhun.foundation.kotlin.ktx.toHeightString
import com.kanzhun.foundation.kotlin.ktx.toSimpleBirthYear
import com.kanzhun.foundation.router.ParentPageRouter
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.bean.ChildUser
import com.kanzhun.marry.parent.bean.getShareProgressString
import com.kanzhun.marry.parent.databinding.ParentShareRecordListItemBinding
import com.kanzhun.marry.parent.module.main.home.getIdentityString
import com.kanzhun.marry.parent.module.main.home.setChildBaseInfo1
import com.kanzhun.marry.parent.module.share.viewmodel.ParentShareRecordViewModel
import com.kanzhun.marry.parent.point.ParentPointReporter

class ParentShareRecordListUserItemProvider(val mViewModel: ParentShareRecordViewModel) : BaseItemProvider<BaseListItem, ParentShareRecordListItemBinding>() {
    override fun onBindItem(binding: ParentShareRecordListItemBinding, item: BaseListItem) {
        if (item is ChildUser) {
            binding.run {
                ivAvatar.load(item.tinyAvatar)
                tvGender.text = item.gender.toGenderString()
                tvBirth.text = item.birthday.toSimpleBirthYear()
                tvHeight.text = item.height.toHeightString()
                //基本信息
                rcvBaseInfo.setChildBaseInfo1(item)
                //认证信息
                tvIdentityInfo.textOrGone(item.getIdentityString(context))

                tvShareTime.text = R.string.parent_s_share.toResourceString(item.forwardTime.toShareTime())
                tvChildProgress.textOrGone(item.getShareProgressString())
                root.clickWithTrigger {
                    ParentPageRouter.jumpToParentPreviewUserInfo(it.context,item.userId,item.lid, PageSource.PARENT_SHARE_RECORD_FRAGMENT)
                }
                ParentPointReporter.reportParentShareListItemExpose(item,mViewModel.bindStatus)
            }
        }
    }


}