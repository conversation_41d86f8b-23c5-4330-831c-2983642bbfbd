package com.kanzhun.marry.parent.bean

import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.api.model.ProfileInfoModel


class ChildUser(val forwardTime: Long, //转发时间
                var isForwarded:Boolean,//是否被转发
                val lid:String? = "",
                val certInfo: ProfileInfoModel.CertInfo? = null,
                val childRelationStatus: Int //被转发孩子与其触达关系状态 1 看了对方 2 发送了喜欢 3 收到了喜欢 4 好友
) : ProfileInfoModel.BaseInfo(), BaseListItem {

    var mLocalItemType: Int = 0
    override fun getLocalItemType(): Int {
        return mLocalItemType
    }

}