package com.kanzhun.marry.parent.module.main.preformance

import android.view.LayoutInflater
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.kotlin.ext.liveEventObserve
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.kotlin.ktx.saveBoolean
import com.kanzhun.foundation.kotlin.ktx.userSp
import com.kanzhun.foundation.views.guideview.Component
import com.kanzhun.foundation.views.guideview.GuideBuilder
import com.kanzhun.marry.parent.databinding.ParentFirstShareChildGuideCoverBinding

/**
 * 分享后的引导蒙层（父母模式）
 */
const val KEY_PARENT_SHARE_GUIDE_COVER = "key_parent_share_guide_cover"

class ParentShareGuidePerformance(private val targetView: View?, val fragment: Fragment) : AbsPerformance() {

    //是否需要展示转发孩子后的引导蒙层
    private var needShowShareGuideCover = userSp().getBoolean(KEY_PARENT_SHARE_GUIDE_COVER, true)


    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        //监听事件
        if (needShowShareGuideCover) {
            observeLiveEventBus()
        }

    }


    private fun observeLiveEventBus() {
        fragment.liveEventObserve(LivedataKeyCommon.EVENT_KEY_PARENT_SHARE_ACTION) { _: String ->
            if (needShowShareGuideCover) {
                needShowShareGuideCover = false
                showGuideCover()
                userSp().saveBoolean(KEY_PARENT_SHARE_GUIDE_COVER, false)
            }
        }
    }

    private fun showGuideCover() {
        targetView?.run {
            GuideBuilder().setTargetView(targetView) //要引导的view
                .setAlpha(150) //透明度0-255
                .setAutoDismiss(true)
                .addComponent(ParentFirstShareGuideComponent())
                .setHighTargetPaddingLeft((-20).dpI)
                .setHighTargetPaddingRight((-20).dpI)
                .setHighTargetCorner(12.dpI)
                .createGuide()
                .show(fragment.requireActivity())
        }

    }


}

class ParentFirstShareGuideComponent : Component {
    override fun getView(inflater: LayoutInflater?): View {
        ParentFirstShareChildGuideCoverBinding.inflate(inflater!!).apply {
            return root
        }
    }

    override fun getAnchor(): Int = Component.ANCHOR_TOP

    override fun getFitPosition() = Component.FIT_CENTER

    override fun getXOffset(): Int = 0

    override fun getYOffset(): Int = 0

}