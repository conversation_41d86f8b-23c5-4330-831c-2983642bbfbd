package com.kanzhun.marry.parent.module.me.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.util.SecurityUtils
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.model.AgreementBean
import com.kanzhun.foundation.model.H5Model
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.parent.api.URLConfigParent
import com.kanzhun.marry.parent.point.ParentPointReporter
import com.kanzhun.utils.SettingBuilder

class ParentInviteOtherBindViewModel : BaseViewModel() {

    val errorLiveData = MutableLiveData<String>()
    val successLiveData = MutableLiveData<Boolean>()
    var agreementResult:MutableLiveData<AgreementBean?> = MutableLiveData()

    fun bindHandler(toString: String) {
        val params = mutableMapOf<String, Any?>()
        params["phoneEn"] = SecurityUtils.rc4Encrypt(toString, SettingBuilder.getInstance().getApmUidPassword())
        HttpExecutor.requestSimplePost(URLConfigParent.URL_PARENT_INVITE_BIND, params, object :
            SimpleRequestCallback(true) {
            override fun onSuccess() {
                successLiveData.postValue(true)
                reportClickEvent("点击发送邀请", "邀请成功")

            }

            override fun dealFail(reason: ErrorReason?) {
                reportClickEvent("点击发送邀请", reason?.errReason)
            }

        })
    }

    fun reportClickEvent(name: String, result: String? = "") {
        ParentPointReporter.reportBindParentClick(mSource, name, result)
    }

    fun getBindProtocolInfo() {
        val observable = RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java).getAgreementDetail(H5Model.URL_BIND_PROTOCOL.id)
        HttpExecutor.execute<AgreementBean>(observable, object : BaseRequestCallback<AgreementBean?>(false) {
            override fun onSuccess(data: AgreementBean?) {
                agreementResult.postValue(data)

            }

            override fun dealFail(reason: ErrorReason?) {

            }
        })
    }

}