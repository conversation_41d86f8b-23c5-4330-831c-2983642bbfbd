package com.kanzhun.marry.parent.module.main.home

import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.bean.ParentHomeDividerCard
import com.kanzhun.marry.parent.databinding.ParentHomeListTomorrowHintTitleBinding

class ParentHomeListTomorrowTitleProvider : BaseItemProvider<BaseListItem, ParentHomeListTomorrowHintTitleBinding>() {
    override fun onBindItem(binding: ParentHomeListTomorrowHintTitleBinding, item: BaseListItem) {
        if (item is ParentHomeDividerCard) {
            binding.run {
                tvTitle.text = R.string.parent_tomorrow_s.toResourceString(item.extraInfo?:"")
            }
        }
    }


}