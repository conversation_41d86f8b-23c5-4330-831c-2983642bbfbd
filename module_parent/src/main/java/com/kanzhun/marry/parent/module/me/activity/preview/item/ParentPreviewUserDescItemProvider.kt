package com.kanzhun.marry.parent.module.me.activity.preview.item

import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.marry.parent.bean.ParentPreviewUserItem
import com.kanzhun.marry.parent.databinding.ParentPreviewUserDescSectionBinding

class ParentPreviewUserDescItemProvider : BaseItemProvider<ParentPreviewUserItem, ParentPreviewUserDescSectionBinding>() {
    override fun onBindItem(binding: ParentPreviewUserDescSectionBinding, item: ParentPreviewUserItem) {
        item.userInfo.run {
            binding.tvDesc.text = item.userInfo.baseInfo.intro
        }
    }
}