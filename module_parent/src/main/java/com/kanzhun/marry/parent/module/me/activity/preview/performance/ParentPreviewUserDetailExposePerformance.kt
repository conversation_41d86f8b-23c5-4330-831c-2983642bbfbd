package com.kanzhun.marry.parent.module.me.activity.preview.performance

import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.kanzhun.common.kotlin.ui.recyclerview.CommonListAdapter
import com.kanzhun.foundation.utils.point.ListItemExposePerformance
import com.kanzhun.marry.parent.bean.ParentPreviewUserItem
import com.kanzhun.marry.parent.module.me.activity.preview.ParentPreviewUserInfoActivity
import com.kanzhun.marry.parent.module.me.activity.preview.item.ParentPreviewUserItemType
import com.kanzhun.marry.parent.point.ParentPointReporter


/**
 * 父母端预览个人信息曝光埋点相关逻辑
 */
class ParentPreviewUserDetailExposePerformance(val activity: ParentPreviewUserInfoActivity) : ListItemExposePerformance() {

    val adapter: CommonListAdapter<ParentPreviewUserItem> = activity.mBinding.recyclerview.adapter as CommonListAdapter<ParentPreviewUserItem>

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        activity.mViewModel.run {
            ParentPointReporter.reportUserDetailExpose(userId, mSource, lid,mSourceFrom)
        }
    }

    override fun getRecyclerView(): RecyclerView = activity.mBinding.recyclerview

    override fun onReportExpose(position: Int) {
        if (position >= 0 && position < adapter.itemCount) {
            val item = adapter.getItem(position)
            if(item.needReportPoint){
                activity.mViewModel.run {
                    if(item.type == ParentPreviewUserItemType.BASE_INFO){
                        ParentPointReporter.reportUserDetailModuleExpose(userId, mSource, lid,
                            type = item.itemName, from = mSourceFrom)
                        if(!item.userInfo.certInfo?.certTagList.isNullOrEmpty()){
                            ParentPointReporter.reportUserDetailModuleExpose(userId, mSource, lid,
                                type = "认证信息",from = mSourceFrom)
                        }

                    }else if(item.type == ParentPreviewUserItemType.MERRY_INFO){
                        if (!item.userInfo.baseInfo?.maritalStatusInfo.isNullOrBlank()
                            || item.userInfo.baseInfo.childbearingPreferenceInfo.isNullOrBlank()
                            ||!item.userInfo.baseInfo?.loveGoalInfo.isNullOrBlank()) {
                            ParentPointReporter.reportUserDetailModuleExpose(userId, mSource, lid,
                                type = "婚恋状况",from = mSourceFrom)
                        }
                        if (!item.userInfo.baseInfo?.familyDesc.isNullOrBlank()) {
                            ParentPointReporter.reportUserDetailModuleExpose(userId, mSource, lid,
                                type = "家庭介绍",from = mSourceFrom)
                        }
                    }else{
                        ParentPointReporter.reportUserDetailModuleExpose(userId, mSource, lid,
                            type = item.itemName,from = mSourceFrom)
                    }


                }
            }


        }
    }
}