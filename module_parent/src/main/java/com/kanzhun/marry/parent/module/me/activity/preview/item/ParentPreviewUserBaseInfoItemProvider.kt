package com.kanzhun.marry.parent.module.me.activity.preview.item

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.joinNotNull
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.kotlin.ktx.loadAvatarWithGender
import com.kanzhun.foundation.kotlin.ktx.toGenderString
import com.kanzhun.foundation.kotlin.ktx.toGenderString1
import com.kanzhun.foundation.kotlin.ktx.toHeightString
import com.kanzhun.foundation.kotlin.ktx.toSimpleBirthYear
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.model.profile.ext.buildAuthInfoList
import com.kanzhun.foundation.model.profile.ext.canShowCarInfo
import com.kanzhun.foundation.model.profile.ext.canShowHouseInfo
import com.kanzhun.foundation.model.profile.ext.canShowIncome
import com.kanzhun.foundation.model.profile.ext.getCurrentAddressString1
import com.kanzhun.foundation.model.profile.ext.getHometownString1
import com.kanzhun.foundation.model.profile.ext.getOriginAddressString1
import com.kanzhun.foundation.model.profile.ext.getShowCarInfo
import com.kanzhun.foundation.model.profile.ext.getShowHouseInfo
import com.kanzhun.foundation.model.profile.ext.getWorkShowInfo
import com.kanzhun.foundation.model.profile.ext.getWorkShowInfoWithCompany
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.bean.ParentPreviewUserItem
import com.kanzhun.marry.parent.databinding.ParentPreviewUserBaseInfoItemBinding
import com.kanzhun.marry.parent.module.main.home.setChildBaseInfo
import com.kanzhun.marry.parent.module.me.bean.ParentBaseInfoItem

class ParentPreviewUserBaseInfoItemProvider : BaseItemProvider<ParentPreviewUserItem, ParentPreviewUserBaseInfoItemBinding>() {
    override fun onBindItem(binding: ParentPreviewUserBaseInfoItemBinding, item: ParentPreviewUserItem) {
        item.userInfo.run {
            binding.ivAvatar.loadAvatarWithGender(baseInfo?.tinyAvatar, baseInfo?.gender ?: 1)
            binding.tvGender.text = baseInfo?.gender?.toGenderString()
            binding.tvBirth.text = baseInfo?.birthday.toSimpleBirthYear()
            val heightString = baseInfo?.height.toHeightString()
            binding.tvHeight.textOrGone(heightString)
            binding.divider12.visible(heightString.isNotBlank())
            binding.rcvBaseInfo.setChildBaseInfo(buildBaseInfoList1(), buildBaseInfoList2())

            if (this.certInfo?.certTagList.isNullOrEmpty()) {
                binding.llIdentity.gone()
            } else {
                binding.llIdentity.visible()
                binding.tvIdentityCount.text = "${this.baseInfo?.gender?.toGenderString1()}完成了${this.certInfo?.certPassCount}项信息真实认证"
                binding.rcvIdentity.adapter = PreviewAuthInfoAdapter().also {
                    it.setNewInstance(this.buildAuthInfoList())
                }
            }


        }
    }

    /**
     * 构造基本信息列表
     */
    private fun ProfileMetaModel.buildBaseInfoList1(): MutableList<ParentBaseInfoItem> {
        return mutableListOf<ParentBaseInfoItem>().also {
            //现居地
            baseInfo.getCurrentAddressString1().run {
                if (this.isNotBlank()) {
                    it.add(ParentBaseInfoItem(R.string.common_current_address.toResourceString(), this))
                }
            }
            //家乡
            baseInfo.getHometownString1().run {
                if (this.isNotBlank()) {
                    it.add(ParentBaseInfoItem(R.string.common_hometown.toResourceString(), this))
                }
            }
            //户口
            baseInfo.getOriginAddressString1().run {
                if (this.isNotBlank()) {
                    it.add(ParentBaseInfoItem(R.string.common_origin_address.toResourceString(), this))
                }
            }
            baseInfo?.ethnicity?.run {
                if (this.isNotBlank()) {
                    //民族
                    it.add(ParentBaseInfoItem(R.string.common_nation.toResourceString(), baseInfo?.ethnicity ?: ""))
                }
            }
            //房产
            if (baseInfo.canShowHouseInfo()) {
                it.add(ParentBaseInfoItem(R.string.common_house_property.toResourceString(), baseInfo.getShowHouseInfo()))
            }
            //车产
            if (baseInfo.canShowCarInfo()) {
                it.add(ParentBaseInfoItem(R.string.common_car_property.toResourceString(), baseInfo.getShowCarInfo()))
            }

        }
    }
}

private fun ProfileMetaModel.buildBaseInfoList2(): MutableList<ParentBaseInfoItem> {
    return mutableListOf<ParentBaseInfoItem>().also {

        //学历
        if (!baseInfo.degreeInfo.isNullOrBlank()) {
            it.add(ParentBaseInfoItem(R.string.common_education.toResourceString(), listOf(baseInfo.degreeInfo, baseInfo.school).joinNotNull("·"), isSingleLine = true))
        }
        //行业职业
        if (baseInfo.canShowCompanyName()) { //带公司
            baseInfo.getWorkShowInfoWithCompany().run {
                if (this.isNotBlank()) {
                    it.add(ParentBaseInfoItem(R.string.common_career.toResourceString(), this, isSingleLine = true))
                }
            }
        } else { //不带公司
            baseInfo.getWorkShowInfo().run {
                if (this.isNotBlank()) {
                    it.add(ParentBaseInfoItem(R.string.common_career.toResourceString(), this, isSingleLine = true))
                }
            }
        }
        //年薪
        if (baseInfo.canShowIncome()) {
            it.add(ParentBaseInfoItem(R.string.common_annual_salary.toResourceString(), baseInfo?.annualIncomeInfo ?: "", isSingleLine = true))
        }

    }
}


private fun ProfileInfoModel.BaseInfo?.canShowCompanyName(): Boolean {
    return !this?.companyName.isNullOrBlank() || !this?.companyNameShort.isNullOrBlank()
}

/**
 * 认证信息列表适配器
 */
class PreviewAuthInfoAdapter : BaseQuickAdapter<String, BaseViewHolder>(R.layout.parent_review_user_identity_tag_item) {
    override fun convert(holder: BaseViewHolder, item: String) {
        holder.setText(R.id.tv_tag_name, item)
    }
}

