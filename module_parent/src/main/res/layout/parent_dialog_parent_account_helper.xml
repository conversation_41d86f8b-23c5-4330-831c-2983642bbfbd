<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_corner_20_top_2_color_white"
    android:paddingHorizontal="20dp"
    android:paddingBottom="40dp">

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:src="@drawable/common_black_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:text="@string/parent_account_helper_question_1"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_18"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintEnd_toStartOf="@id/iv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_close" />


    <TextView
        android:id="@+id/tvQuestion1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="10dp"
        android:text="@string/parent_account_helper_question_1"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_18"
        app:layout_constraintBottom_toBottomOf="@id/iv_close"
        app:layout_constraintEnd_toStartOf="@id/iv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_close" />


    <TextView
        android:id="@+id/tvAnswer1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="10dp"
        android:text="@string/parent_account_helper_answer_1"
        android:textColor="@color/common_color_5E5E5E"
        android:textSize="@dimen/common_text_sp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvQuestion1" />

    <TextView
        android:id="@+id/tvQuestion2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="10dp"
        android:text="@string/parent_account_helper_question_2"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAnswer1" />


    <TextView
        android:id="@+id/tvAnswer2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="10dp"
        android:text="@string/parent_account_helper_answer_2"
        android:textColor="@color/common_color_5E5E5E"
        android:textSize="@dimen/common_text_sp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvQuestion2" />

    <TextView
        android:id="@+id/tvProtocol"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="账号绑定协议"
        android:textColor="@color/common_color_0046BD"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAnswer2" />

</androidx.constraintlayout.widget.ConstraintLayout>
