<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginTop="8dp"
    android:background="@drawable/common_bg_conor_12_color_white"
    android:orientation="vertical"
    android:paddingBottom="24dp">


    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:text="@string/parent_marry_info"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_24" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:orientation="vertical"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:itemCount="3"
        tools:listitem="@layout/parent_home_list_item_info_item" />


    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tv_sub_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:text="@string/parent_family_desc"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_24" />

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="8dp"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_18"
        tools:text="@string/common_long_placeholder" />

</LinearLayout>