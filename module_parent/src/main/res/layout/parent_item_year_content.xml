<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="14dp"
    android:layout_height="wrap_content">

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:layout_width="wrap_content"
        android:id="@+id/idItem"
        android:paddingTop="8dp"
        android:paddingBottom="7dp"
        app:qmui_backgroundColor="@color/common_color_F5F5F5"
        android:minHeight="40dp"
        android:minWidth="100dp"
        app:qmui_radius="22.5dp"
        android:textSize="18dp"
        android:textColor="@color/common_color_292929"
        android:paddingHorizontal="28dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center"
        tools:text="2000"
        android:layout_height="wrap_content"/>
    
    


</androidx.constraintlayout.widget.ConstraintLayout>