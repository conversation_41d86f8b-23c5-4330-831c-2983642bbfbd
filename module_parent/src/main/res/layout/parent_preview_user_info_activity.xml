<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/common_color_F5F5F5"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.kanzhun.common.views.AppTitleView
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
         />

    <com.kanzhun.common.kotlin.ui.statelayout.StateLayout
        android:id="@+id/stateLayout"
        app:empty_layout="@layout/parent_preview_user_info_empty_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/common_white">

                <com.kanzhun.marry.parent.views.ParentShareButtonView
                    android:id="@+id/shareView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginVertical="10dp" />

            </FrameLayout>
        </LinearLayout>
    </com.kanzhun.common.kotlin.ui.statelayout.StateLayout>


</LinearLayout>